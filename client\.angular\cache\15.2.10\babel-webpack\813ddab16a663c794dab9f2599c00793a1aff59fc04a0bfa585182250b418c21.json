{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { environment } from '../../../../../environments/environment';\nimport { EventEmitter } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { AppConfig } from 'app/app-config';\nimport { DataTableDirective } from 'angular-datatables';\nimport Swal from 'sweetalert2';\nimport moment from 'moment';\nimport { FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"app/services/stage.service\";\nimport * as i6 from \"../../../../services/user.service\";\nimport * as i7 from \"app/services/loading.service\";\nimport * as i8 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i9 from \"ngx-toastr\";\nimport * as i10 from \"app/services/export.service\";\nimport * as i11 from \"@ng-bootstrap/ng-bootstrap\";\nconst _c0 = [\"modalAssignReferee\"];\nfunction StageMatchesComponent_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 9, 10)(2, \"li\", 11)(3, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function StageMatchesComponent_ul_4_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.filterFriendlyMatches(false));\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 11)(7, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function StageMatchesComponent_ul_4_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.filterFriendlyMatches(true));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"All Matches\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 4, \"Friendly Matches\"));\n  }\n}\nfunction StageMatchesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-assign-referees\", 13);\n    i0.ɵɵlistener(\"onSubmit\", function StageMatchesComponent_ng_template_8_Template_app_modal_assign_referees_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSubmitAssignReferee($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"isMultipleAssign\", ctx_r2.isMultipleAssign)(\"selectedIds\", ctx_r2.selectedIds)(\"listReferees\", ctx_r2.listReferees)(\"assignRefereeForm\", ctx_r2.assignRefereeForm)(\"assignRefereeFields\", ctx_r2.assignRefereeFields)(\"assignRefereeModel\", ctx_r2.assignRefereeModel);\n  }\n}\nexport class StageMatchesComponent {\n  // setup referee\n  constructor(_http, _route, _translateService, _commonsService, _stageService, _userService, _loadingService, _coreSidebarService, _toastr, _exportService, _modalService) {\n    this._http = _http;\n    this._route = _route;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._stageService = _stageService;\n    this._userService = _userService;\n    this._loadingService = _loadingService;\n    this._coreSidebarService = _coreSidebarService;\n    this._toastr = _toastr;\n    this._exportService = _exportService;\n    this._modalService = _modalService;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.AppConfig = AppConfig;\n    this.hasMatch = false;\n    this.isComplete = false;\n    this.onUpdateScore = new EventEmitter();\n    this.onDataChange = new EventEmitter();\n    this.listReferees = [];\n    this.friendlyMatchesActive = false;\n    this.fields_subject = new Subject();\n    this.cancelOptions = [];\n    this.paramsToPost = {};\n    this.is_score_updated = false;\n    this.table_name = 'matches-table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Add match'),\n        edit: this._translateService.instant('Edit match'),\n        remove: this._translateService.instant('Remove match')\n      },\n      url: `${environment.apiUrl}/stage-matches/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.location = {\n      options: [],\n      selected: null\n    };\n    this.teams = {\n      options: [],\n      selected: null\n    };\n    this.roundLevelOpts = [];\n    this.editMatch = [{\n      key: 'date',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Date'),\n        placeholder: this._translateService.instant('Enter date of match'),\n        // required: true,\n        type: 'date',\n        max: '2100-12-31'\n      },\n      expressions: {\n        'props.required': '(model.hasOwnProperty(\"start_time_short\") && model.start_time_short!=\"\") || (model.hasOwnProperty(\"end_time_short\") && model.end_time_short!=\"\")'\n      }\n    }, {\n      key: 'start_time_short',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Start time'),\n        placeholder: this._translateService.instant('Enter start time of match'),\n        // required: true,\n        type: 'time'\n      },\n      expressions: {\n        'props.required': '(model.hasOwnProperty(\"date\") && model.date!=\"\") || (model.hasOwnProperty(\"end_time_short\") && model.end_time_short!=\"\")'\n      }\n    }, {\n      key: 'end_time_short',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('End time'),\n        placeholder: this._translateService.instant('Enter end time of match'),\n        // required: true,\n        type: 'time'\n      }\n    }, {\n      key: 'location_id',\n      type: 'select',\n      props: {\n        label: this._translateService.instant('Location'),\n        placeholder: this._translateService.instant('Select location'),\n        // required: true,\n        options: this.location.options\n      }\n    }, {\n      key: 'home_team_id',\n      type: 'select',\n      props: {\n        hideOnMultiple: true,\n        label: this._translateService.instant('Home team'),\n        placeholder: this._translateService.instant('Select home team'),\n        options: []\n      }\n    }, {\n      key: 'away_team_id',\n      type: 'select',\n      props: {\n        hideOnMultiple: true,\n        label: this._translateService.instant('Away team'),\n        placeholder: this._translateService.instant('Select away team'),\n        options: []\n      }\n    }];\n    this.updateScore = [];\n    this.cancelMatch = [{\n      key: 'status',\n      type: 'radio',\n      props: {\n        label: this._translateService.instant('Cancel type'),\n        required: true,\n        options: this.cancelOptions\n      }\n    }, {\n      key: 'description',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Reason'),\n        placeholder: this._translateService.instant('Enter reason')\n      }\n    }];\n    this.updateRankMatch = [{\n      key: 'home_label_id',\n      type: 'ng-select',\n      props: {\n        hideOnMultiple: true,\n        label: this._translateService.instant('Home label'),\n        placeholder: this._translateService.instant('Select group'),\n        closeOnSelect: true,\n        options: []\n      }\n    }, {\n      key: 'away_label_id',\n      type: 'ng-select',\n      props: {\n        hideOnMultiple: true,\n        label: this._translateService.instant('Away label'),\n        placeholder: this._translateService.instant('Select away label'),\n        closeOnSelect: true,\n        options: []\n      }\n    }];\n    this.fields = this.editMatch;\n    this.rank_fields = this.updateRankMatch;\n    this.isMultipleAssign = true;\n    this.selectedIds = [];\n    this.assignRefereeForm = new FormGroup({});\n    this.assignRefereeModel = {};\n    this.assignRefereeFields = [{\n      key: 'list_referees',\n      type: 'ng-select',\n      props: {\n        multiple: true,\n        hideOnMultiple: true,\n        defaultValue: [],\n        label: this._translateService.instant('Match Referee'),\n        placeholder: this._translateService.instant('Select match referees'),\n        options: []\n      },\n      hooks: {}\n    }];\n  }\n  ngOnInit() {\n    this.loadInitSettings();\n    this.setUp();\n    this.buildTable();\n    this.getListReferee();\n  }\n  loadInitSettings() {\n    const settings = localStorage.getItem('initSettings');\n    if (settings) {\n      this.initSettings = JSON.parse(settings);\n      console.log('initSettings:', this.initSettings);\n    } else {\n      console.log('No initSettings found in localStorage');\n    }\n  }\n  setUp() {\n    AppConfig.CANCEL_MATCH_TYPES.forEach(type => {\n      this.cancelOptions.push({\n        value: type,\n        label: this._translateService.instant(type)\n      });\n    });\n    this.editMatch.push({\n      key: 'stage_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      },\n      defaultValue: this.stage.id\n    });\n    this.updateScore = [{\n      key: 'type',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      },\n      defaultValue: this.stage.type\n    }, {\n      key: 'home_score',\n      type: 'core-touchspin',\n      props: {\n        label: this._translateService.instant('Home score'),\n        required: true,\n        type: 'number',\n        min: 0,\n        max: 999,\n        step: 1\n      },\n      defaultValue: 0\n    }, {\n      key: 'away_score',\n      type: 'core-touchspin',\n      props: {\n        label: this._translateService.instant('Away score'),\n        required: true,\n        type: 'number',\n        min: 0,\n        max: 999,\n        step: 1\n      },\n      defaultValue: 0\n    }, {\n      key: 'home_penalty',\n      type: 'core-touchspin',\n      props: {\n        label: this._translateService.instant('Home penalty score'),\n        // required: true,\n        type: 'number',\n        min: 0,\n        max: 100,\n        step: 1\n      },\n      expressions: {\n        hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\n      },\n      defaultValue: 0\n    }, {\n      key: 'away_penalty',\n      type: 'core-touchspin',\n      props: {\n        label: this._translateService.instant('Away penalty score'),\n        // required: true,\n        type: 'number',\n        min: 0,\n        max: 100,\n        step: 1\n      },\n      expressions: {\n        hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\n      },\n      defaultValue: 0\n    }];\n    this.contentHeader = {\n      headerTitle: this._translateService.instant('League Reports'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._translateService.instant('Leagues'),\n          isLink: false\n        }, {\n          name: this._translateService.instant('League Reports'),\n          isLink: false\n        }]\n      }\n    };\n    if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league || this.stage.type === AppConfig.TOURNAMENT_TYPES.groups) {\n      // get round level options in stage\n      for (let i = 1; i <= this.stage.no_encounters; i++) {\n        this.roundLevelOpts.push({\n          value: `${i}`,\n          label: `${this._translateService.instant('Round')} ${i}`\n        });\n      }\n    }\n    if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n      // add match type at beginning of editMatch\n      this.editMatch.splice(0, 0, {\n        key: 'match_type',\n        type: 'select',\n        props: {\n          label: this._translateService.instant('Match type'),\n          required: true,\n          options: [{\n            value: 1,\n            label: this._translateService.instant('League Match')\n          }, {\n            value: 2,\n            label: this._translateService.instant('Friendly Match')\n          }]\n        }\n      });\n    }\n    if (this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout) {\n      // find round level index in editMatch\n      let roundLevelIndex = this.editMatch.findIndex(field => field.key === 'round_level');\n      // remove round level in editMatch\n      if (roundLevelIndex > -1) {\n        this.editMatch.splice(roundLevelIndex, 1);\n      }\n      // add round name at beginning of editMatch\n      this.editMatch.splice(0, 0, {\n        key: 'round_name',\n        type: 'input',\n        props: {\n          hideOnMultiple: true,\n          label: this._translateService.instant('Match name'),\n          placeholder: this._translateService.instant('Enter round name')\n        }\n      });\n    }\n  }\n  buildTable() {\n    var _this = this;\n    let btns = [{\n      text: `<i class=\"fa-solid fa-plus\"></i> ${this._translateService.instant('Add')}`,\n      action: (e, dt, node, config) => {\n        // check if stage type is league\n        if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('You can not add match in this stage'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        this.editor('create', 'editMatch');\n      }\n    }, {\n      // drop down button\n      extend: 'collection',\n      background: false,\n      text: this._translateService.instant('Manage Matches'),\n      className: 'action-button',\n      buttons: [{\n        attr: {\n          id: 'auto-generate-btn'\n        },\n        text: `<i class=\"fa-solid fa-wand-magic-sparkles\"></i> ${this._translateService.instant('Auto Generate')}`,\n        action: (e, dt, node, config) => {\n          this.autoGenerateMatches();\n        }\n      }, {\n        text: `<i class=\"fa-solid fa-repeat-1\"></i> ${this._translateService.instant('Add replace match')}`,\n        action: (e, dt, node, config) => {\n          // get selected rows\n          let selectedRows = dt.rows({\n            selected: true\n          }).data();\n          // check if selected rows > 1\n          if (selectedRows.length > 1) {\n            Swal.fire({\n              title: this._translateService.instant('Notification'),\n              text: this._translateService.instant('Please select only one match'),\n              icon: 'warning',\n              confirmButtonText: this._translateService.instant('Ok'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n            return;\n          }\n          let row = selectedRows[0];\n          let hasCancel = false;\n          if (this.checkRowIsCancelled(row)) {\n            hasCancel = true;\n          }\n          if (!hasCancel) {\n            Swal.fire({\n              title: this._translateService.instant('Notification'),\n              text: this._translateService.instant('You can not add replace match for this match'),\n              icon: 'warning',\n              confirmButtonText: this._translateService.instant('Ok'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n            return;\n          }\n          this.editor('create', 'addReplaceMatch', row);\n        },\n        extend: 'selected'\n      }, {\n        text: `<i class=\"feather icon-repeat\"></i> ${this._translateService.instant('Swap teams')}`,\n        action: (e, dt, node, config) => {\n          // get selected rows\n          let selectedRows = dt.rows({\n            selected: true\n          }).data();\n          // check if selected rows > 1\n          if (selectedRows.length > 1) {\n            Swal.fire({\n              title: this._translateService.instant('Notification'),\n              text: this._translateService.instant('Please select only one match'),\n              icon: 'warning',\n              confirmButtonText: this._translateService.instant('Ok'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n            return;\n          }\n          // check if home_team_id or away_team_id is null\n          if (!selectedRows[0].home_team_id || !selectedRows[0].away_team_id) {\n            Swal.fire({\n              title: this._translateService.instant('Notification'),\n              text: this._translateService.instant('You can not swap teams for this match'),\n              icon: 'warning',\n              confirmButtonText: this._translateService.instant('Ok'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n            return;\n          } else {\n            // confirm swap teams\n            Swal.fire({\n              title: this._translateService.instant('Are you sure?'),\n              text: this._translateService.instant('Are you sure you want to swap teams in this match?'),\n              icon: 'warning',\n              reverseButtons: true,\n              showCancelButton: true,\n              confirmButtonText: this._translateService.instant('Yes'),\n              cancelButtonText: this._translateService.instant('No'),\n              buttonsStyling: false,\n              customClass: {\n                confirmButton: 'btn btn-primary ml-1',\n                cancelButton: 'btn btn-outline-primary'\n              }\n            }).then(result => {\n              if (result.value) {\n                this.swapTeams(selectedRows[0]);\n              }\n            });\n          }\n        },\n        extend: 'selected'\n      }, {\n        text: `<i class=\"feather icon-flag\"></i>${this._translateService.instant('Assign Referee')}`,\n        action: () => this.openModal(),\n        extend: 'selected'\n      }]\n    }, {\n      // drop down button\n      extend: 'collection',\n      background: false,\n      text: this._translateService.instant('Manage Score'),\n      className: 'action-button',\n      buttons: [{\n        text: `<i class=\"feather icon-edit\"></i> ${this._translateService.instant('Update Score')}`,\n        action: (e, dt, node, config) => {\n          let selectedRows = dt.rows({\n            selected: true\n          }).data();\n          let row = selectedRows[0];\n          let validUpdate = false;\n          if (this.checkValidateUpdateScore(row)) {\n            validUpdate = true;\n          }\n          if (!validUpdate) {\n            Swal.fire({\n              title: this._translateService.instant('Cannot update score'),\n              text: this._translateService.instant('Please update the date, time and location of the match'),\n              icon: 'warning',\n              confirmButtonText: this._translateService.instant('Ok'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n          } else {\n            this.editor('edit', 'updateScore');\n          }\n        },\n        extend: 'selected'\n      }, {\n        text: `<i class=\"feather icon-rotate-ccw\"></i> ${this._translateService.instant('Reset Score')}`,\n        action: (e, dt, node, config) => {\n          // get selected rows\n          let selectedRows = dt.rows({\n            selected: true\n          }).data();\n          // get stage_match id\n          let ids = [];\n          selectedRows.map(row => ids.push(row.id));\n          // confirm reset score\n          Swal.fire({\n            title: this._translateService.instant('Are you sure?'),\n            text: this._translateService.instant('Are you sure you want to reset score this match(s)?'),\n            reverseButtons: true,\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonText: this._translateService.instant('Yes'),\n            cancelButtonText: this._translateService.instant('No'),\n            buttonsStyling: false,\n            customClass: {\n              confirmButton: 'btn btn-primary ml-1',\n              cancelButton: 'btn btn-outline-primary'\n            }\n          }).then(result => {\n            if (result.value) {\n              this.resetScore(ids);\n            }\n          });\n        },\n        extend: 'selected'\n      }]\n    }, {\n      text: `<i class=\"feather icon-edit\"></i>${this._translateService.instant('Edit')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        if (selectedRows.length > 1) {\n          // hide round level\n          let roundLevelIndex = this.editMatch.findIndex(x => x.key == 'round_level');\n          if (roundLevelIndex > -1) {\n            this.editMatch.splice(roundLevelIndex, 1);\n          }\n          let roundNameIndex = this.editMatch.findIndex(x => x.key == 'round_name');\n          if (roundNameIndex > -1) {\n            this.editMatch.splice(roundNameIndex, 1);\n          }\n        } else {\n          // show round level\n          let roundLevelIndex = this.editMatch.findIndex(x => x.key == 'round_level');\n          // if not found\n          if (roundLevelIndex == -1 && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n            this.editMatch.splice(3, 0, {\n              key: 'round_level',\n              type: 'select',\n              props: {\n                label: this._translateService.instant('Round level'),\n                placeholder: this._translateService.instant('Select round level'),\n                // required: true,\n                options: this.roundLevelOpts\n              }\n            });\n          }\n          if (this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout) {\n            let roundNameIndex = this.editMatch.findIndex(x => x.key == 'round_name');\n            if (roundNameIndex == -1) {\n              this.editMatch.splice(0, 0, {\n                key: 'round_name',\n                type: 'input',\n                props: {\n                  label: this._translateService.instant('Match name'),\n                  placeholder: this._translateService.instant('Enter round name')\n                }\n              });\n            }\n          }\n        }\n        this.editor('edit', 'editMatch');\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"fa-solid fa-repeat-1\"></i> ${this._translateService.instant('Add replace match')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // check if selected rows > 1\n        if (selectedRows.length > 1) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('Please select only one match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        let row = selectedRows[0];\n        let hasCancel = false;\n        if (this.checkRowIsCancelled(row)) {\n          hasCancel = true;\n        }\n        if (!hasCancel) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('You can not add replace match for this match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        this.editor('create', 'addReplaceMatch', row);\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"feather icon-repeat\"></i> ${this._translateService.instant('Swap teams')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // check if selected rows > 1\n        if (selectedRows.length > 1) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('Please select only one match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        // check if home_team_id or away_team_id is null\n        if (selectedRows[0].status !== 'pass' && (!selectedRows[0].home_team_id || !selectedRows[0].away_team_id)) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('You can not swap teams for this match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        } else {\n          // confirm swap teams\n          Swal.fire({\n            title: this._translateService.instant('Are you sure?'),\n            text: this._translateService.instant('Are you sure you want to swap teams in this match?'),\n            icon: 'warning',\n            reverseButtons: true,\n            showCancelButton: true,\n            confirmButtonText: this._translateService.instant('Yes'),\n            cancelButtonText: this._translateService.instant('No'),\n            buttonsStyling: false,\n            customClass: {\n              confirmButton: 'btn btn-primary ml-1',\n              cancelButton: 'btn btn-outline-primary'\n            }\n          }).then(result => {\n            if (result.value) {\n              this.swapTeams(selectedRows[0]);\n            }\n          });\n        }\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"fa-solid fa-ban\"></i> ${this._translateService.instant('Cancel')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // check if selected rows has status is not can play\n        let hasCancel = false;\n        selectedRows.map(row => {\n          if (this.checkRowIsCancelled(row)) {\n            hasCancel = true;\n          }\n        });\n        if (hasCancel) {\n          Swal.fire({\n            title: this._translateService.instant('Warning'),\n            text: this._translateService.instant('You can not cancel match that has been cancelled'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('OK'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        // confirm cancel match\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          text: this._translateService.instant('Are you sure you want to cancel this match(s)?'),\n          reverseButtons: true,\n          icon: 'warning',\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Yes'),\n          cancelButtonText: this._translateService.instant('No'),\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary ml-1',\n            cancelButton: 'btn btn-outline-primary'\n          }\n        }).then(result => {\n          if (result.value) {\n            this.editor('edit', 'cancelMatch');\n          }\n        });\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"feather icon-trash\"></i> ${this._translateService.instant('Delete')}`,\n      extend: 'selected',\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // get ids\n        let ids = [];\n        selectedRows.map(row => {\n          ids.push(row.id);\n        });\n        // confirm delete\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          text: this._translateService.instant('You will not be able to recover this!'),\n          reverseButtons: true,\n          icon: 'warning',\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Yes'),\n          cancelButtonText: this._translateService.instant('No'),\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary ml-1',\n            cancelButton: 'btn btn-outline-primary'\n          }\n        }).then(result => {\n          if (result.value) {\n            // delete\n            this._loadingService.show();\n            this._stageService.deleteMatchesInStage(ids, this.stage.id).toPromise().then(resp => {\n              this._toastr.success(this._translateService.instant('Deleted successfully'));\n              dt.ajax.reload();\n              this.onDataChange.emit(resp);\n            });\n          }\n        });\n      }\n    }, {\n      text: `<i class=\"fas fa-file-export mr-1\"></i> ${this._translateService.instant('Export')}`,\n      extend: 'csv',\n      action: function () {\n        var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n          const data = dt.buttons.exportData();\n          yield _this._exportService.exportCsv(data, 'Matches.csv');\n        });\n        return function action(_x, _x2, _x3, _x4) {\n          return _ref.apply(this, arguments);\n        };\n      }()\n    }, {\n      text: `<i class=\"fa-solid fa-code\"></i> ${this._translateService.instant('Define Team')}`,\n      action: (e, dt, node, config) => {\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // check if selected rows > 1\n        if (selectedRows.length > 1) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('Please select only one match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        this.editor('edit', 'updateRankMatch');\n      },\n      extend: 'selected'\n    }, {\n      text: this._translateService.instant('Columns'),\n      extend: 'colvis'\n    }];\n    // if stage type is league\n    if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {\n      // remove first button\n      btns.splice(0, 1);\n    }\n    if (this.stage.type != AppConfig.TOURNAMENT_TYPES.knockout) {\n      btns.splice(-2, 1);\n    }\n    if (this.tournament.type_knockout != AppConfig.KNOCKOUT_TYPES.type4 && this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout) {\n      const manageMatchesGroup = btns.find(btn => btn.text === \"Manage Matches\");\n      if (manageMatchesGroup && Array.isArray(manageMatchesGroup.buttons)) {\n        manageMatchesGroup.buttons = manageMatchesGroup.buttons.filter(btn => !(btn.attr && btn.attr.id === \"auto-generate-btn\"));\n      }\n      // btns.splice(0, 1);\n    }\n\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: window.innerWidth > 768 ? 'os' : 'multi',\n      // serverSide: true,\n      rowId: 'id',\n      rowGroup: {\n        dataSrc: 'group_round'\n      },\n      order: [[2, 'asc']],\n      ajax: (dataTablesParameters, callback) => {\n        // add season id\n        this._http.post(`${environment.apiUrl}/stage-matches/all-in-stage/${this.stage.id}`, dataTablesParameters).subscribe(resp => {\n          // find fields has key location_id and set options\n          this.fields.forEach(field => {\n            if (field.key === 'location_id') {\n              field.props.options = resp.options.location;\n            }\n            if (field.key === 'home_team_id') {\n              field.props.options = resp.options.teams;\n            }\n            if (field.key === 'away_team_id') {\n              field.props.options = resp.options.teams;\n            }\n          });\n          this.rank_fields.forEach(field => {\n            if (field.key === 'home_label_id') {\n              field.props.options = resp.options.rank_label;\n            }\n            if (field.key === 'away_label_id') {\n              field.props.options = resp.options.rank_label;\n            }\n          });\n          this.filterFriendlyMatches(this.friendlyMatchesActive);\n          this.hasMatch = resp.data.length > 0;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      initComplete: () => {\n        this.isComplete = true;\n      },\n      responsive: false,\n      scrollX: true,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -7\n      }, {\n        responsivePriority: 1,\n        targets: -6\n      }, {\n        responsivePriority: 1,\n        targets: -5\n      }, {\n        responsivePriority: 1,\n        targets: -4\n      }, {\n        responsivePriority: 1,\n        targets: -3\n      }, {\n        responsivePriority: 1,\n        targets: -2\n      }],\n      columns: [{\n        data: 'order',\n        visible: false\n      }, {\n        data: 'match_type',\n        visible: false\n      }, {\n        data: 'group_round',\n        visible: false\n      }, {\n        title: this._translateService.instant('No'),\n        data: 'match_number',\n        visible: this.stage.type !== AppConfig.TOURNAMENT_TYPES.league,\n        render: function (data, type, row, meta) {\n          return data ? `${data}` : `${meta.row + 1}`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'date',\n        orderable: false,\n        className: 'text-center p-1',\n        render: function (data, type, row) {\n          let content = '';\n          if (!data || data === 'TBD') {\n            content = 'TBD';\n          } else {\n            // format to HH:mm from ISO 8601\n            content = moment(data).format('YYYY-MM-DD');\n          }\n          return `<p style=\"margin:0; min-width: max-content\">${content}</p>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time_short',\n        orderable: false,\n        className: 'text-center p-1',\n        render: function (data, type, row) {\n          if (!data || row.start_time_short == 'TBD' || !row.start_time_short) {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time_short',\n        orderable: false,\n        className: 'text-center p-1',\n        render: function (data, type, row) {\n          if (!data || row.end_time_short == 'TBD' || !row.end_time_short) {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location_name'\n      }, {\n        title: this._translateService.instant('Home team'),\n        data: 'home_team_name',\n        className: 'text-center p-1',\n        orderable: false,\n        render: function (data, type, row) {\n          if (data === 'TBD') {\n            if (row.home_text) {\n              return row.home_text;\n            } else if (row.home_label && row.home_label.label) {\n              return row.home_label.label;\n            }\n          }\n          return data;\n        }\n      }, {\n        data: null,\n        className: 'text-center p-0 font-weight-bolder',\n        render: function (data, type, row) {\n          return `VS`;\n        },\n        orderable: false\n      }, {\n        title: this._translateService.instant('Away team'),\n        data: 'away_team_name',\n        className: 'text-center p-1',\n        orderable: false,\n        render: function (data, type, row) {\n          if (data === 'TBD') {\n            if (row.away_text) {\n              return row.away_text;\n            } else if (row.away_label && row.away_label.label) {\n              return row.away_label.label;\n            }\n          }\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center p-1',\n        orderable: false,\n        render: function (data, type, row) {\n          if (row.home_penalty != null) {\n            return `${data}<br> ( ${row.home_penalty} )`;\n          }\n          return data;\n        }\n      }, {\n        data: null,\n        className: 'text-center p-0',\n        orderable: false,\n        render: function (data, type, row) {\n          return ` - `;\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center p-1',\n        orderable: false,\n        render: function (data, type, row) {\n          if (row.away_penalty != null) {\n            return `${data}<br> ( ${row.away_penalty} )`;\n          }\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Referees'),\n        visible: false,\n        data: 'referees',\n        render: data => {\n          return `<div class=\"d-flex flex-column gap-1\">\n              ${data.map(item => {\n            const refereeName = item.user ? `${item.user.first_name} ${item.user.last_name}` : item.referee_name;\n            return `<p style=\"min-width: max-content; margin: 0\">${refereeName}</p>`;\n          }).join('')}\n            </div>`;\n        }\n      }, {\n        title: this._translateService.instant('Status'),\n        data: 'status',\n        render: (data, type, row) => {\n          const now = moment();\n          const currentTime = now.format('HH:mm');\n          const today = now.format('YYYY-MM-DD');\n          if (!data) {\n            // check if match time is over\n            if (row.end_time_short == 'TBD') {\n              return `<span class=\"badge badge-secondary text-capitalize\">TBD</span>`;\n            } else {\n              if (row.date == today && row.end_time_short < currentTime || row.date < today) {\n                return `<span class=\"badge badge-success text-capitalize\">${this._translateService.instant('Finished')}</span>`;\n              } else if (row.date == today && row.start_time_short <= currentTime && row.end_time_short >= currentTime) {\n                return `<span class=\"badge badge-warning text-capitalize\">${this._translateService.instant('In Progress')}</span>`;\n              } else if (row.date > today || row.date == today && row.start_time_short > currentTime) {\n                return `<span class=\"badge badge-info text-capitalize\">${this._translateService.instant('Upcoming')}</span>`;\n              }\n            }\n          } else {\n            if (AppConfig.CANCEL_MATCH_TYPES.includes(data)) {\n              return `<span class=\"badge badge-danger text-capitalize\">${data}</span>`;\n            } else {\n              return `<span class=\"badge badge-secondary text-capitalize\">${data}</span>`;\n            }\n          }\n        },\n        className: 'text-center p-1',\n        orderable: false\n      }],\n      lengthMenu: [[-1], ['All']],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: btns\n      }\n    };\n    switch (this.stage.type) {\n      case AppConfig.TOURNAMENT_TYPES.knockout:\n        this.dtOptions.order = [[0, 'asc']];\n        // add rowGroup\n        this.dtOptions.rowGroup = {\n          dataSrc: 'round_name'\n        };\n        // insert round_name column at index 6\n        this.dtOptions.columns.splice(7, 0, {\n          title: this._translateService.instant('Name'),\n          data: 'round_name',\n          className: 'text-center',\n          render: function (data) {\n            return `<p style=\"margin:0; min-width: max-content\">${data}</p>`;\n          }\n        });\n        break;\n      case AppConfig.TOURNAMENT_TYPES.groups:\n        this.dtOptions.order = [[2, 'asc']];\n        // insert round_name column at index 5\n        this.dtOptions.columns.splice(7, 0, {\n          title: this._translateService.instant('Round'),\n          data: 'round_name',\n          className: 'text-center',\n          render: function (data, type, row) {\n            // split round_name by - and get the last item\n            if (!data) return '';\n            let round_name = data.split('-').pop();\n            return `<p style=\"margin:0; min-width: max-content\">${round_name}</p>`;\n          }\n        });\n        break;\n      case AppConfig.TOURNAMENT_TYPES.league:\n        // clear rowGroup\n        this.dtOptions.rowGroup = null;\n        this.dtOptions.columns.splice(7, 0, {\n          title: this._translateService.instant('Round'),\n          data: 'round_name',\n          className: 'text-center',\n          render: function (data) {\n            console.log('data', data);\n            return `<p style=\"margin:0; min-width: max-content\">${data ?? ''}</p>`;\n          }\n        });\n        // order by round_level\n        this.dtOptions.order = [[5, 'asc']];\n        break;\n      default:\n        break;\n    }\n  }\n  addOrRemoveRoundLevel(is_friendly) {\n    const round_level = {\n      key: 'round_level',\n      type: 'select',\n      props: {\n        label: this._translateService.instant('Round level'),\n        placeholder: this._translateService.instant('Select round level'),\n        // required: true,\n        options: this.roundLevelOpts\n      }\n    };\n    if (is_friendly) {\n      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {\n        // remove round_level from editMatch\n        const round_level_index = this.editMatch.findIndex(item => item.key === 'round_level');\n        // check if round_level exist\n        if (round_level_index > -1) {\n          this.editMatch.splice(round_level_index, 1);\n        }\n      }\n    } else {\n      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {\n        const round_level_index = this.editMatch.findIndex(item => item.key === 'round_level');\n        // check if round_level not exist\n        if (round_level_index < 0) {\n          this.editMatch.splice(2, 0, {\n            ...round_level\n          });\n        }\n      }\n    }\n  }\n  filterFriendlyMatches(active) {\n    this.friendlyMatchesActive = active;\n    this.addOrRemoveRoundLevel(active);\n    // deselect all rows\n    this.dtElement.dtInstance.then(dtInstance => {\n      // round level backup\n      dtInstance.rows().deselect();\n      if (!active) {\n        // show column round name\n        dtInstance.column(5).visible(true);\n        dtInstance.column(1).search(1).draw();\n        // disable button auto generate\n        let btns = dtInstance.button('#auto-generate-btn');\n        console.log(btns);\n        if (btns) {\n          btns.enable();\n        }\n      } else {\n        // hide column round name\n        dtInstance.column(5).visible(true);\n        dtInstance.column(1).search(2).draw();\n        // enable button auto generate\n        let btns = dtInstance.button('#auto-generate-btn');\n        console.log(btns);\n        if (btns) {\n          btns.disable();\n        }\n      }\n    });\n  }\n  checkRowIsCancelled(row) {\n    let hasCancel = false;\n    // if row has status in AppConfig.CANCEL_MATCH_TYPES\n    if (AppConfig.CANCEL_MATCH_TYPES.includes(row.status)) {\n      hasCancel = true;\n    }\n    return hasCancel;\n  }\n  _generateMatches(confirmOverride = false) {\n    const formData = new FormData();\n    formData.append('stage_id', this.stage.id.toString());\n    formData.append('confirm_override', confirmOverride ? '1' : '0');\n    this._stageService.autoGenerateMatches(formData).subscribe(resp => {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n        this.onDataChange.emit(resp);\n      });\n    }, error => {\n      if (error.warning) {\n        Swal.fire({\n          title: this._translateService.instant('Cannot Auto Generate'),\n          html: error.warning.replace(/\\n/g, '<br>'),\n          icon: 'warning',\n          confirmButtonText: this._translateService.instant('OK'),\n          customClass: {\n            confirmButton: 'btn btn-primary'\n          }\n        });\n        return;\n      } else {\n        Swal.fire({\n          title: this._translateService.instant('Error'),\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK'),\n          customClass: {\n            confirmButton: 'btn btn-primary'\n          }\n        });\n      }\n    });\n  }\n  autoGenerateMatches() {\n    if (this.hasMatch) {\n      this._loadingService.show();\n      Swal.fire({\n        title: `${this._translateService.instant('Are you sure?')}`,\n        html: `\n            <p>${this._translateService.instant('All current matches will be deleted unless they contain information')}.</p>\n            <div class=\"swal2-checkbox-container\" style=\"display:flex;justify-content:center;gap: 10px;\">\n                <input type=\"checkbox\" id=\"confirmCheckbox\" >\n                <label for=\"confirmCheckbox\" class=\"swal2-label\">${this._translateService.instant('I confirm that I want to generate all current matches')}</label>\n            </div>\n        `,\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonText: this._translateService.instant('Yes'),\n        cancelButtonText: this._translateService.instant('No')\n      }).then(result => {\n        if (result.isConfirmed) {\n          const checkbox = document.getElementById('confirmCheckbox');\n          const isChecked = checkbox?.checked ?? false;\n          Swal.fire({\n            title: this._translateService.instant('Match number will be reset'),\n            html: `<p>${this._translateService.instant('All match numbers in the tournament will be reset and regenerated. Are you sure you want to continue?')}</p>`,\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonText: this._translateService.instant('Yes, continue'),\n            cancelButtonText: this._translateService.instant('Cancel')\n          }).then(subResult => {\n            if (subResult.isConfirmed) {\n              this._generateMatches(isChecked);\n            } else {\n              this._loadingService.dismiss();\n            }\n          });\n        } else {\n          this._loadingService.dismiss();\n        }\n      });\n    } else {\n      if (this.isComplete) {\n        this._generateMatches(false);\n      }\n    }\n  }\n  resetScore(stage_match_id) {\n    console.log(`reset score for ${stage_match_id}`);\n    this._loadingService.show();\n    this._stageService.resetScore(stage_match_id).subscribe(resp => {\n      console.log(resp);\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n        this.onDataChange.emit(resp);\n      });\n    }, error => {\n      Swal.fire({\n        title: this._translateService.instant('Error'),\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK'),\n        customClass: {\n          confirmButton: 'btn btn-primary'\n        }\n      });\n    });\n  }\n  swapTeams(stage_match) {\n    this._loadingService.show();\n    this._stageService.swapTeams(stage_match).subscribe(resp => {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n        this.onDataChange.emit(resp);\n      });\n    });\n  }\n  onSuccess($event) {\n    this.dtElement.dtInstance.then(dtInstance => {\n      dtInstance.ajax.reload();\n    });\n    if (this.stage.type === AppConfig.TOURNAMENT_TYPES.groups) {\n      this._stageService.checkMatchScore(this.stage.id).subscribe(resp => {\n        console.log('checkMatchScore', resp);\n      }, error => {\n        console.log('checkMatchScore', error);\n      });\n    }\n    if (this.is_score_updated) {\n      this.onUpdateScore.emit($event);\n    }\n    this.onDataChange.emit($event);\n  }\n  checkValidateUpdateScore(row) {\n    return !(!row.date || !row.start_time || !row.end_time || !row.location);\n  }\n  editor(action, fields, row) {\n    this.params.use_data = false;\n    this.paramsToPost = {\n      stage_type: this.stage.type,\n      type_action: fields\n    };\n    // check has row with status is pass in selected rows\n    this.dtElement.dtInstance.then(dtInstance => {\n      let selectedRows = dtInstance.rows({\n        selected: true\n      }).data();\n      console.log('selectedRows', selectedRows);\n      switch (fields) {\n        case 'editMatch':\n          this.params.title.edit = this._translateService.instant('Edit match');\n          this.fields = [...this.editMatch, {\n            key: 'match_status',\n            type: 'input',\n            props: {\n              type: 'hidden'\n            },\n            defaultValue: selectedRows[0]?.status\n          }].map(item => ({\n            ...item\n          }));\n          console.log('fields', this.fields);\n          this.is_score_updated = false;\n          this._http.post(`${environment.apiUrl}/stage-matches/options/${this.stage.id}`, {}).subscribe(resp => {\n            this.fields.forEach(item => {\n              if (item.key === 'home_team_id' || item.key === 'away_team_id') {\n                item.props.options = resp.options.teams;\n              }\n              if (item.key === 'location_id') {\n                item.props.options = resp.options.location;\n              }\n            });\n            this.fields.forEach(item => {\n              if (item.key === 'referee') {\n                item.value = selectedRows[0]?.referees?.map(r => r.id) || [];\n              }\n            });\n            this.fields_subject.next(this.fields);\n            this.params.action = action;\n            this.params.row = row ? row : null;\n            this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n          });\n          return;\n        case 'updateScore':\n          this.params.title.edit = this._translateService.instant('Update Score');\n          this.fields = this.updateScore;\n          this.is_score_updated = true;\n          break;\n        case 'cancelMatch':\n          this.params.title.edit = this._translateService.instant('Cancel Match');\n          this.fields = this.cancelMatch;\n          this.is_score_updated = false;\n          break;\n        case 'addReplaceMatch':\n          console.log(row);\n          let paramsPost = {\n            'data[0][round_name]': row.round_name,\n            'data[0][round_level]': row.round_level,\n            'data[0][match_id]': row.id,\n            'data[0][match_number]': row.match_number || 0\n          };\n          // merge paramsToPost and paramsPost\n          this.paramsToPost = {\n            ...this.paramsToPost,\n            ...paramsPost\n          };\n          this.params.title.edit = this._translateService.instant('Add Replace Match');\n          this.fields = this.editMatch;\n          this.params.use_data = true;\n          break;\n        case 'updateRankMatch':\n          this.params.title.edit = this._translateService.instant('Edit Rank');\n          this.fields = this.updateRankMatch;\n          this.is_score_updated = false;\n          break;\n        default:\n          this.params.title.edit = this._translateService.instant('Add Match');\n          this.fields = this.editMatch.map(item => {\n            if (item.key === 'referee') {\n              item['defaultValue'] = [];\n              item['value'] = [];\n            }\n            return item;\n          });\n          this.is_score_updated = false;\n          break;\n      }\n      this.fields_subject.next(this.fields);\n      this.params.action = action;\n      this.params.row = row ? row : null;\n      this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n    });\n  }\n  onCloseSidebar(event) {\n    this.editMatch.forEach(e => {\n      if (e.key === 'referee') {\n        e['defaultValue'] = [];\n        e['value'] = [];\n      }\n    });\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n  }\n  openModal() {\n    this.dtElement.dtInstance.then(dtInstance => {\n      let selectedRows = dtInstance.rows({\n        selected: true\n      }).data();\n      this.selectedIds = [];\n      selectedRows.map(row => {\n        return this.selectedIds.push(row.id);\n      });\n      if (selectedRows.length > 1) {\n        this.isMultipleAssign = true;\n        this.assignRefereeModel = {\n          ...this.assignRefereeModel,\n          list_referees: []\n        };\n      } else {\n        this.isMultipleAssign = false;\n        this.assignRefereeModel = {\n          ...this.assignRefereeModel,\n          list_referees: selectedRows[0]?.referees?.map(r => r.id) || []\n        };\n      }\n      this._modalService.open(this.modalAssignReferee, {\n        centered: true,\n        size: 'lg',\n        beforeDismiss: () => {\n          this.assignRefereeModel = {};\n          this.assignRefereeFields.forEach(item => {\n            if (item.key === 'list_referees') {\n              item['defaultValue'] = [];\n            }\n          });\n          return true;\n        }\n      });\n    });\n  }\n  getListReferee() {\n    this._stageService.getListRefereesByStageId(this.stage.id).subscribe(response => {\n      this.listReferees = response['data'];\n      this.assignRefereeFields.forEach(item => {\n        if (item.key === 'list_referees') {\n          item.props.options = response['data'].map(referee => {\n            console.log(referee);\n            return {\n              label: referee.user ? `${referee.user.first_name} ${referee.user.last_name}` : referee.referee_name,\n              value: referee.id\n            };\n          });\n        }\n      });\n    });\n  }\n  onSubmitAssignReferee(event) {\n    this.dtElement.dtInstance.then(dtInstance => {\n      dtInstance.ajax.reload();\n    });\n  }\n  autoschedule() {\n    this._stageService.autoSchedule(this.stage.id).subscribe(response => {\n      console.log(response);\n    });\n  }\n  static #_ = this.ɵfac = function StageMatchesComponent_Factory(t) {\n    return new (t || StageMatchesComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.StageService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.LoadingService), i0.ɵɵdirectiveInject(i8.CoreSidebarService), i0.ɵɵdirectiveInject(i9.ToastrService), i0.ɵɵdirectiveInject(i10.ExportService), i0.ɵɵdirectiveInject(i11.NgbModal));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StageMatchesComponent,\n    selectors: [[\"app-stage-matches\"]],\n    viewQuery: function StageMatchesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalAssignReferee = _t.first);\n      }\n    },\n    inputs: {\n      stage: \"stage\",\n      tournament: \"tournament\"\n    },\n    outputs: {\n      onUpdateScore: \"onUpdateScore\",\n      onDataChange: \"onDataChange\"\n    },\n    decls: 10,\n    vars: 9,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [1, \"card\"], [1, \"card-header\", \"px-0\", \"pt-0\"], [\"ngbNav\", \"\", \"class\", \"nav-tabs\", 4, \"ngIf\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\", \"paramsToPost\", \"fields_subject\", \"onSuccess\", \"onClose\"], [\"modalAssignReferee\", \"\"], [\"ngbNav\", \"\", 1, \"nav-tabs\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\"], [\"ngbNavLink\", \"\", 3, \"click\"], [3, \"isMultipleAssign\", \"selectedIds\", \"listReferees\", \"assignRefereeForm\", \"assignRefereeFields\", \"assignRefereeModel\", \"onSubmit\"]],\n    template: function StageMatchesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtemplate(4, StageMatchesComponent_ul_4_Template, 10, 6, \"ul\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(5, \"table\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"core-sidebar\", 6)(7, \"app-editor-sidebar\", 7);\n        i0.ɵɵlistener(\"onSuccess\", function StageMatchesComponent_Template_app_editor_sidebar_onSuccess_7_listener($event) {\n          return ctx.onSuccess($event);\n        })(\"onClose\", function StageMatchesComponent_Template_app_editor_sidebar_onClose_7_listener($event) {\n          return ctx.onCloseSidebar($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, StageMatchesComponent_ng_template_8_Template, 1, 6, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage.type === ctx.AppConfig.TOURNAMENT_TYPES.league);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params)(\"paramsToPost\", ctx.paramsToPost)(\"fields_subject\", ctx.fields_subject);\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";AAAA,SAASA,WAAW,QAAQ,yCAAyC;AAErE,SAEEC,YAAY,QAOP,eAAe;AAKtB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,OAAOC,IAAI,MAAM,aAAa;AAK9B,OAAOC,MAAM,MAAM,QAAQ;AAI3B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;ICzBlCC,EAAA,CAAAC,cAAA,gBAKC;IAEiBD,EAAA,CAAAE,UAAA,mBAAAC,uDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IAACT,EAAA,CAAAU,MAAA,GAEjD;;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEVX,EAAA,CAAAC,cAAA,aAAe;IACCD,EAAA,CAAAE,UAAA,mBAAAU,uDAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAQ,MAAA,GAAAb,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAK,MAAA,CAAAJ,qBAAA,CAAsB,IAAI,CAAC;IAAA,EAAC;IAACT,EAAA,CAAAU,MAAA,GAEhD;;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;IAP6CX,EAAA,CAAAc,SAAA,GAEjD;IAFiDd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,sBAEjD;IAGgDhB,EAAA,CAAAc,SAAA,GAEhD;IAFgDd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,2BAEhD;;;;;;IAiCdhB,EAAA,CAAAC,cAAA,oCAQE;IADAD,EAAA,CAAAE,UAAA,sBAAAe,2FAAAC,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAO,aAAA;MAAA,OAAYP,EAAA,CAAAQ,WAAA,CAAAY,MAAA,CAAAC,qBAAA,CAAAH,MAAA,CAA6B;IAAA,EAAC;IAP5ClB,EAAA,CAAAW,YAAA,EAQE;;;;IAPAX,EAAA,CAAAsB,UAAA,qBAAAC,MAAA,CAAAC,gBAAA,CAAqC,gBAAAD,MAAA,CAAAE,WAAA,kBAAAF,MAAA,CAAAG,YAAA,uBAAAH,MAAA,CAAAI,iBAAA,yBAAAJ,MAAA,CAAAK,mBAAA,wBAAAL,MAAA,CAAAM,kBAAA;;;ADbzC,OAAM,MAAOC,qBAAqB;EAkBhC;EAEAC,YACUC,KAAiB,EAClBC,MAAsB,EACtBC,iBAAmC,EACnCC,eAA+B,EAC/BC,aAA2B,EAC3BC,YAAyB,EACzBC,eAA+B,EAC/BC,mBAAuC,EACvCC,OAAsB,EACrBC,cAA6B,EAC7BC,aAAuB;IAVvB,KAAAV,KAAK,GAALA,KAAK;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,OAAO,GAAPA,OAAO;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IA7BvB,KAAAC,SAAS,GAAQ/C,kBAAkB;IAGnC,KAAAgD,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAInD,OAAO,EAAe;IAC5D,KAAAC,SAAS,GAAGA,SAAS;IAErB,KAAAmD,QAAQ,GAAY,KAAK;IACjB,KAAAC,UAAU,GAAY,KAAK;IAEzB,KAAAC,aAAa,GAAG,IAAIvD,YAAY,EAAO;IACvC,KAAAwD,YAAY,GAAG,IAAIxD,YAAY,EAAO;IAGhD,KAAAiC,YAAY,GAAG,EAAE;IAmBjB,KAAAwB,qBAAqB,GAAG,KAAK;IACtB,KAAAC,cAAc,GAAG,IAAIzD,OAAO,EAAO;IACnC,KAAA0D,aAAa,GAAG,EAAE;IAClB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,UAAU,GAAG,eAAe;IAC5B,KAAAC,MAAM,GAAwB;MACnCC,SAAS,EAAE,IAAI,CAACF,UAAU;MAC1BG,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAACzB,iBAAiB,CAAC0B,OAAO,CAAC,WAAW,CAAC;QACnDC,IAAI,EAAE,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QAClDE,MAAM,EAAE,IAAI,CAAC5B,iBAAiB,CAAC0B,OAAO,CAAC,cAAc;OACtD;MACDG,GAAG,EAAE,GAAGvE,WAAW,CAACwE,MAAM,uBAAuB;MACjDC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IACM,KAAAC,QAAQ,GAAG;MAChBC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;KACX;IACM,KAAAC,KAAK,GAAG;MACbF,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;KACX;IAED,KAAAE,cAAc,GAAG,EAAE;IAEZ,KAAAC,SAAS,GAAG,CACjB;MACEC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;QAC7CiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,qBAAqB,CAAC;QAClE;QACAc,IAAI,EAAE,MAAM;QACZI,GAAG,EAAE;OACN;MACDC,WAAW,EAAE;QACX,gBAAgB,EACd;;KAEL,EACD;MACEN,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QACnDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CACzC,2BAA2B,CAC5B;QACD;QACAc,IAAI,EAAE;OACP;MACDK,WAAW,EAAE;QACX,gBAAgB,EACd;;KAEL,EACD;MACEN,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,UAAU,CAAC;QACjDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,yBAAyB,CAAC;QACtE;QACAc,IAAI,EAAE;;KAET,EAED;MACED,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,UAAU,CAAC;QACjDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,iBAAiB,CAAC;QAC9D;QACAQ,OAAO,EAAE,IAAI,CAACD,QAAQ,CAACC;;KAE1B,EAED;MACEK,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLK,cAAc,EAAE,IAAI;QACpBJ,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,WAAW,CAAC;QAClDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,kBAAkB,CAAC;QAC/DQ,OAAO,EAAE;;KAEZ,EACD;MACEK,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLK,cAAc,EAAE,IAAI;QACpBJ,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,WAAW,CAAC;QAClDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,kBAAkB,CAAC;QAC/DQ,OAAO,EAAE;;KAEZ,CACF;IAEM,KAAAa,WAAW,GAAG,EAAE;IAEhB,KAAAC,WAAW,GAAG,CACnB;MACET,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,aAAa,CAAC;QACpDuB,QAAQ,EAAE,IAAI;QACdf,OAAO,EAAE,IAAI,CAAChB;;KAEjB,EACD;MACEqB,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,QAAQ,CAAC;QAC/CiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,cAAc;;KAE7D,CACF;IAEM,KAAAwB,eAAe,GAAG,CACvB;MACEX,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLK,cAAc,EAAE,IAAI;QACpBJ,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QACnDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;QAC3DyB,aAAa,EAAE,IAAI;QACnBjB,OAAO,EAAE;;KAEZ,EACD;MACEK,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLK,cAAc,EAAE,IAAI;QACpBJ,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QACnDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,mBAAmB,CAAC;QAChEyB,aAAa,EAAE,IAAI;QACnBjB,OAAO,EAAE;;KAEZ,CACF;IAEM,KAAAkB,MAAM,GAAU,IAAI,CAACd,SAAS;IAE9B,KAAAe,WAAW,GAAU,IAAI,CAACH,eAAe;IAo6CzC,KAAA5D,gBAAgB,GAAG,IAAI;IACvB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAE,iBAAiB,GAAG,IAAI5B,SAAS,CAAC,EAAE,CAAC;IACrC,KAAA8B,kBAAkB,GAAG,EAAE;IACvB,KAAAD,mBAAmB,GAAG,CAC3B;MACE6C,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLa,QAAQ,EAAE,IAAI;QACdR,cAAc,EAAE,IAAI;QACpBS,YAAY,EAAE,EAAE;QAChBb,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,eAAe,CAAC;QACtDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,uBAAuB,CAAC;QACpEQ,OAAO,EAAE;OACV;MACDsB,KAAK,EAAE;KACR,CACF;EAjlDD;EA6JAC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,gBAAgBA,CAAA;IACd,MAAMI,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACrD,IAAIF,QAAQ,EAAE;MACZ,IAAI,CAACG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MACxCM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACJ,YAAY,CAAC;KAChD,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;EAEAV,KAAKA,CAAA;IACHlG,SAAS,CAAC6G,kBAAkB,CAACC,OAAO,CAAE/B,IAAI,IAAI;MAC5C,IAAI,CAACtB,aAAa,CAACsD,IAAI,CAAC;QACtBC,KAAK,EAAEjC,IAAI;QACXE,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAACc,IAAI;OAC3C,CAAC;IACJ,CAAC,CAAC;IACD,IAAI,CAACF,SAAiB,CAACkC,IAAI,CAAC;MAC3BjC,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;OACP;MACDe,YAAY,EAAE,IAAI,CAACmB,KAAK,CAACC;KAC1B,CAAC;IAEF,IAAI,CAAC5B,WAAW,GAAG,CACjB;MACER,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;OACP;MACDe,YAAY,EAAE,IAAI,CAACmB,KAAK,CAAClC;KAC1B,EACD;MACED,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QACnDuB,QAAQ,EAAE,IAAI;QACdT,IAAI,EAAE,QAAQ;QACdoC,GAAG,EAAE,CAAC;QACNhC,GAAG,EAAE,GAAG;QACRiC,IAAI,EAAE;OACP;MACDtB,YAAY,EAAE;KACf,EACD;MACEhB,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QACnDuB,QAAQ,EAAE,IAAI;QACdT,IAAI,EAAE,QAAQ;QACdoC,GAAG,EAAE,CAAC;QACNhC,GAAG,EAAE,GAAG;QACRiC,IAAI,EAAE;OACP;MACDtB,YAAY,EAAE;KACf,EACD;MACEhB,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,oBAAoB,CAAC;QAC3D;QACAc,IAAI,EAAE,QAAQ;QACdoC,GAAG,EAAE,CAAC;QACNhC,GAAG,EAAE,GAAG;QACRiC,IAAI,EAAE;OACP;MACDhC,WAAW,EAAE;QACXiC,IAAI,EAAE,kBAAkBrH,SAAS,CAACsH,gBAAgB,CAACC,QAAQ,iDAAiD,IAAI,CAACf,YAAY,CAACgB,UAAU;OACzI;MACD1B,YAAY,EAAE;KACf,EACD;MACEhB,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,oBAAoB,CAAC;QAC3D;QACAc,IAAI,EAAE,QAAQ;QACdoC,GAAG,EAAE,CAAC;QACNhC,GAAG,EAAE,GAAG;QACRiC,IAAI,EAAE;OACP;MACDhC,WAAW,EAAE;QACXiC,IAAI,EAAE,kBAAkBrH,SAAS,CAACsH,gBAAgB,CAACC,QAAQ,iDAAiD,IAAI,CAACf,YAAY,CAACgB,UAAU;OACzI;MACD1B,YAAY,EAAE;KACf,CACF;IAED,IAAI,CAAC2B,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACnF,iBAAiB,CAAC0B,OAAO,CAAC,gBAAgB,CAAC;MAC7D0D,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACV7C,IAAI,EAAE,EAAE;QACR8C,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAACvF,iBAAiB,CAAC0B,OAAO,CAAC,SAAS,CAAC;UAC/C8D,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACvF,iBAAiB,CAAC0B,OAAO,CAAC,gBAAgB,CAAC;UACtD8D,MAAM,EAAE;SACT;;KAGN;IAED,IACE,IAAI,CAACd,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACU,MAAM,IACrD,IAAI,CAACf,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACW,MAAM,EACrD;MACA;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACjB,KAAK,CAACkB,aAAa,EAAED,CAAC,EAAE,EAAE;QAClD,IAAI,CAACtD,cAAc,CAACmC,IAAI,CAAC;UACvBC,KAAK,EAAE,GAAGkB,CAAC,EAAE;UACbjD,KAAK,EAAE,GAAG,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,OAAO,CAAC,IAAIiE,CAAC;SACvD,CAAC;;;IAIN,IAAI,IAAI,CAACjB,KAAK,CAAClC,IAAI,IAAI/E,SAAS,CAACsH,gBAAgB,CAACU,MAAM,EAAE;MACxD;MACC,IAAI,CAACnD,SAAiB,CAACuD,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;QACnCtD,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE;UACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;UACnDuB,QAAQ,EAAE,IAAI;UACdf,OAAO,EAAE,CACP;YACEuC,KAAK,EAAE,CAAC;YACR/B,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,cAAc;WACrD,EACD;YACE+C,KAAK,EAAE,CAAC;YACR/B,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,gBAAgB;WACvD;;OAGN,CAAC;;IAGJ,IACE,IAAI,CAACgD,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACC,QAAQ,EACvD;MACA;MACA,IAAIc,eAAe,GAAI,IAAI,CAACxD,SAAiB,CAACyD,SAAS,CACpDC,KAAU,IAAKA,KAAK,CAACzD,GAAG,KAAK,aAAa,CAC5C;MACD;MACA,IAAIuD,eAAe,GAAG,CAAC,CAAC,EAAE;QACvB,IAAI,CAACxD,SAAiB,CAACuD,MAAM,CAACC,eAAe,EAAE,CAAC,CAAC;;MAGpD;MACC,IAAI,CAACxD,SAAiB,CAACuD,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;QACnCtD,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACLK,cAAc,EAAE,IAAI;UACpBJ,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;UACnDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,kBAAkB;;OAEjE,CAAC;;EAEN;EAEAkC,UAAUA,CAAA;IAAA,IAAAqC,KAAA;IACR,IAAIC,IAAI,GAAG,CACT;MACEC,IAAI,EAAE,oCAAoC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACtE,KAAK,CACN,EAAE;MACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAI,IAAI,CAAC7B,KAAK,CAAClC,IAAI,IAAI/E,SAAS,CAACsH,gBAAgB,CAACU,MAAM,EAAE;UACxD9H,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;YACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,qCAAqC,CACtC;YACD+E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACvDiF,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAGF,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC;MACpC;KACD,EACD;MACE;MACAC,MAAM,EAAE,YAAY;MACpBC,UAAU,EAAE,KAAK;MACjBZ,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAAC,gBAAgB,CAAC;MACtDsF,SAAS,EAAE,eAAe;MAC1BC,OAAO,EAAE,CACP;QACEC,IAAI,EAAE;UACJvC,EAAE,EAAE;SACL;QACDwB,IAAI,EAAE,mDAAmD,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACrF,eAAe,CAChB,EAAE;QACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;UAC9B,IAAI,CAACY,mBAAmB,EAAE;QAC5B;OACD,EACD;QACEhB,IAAI,EAAE,wCAAwC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAC1E,mBAAmB,CACpB,EAAE;QACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;UAC9B;UACA,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;YAAElF,QAAQ,EAAE;UAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;UACrD;UACA,IAAIF,YAAY,CAACG,MAAM,GAAG,CAAC,EAAE;YAC3B5J,IAAI,CAAC6I,IAAI,CAAC;cACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;cACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,8BAA8B,CAC/B;cACD+E,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;cACvDiF,WAAW,EAAE;gBACXC,aAAa,EAAE;;aAElB,CAAC;YACF;;UAEF,IAAIY,GAAG,GAAGJ,YAAY,CAAC,CAAC,CAAC;UACzB,IAAIK,SAAS,GAAG,KAAK;UACrB,IAAI,IAAI,CAACC,mBAAmB,CAACF,GAAG,CAAC,EAAE;YACjCC,SAAS,GAAG,IAAI;;UAElB,IAAI,CAACA,SAAS,EAAE;YACd9J,IAAI,CAAC6I,IAAI,CAAC;cACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;cACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,8CAA8C,CAC/C;cACD+E,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;cACvDiF,WAAW,EAAE;gBACXC,aAAa,EAAE;;aAElB,CAAC;YACF;;UAEF,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,EAAEW,GAAG,CAAC;QAC/C,CAAC;QACDV,MAAM,EAAE;OACT,EACD;QACEX,IAAI,EAAE,uCAAuC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACzE,YAAY,CACb,EAAE;QACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;UAC9B;UACA,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;YAAElF,QAAQ,EAAE;UAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;UACrD;UACA,IAAIF,YAAY,CAACG,MAAM,GAAG,CAAC,EAAE;YAC3B5J,IAAI,CAAC6I,IAAI,CAAC;cACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;cACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,8BAA8B,CAC/B;cACD+E,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;cACvDiF,WAAW,EAAE;gBACXC,aAAa,EAAE;;aAElB,CAAC;YACF;;UAGF;UACA,IAAI,CAACQ,YAAY,CAAC,CAAC,CAAC,CAACO,YAAY,IAAI,CAACP,YAAY,CAAC,CAAC,CAAC,CAACQ,YAAY,EAAE;YAClEjK,IAAI,CAAC6I,IAAI,CAAC;cACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;cACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,uCAAuC,CACxC;cACD+E,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;cACvDiF,WAAW,EAAE;gBACXC,aAAa,EAAE;;aAElB,CAAC;YACF;WACD,MAAM;YACL;YACAjJ,IAAI,CAAC6I,IAAI,CAAC;cACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,eAAe,CAAC;cACtDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,oDAAoD,CACrD;cACD+E,IAAI,EAAE,SAAS;cACfoB,cAAc,EAAE,IAAI;cACpBC,gBAAgB,EAAE,IAAI;cACtBpB,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,KAAK,CAAC;cACxDqG,gBAAgB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;cACtDsG,cAAc,EAAE,KAAK;cACrBrB,WAAW,EAAE;gBACXC,aAAa,EAAE,sBAAsB;gBACrCqB,YAAY,EAAE;;aAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;cACjB,IAAIA,MAAM,CAAC1D,KAAK,EAAE;gBAChB,IAAI,CAAC2D,SAAS,CAAChB,YAAY,CAAC,CAAC,CAAC,CAAC;;YAEnC,CAAC,CAAC;;QAEN,CAAC;QACDN,MAAM,EAAE;OACT,EACD;QACEX,IAAI,EAAE,oCAAoC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACtE,gBAAgB,CACjB,EAAE;QACHM,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACqG,SAAS,EAAE;QAC9BvB,MAAM,EAAE;OACT;KAEJ,EACD;MACE;MACAA,MAAM,EAAE,YAAY;MACpBC,UAAU,EAAE,KAAK;MACjBZ,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;MACpDsF,SAAS,EAAE,eAAe;MAC1BC,OAAO,EAAE,CACP;QACEd,IAAI,EAAE,qCAAqC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACvE,cAAc,CACf,EAAE;QACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;UAC9B,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;YAAElF,QAAQ,EAAE;UAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;UACrD,IAAIE,GAAG,GAAGJ,YAAY,CAAC,CAAC,CAAC;UACzB,IAAIkB,WAAW,GAAG,KAAK;UACvB,IAAI,IAAI,CAACC,wBAAwB,CAACf,GAAG,CAAC,EAAE;YACtCc,WAAW,GAAG,IAAI;;UAEpB,IAAI,CAACA,WAAW,EAAE;YAChB3K,IAAI,CAAC6I,IAAI,CAAC;cACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,qBAAqB,CAAC;cAC5DyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,wDAAwD,CACzD;cACD+E,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;cACvDiF,WAAW,EAAE;gBACXC,aAAa,EAAE;;aAElB,CAAC;WACH,MAAM;YACL,IAAI,CAACC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC;;QAEtC,CAAC;QACDC,MAAM,EAAE;OACT,EACD;QACEX,IAAI,EAAE,2CAA2C,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAC7E,aAAa,CACd,EAAE;QACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;UAC9B;UACA,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;YAAElF,QAAQ,EAAE;UAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;UAErD;UACA,IAAIkB,GAAG,GAAG,EAAE;UAEZpB,YAAY,CAACqB,GAAG,CAAEjB,GAAG,IAAKgB,GAAG,CAAChE,IAAI,CAACgD,GAAG,CAAC7C,EAAE,CAAC,CAAC;UAE3C;UACAhH,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,eAAe,CAAC;YACtDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,qDAAqD,CACtD;YACDmG,cAAc,EAAE,IAAI;YACpBpB,IAAI,EAAE,SAAS;YACfqB,gBAAgB,EAAE,IAAI;YACtBpB,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,KAAK,CAAC;YACxDqG,gBAAgB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACtDsG,cAAc,EAAE,KAAK;YACrBrB,WAAW,EAAE;cACXC,aAAa,EAAE,sBAAsB;cACrCqB,YAAY,EAAE;;WAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;YACjB,IAAIA,MAAM,CAAC1D,KAAK,EAAE;cAChB,IAAI,CAACiE,UAAU,CAACF,GAAG,CAAC;;UAExB,CAAC,CAAC;QACJ,CAAC;QACD1B,MAAM,EAAE;OACT;KAEJ,EACD;MACEX,IAAI,EAAE,oCAAoC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACtE,MAAM,CACP,EAAE;MACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;UAAElF,QAAQ,EAAE;QAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;QAErD,IAAIF,YAAY,CAACG,MAAM,GAAG,CAAC,EAAE;UAC3B;UACA,IAAIzB,eAAe,GAAI,IAAI,CAACxD,SAAiB,CAACyD,SAAS,CACpD4C,CAAC,IAAKA,CAAC,CAACpG,GAAG,IAAI,aAAa,CAC9B;UACD,IAAIuD,eAAe,GAAG,CAAC,CAAC,EAAE;YACvB,IAAI,CAACxD,SAAiB,CAACuD,MAAM,CAACC,eAAe,EAAE,CAAC,CAAC;;UAGpD,IAAI8C,cAAc,GAAI,IAAI,CAACtG,SAAiB,CAACyD,SAAS,CACnD4C,CAAC,IAAKA,CAAC,CAACpG,GAAG,IAAI,YAAY,CAC7B;UAED,IAAIqG,cAAc,GAAG,CAAC,CAAC,EAAE;YACtB,IAAI,CAACtG,SAAiB,CAACuD,MAAM,CAAC+C,cAAc,EAAE,CAAC,CAAC;;SAEpD,MAAM;UACL;UAEA,IAAI9C,eAAe,GAAI,IAAI,CAACxD,SAAiB,CAACyD,SAAS,CACpD4C,CAAC,IAAKA,CAAC,CAACpG,GAAG,IAAI,aAAa,CAC9B;UACD;UACA,IACEuD,eAAe,IAAI,CAAC,CAAC,IACrB,IAAI,CAACpB,KAAK,CAAClC,IAAI,IAAI/E,SAAS,CAACsH,gBAAgB,CAACU,MAAM,EACpD;YACC,IAAI,CAACnD,SAAiB,CAACuD,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;cACnCtD,GAAG,EAAE,aAAa;cAClBC,IAAI,EAAE,QAAQ;cACdC,KAAK,EAAE;gBACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,aAAa,CAAC;gBACpDiB,WAAW,EACT,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,oBAAoB,CAAC;gBACtD;gBACAQ,OAAO,EAAE,IAAI,CAACG;;aAEjB,CAAC;;UAEJ,IACE,IAAI,CAACqC,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACC,QAAQ,EACvD;YACA,IAAI4D,cAAc,GAAI,IAAI,CAACtG,SAAiB,CAACyD,SAAS,CACnD4C,CAAC,IAAKA,CAAC,CAACpG,GAAG,IAAI,YAAY,CAC7B;YAED,IAAIqG,cAAc,IAAI,CAAC,CAAC,EAAE;cACvB,IAAI,CAACtG,SAAiB,CAACuD,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;gBACnCtD,GAAG,EAAE,YAAY;gBACjBC,IAAI,EAAE,OAAO;gBACbC,KAAK,EAAE;kBACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;kBACnDiB,WAAW,EACT,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,kBAAkB;;eAEtD,CAAC;;;;QAKR,IAAI,CAACmF,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC;MAClC,CAAC;MACDC,MAAM,EAAE;KACT,EACD;MACEX,IAAI,EAAE,wCAAwC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAC1E,mBAAmB,CACpB,EAAE;MACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;UAAElF,QAAQ,EAAE;QAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;QACrD;QACA,IAAIF,YAAY,CAACG,MAAM,GAAG,CAAC,EAAE;UAC3B5J,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;YACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,8BAA8B,CAC/B;YACD+E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACvDiF,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAEF,IAAIY,GAAG,GAAGJ,YAAY,CAAC,CAAC,CAAC;QACzB,IAAIK,SAAS,GAAG,KAAK;QACrB,IAAI,IAAI,CAACC,mBAAmB,CAACF,GAAG,CAAC,EAAE;UACjCC,SAAS,GAAG,IAAI;;QAElB,IAAI,CAACA,SAAS,EAAE;UACd9J,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;YACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,8CAA8C,CAC/C;YACD+E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACvDiF,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAEF,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,EAAEW,GAAG,CAAC;MAC/C,CAAC;MACDV,MAAM,EAAE;KACT,EACD;MACEX,IAAI,EAAE,uCAAuC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACzE,YAAY,CACb,EAAE;MACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;UAAElF,QAAQ,EAAE;QAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;QACrD;QACA,IAAIF,YAAY,CAACG,MAAM,GAAG,CAAC,EAAE;UAC3B5J,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;YACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,8BAA8B,CAC/B;YACD+E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACvDiF,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAGF;QAEA,IAAIQ,YAAY,CAAC,CAAC,CAAC,CAACyB,MAAM,KAAK,MAAM,KAAK,CAACzB,YAAY,CAAC,CAAC,CAAC,CAACO,YAAY,IAAI,CAACP,YAAY,CAAC,CAAC,CAAC,CAACQ,YAAY,CAAC,EAAE;UACzGjK,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;YACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,uCAAuC,CACxC;YACD+E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACvDiF,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;SACD,MAAM;UACL;UACAjJ,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,eAAe,CAAC;YACtDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,oDAAoD,CACrD;YACD+E,IAAI,EAAE,SAAS;YACfoB,cAAc,EAAE,IAAI;YACpBC,gBAAgB,EAAE,IAAI;YACtBpB,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,KAAK,CAAC;YACxDqG,gBAAgB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACtDsG,cAAc,EAAE,KAAK;YACrBrB,WAAW,EAAE;cACXC,aAAa,EAAE,sBAAsB;cACrCqB,YAAY,EAAE;;WAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;YACjB,IAAIA,MAAM,CAAC1D,KAAK,EAAE;cAChB,IAAI,CAAC2D,SAAS,CAAChB,YAAY,CAAC,CAAC,CAAC,CAAC;;UAEnC,CAAC,CAAC;;MAEN,CAAC;MACDN,MAAM,EAAE;KACT,EACD;MACEX,IAAI,EAAE,mCAAmC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACrE,QAAQ,CACT,EAAE;MACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;UAAElF,QAAQ,EAAE;QAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;QACrD;QACA,IAAIG,SAAS,GAAG,KAAK;QACrBL,YAAY,CAACqB,GAAG,CAAEjB,GAAG,IAAI;UACvB,IAAI,IAAI,CAACE,mBAAmB,CAACF,GAAG,CAAC,EAAE;YACjCC,SAAS,GAAG,IAAI;;QAEpB,CAAC,CAAC;QAEF,IAAIA,SAAS,EAAE;UACb9J,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,SAAS,CAAC;YAChDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,kDAAkD,CACnD;YACD+E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACvDiF,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UAEF;;QAEF;QACAjJ,IAAI,CAAC6I,IAAI,CAAC;UACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,eAAe,CAAC;UACtDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,gDAAgD,CACjD;UACDmG,cAAc,EAAE,IAAI;UACpBpB,IAAI,EAAE,SAAS;UACfqB,gBAAgB,EAAE,IAAI;UACtBpB,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,KAAK,CAAC;UACxDqG,gBAAgB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;UACtDsG,cAAc,EAAE,KAAK;UACrBrB,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCqB,YAAY,EAAE;;SAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAAC1D,KAAK,EAAE;YAChB,IAAI,CAACoC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC;;QAEtC,CAAC,CAAC;MACJ,CAAC;MAEDC,MAAM,EAAE;KACT,EACD;MACEX,IAAI,EAAE,sCAAsC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACxE,QAAQ,CACT,EAAE;MACHoF,MAAM,EAAE,UAAU;MAClB9E,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;UAAElF,QAAQ,EAAE;QAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;QACrD;QACA,IAAIkB,GAAG,GAAG,EAAE;QACZpB,YAAY,CAACqB,GAAG,CAAEjB,GAAG,IAAI;UACvBgB,GAAG,CAAChE,IAAI,CAACgD,GAAG,CAAC7C,EAAE,CAAC;QAClB,CAAC,CAAC;QAEF;QACAhH,IAAI,CAAC6I,IAAI,CAAC;UACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,eAAe,CAAC;UACtDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,uCAAuC,CACxC;UACDmG,cAAc,EAAE,IAAI;UACpBpB,IAAI,EAAE,SAAS;UACfqB,gBAAgB,EAAE,IAAI;UACtBpB,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,KAAK,CAAC;UACxDqG,gBAAgB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;UACtDsG,cAAc,EAAE,KAAK;UACrBrB,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCqB,YAAY,EAAE;;SAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAAC1D,KAAK,EAAE;YAChB;YACA,IAAI,CAACrE,eAAe,CAAC0I,IAAI,EAAE;YAC3B,IAAI,CAAC5I,aAAa,CACf6I,oBAAoB,CAACP,GAAG,EAAE,IAAI,CAAC9D,KAAK,CAACC,EAAE,CAAC,CACxCqE,SAAS,EAAE,CACXd,IAAI,CAAEe,IAAI,IAAI;cACb,IAAI,CAAC3I,OAAO,CAAC4I,OAAO,CAClB,IAAI,CAAClJ,iBAAiB,CAAC0B,OAAO,CAAC,sBAAsB,CAAC,CACvD;cACD2E,EAAE,CAAC8C,IAAI,CAACC,MAAM,EAAE;cAChB,IAAI,CAACrI,YAAY,CAACsI,IAAI,CAACJ,IAAI,CAAC;YAC9B,CAAC,CAAC;;QAER,CAAC,CAAC;MACJ;KACD,EACD;MACE9C,IAAI,EAAE,2CAA2C,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAC7E,QAAQ,CACT,EAAE;MACHoF,MAAM,EAAE,KAAK;MACb9E,MAAM;QAAA,IAAAsH,IAAA,GAAAC,iBAAA,CAAE,WAAOnD,CAAM,EAAEC,EAAO,EAAEmD,MAAW,EAAEjD,MAAW,EAAI;UAC1D,MAAMe,IAAI,GAAGjB,EAAE,CAACY,OAAO,CAACwC,UAAU,EAAE;UACpC,MAAMxD,KAAI,CAAC1F,cAAc,CAACmJ,SAAS,CAACpC,IAAI,EAAE,aAAa,CAAC;QAC1D,CAAC;QAAA,gBAAAtF,OAAA2H,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;UAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;QAAA;MAAA;KACF,EACD;MACE7D,IAAI,EAAE,oCAAoC,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CACtE,aAAa,CACd,EAAE;MACHM,MAAM,EAAEA,CAACoE,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B,IAAIa,YAAY,GAAGf,EAAE,CAACgB,IAAI,CAAC;UAAElF,QAAQ,EAAE;QAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;QACrD;QACA,IAAIF,YAAY,CAACG,MAAM,GAAG,CAAC,EAAE;UAC3B5J,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;YACrDyE,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAClC,8BAA8B,CAC/B;YACD+E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;YACvDiF,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAEF,IAAI,CAACC,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC;MACxC,CAAC;MACDC,MAAM,EAAE;KACT,EACD;MACEX,IAAI,EAAE,IAAI,CAACnG,iBAAiB,CAAC0B,OAAO,CAAC,SAAS,CAAC;MAC/CoF,MAAM,EAAE;KACT,CACF;IAED;IACA,IAAI,IAAI,CAACpC,KAAK,CAAClC,IAAI,IAAI/E,SAAS,CAACsH,gBAAgB,CAACU,MAAM,EAAE;MACxD;MACAS,IAAI,CAACL,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;IAGnB,IACE,IAAI,CAACnB,KAAK,CAAClC,IAAI,IAAI/E,SAAS,CAACsH,gBAAgB,CAACC,QAAQ,EACtD;MACAkB,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAGpB,IACE,IAAI,CAACoE,UAAU,CAACC,aAAa,IAAIzM,SAAS,CAAC0M,cAAc,CAACC,KAAK,IAC/D,IAAI,CAAC1F,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACC,QAAQ,EACvD;MACA,MAAMqF,kBAAkB,GAAGnE,IAAI,CAACoE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACpE,IAAI,KAAK,gBAAgB,CAAC;MAC1E,IAAIkE,kBAAkB,IAAIG,KAAK,CAACC,OAAO,CAACJ,kBAAkB,CAACpD,OAAO,CAAC,EAAE;QACnEoD,kBAAkB,CAACpD,OAAO,GAAGoD,kBAAkB,CAACpD,OAAO,CAACyD,MAAM,CAC5DH,GAAG,IAAI,EAAEA,GAAG,CAACrD,IAAI,IAAIqD,GAAG,CAACrD,IAAI,CAACvC,EAAE,KAAK,mBAAmB,CAAC,CAC1D;;MAEH;;;IAGF,IAAI,CAACjE,SAAS,GAAG;MACfiK,GAAG,EAAE,IAAI,CAAC1K,eAAe,CAAC2K,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAEC,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,IAAI,GAAG,OAAO;MAChD;MACAC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAa,CAAE;MACpCC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;MACnBhC,IAAI,EAAEA,CAACiC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAI,CAACvL,KAAK,CACPwL,IAAI,CACH,GAAGhO,WAAW,CAACwE,MAAM,+BAA+B,IAAI,CAAC4C,KAAK,CAACC,EAAE,EAAE,EACnEyG,oBAAoB,CACrB,CACAG,SAAS,CAAEtC,IAAS,IAAI;UACvB;UACA,IAAI,CAAC7F,MAAM,CAACmB,OAAO,CAAEyB,KAAK,IAAI;YAC5B,IAAIA,KAAK,CAACzD,GAAG,KAAK,aAAa,EAAE;cAC/ByD,KAAK,CAACvD,KAAK,CAACP,OAAO,GAAG+G,IAAI,CAAC/G,OAAO,CAACD,QAAQ;;YAE7C,IAAI+D,KAAK,CAACzD,GAAG,KAAK,cAAc,EAAE;cAChCyD,KAAK,CAACvD,KAAK,CAACP,OAAO,GAAG+G,IAAI,CAAC/G,OAAO,CAACE,KAAK;;YAE1C,IAAI4D,KAAK,CAACzD,GAAG,KAAK,cAAc,EAAE;cAChCyD,KAAK,CAACvD,KAAK,CAACP,OAAO,GAAG+G,IAAI,CAAC/G,OAAO,CAACE,KAAK;;UAE5C,CAAC,CAAC;UAEF,IAAI,CAACiB,WAAW,CAACkB,OAAO,CAAEyB,KAAK,IAAI;YACjC,IAAIA,KAAK,CAACzD,GAAG,KAAK,eAAe,EAAE;cACjCyD,KAAK,CAACvD,KAAK,CAACP,OAAO,GAAG+G,IAAI,CAAC/G,OAAO,CAACsJ,UAAU;;YAE/C,IAAIxF,KAAK,CAACzD,GAAG,KAAK,eAAe,EAAE;cACjCyD,KAAK,CAACvD,KAAK,CAACP,OAAO,GAAG+G,IAAI,CAAC/G,OAAO,CAACsJ,UAAU;;UAEjD,CAAC,CAAC;UAEF,IAAI,CAACjN,qBAAqB,CAAC,IAAI,CAACyC,qBAAqB,CAAC;UAEtD,IAAI,CAACJ,QAAQ,GAAGqI,IAAI,CAAC3B,IAAI,CAACC,MAAM,GAAG,CAAC;UAEpC8D,QAAQ,CAAC;YACPI,YAAY,EAAExC,IAAI,CAACwC,YAAY;YAC/BC,eAAe,EAAEzC,IAAI,CAACyC,eAAe;YACrCpE,IAAI,EAAE2B,IAAI,CAAC3B;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDqE,YAAY,EAAEA,CAAA,KAAK;QACjB,IAAI,CAAC9K,UAAU,GAAG,IAAI;MACxB,CAAC;MACD+K,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI,CAAC7L,eAAe,CAAC2K,iBAAiB,CAACmB,IAAI;MACrDC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CACvC;MACDC,OAAO,EAAE,CACP;QAAE7E,IAAI,EAAE,OAAO;QAAE8E,OAAO,EAAE;MAAK,CAAE,EACjC;QAAE9E,IAAI,EAAE,YAAY;QAAE8E,OAAO,EAAE;MAAK,CAAE,EACtC;QAAE9E,IAAI,EAAE,aAAa;QAAE8E,OAAO,EAAE;MAAK,CAAE,EACvC;QACE5K,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;QAC3C4F,IAAI,EAAE,cAAc;QACpB8E,OAAO,EAAE,IAAI,CAAC1H,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACU,MAAM;QAC9D4G,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG,EAAE8E,IAAI;UACrC,OAAOhF,IAAI,GAAG,GAAGA,IAAI,EAAE,GAAG,GAAGgF,IAAI,CAAC9E,GAAG,GAAG,CAAC,EAAE;QAC7C;OACD,EACD;QACEhG,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;QAC7C4F,IAAI,EAAE,MAAM;QACZiF,SAAS,EAAE,KAAK;QAChBvF,SAAS,EAAE,iBAAiB;QAC5BqF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,IAAIgF,OAAO,GAAG,EAAE;UAChB,IAAI,CAAClF,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAE;YAC3BkF,OAAO,GAAG,KAAK;WAChB,MAAM;YACL;YACAA,OAAO,GAAG5O,MAAM,CAAC0J,IAAI,CAAC,CAACmF,MAAM,CAAC,YAAY,CAAC;;UAE7C,OAAO,+CAA+CD,OAAO,MAAM;QACrE;OACD,EACD;QACEhL,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QACnD4F,IAAI,EAAE,kBAAkB;QACxBiF,SAAS,EAAE,KAAK;QAChBvF,SAAS,EAAE,iBAAiB;QAC5BqF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,IACE,CAACF,IAAI,IACLE,GAAG,CAACkF,gBAAgB,IAAI,KAAK,IAC7B,CAAClF,GAAG,CAACkF,gBAAgB,EACrB;YACA,OAAO,KAAK;;UAEd;UACA,OAAOpF,IAAI;QACb;OACD,EACD;QACE9F,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,UAAU,CAAC;QACjD4F,IAAI,EAAE,gBAAgB;QACtBiF,SAAS,EAAE,KAAK;QAChBvF,SAAS,EAAE,iBAAiB;QAC5BqF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,IAAI,CAACF,IAAI,IAAIE,GAAG,CAACmF,cAAc,IAAI,KAAK,IAAI,CAACnF,GAAG,CAACmF,cAAc,EAAE;YAC/D,OAAO,KAAK;;UAEd;UACA,OAAOrF,IAAI;QACb;OACD,EACD;QACE9F,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,UAAU,CAAC;QACjD4F,IAAI,EAAE;OACP,EACD;QACE9F,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,WAAW,CAAC;QAClD4F,IAAI,EAAE,gBAAgB;QACtBN,SAAS,EAAE,iBAAiB;QAC5BuF,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,IAAIF,IAAI,KAAK,KAAK,EAAE;YAClB,IAAIE,GAAG,CAACoF,SAAS,EAAE;cACjB,OAAOpF,GAAG,CAACoF,SAAS;aACrB,MAAM,IAAIpF,GAAG,CAACqF,UAAU,IAAIrF,GAAG,CAACqF,UAAU,CAACnK,KAAK,EAAE;cACjD,OAAO8E,GAAG,CAACqF,UAAU,CAACnK,KAAK;;;UAG/B,OAAO4E,IAAI;QACb;OACD,EACD;QACEA,IAAI,EAAE,IAAI;QACVN,SAAS,EAAE,oCAAoC;QAC/CqF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,OAAO,IAAI;QACb,CAAC;QACD+E,SAAS,EAAE;OACZ,EACD;QACE/K,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,WAAW,CAAC;QAClD4F,IAAI,EAAE,gBAAgB;QACtBN,SAAS,EAAE,iBAAiB;QAC5BuF,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,IAAIF,IAAI,KAAK,KAAK,EAAE;YAClB,IAAIE,GAAG,CAACsF,SAAS,EAAE;cACjB,OAAOtF,GAAG,CAACsF,SAAS;aACrB,MAAM,IAAItF,GAAG,CAACuF,UAAU,IAAIvF,GAAG,CAACuF,UAAU,CAACrK,KAAK,EAAE;cACjD,OAAO8E,GAAG,CAACuF,UAAU,CAACrK,KAAK;;;UAG/B,OAAO4E,IAAI;QACb;OACD,EACD;QACE9F,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QACnD4F,IAAI,EAAE,YAAY;QAClBN,SAAS,EAAE,iBAAiB;QAC5BuF,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,IAAIA,GAAG,CAACwF,YAAY,IAAI,IAAI,EAAE;YAC5B,OAAO,GAAG1F,IAAI,UAAUE,GAAG,CAACwF,YAAY,IAAI;;UAE9C,OAAO1F,IAAI;QACb;OACD,EACD;QACEA,IAAI,EAAE,IAAI;QACVN,SAAS,EAAE,iBAAiB;QAC5BuF,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,OAAO,KAAK;QACd;OACD,EACD;QACEhG,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;QACnD4F,IAAI,EAAE,YAAY;QAClBN,SAAS,EAAE,iBAAiB;QAC5BuF,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;UAC/B,IAAIA,GAAG,CAACyF,YAAY,IAAI,IAAI,EAAE;YAC5B,OAAO,GAAG3F,IAAI,UAAUE,GAAG,CAACyF,YAAY,IAAI;;UAE9C,OAAO3F,IAAI;QACb;OACD,EACD;QACE9F,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,UAAU,CAAC;QACjD0K,OAAO,EAAE,KAAK;QACd9E,IAAI,EAAE,UAAU;QAChB+E,MAAM,EAAG/E,IAAI,IAAI;UACf,OAAO;gBACHA,IAAI,CAACmB,GAAG,CAAEyE,IAAI,IAAI;YACpB,MAAMC,WAAW,GAAGD,IAAI,CAACE,IAAI,GAAG,GAAGF,IAAI,CAACE,IAAI,CAACC,UAAU,IAAIH,IAAI,CAACE,IAAI,CAACE,SAAS,EAAE,GAAGJ,IAAI,CAACK,YAAY;YACpG,OAAO,gDAAgDJ,WAAW,MAAM;UAC1E,CAAC,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC;mBACJ;QACT;OACD,EACD;QACEhM,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,QAAQ,CAAC;QAC/C4F,IAAI,EAAE,QAAQ;QACd+E,MAAM,EAAEA,CAAC/E,IAAY,EAAE9E,IAAI,EAAEgF,GAAG,KAAI;UAClC,MAAMiG,GAAG,GAAG7P,MAAM,EAAE;UACpB,MAAM8P,WAAW,GAAGD,GAAG,CAAChB,MAAM,CAAC,OAAO,CAAC;UACvC,MAAMkB,KAAK,GAAGF,GAAG,CAAChB,MAAM,CAAC,YAAY,CAAC;UACtC,IAAI,CAACnF,IAAI,EAAE;YACT;YACA,IAAIE,GAAG,CAACmF,cAAc,IAAI,KAAK,EAAE;cAC/B,OAAO,gEAAgE;aACxE,MAAM;cACL,IACGnF,GAAG,CAACoG,IAAI,IAAID,KAAK,IAAInG,GAAG,CAACmF,cAAc,GAAGe,WAAW,IACtDlG,GAAG,CAACoG,IAAI,GAAGD,KAAK,EAChB;gBACA,OAAO,qDAAqD,IAAI,CAAC3N,iBAAiB,CAAC0B,OAAO,CACxF,UAAU,CACX,SAAS;eACX,MAAM,IACL8F,GAAG,CAACoG,IAAI,IAAID,KAAK,IACjBnG,GAAG,CAACkF,gBAAgB,IAAIgB,WAAW,IACnClG,GAAG,CAACmF,cAAc,IAAIe,WAAW,EACjC;gBACA,OAAO,qDAAqD,IAAI,CAAC1N,iBAAiB,CAAC0B,OAAO,CACxF,aAAa,CACd,SAAS;eACX,MAAM,IACL8F,GAAG,CAACoG,IAAI,GAAGD,KAAK,IACfnG,GAAG,CAACoG,IAAI,IAAID,KAAK,IAAInG,GAAG,CAACkF,gBAAgB,GAAGgB,WAAY,EACzD;gBACA,OAAO,kDAAkD,IAAI,CAAC1N,iBAAiB,CAAC0B,OAAO,CACrF,UAAU,CACX,SAAS;;;WAGf,MAAM;YACL,IAAIjE,SAAS,CAAC6G,kBAAkB,CAACuJ,QAAQ,CAACvG,IAAI,CAAC,EAAE;cAC/C,OAAO,oDAAoDA,IAAI,SAAS;aACzE,MAAM;cACL,OAAO,uDAAuDA,IAAI,SAAS;;;QAGjF,CAAC;QACDN,SAAS,EAAE,iBAAiB;QAC5BuF,SAAS,EAAE;OACZ,CACF;MACDuB,UAAU,EAAE,CACV,CAAC,CAAC,CAAC,CAAC,EACJ,CAAC,KAAK,CAAC,CACR;MACD7G,OAAO,EAAE;QACP0D,GAAG,EAAE,IAAI,CAAC1K,eAAe,CAAC2K,iBAAiB,CAAC3D,OAAO,CAAC0D,GAAG;QACvD1D,OAAO,EAAEf;;KAEZ;IAED,QAAQ,IAAI,CAACxB,KAAK,CAAClC,IAAI;MACrB,KAAK/E,SAAS,CAACsH,gBAAgB,CAACC,QAAQ;QACtC,IAAI,CAACtE,SAAS,CAACyK,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACnC;QACA,IAAI,CAACzK,SAAS,CAACuK,QAAQ,GAAG;UAAEC,OAAO,EAAE;QAAY,CAAE;QACnD;QACA,IAAI,CAACxK,SAAS,CAACyL,OAAO,CAACtG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;UAClCrE,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;UAC7C4F,IAAI,EAAE,YAAY;UAClBN,SAAS,EAAE,aAAa;UACxBqF,MAAM,EAAE,SAAAA,CAAU/E,IAAI;YACpB,OAAO,+CAA+CA,IAAI,MAAM;UAClE;SACD,CAAC;QAEF;MACF,KAAK7J,SAAS,CAACsH,gBAAgB,CAACW,MAAM;QACpC,IAAI,CAAChF,SAAS,CAACyK,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEnC;QACA,IAAI,CAACzK,SAAS,CAACyL,OAAO,CAACtG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;UAClCrE,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,OAAO,CAAC;UAC9C4F,IAAI,EAAE,YAAY;UAClBN,SAAS,EAAE,aAAa;UACxBqF,MAAM,EAAE,SAAAA,CAAU/E,IAAI,EAAE9E,IAAI,EAAEgF,GAAG;YAC/B;YACA,IAAI,CAACF,IAAI,EAAE,OAAO,EAAE;YACpB,IAAIyG,UAAU,GAAGzG,IAAI,CAAC0G,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;YACtC,OAAO,+CAA+CF,UAAU,MAAM;UACxE;SACD,CAAC;QACF;MACF,KAAKtQ,SAAS,CAACsH,gBAAgB,CAACU,MAAM;QACpC;QACA,IAAI,CAAC/E,SAAS,CAACuK,QAAQ,GAAG,IAAI;QAC9B,IAAI,CAACvK,SAAS,CAACyL,OAAO,CAACtG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;UAClCrE,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,OAAO,CAAC;UAC9C4F,IAAI,EAAE,YAAY;UAClBN,SAAS,EAAE,aAAa;UACxBqF,MAAM,EAAE,SAAAA,CAAU/E,IAAI;YACpBlD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEiD,IAAI,CAAC;YACzB,OAAO,+CAA+CA,IAAI,IAAI,EAAE,MAAM;UACxE;SACD,CAAC;QACF;QACA,IAAI,CAAC5G,SAAS,CAACyK,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACnC;MAEF;QACE;;EAEN;EAEA+C,qBAAqBA,CAACC,WAAoB;IACxC,MAAMC,WAAW,GAAG;MAClB7L,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC,aAAa,CAAC;QACpDiB,WAAW,EAAE,IAAI,CAAC3C,iBAAiB,CAAC0B,OAAO,CAAC,oBAAoB,CAAC;QACjE;QACAQ,OAAO,EAAE,IAAI,CAACG;;KAEjB;IAED,IAAI8L,WAAW,EAAE;MACf,IAAI,IAAI,CAACzJ,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACU,MAAM,EAAE;QACzD;QACA,MAAM4I,iBAAiB,GAAI,IAAI,CAAC/L,SAAiB,CAACyD,SAAS,CACxDmH,IAAI,IAAKA,IAAI,CAAC3K,GAAG,KAAK,aAAa,CACrC;QACD;QACA,IAAI8L,iBAAiB,GAAG,CAAC,CAAC,EAAE;UACzB,IAAI,CAAC/L,SAAiB,CAACuD,MAAM,CAACwI,iBAAiB,EAAE,CAAC,CAAC;;;KAGzD,MAAM;MACL,IAAI,IAAI,CAAC3J,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACU,MAAM,EAAE;QACzD,MAAM4I,iBAAiB,GAAI,IAAI,CAAC/L,SAAiB,CAACyD,SAAS,CACxDmH,IAAI,IAAKA,IAAI,CAAC3K,GAAG,KAAK,aAAa,CACrC;QACD;QACA,IAAI8L,iBAAiB,GAAG,CAAC,EAAE;UACxB,IAAI,CAAC/L,SAAiB,CAACuD,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;YACnC,GAAGuI;WACJ,CAAC;;;;EAIV;EAEA7P,qBAAqBA,CAAC+P,MAAM;IAC1B,IAAI,CAACtN,qBAAqB,GAAGsN,MAAM;IACnC,IAAI,CAACJ,qBAAqB,CAACI,MAAM,CAAC;IAClC;IACA,IAAI,CAAC7N,SAAS,CAAC8N,UAAU,CAACrG,IAAI,CAAEqG,UAAgC,IAAI;MAClE;MACAA,UAAU,CAAClH,IAAI,EAAE,CAACmH,QAAQ,EAAE;MAC5B,IAAI,CAACF,MAAM,EAAE;QACX;QACAC,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACrC,OAAO,CAAC,IAAI,CAAC;QAClCmC,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;QACrC;QACA,IAAIzI,IAAI,GAAGqI,UAAU,CAAC/E,MAAM,CAAC,oBAAoB,CAAC;QAClDpF,OAAO,CAACC,GAAG,CAAC6B,IAAI,CAAC;QACjB,IAAIA,IAAI,EAAE;UACRA,IAAI,CAAC0I,MAAM,EAAE;;OAEhB,MAAM;QACL;QACAL,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACrC,OAAO,CAAC,IAAI,CAAC;QAClCmC,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;QACrC;QACA,IAAIzI,IAAI,GAAGqI,UAAU,CAAC/E,MAAM,CAAC,oBAAoB,CAAC;QAClDpF,OAAO,CAACC,GAAG,CAAC6B,IAAI,CAAC;QACjB,IAAIA,IAAI,EAAE;UACRA,IAAI,CAAC2I,OAAO,EAAE;;;IAGpB,CAAC,CAAC;EACJ;EAEAnH,mBAAmBA,CAACF,GAAG;IACrB,IAAIC,SAAS,GAAG,KAAK;IACrB;IACA,IAAIhK,SAAS,CAAC6G,kBAAkB,CAACuJ,QAAQ,CAACrG,GAAG,CAACqB,MAAM,CAAC,EAAE;MACrDpB,SAAS,GAAG,IAAI;;IAElB,OAAOA,SAAS;EAClB;EAEQqH,gBAAgBA,CAACC,eAAA,GAA2B,KAAK;IACtD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAChCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAACxK,KAAK,CAACC,EAAE,CAACwK,QAAQ,EAAE,CAAC;IACrDH,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAEH,eAAe,GAAG,GAAG,GAAG,GAAG,CAAC;IAChE,IAAI,CAAC7O,aAAa,CAACiH,mBAAmB,CAAC6H,QAAQ,CAAC,CAACzD,SAAS,CACvDtC,IAAS,IAAI;MACZ,IAAI,CAACxI,SAAS,CAAC8N,UAAU,CAACrG,IAAI,CAAEqG,UAA0B,IAAI;QAC5DA,UAAU,CAACpF,IAAI,CAACC,MAAM,EAAE;QACxB,IAAI,CAACrI,YAAY,CAACsI,IAAI,CAACJ,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,EACAmG,KAAK,IAAI;MACR,IAAIA,KAAK,CAACC,OAAO,EAAE;QACjB1R,IAAI,CAAC6I,IAAI,CAAC;UACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,sBAAsB,CAAC;UAC7D4N,IAAI,EAAEF,KAAK,CAACC,OAAO,CAACE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;UAC1C9I,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;UACvDiF,WAAW,EAAE;YACXC,aAAa,EAAE;;SAElB,CAAC;QACF;OACD,MAAM;QACLjJ,IAAI,CAAC6I,IAAI,CAAC;UACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,OAAO,CAAC;UAC9CyE,IAAI,EAAEiJ,KAAK,CAACI,OAAO;UACnB/I,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;UACvDiF,WAAW,EAAE;YACXC,aAAa,EAAE;;SAElB,CAAC;;IAEN,CAAC,CACF;EACH;EAEAO,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACvG,QAAQ,EAAE;MACjB,IAAI,CAACR,eAAe,CAAC0I,IAAI,EAAE;MAE3BnL,IAAI,CAAC6I,IAAI,CAAC;QACRhF,KAAK,EAAE,GAAG,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,eAAe,CAAC,EAAE;QAC3D4N,IAAI,EAAE;iBACG,IAAI,CAACtP,iBAAiB,CAAC0B,OAAO,CACjC,qEAAqE,CACtE;;;mEAGsD,IAAI,CAAC1B,iBAAiB,CAAC0B,OAAO,CAC/E,uDAAuD,CACxD;;SAER;QACD+E,IAAI,EAAE,SAAS;QACfqB,gBAAgB,EAAE,IAAI;QACtBpB,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,KAAK,CAAC;QACxDqG,gBAAgB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC0B,OAAO,CAAC,IAAI;OACtD,CAAC,CAACwG,IAAI,CAAEC,MAAM,IAAI;QACjB,IAAIA,MAAM,CAACsH,WAAW,EAAE;UACtB,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAqB;UAC/E,MAAMC,SAAS,GAAGH,QAAQ,EAAEI,OAAO,IAAI,KAAK;UAC5CnS,IAAI,CAAC6I,IAAI,CAAC;YACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,4BAA4B,CAAC;YACnE4N,IAAI,EAAE,MAAM,IAAI,CAACtP,iBAAiB,CAAC0B,OAAO,CACxC,uGAAuG,CACxG,MAAM;YACP+E,IAAI,EAAE,SAAS;YACfqB,gBAAgB,EAAE,IAAI;YACtBpB,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,eAAe,CAAC;YAClEqG,gBAAgB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC0B,OAAO,CAAC,QAAQ;WAC1D,CAAC,CAACwG,IAAI,CAAE6H,SAAS,IAAI;YACpB,IAAIA,SAAS,CAACN,WAAW,EAAE;cACzB,IAAI,CAACX,gBAAgB,CAACe,SAAS,CAAC;aACjC,MAAM;cACL,IAAI,CAACzP,eAAe,CAAC4P,OAAO,EAAE;;UAElC,CAAC,CAAC;SACH,MAAM;UACL,IAAI,CAAC5P,eAAe,CAAC4P,OAAO,EAAE;;MAElC,CAAC,CAAC;KACH,MAAM;MACL,IAAI,IAAI,CAACnP,UAAU,EAAE;QACnB,IAAI,CAACiO,gBAAgB,CAAC,KAAK,CAAC;;;EAGlC;EAEApG,UAAUA,CAACuH,cAAc;IACvB7L,OAAO,CAACC,GAAG,CAAC,mBAAmB4L,cAAc,EAAE,CAAC;IAChD,IAAI,CAAC7P,eAAe,CAAC0I,IAAI,EAAE;IAC3B,IAAI,CAAC5I,aAAa,CAACwI,UAAU,CAACuH,cAAc,CAAC,CAAC1E,SAAS,CACpDtC,IAAS,IAAI;MACZ7E,OAAO,CAACC,GAAG,CAAC4E,IAAI,CAAC;MAEjB,IAAI,CAACxI,SAAS,CAAC8N,UAAU,CAACrG,IAAI,CAAEqG,UAA0B,IAAI;QAC5DA,UAAU,CAACpF,IAAI,CAACC,MAAM,EAAE;QACxB,IAAI,CAACrI,YAAY,CAACsI,IAAI,CAACJ,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,EACAmG,KAAK,IAAI;MACRzR,IAAI,CAAC6I,IAAI,CAAC;QACRhF,KAAK,EAAE,IAAI,CAACxB,iBAAiB,CAAC0B,OAAO,CAAC,OAAO,CAAC;QAC9CyE,IAAI,EAAEiJ,KAAK,CAACI,OAAO;QACnB/I,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0B,OAAO,CAAC,IAAI,CAAC;QACvDiF,WAAW,EAAE;UACXC,aAAa,EAAE;;OAElB,CAAC;IACJ,CAAC,CACF;EACH;EAEAwB,SAASA,CAAC8H,WAAuB;IAC/B,IAAI,CAAC9P,eAAe,CAAC0I,IAAI,EAAE;IAC3B,IAAI,CAAC5I,aAAa,CAACkI,SAAS,CAAC8H,WAAW,CAAC,CAAC3E,SAAS,CAAEtC,IAAS,IAAI;MAChE,IAAI,CAACxI,SAAS,CAAC8N,UAAU,CAACrG,IAAI,CAAEqG,UAA0B,IAAI;QAC5DA,UAAU,CAACpF,IAAI,CAACC,MAAM,EAAE;QACxB,IAAI,CAACrI,YAAY,CAACsI,IAAI,CAACJ,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAkH,SAASA,CAACnR,MAAM;IACd,IAAI,CAACyB,SAAS,CAAC8N,UAAU,CAACrG,IAAI,CAAEqG,UAA0B,IAAI;MAC5DA,UAAU,CAACpF,IAAI,CAACC,MAAM,EAAE;IAC1B,CAAC,CAAC;IACF,IAAI,IAAI,CAAC1E,KAAK,CAAClC,IAAI,KAAK/E,SAAS,CAACsH,gBAAgB,CAACW,MAAM,EAAE;MACzD,IAAI,CAACxF,aAAa,CAACkQ,eAAe,CAAC,IAAI,CAAC1L,KAAK,CAACC,EAAE,CAAC,CAAC4G,SAAS,CACxDtC,IAAS,IAAI;QACZ7E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4E,IAAI,CAAC;MACtC,CAAC,EACAmG,KAAK,IAAI;QACRhL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+K,KAAK,CAAC;MACvC,CAAC,CACF;;IAGH,IAAI,IAAI,CAAChO,gBAAgB,EAAE;MACzB,IAAI,CAACN,aAAa,CAACuI,IAAI,CAACrK,MAAM,CAAC;;IAEjC,IAAI,CAAC+B,YAAY,CAACsI,IAAI,CAACrK,MAAM,CAAC;EAChC;EAEAuJ,wBAAwBA,CAACf,GAAG;IAC1B,OAAO,EAAE,CAACA,GAAG,CAACoG,IAAI,IAAI,CAACpG,GAAG,CAAC6I,UAAU,IAAI,CAAC7I,GAAG,CAAC8I,QAAQ,IAAI,CAAC9I,GAAG,CAACvF,QAAQ,CAAC;EAC1E;EAEA4E,MAAMA,CAAC7E,MAAM,EAAEoB,MAAM,EAAEoE,GAAI;IACzB,IAAI,CAAClG,MAAM,CAACiP,QAAQ,GAAG,KAAK;IAC5B,IAAI,CAACpP,YAAY,GAAG;MAClBqP,UAAU,EAAE,IAAI,CAAC9L,KAAK,CAAClC,IAAI;MAC3BiO,WAAW,EAAErN;KACd;IACD;IACA,IAAI,CAAC3C,SAAS,CAAC8N,UAAU,CAACrG,IAAI,CAAEqG,UAA0B,IAAI;MAC5D,IAAInH,YAAY,GAAGmH,UAAU,CAAClH,IAAI,CAAC;QAAElF,QAAQ,EAAE;MAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;MAC7DlD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE+C,YAAY,CAAC;MAEzC,QAAQhE,MAAM;QACZ,KAAK,WAAW;UACd,IAAI,CAAC9B,MAAM,CAACE,KAAK,CAACG,IAAI,GAAG,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,YAAY,CAAC;UAErE,IAAI,CAAC0B,MAAM,GAAG,CAAC,GAAG,IAAI,CAACd,SAAS,EAAE;YAChCC,GAAG,EAAE,cAAc;YACnBC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE;cACLD,IAAI,EAAE;aACP;YACDe,YAAY,EAAE6D,YAAY,CAAC,CAAC,CAAC,EAAEyB;WAChC,CAAC,CAACJ,GAAG,CAAEyE,IAAI,KAAM;YAAE,GAAGA;UAAI,CAAE,CAAC,CAAC;UAC/B9I,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACjB,MAAM,CAAC;UAClC,IAAI,CAAChC,gBAAgB,GAAG,KAAK;UAE7B,IAAI,CAACtB,KAAK,CACPwL,IAAI,CAAM,GAAGhO,WAAW,CAACwE,MAAM,0BAA0B,IAAI,CAAC4C,KAAK,CAACC,EAAE,EAAE,EAAE,EAAE,CAAC,CAC7E4G,SAAS,CAAEtC,IAAS,IAAI;YACvB,IAAI,CAAC7F,MAAM,CAACmB,OAAO,CAAE2I,IAAI,IAAI;cAC3B,IAAIA,IAAI,CAAC3K,GAAG,KAAK,cAAc,IAAI2K,IAAI,CAAC3K,GAAG,KAAK,cAAc,EAAE;gBAC9D2K,IAAI,CAACzK,KAAK,CAACP,OAAO,GAAG+G,IAAI,CAAC/G,OAAO,CAACE,KAAK;;cAEzC,IAAI8K,IAAI,CAAC3K,GAAG,KAAK,aAAa,EAAE;gBAC9B2K,IAAI,CAACzK,KAAK,CAACP,OAAO,GAAG+G,IAAI,CAAC/G,OAAO,CAACD,QAAQ;;YAE9C,CAAC,CAAC;YAEF,IAAI,CAACmB,MAAM,CAACmB,OAAO,CAAE2I,IAAI,IAAI;cAC3B,IAAIA,IAAI,CAAC3K,GAAG,KAAK,SAAS,EAAE;gBAC1B2K,IAAI,CAACzI,KAAK,GAAG2C,YAAY,CAAC,CAAC,CAAC,EAAEsJ,QAAQ,EAAEjI,GAAG,CAAEkI,CAAC,IAAKA,CAAC,CAAChM,EAAE,CAAC,IAAI,EAAE;;YAElE,CAAC,CAAC;YAEF,IAAI,CAAC1D,cAAc,CAAC2P,IAAI,CAAC,IAAI,CAACxN,MAAM,CAAC;YACrC,IAAI,CAAC9B,MAAM,CAACU,MAAM,GAAGA,MAAM;YAC3B,IAAI,CAACV,MAAM,CAACkG,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;YAClC,IAAI,CAACnH,mBAAmB,CAACwQ,kBAAkB,CAAC,IAAI,CAACxP,UAAU,CAAC,CAACyP,UAAU,EAAE;UAC3E,CAAC,CAAC;UAEJ;QACF,KAAK,aAAa;UAChB,IAAI,CAACxP,MAAM,CAACE,KAAK,CAACG,IAAI,GACpB,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;UAChD,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAACL,WAAW;UAC9B,IAAI,CAAC3B,gBAAgB,GAAG,IAAI;UAC5B;QACF,KAAK,aAAa;UAChB,IAAI,CAACE,MAAM,CAACE,KAAK,CAACG,IAAI,GACpB,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,cAAc,CAAC;UAChD,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAACJ,WAAW;UAC9B,IAAI,CAAC5B,gBAAgB,GAAG,KAAK;UAC7B;QACF,KAAK,iBAAiB;UACpBgD,OAAO,CAACC,GAAG,CAACmD,GAAG,CAAC;UAEhB,IAAIuJ,UAAU,GAAG;YACf,qBAAqB,EAAEvJ,GAAG,CAACuG,UAAU;YACrC,sBAAsB,EAAEvG,GAAG,CAAC4G,WAAW;YACvC,mBAAmB,EAAE5G,GAAG,CAAC7C,EAAE;YAC3B,uBAAuB,EAAE6C,GAAG,CAACwJ,YAAY,IAAI;WAC9C;UACD;UACA,IAAI,CAAC7P,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAG4P;UAAU,CAAE;UAC3D,IAAI,CAACzP,MAAM,CAACE,KAAK,CAACG,IAAI,GACpB,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,mBAAmB,CAAC;UACrD,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAACd,SAAS;UAC5B,IAAI,CAAChB,MAAM,CAACiP,QAAQ,GAAG,IAAI;UAC3B;QACF,KAAK,iBAAiB;UACpB,IAAI,CAACjP,MAAM,CAACE,KAAK,CAACG,IAAI,GAAG,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,WAAW,CAAC;UACpE,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAACF,eAAe;UAClC,IAAI,CAAC9B,gBAAgB,GAAG,KAAK;UAC7B;QACF;UACE,IAAI,CAACE,MAAM,CAACE,KAAK,CAACG,IAAI,GAAG,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,WAAW,CAAC;UACpE,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAACd,SAAS,CAACmG,GAAG,CAAEyE,IAAI,IAAI;YACxC,IAAIA,IAAI,CAAC3K,GAAG,KAAK,SAAS,EAAE;cAC1B2K,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;cACzBA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;;YAEpB,OAAOA,IAAI;UACb,CAAC,CAAC;UACF,IAAI,CAAC9L,gBAAgB,GAAG,KAAK;UAC7B;;MAEJ,IAAI,CAACH,cAAc,CAAC2P,IAAI,CAAC,IAAI,CAACxN,MAAM,CAAC;MACrC,IAAI,CAAC9B,MAAM,CAACU,MAAM,GAAGA,MAAM;MAC3B,IAAI,CAACV,MAAM,CAACkG,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;MAElC,IAAI,CAACnH,mBAAmB,CAACwQ,kBAAkB,CAAC,IAAI,CAACxP,UAAU,CAAC,CAACyP,UAAU,EAAE;IAC3E,CAAC,CAAC;EACJ;EAEAG,cAAcA,CAACC,KAAK;IAClB,IAAI,CAAC5O,SAAS,CAACiC,OAAO,CAAE6B,CAAC,IAAI;MAC3B,IAAIA,CAAC,CAAC7D,GAAG,KAAK,SAAS,EAAE;QACvB6D,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE;QACtBA,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;;IAEnB,CAAC,CAAC;EACJ;EAEA+K,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACzQ,SAAS,CAACiQ,IAAI,CAAC,IAAI,CAAClQ,SAAS,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA2Q,WAAWA,CAAA;IACT,IAAI,CAAC1Q,SAAS,CAAC2Q,WAAW,EAAE;EAC9B;EA0BAjJ,SAASA,CAAA;IACP,IAAI,CAAC5H,SAAS,CAAC8N,UAAU,CAACrG,IAAI,CAAEqG,UAAgC,IAAI;MAClE,IAAInH,YAAY,GAAGmH,UAAU,CAAClH,IAAI,CAAC;QAAElF,QAAQ,EAAE;MAAI,CAAE,CAAC,CAACmF,IAAI,EAAE;MAE7D,IAAI,CAAC/H,WAAW,GAAG,EAAE;MACrB6H,YAAY,CAACqB,GAAG,CAAEjB,GAAG,IAAI;QACvB,OAAO,IAAI,CAACjI,WAAW,CAACiF,IAAI,CAACgD,GAAG,CAAC7C,EAAE,CAAC;MACtC,CAAC,CAAC;MAEF,IAAIyC,YAAY,CAACG,MAAM,GAAG,CAAC,EAAE;QAC3B,IAAI,CAACjI,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACK,kBAAkB,GAAG;UACxB,GAAG,IAAI,CAACA,kBAAkB;UAC1B4R,aAAa,EAAE;SAChB;OACF,MAAM;QACL,IAAI,CAACjS,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACK,kBAAkB,GAAG;UACxB,GAAG,IAAI,CAACA,kBAAkB;UAC1B4R,aAAa,EAAEnK,YAAY,CAAC,CAAC,CAAC,EAAEsJ,QAAQ,EAAEjI,GAAG,CAAEkI,CAAC,IAAKA,CAAC,CAAChM,EAAE,CAAC,IAAI;SAC/D;;MAGH,IAAI,CAACnE,aAAa,CAACgR,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;QAC/CC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,IAAI;QACVC,aAAa,EAAEA,CAAA,KAAK;UAClB,IAAI,CAACjS,kBAAkB,GAAG,EAAE;UAC5B,IAAI,CAACD,mBAAmB,CAAC6E,OAAO,CAAE2I,IAAI,IAAI;YACxC,IAAIA,IAAI,CAAC3K,GAAG,KAAK,eAAe,EAAE;cAChC2K,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;;UAE7B,CAAC,CAAC;UACF,OAAO,IAAI;QACb;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEArJ,cAAcA,CAAA;IACZ,IAAI,CAAC3D,aAAa,CAAC2R,wBAAwB,CAAC,IAAI,CAACnN,KAAK,CAACC,EAAE,CAAC,CAAC4G,SAAS,CAAEuG,QAAQ,IAAI;MAChF,IAAI,CAACtS,YAAY,GAAGsS,QAAQ,CAAC,MAAM,CAAC;MACpC,IAAI,CAACpS,mBAAmB,CAAC6E,OAAO,CAAE2I,IAAI,IAAI;QACxC,IAAIA,IAAI,CAAC3K,GAAG,KAAK,eAAe,EAAE;UAChC2K,IAAI,CAACzK,KAAK,CAACP,OAAO,GAAG4P,QAAQ,CAAC,MAAM,CAAC,CAACrJ,GAAG,CAAEsJ,OAAO,IAAI;YACpD3N,OAAO,CAACC,GAAG,CAAC0N,OAAO,CAAC;YACpB,OAAO;cACLrP,KAAK,EAAEqP,OAAO,CAAC3E,IAAI,GACf,GAAG2E,OAAO,CAAC3E,IAAI,CAACC,UAAU,IAAI0E,OAAO,CAAC3E,IAAI,CAACE,SAAS,EAAE,GACtDyE,OAAO,CAACxE,YAAY;cACxB9I,KAAK,EAAEsN,OAAO,CAACpN;aAChB;UACH,CAAC,CAAC;;MAEN,CAAC,CAAC;IACJ,CAAC,CACA;EACH;EAEAxF,qBAAqBA,CAAC+R,KAAK;IACzB,IAAI,CAACzQ,SAAS,CAAC8N,UAAU,CAACrG,IAAI,CAAEqG,UAA0B,IAAI;MAC5DA,UAAU,CAACpF,IAAI,CAACC,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA4I,YAAYA,CAAA;IACV,IAAI,CAAC9R,aAAa,CAAC+R,YAAY,CAAC,IAAI,CAACvN,KAAK,CAACC,EAAE,CAAC,CAAC4G,SAAS,CAAEuG,QAAQ,IAAI;MACpE1N,OAAO,CAACC,GAAG,CAACyN,QAAQ,CAAC;IACvB,CAAC,CAAC;EACJ;EAAC,QAAAI,CAAA;qBA1rDUtS,qBAAqB,EAAA9B,EAAA,CAAAqU,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAvU,EAAA,CAAAqU,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzU,EAAA,CAAAqU,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA3U,EAAA,CAAAqU,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA7U,EAAA,CAAAqU,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA/U,EAAA,CAAAqU,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAAjV,EAAA,CAAAqU,iBAAA,CAAAa,EAAA,CAAAC,cAAA,GAAAnV,EAAA,CAAAqU,iBAAA,CAAAe,EAAA,CAAAC,kBAAA,GAAArV,EAAA,CAAAqU,iBAAA,CAAAiB,EAAA,CAAAC,aAAA,GAAAvV,EAAA,CAAAqU,iBAAA,CAAAmB,GAAA,CAAAC,aAAA,GAAAzV,EAAA,CAAAqU,iBAAA,CAAAqB,GAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA;UAArB9T,qBAAqB;IAAA+T,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBACrBpW,kBAAkB;;;;;;;;;;;;;;;;;;;;;;QCxC/BI,EAAA,CAAAC,cAAA,aAA+C;QAIvCD,EAAA,CAAAkW,UAAA,IAAAC,mCAAA,iBAgBK;QACPnW,EAAA,CAAAW,YAAA,EAAM;QAENX,EAAA,CAAAoW,SAAA,eAKS;QACXpW,EAAA,CAAAW,YAAA,EAAM;QAIVX,EAAA,CAAAC,cAAA,sBAIC;QAKGD,EAAA,CAAAE,UAAA,uBAAAmW,uEAAAnV,MAAA;UAAA,OAAa+U,GAAA,CAAA5D,SAAA,CAAAnR,MAAA,CAAiB;QAAA,EAAC,qBAAAoV,qEAAApV,MAAA;UAAA,OACpB+U,GAAA,CAAA9C,cAAA,CAAAjS,MAAA,CAAsB;QAAA,EADF;QAKjClB,EAAA,CAAAW,YAAA,EAAqB;QAGvBX,EAAA,CAAAkW,UAAA,IAAAK,4CAAA,gCAAAvW,EAAA,CAAAwW,sBAAA,CAUc;;;QApDHxW,EAAA,CAAAc,SAAA,GAAsD;QAAtDd,EAAA,CAAAsB,UAAA,SAAA2U,GAAA,CAAArP,KAAA,CAAAlC,IAAA,KAAAuR,GAAA,CAAAtW,SAAA,CAAAsH,gBAAA,CAAAU,MAAA,CAAsD;QAiBzD3H,EAAA,CAAAc,SAAA,GAAuB;QAAvBd,EAAA,CAAAsB,UAAA,cAAA2U,GAAA,CAAArT,SAAA,CAAuB,cAAAqT,GAAA,CAAApT,SAAA;QAU7B7C,EAAA,CAAAc,SAAA,GAAmB;QAAnBd,EAAA,CAAAsB,UAAA,SAAA2U,GAAA,CAAA1S,UAAA,CAAmB;QAIjBvD,EAAA,CAAAc,SAAA,GAAmB;QAAnBd,EAAA,CAAAsB,UAAA,UAAA2U,GAAA,CAAAtT,SAAA,CAAmB,WAAAsT,GAAA,CAAA3Q,MAAA,YAAA2Q,GAAA,CAAAzS,MAAA,kBAAAyS,GAAA,CAAA5S,YAAA,oBAAA4S,GAAA,CAAA9S,cAAA", "names": ["environment", "EventEmitter", "Subject", "AppConfig", "DataTableDirective", "<PERSON><PERSON>", "moment", "FormGroup", "i0", "ɵɵelementStart", "ɵɵlistener", "StageMatchesComponent_ul_4_Template_a_click_3_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "filterFriendlyMatches", "ɵɵtext", "ɵɵelementEnd", "StageMatchesComponent_ul_4_Template_a_click_7_listener", "ctx_r6", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "StageMatchesComponent_ng_template_8_Template_app_modal_assign_referees_onSubmit_0_listener", "$event", "_r9", "ctx_r8", "onSubmitAssignReferee", "ɵɵproperty", "ctx_r2", "isMultipleAssign", "selectedIds", "listReferees", "assignRefereeForm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assignRefereeModel", "StageMatchesComponent", "constructor", "_http", "_route", "_translateService", "_commonsService", "_stageService", "_userService", "_loadingService", "_coreSidebarService", "_toastr", "_exportService", "_modalService", "dtElement", "dtOptions", "dtTrigger", "hasMatch", "isComplete", "onUpdateScore", "onDataChange", "friendlyMatchesActive", "fields_subject", "cancelOptions", "paramsToPost", "is_score_updated", "table_name", "params", "editor_id", "title", "create", "instant", "edit", "remove", "url", "apiUrl", "method", "action", "location", "options", "selected", "teams", "roundLevelOpts", "editMatch", "key", "type", "props", "label", "placeholder", "max", "expressions", "hideOnMultiple", "updateScore", "cancelMatch", "required", "updateRankMatch", "closeOnSelect", "fields", "rank_fields", "multiple", "defaultValue", "hooks", "ngOnInit", "loadInitSettings", "setUp", "buildTable", "getListReferee", "settings", "localStorage", "getItem", "initSettings", "JSON", "parse", "console", "log", "CANCEL_MATCH_TYPES", "for<PERSON>ach", "push", "value", "stage", "id", "min", "step", "hide", "TOURNAMENT_TYPES", "knockout", "sport_type", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "league", "groups", "i", "no_encounters", "splice", "roundLevelIndex", "findIndex", "field", "_this", "btns", "text", "e", "dt", "node", "config", "fire", "icon", "confirmButtonText", "customClass", "confirmButton", "editor", "extend", "background", "className", "buttons", "attr", "autoGenerateMatches", "selectedRows", "rows", "data", "length", "row", "hasCancel", "checkRowIsCancelled", "home_team_id", "away_team_id", "reverseButtons", "showCancelButton", "cancelButtonText", "buttonsStyling", "cancelButton", "then", "result", "swapTeams", "openModal", "validUpdate", "checkValidateUpdateScore", "ids", "map", "resetScore", "x", "roundNameIndex", "status", "show", "deleteMatchesInStage", "to<PERSON>romise", "resp", "success", "ajax", "reload", "emit", "_ref", "_asyncToGenerator", "button", "exportData", "exportCsv", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "tournament", "type_knockout", "KNOCKOUT_TYPES", "type4", "manageMatchesGroup", "find", "btn", "Array", "isArray", "filter", "dom", "dataTableDefaults", "select", "window", "innerWidth", "rowId", "rowGroup", "dataSrc", "order", "dataTablesParameters", "callback", "post", "subscribe", "rank_label", "recordsTotal", "recordsFiltered", "initComplete", "responsive", "scrollX", "language", "lang", "columnDefs", "responsivePriority", "targets", "columns", "visible", "render", "meta", "orderable", "content", "format", "start_time_short", "end_time_short", "home_text", "home_label", "away_text", "away_label", "home_penalty", "away_penalty", "item", "<PERSON><PERSON><PERSON>", "user", "first_name", "last_name", "referee_name", "join", "now", "currentTime", "today", "date", "includes", "lengthMenu", "round_name", "split", "pop", "addOrRemoveRoundLevel", "is_friendly", "round_level", "round_level_index", "active", "dtInstance", "deselect", "column", "search", "draw", "enable", "disable", "_generateMatches", "confirmOverride", "formData", "FormData", "append", "toString", "error", "warning", "html", "replace", "message", "isConfirmed", "checkbox", "document", "getElementById", "isChecked", "checked", "subResult", "dismiss", "stage_match_id", "stage_match", "onSuccess", "checkMatchScore", "start_time", "end_time", "use_data", "stage_type", "type_action", "referees", "r", "next", "getSidebarRegistry", "toggle<PERSON><PERSON>", "paramsPost", "match_number", "onCloseSidebar", "event", "ngAfterViewInit", "setTimeout", "ngOnDestroy", "unsubscribe", "list_referees", "open", "modalAssignReferee", "centered", "size", "<PERSON><PERSON><PERSON><PERSON>", "getListRefereesByStageId", "response", "referee", "autoschedule", "autoSchedule", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "ActivatedRoute", "i3", "TranslateService", "i4", "CommonsService", "i5", "StageService", "i6", "UserService", "i7", "LoadingService", "i8", "CoreSidebarService", "i9", "ToastrService", "i10", "ExportService", "i11", "NgbModal", "_2", "selectors", "viewQuery", "StageMatchesComponent_Query", "rf", "ctx", "ɵɵtemplate", "StageMatchesComponent_ul_4_Template", "ɵɵelement", "StageMatchesComponent_Template_app_editor_sidebar_onSuccess_7_listener", "StageMatchesComponent_Template_app_editor_sidebar_onClose_7_listener", "StageMatchesComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-matches\\stage-matches.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-matches\\stage-matches.component.html"], "sourcesContent": ["import { environment } from '../../../../../environments/environment';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  Output,\r\n  TemplateRef,\r\n  ViewChild,\r\n  ViewEncapsulation\r\n} from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { ExportService } from 'app/services/export.service';\r\nimport { Subject } from 'rxjs';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport Swal from 'sweetalert2';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport moment from 'moment';\r\nimport { StageMatch } from 'app/interfaces/stages';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { UserService } from '../../../../services/user.service';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { SeasonService } from '../../../../services/season.service';\r\n\r\n@Component({\r\n  selector: 'app-stage-matches',\r\n  templateUrl: './stage-matches.component.html',\r\n  styleUrls: ['./stage-matches.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class StageMatchesComponent implements OnInit {\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  @Input() stage: any;\r\n  public contentHeader: object;\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  AppConfig = AppConfig;\r\n  @Input() tournament: any;\r\n  hasMatch: boolean = false;\r\n  private isComplete: boolean = false;\r\n  tournament_id: any;\r\n  @Output() onUpdateScore = new EventEmitter<any>();\r\n  @Output() onDataChange = new EventEmitter<any>();\r\n  initSettings: any;\r\n\r\n  listReferees = [];\r\n\r\n  // setup referee\r\n\r\n  constructor(\r\n    private _http: HttpClient,\r\n    public _route: ActivatedRoute,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public _stageService: StageService,\r\n    public _userService: UserService,\r\n    public _loadingService: LoadingService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _toastr: ToastrService,\r\n    private _exportService: ExportService,\r\n    private _modalService: NgbModal\r\n  ) {\r\n  }\r\n\r\n  friendlyMatchesActive = false;\r\n  public fields_subject = new Subject<any>();\r\n  public cancelOptions = [];\r\n  public paramsToPost = {};\r\n  public is_score_updated = false;\r\n  public table_name = 'matches-table';\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: this._translateService.instant('Add match'),\r\n      edit: this._translateService.instant('Edit match'),\r\n      remove: this._translateService.instant('Remove match')\r\n    },\r\n    url: `${environment.apiUrl}/stage-matches/editor`,\r\n    method: 'POST',\r\n    action: 'create'\r\n  };\r\n  public location = {\r\n    options: [],\r\n    selected: null\r\n  };\r\n  public teams = {\r\n    options: [],\r\n    selected: null\r\n  };\r\n\r\n  roundLevelOpts = [];\r\n\r\n  public editMatch = [\r\n    {\r\n      key: 'date',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Date'),\r\n        placeholder: this._translateService.instant('Enter date of match'),\r\n        // required: true,\r\n        type: 'date',\r\n        max: '2100-12-31'\r\n      },\r\n      expressions: {\r\n        'props.required':\r\n          '(model.hasOwnProperty(\"start_time_short\") && model.start_time_short!=\"\") || (model.hasOwnProperty(\"end_time_short\") && model.end_time_short!=\"\")'\r\n      }\r\n    },\r\n    {\r\n      key: 'start_time_short',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Start time'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter start time of match'\r\n        ),\r\n        // required: true,\r\n        type: 'time'\r\n      },\r\n      expressions: {\r\n        'props.required':\r\n          '(model.hasOwnProperty(\"date\") && model.date!=\"\") || (model.hasOwnProperty(\"end_time_short\") && model.end_time_short!=\"\")'\r\n      }\r\n    },\r\n    {\r\n      key: 'end_time_short',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('End time'),\r\n        placeholder: this._translateService.instant('Enter end time of match'),\r\n        // required: true,\r\n        type: 'time'\r\n      }\r\n    },\r\n\r\n    {\r\n      key: 'location_id',\r\n      type: 'select',\r\n      props: {\r\n        label: this._translateService.instant('Location'),\r\n        placeholder: this._translateService.instant('Select location'),\r\n        // required: true,\r\n        options: this.location.options\r\n      }\r\n    },\r\n\r\n    {\r\n      key: 'home_team_id',\r\n      type: 'select',\r\n      props: {\r\n        hideOnMultiple: true,\r\n        label: this._translateService.instant('Home team'),\r\n        placeholder: this._translateService.instant('Select home team'),\r\n        options: []\r\n      }\r\n    },\r\n    {\r\n      key: 'away_team_id',\r\n      type: 'select',\r\n      props: {\r\n        hideOnMultiple: true,\r\n        label: this._translateService.instant('Away team'),\r\n        placeholder: this._translateService.instant('Select away team'),\r\n        options: []\r\n      }\r\n    }\r\n  ];\r\n\r\n  public updateScore = [];\r\n\r\n  public cancelMatch = [\r\n    {\r\n      key: 'status',\r\n      type: 'radio',\r\n      props: {\r\n        label: this._translateService.instant('Cancel type'),\r\n        required: true,\r\n        options: this.cancelOptions\r\n      }\r\n    },\r\n    {\r\n      key: 'description',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Reason'),\r\n        placeholder: this._translateService.instant('Enter reason')\r\n      }\r\n    }\r\n  ];\r\n\r\n  public updateRankMatch = [\r\n    {\r\n      key: 'home_label_id',\r\n      type: 'ng-select',\r\n      props: {\r\n        hideOnMultiple: true,\r\n        label: this._translateService.instant('Home label'),\r\n        placeholder: this._translateService.instant('Select group'),\r\n        closeOnSelect: true,\r\n        options: []\r\n      }\r\n    },\r\n    {\r\n      key: 'away_label_id',\r\n      type: 'ng-select',\r\n      props: {\r\n        hideOnMultiple: true,\r\n        label: this._translateService.instant('Away label'),\r\n        placeholder: this._translateService.instant('Select away label'),\r\n        closeOnSelect: true,\r\n        options: []\r\n      }\r\n    }\r\n  ];\r\n\r\n  public fields: any[] = this.editMatch;\r\n\r\n  public rank_fields: any[] = this.updateRankMatch;\r\n\r\n  ngOnInit(): void {\r\n    this.loadInitSettings();\r\n    this.setUp();\r\n    this.buildTable();\r\n    this.getListReferee();\r\n  }\r\n\r\n  loadInitSettings() {\r\n    const settings = localStorage.getItem('initSettings');\r\n    if (settings) {\r\n      this.initSettings = JSON.parse(settings);\r\n      console.log('initSettings:', this.initSettings);\r\n    } else {\r\n      console.log('No initSettings found in localStorage');\r\n    }\r\n  }\r\n\r\n  setUp() {\r\n    AppConfig.CANCEL_MATCH_TYPES.forEach((type) => {\r\n      this.cancelOptions.push({\r\n        value: type,\r\n        label: this._translateService.instant(type)\r\n      });\r\n    });\r\n    (this.editMatch as any).push({\r\n      key: 'stage_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      },\r\n      defaultValue: this.stage.id\r\n    });\r\n\r\n    this.updateScore = [\r\n      {\r\n        key: 'type',\r\n        type: 'input',\r\n        props: {\r\n          type: 'hidden'\r\n        },\r\n        defaultValue: this.stage.type\r\n      },\r\n      {\r\n        key: 'home_score',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this._translateService.instant('Home score'),\r\n          required: true,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 999,\r\n          step: 1\r\n        },\r\n        defaultValue: 0\r\n      },\r\n      {\r\n        key: 'away_score',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this._translateService.instant('Away score'),\r\n          required: true,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 999,\r\n          step: 1\r\n        },\r\n        defaultValue: 0\r\n      },\r\n      {\r\n        key: 'home_penalty',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this._translateService.instant('Home penalty score'),\r\n          // required: true,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 100,\r\n          step: 1\r\n        },\r\n        expressions: {\r\n          hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\r\n        },\r\n        defaultValue: 0\r\n      },\r\n      {\r\n        key: 'away_penalty',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this._translateService.instant('Away penalty score'),\r\n          // required: true,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 100,\r\n          step: 1\r\n        },\r\n        expressions: {\r\n          hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\r\n        },\r\n        defaultValue: 0\r\n      }\r\n    ];\r\n\r\n    this.contentHeader = {\r\n      headerTitle: this._translateService.instant('League Reports'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._translateService.instant('Leagues'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._translateService.instant('League Reports'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    if (\r\n      this.stage.type === AppConfig.TOURNAMENT_TYPES.league ||\r\n      this.stage.type === AppConfig.TOURNAMENT_TYPES.groups\r\n    ) {\r\n      // get round level options in stage\r\n      for (let i = 1; i <= this.stage.no_encounters; i++) {\r\n        this.roundLevelOpts.push({\r\n          value: `${i}`,\r\n          label: `${this._translateService.instant('Round')} ${i}`\r\n        });\r\n      }\r\n    }\r\n\r\n    if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\r\n      // add match type at beginning of editMatch\r\n      (this.editMatch as any).splice(0, 0, {\r\n        key: 'match_type',\r\n        type: 'select',\r\n        props: {\r\n          label: this._translateService.instant('Match type'),\r\n          required: true,\r\n          options: [\r\n            {\r\n              value: 1,\r\n              label: this._translateService.instant('League Match')\r\n            },\r\n            {\r\n              value: 2,\r\n              label: this._translateService.instant('Friendly Match')\r\n            }\r\n          ]\r\n        }\r\n      });\r\n    }\r\n\r\n    if (\r\n      this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout\r\n    ) {\r\n      // find round level index in editMatch\r\n      let roundLevelIndex = (this.editMatch as any).findIndex(\r\n        (field: any) => field.key === 'round_level'\r\n      );\r\n      // remove round level in editMatch\r\n      if (roundLevelIndex > -1) {\r\n        (this.editMatch as any).splice(roundLevelIndex, 1);\r\n      }\r\n\r\n      // add round name at beginning of editMatch\r\n      (this.editMatch as any).splice(0, 0, {\r\n        key: 'round_name',\r\n        type: 'input',\r\n        props: {\r\n          hideOnMultiple: true,\r\n          label: this._translateService.instant('Match name'),\r\n          placeholder: this._translateService.instant('Enter round name')\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  buildTable() {\r\n    let btns = [\r\n      {\r\n        text: `<i class=\"fa-solid fa-plus\"></i> ${this._translateService.instant(\r\n          'Add'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // check if stage type is league\r\n          if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'You can not add match in this stage'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n\r\n          this.editor('create', 'editMatch');\r\n        }\r\n      },\r\n      {\r\n        // drop down button\r\n        extend: 'collection',\r\n        background: false,\r\n        text: this._translateService.instant('Manage Matches'),\r\n        className: 'action-button',\r\n        buttons: [\r\n          {\r\n            attr: {\r\n              id: 'auto-generate-btn'\r\n            },\r\n            text: `<i class=\"fa-solid fa-wand-magic-sparkles\"></i> ${this._translateService.instant(\r\n              'Auto Generate'\r\n            )}`,\r\n            action: (e, dt, node, config) => {\r\n              this.autoGenerateMatches();\r\n            }\r\n          },\r\n          {\r\n            text: `<i class=\"fa-solid fa-repeat-1\"></i> ${this._translateService.instant(\r\n              'Add replace match'\r\n            )}`,\r\n            action: (e, dt, node, config) => {\r\n              // get selected rows\r\n              let selectedRows = dt.rows({ selected: true }).data();\r\n              // check if selected rows > 1\r\n              if (selectedRows.length > 1) {\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Notification'),\r\n                  text: this._translateService.instant(\r\n                    'Please select only one match'\r\n                  ),\r\n                  icon: 'warning',\r\n                  confirmButtonText: this._translateService.instant('Ok'),\r\n                  customClass: {\r\n                    confirmButton: 'btn btn-primary'\r\n                  }\r\n                });\r\n                return;\r\n              }\r\n              let row = selectedRows[0];\r\n              let hasCancel = false;\r\n              if (this.checkRowIsCancelled(row)) {\r\n                hasCancel = true;\r\n              }\r\n              if (!hasCancel) {\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Notification'),\r\n                  text: this._translateService.instant(\r\n                    'You can not add replace match for this match'\r\n                  ),\r\n                  icon: 'warning',\r\n                  confirmButtonText: this._translateService.instant('Ok'),\r\n                  customClass: {\r\n                    confirmButton: 'btn btn-primary'\r\n                  }\r\n                });\r\n                return;\r\n              }\r\n              this.editor('create', 'addReplaceMatch', row);\r\n            },\r\n            extend: 'selected'\r\n          },\r\n          {\r\n            text: `<i class=\"feather icon-repeat\"></i> ${this._translateService.instant(\r\n              'Swap teams'\r\n            )}`,\r\n            action: (e, dt, node, config) => {\r\n              // get selected rows\r\n              let selectedRows = dt.rows({ selected: true }).data();\r\n              // check if selected rows > 1\r\n              if (selectedRows.length > 1) {\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Notification'),\r\n                  text: this._translateService.instant(\r\n                    'Please select only one match'\r\n                  ),\r\n                  icon: 'warning',\r\n                  confirmButtonText: this._translateService.instant('Ok'),\r\n                  customClass: {\r\n                    confirmButton: 'btn btn-primary'\r\n                  }\r\n                });\r\n                return;\r\n              }\r\n\r\n              // check if home_team_id or away_team_id is null\r\n              if (!selectedRows[0].home_team_id || !selectedRows[0].away_team_id) {\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Notification'),\r\n                  text: this._translateService.instant(\r\n                    'You can not swap teams for this match'\r\n                  ),\r\n                  icon: 'warning',\r\n                  confirmButtonText: this._translateService.instant('Ok'),\r\n                  customClass: {\r\n                    confirmButton: 'btn btn-primary'\r\n                  }\r\n                });\r\n                return;\r\n              } else {\r\n                // confirm swap teams\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Are you sure?'),\r\n                  text: this._translateService.instant(\r\n                    'Are you sure you want to swap teams in this match?'\r\n                  ),\r\n                  icon: 'warning',\r\n                  reverseButtons: true,\r\n                  showCancelButton: true,\r\n                  confirmButtonText: this._translateService.instant('Yes'),\r\n                  cancelButtonText: this._translateService.instant('No'),\r\n                  buttonsStyling: false,\r\n                  customClass: {\r\n                    confirmButton: 'btn btn-primary ml-1',\r\n                    cancelButton: 'btn btn-outline-primary'\r\n                  }\r\n                }).then((result) => {\r\n                  if (result.value) {\r\n                    this.swapTeams(selectedRows[0]);\r\n                  }\r\n                });\r\n              }\r\n            },\r\n            extend: 'selected'\r\n          },\r\n          {\r\n            text: `<i class=\"feather icon-flag\"></i>${this._translateService.instant(\r\n              'Assign Referee'\r\n            )}`,\r\n            action: () => this.openModal(),\r\n            extend: 'selected'\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        // drop down button\r\n        extend: 'collection',\r\n        background: false,\r\n        text: this._translateService.instant('Manage Score'),\r\n        className: 'action-button',\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"feather icon-edit\"></i> ${this._translateService.instant(\r\n              'Update Score'\r\n            )}`,\r\n            action: (e, dt, node, config) => {\r\n              let selectedRows = dt.rows({ selected: true }).data();\r\n              let row = selectedRows[0];\r\n              let validUpdate = false;\r\n              if (this.checkValidateUpdateScore(row)) {\r\n                validUpdate = true;\r\n              }\r\n              if (!validUpdate) {\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Cannot update score'),\r\n                  text: this._translateService.instant(\r\n                    'Please update the date, time and location of the match'\r\n                  ),\r\n                  icon: 'warning',\r\n                  confirmButtonText: this._translateService.instant('Ok'),\r\n                  customClass: {\r\n                    confirmButton: 'btn btn-primary'\r\n                  }\r\n                });\r\n              } else {\r\n                this.editor('edit', 'updateScore');\r\n              }\r\n            },\r\n            extend: 'selected'\r\n          },\r\n          {\r\n            text: `<i class=\"feather icon-rotate-ccw\"></i> ${this._translateService.instant(\r\n              'Reset Score'\r\n            )}`,\r\n            action: (e, dt, node, config) => {\r\n              // get selected rows\r\n              let selectedRows = dt.rows({ selected: true }).data();\r\n\r\n              // get stage_match id\r\n              let ids = [];\r\n\r\n              selectedRows.map((row) => ids.push(row.id));\r\n\r\n              // confirm reset score\r\n              Swal.fire({\r\n                title: this._translateService.instant('Are you sure?'),\r\n                text: this._translateService.instant(\r\n                  'Are you sure you want to reset score this match(s)?'\r\n                ),\r\n                reverseButtons: true,\r\n                icon: 'warning',\r\n                showCancelButton: true,\r\n                confirmButtonText: this._translateService.instant('Yes'),\r\n                cancelButtonText: this._translateService.instant('No'),\r\n                buttonsStyling: false,\r\n                customClass: {\r\n                  confirmButton: 'btn btn-primary ml-1',\r\n                  cancelButton: 'btn btn-outline-primary'\r\n                }\r\n              }).then((result) => {\r\n                if (result.value) {\r\n                  this.resetScore(ids);\r\n                }\r\n              });\r\n            },\r\n            extend: 'selected'\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-edit\"></i>${this._translateService.instant(\r\n          'Edit'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n\r\n          if (selectedRows.length > 1) {\r\n            // hide round level\r\n            let roundLevelIndex = (this.editMatch as any).findIndex(\r\n              (x) => x.key == 'round_level'\r\n            );\r\n            if (roundLevelIndex > -1) {\r\n              (this.editMatch as any).splice(roundLevelIndex, 1);\r\n            }\r\n\r\n            let roundNameIndex = (this.editMatch as any).findIndex(\r\n              (x) => x.key == 'round_name'\r\n            );\r\n\r\n            if (roundNameIndex > -1) {\r\n              (this.editMatch as any).splice(roundNameIndex, 1);\r\n            }\r\n          } else {\r\n            // show round level\r\n\r\n            let roundLevelIndex = (this.editMatch as any).findIndex(\r\n              (x) => x.key == 'round_level'\r\n            );\r\n            // if not found\r\n            if (\r\n              roundLevelIndex == -1 &&\r\n              this.stage.type == AppConfig.TOURNAMENT_TYPES.league\r\n            ) {\r\n              (this.editMatch as any).splice(3, 0, {\r\n                key: 'round_level',\r\n                type: 'select',\r\n                props: {\r\n                  label: this._translateService.instant('Round level'),\r\n                  placeholder:\r\n                    this._translateService.instant('Select round level'),\r\n                  // required: true,\r\n                  options: this.roundLevelOpts\r\n                }\r\n              });\r\n            }\r\n            if (\r\n              this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout\r\n            ) {\r\n              let roundNameIndex = (this.editMatch as any).findIndex(\r\n                (x) => x.key == 'round_name'\r\n              );\r\n\r\n              if (roundNameIndex == -1) {\r\n                (this.editMatch as any).splice(0, 0, {\r\n                  key: 'round_name',\r\n                  type: 'input',\r\n                  props: {\r\n                    label: this._translateService.instant('Match name'),\r\n                    placeholder:\r\n                      this._translateService.instant('Enter round name')\r\n                  }\r\n                });\r\n              }\r\n            }\r\n          }\r\n\r\n          this.editor('edit', 'editMatch');\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"fa-solid fa-repeat-1\"></i> ${this._translateService.instant(\r\n          'Add replace match'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // check if selected rows > 1\r\n          if (selectedRows.length > 1) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'Please select only one match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n          let row = selectedRows[0];\r\n          let hasCancel = false;\r\n          if (this.checkRowIsCancelled(row)) {\r\n            hasCancel = true;\r\n          }\r\n          if (!hasCancel) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'You can not add replace match for this match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n          this.editor('create', 'addReplaceMatch', row);\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-repeat\"></i> ${this._translateService.instant(\r\n          'Swap teams'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // check if selected rows > 1\r\n          if (selectedRows.length > 1) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'Please select only one match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n\r\n          // check if home_team_id or away_team_id is null\r\n\r\n          if (selectedRows[0].status !== 'pass' && (!selectedRows[0].home_team_id || !selectedRows[0].away_team_id)) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'You can not swap teams for this match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          } else {\r\n            // confirm swap teams\r\n            Swal.fire({\r\n              title: this._translateService.instant('Are you sure?'),\r\n              text: this._translateService.instant(\r\n                'Are you sure you want to swap teams in this match?'\r\n              ),\r\n              icon: 'warning',\r\n              reverseButtons: true,\r\n              showCancelButton: true,\r\n              confirmButtonText: this._translateService.instant('Yes'),\r\n              cancelButtonText: this._translateService.instant('No'),\r\n              buttonsStyling: false,\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary ml-1',\r\n                cancelButton: 'btn btn-outline-primary'\r\n              }\r\n            }).then((result) => {\r\n              if (result.value) {\r\n                this.swapTeams(selectedRows[0]);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"fa-solid fa-ban\"></i> ${this._translateService.instant(\r\n          'Cancel'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // check if selected rows has status is not can play\r\n          let hasCancel = false;\r\n          selectedRows.map((row) => {\r\n            if (this.checkRowIsCancelled(row)) {\r\n              hasCancel = true;\r\n            }\r\n          });\r\n\r\n          if (hasCancel) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Warning'),\r\n              text: this._translateService.instant(\r\n                'You can not cancel match that has been cancelled'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n\r\n            return;\r\n          }\r\n          // confirm cancel match\r\n          Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant(\r\n              'Are you sure you want to cancel this match(s)?'\r\n            ),\r\n            reverseButtons: true,\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            confirmButtonText: this._translateService.instant('Yes'),\r\n            cancelButtonText: this._translateService.instant('No'),\r\n            buttonsStyling: false,\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary ml-1',\r\n              cancelButton: 'btn btn-outline-primary'\r\n            }\r\n          }).then((result) => {\r\n            if (result.value) {\r\n              this.editor('edit', 'cancelMatch');\r\n            }\r\n          });\r\n        },\r\n\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-trash\"></i> ${this._translateService.instant(\r\n          'Delete'\r\n        )}`,\r\n        extend: 'selected',\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // get ids\r\n          let ids = [];\r\n          selectedRows.map((row) => {\r\n            ids.push(row.id);\r\n          });\r\n\r\n          // confirm delete\r\n          Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant(\r\n              'You will not be able to recover this!'\r\n            ),\r\n            reverseButtons: true,\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            confirmButtonText: this._translateService.instant('Yes'),\r\n            cancelButtonText: this._translateService.instant('No'),\r\n            buttonsStyling: false,\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary ml-1',\r\n              cancelButton: 'btn btn-outline-primary'\r\n            }\r\n          }).then((result) => {\r\n            if (result.value) {\r\n              // delete\r\n              this._loadingService.show();\r\n              this._stageService\r\n                .deleteMatchesInStage(ids, this.stage.id)\r\n                .toPromise()\r\n                .then((resp) => {\r\n                  this._toastr.success(\r\n                    this._translateService.instant('Deleted successfully')\r\n                  );\r\n                  dt.ajax.reload();\r\n                  this.onDataChange.emit(resp);\r\n                });\r\n            }\r\n          });\r\n        }\r\n      },\r\n      {\r\n        text: `<i class=\"fas fa-file-export mr-1\"></i> ${this._translateService.instant(\r\n          'Export'\r\n        )}`,\r\n        extend: 'csv',\r\n        action: async (e: any, dt: any, button: any, config: any) => {\r\n          const data = dt.buttons.exportData();\r\n          await this._exportService.exportCsv(data, 'Matches.csv');\r\n        }\r\n      },\r\n      {\r\n        text: `<i class=\"fa-solid fa-code\"></i> ${this._translateService.instant(\r\n          'Define Team'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // check if selected rows > 1\r\n          if (selectedRows.length > 1) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'Please select only one match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n          this.editor('edit', 'updateRankMatch');\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: this._translateService.instant('Columns'),\r\n        extend: 'colvis'\r\n      }\r\n    ];\r\n\r\n    // if stage type is league\r\n    if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {\r\n      // remove first button\r\n      btns.splice(0, 1);\r\n    }\r\n\r\n    if (\r\n      this.stage.type != AppConfig.TOURNAMENT_TYPES.knockout\r\n    ) {\r\n      btns.splice(-2, 1);\r\n    }\r\n\r\n    if (\r\n      this.tournament.type_knockout != AppConfig.KNOCKOUT_TYPES.type4 &&\r\n      this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout\r\n    ) {\r\n      const manageMatchesGroup = btns.find(btn => btn.text === \"Manage Matches\");\r\n      if (manageMatchesGroup && Array.isArray(manageMatchesGroup.buttons)) {\r\n        manageMatchesGroup.buttons = manageMatchesGroup.buttons.filter(\r\n          btn => !(btn.attr && btn.attr.id === \"auto-generate-btn\")\r\n        );\r\n      }\r\n      // btns.splice(0, 1);\r\n    }\r\n\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: window.innerWidth > 768 ? 'os' : 'multi',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      rowGroup: { dataSrc: 'group_round' },\r\n      order: [[2, 'asc']],\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        // add season id\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/stage-matches/all-in-stage/${this.stage.id}`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            // find fields has key location_id and set options\r\n            this.fields.forEach((field) => {\r\n              if (field.key === 'location_id') {\r\n                field.props.options = resp.options.location;\r\n              }\r\n              if (field.key === 'home_team_id') {\r\n                field.props.options = resp.options.teams;\r\n              }\r\n              if (field.key === 'away_team_id') {\r\n                field.props.options = resp.options.teams;\r\n              }\r\n            });\r\n\r\n            this.rank_fields.forEach((field) => {\r\n              if (field.key === 'home_label_id') {\r\n                field.props.options = resp.options.rank_label;\r\n              }\r\n              if (field.key === 'away_label_id') {\r\n                field.props.options = resp.options.rank_label;\r\n              }\r\n            });\r\n\r\n            this.filterFriendlyMatches(this.friendlyMatchesActive);\r\n\r\n            this.hasMatch = resp.data.length > 0;\r\n\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      initComplete: () => {\r\n        this.isComplete = true;\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: -7 },\r\n        { responsivePriority: 1, targets: -6 },\r\n        { responsivePriority: 1, targets: -5 },\r\n        { responsivePriority: 1, targets: -4 },\r\n        { responsivePriority: 1, targets: -3 },\r\n        { responsivePriority: 1, targets: -2 }\r\n      ],\r\n      columns: [\r\n        { data: 'order', visible: false },\r\n        { data: 'match_type', visible: false },\r\n        { data: 'group_round', visible: false },\r\n        {\r\n          title: this._translateService.instant('No'),\r\n          data: 'match_number',\r\n          visible: this.stage.type !== AppConfig.TOURNAMENT_TYPES.league,\r\n          render: function (data, type, row, meta) {\r\n            return data ? `${data}` : `${meta.row + 1}`;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Date'),\r\n          data: 'date',\r\n          orderable: false,\r\n          className: 'text-center p-1',\r\n          render: function (data, type, row) {\r\n            let content = '';\r\n            if (!data || data === 'TBD') {\r\n              content = 'TBD';\r\n            } else {\r\n              // format to HH:mm from ISO 8601\r\n              content = moment(data).format('YYYY-MM-DD');\r\n            }\r\n            return `<p style=\"margin:0; min-width: max-content\">${content}</p>`;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Start time'),\r\n          data: 'start_time_short',\r\n          orderable: false,\r\n          className: 'text-center p-1',\r\n          render: function (data, type, row) {\r\n            if (\r\n              !data ||\r\n              row.start_time_short == 'TBD' ||\r\n              !row.start_time_short\r\n            ) {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('End time'),\r\n          data: 'end_time_short',\r\n          orderable: false,\r\n          className: 'text-center p-1',\r\n          render: function (data, type, row) {\r\n            if (!data || row.end_time_short == 'TBD' || !row.end_time_short) {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Location'),\r\n          data: 'location_name'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home team'),\r\n          data: 'home_team_name',\r\n          className: 'text-center p-1',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            if (data === 'TBD') {\r\n              if (row.home_text) {\r\n                return row.home_text;\r\n              } else if (row.home_label && row.home_label.label) {\r\n                return row.home_label.label;\r\n              }\r\n            }\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center p-0 font-weight-bolder',\r\n          render: function (data, type, row) {\r\n            return `VS`;\r\n          },\r\n          orderable: false\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away team'),\r\n          data: 'away_team_name',\r\n          className: 'text-center p-1',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            if (data === 'TBD') {\r\n              if (row.away_text) {\r\n                return row.away_text;\r\n              } else if (row.away_label && row.away_label.label) {\r\n                return row.away_label.label;\r\n              }\r\n            }\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home score'),\r\n          data: 'home_score',\r\n          className: 'text-center p-1',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            if (row.home_penalty != null) {\r\n              return `${data}<br> ( ${row.home_penalty} )`;\r\n            }\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center p-0',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            return ` - `;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away score'),\r\n          data: 'away_score',\r\n          className: 'text-center p-1',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            if (row.away_penalty != null) {\r\n              return `${data}<br> ( ${row.away_penalty} )`;\r\n            }\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Referees'),\r\n          visible: false,\r\n          data: 'referees',\r\n          render: (data) => {\r\n            return `<div class=\"d-flex flex-column gap-1\">\r\n              ${data.map((item) => {\r\n              const refereeName = item.user ? `${item.user.first_name} ${item.user.last_name}` : item.referee_name;\r\n              return `<p style=\"min-width: max-content; margin: 0\">${refereeName}</p>`;\r\n            }).join('')}\r\n            </div>`;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Status'),\r\n          data: 'status',\r\n          render: (data: string, type, row) => {\r\n            const now = moment();\r\n            const currentTime = now.format('HH:mm');\r\n            const today = now.format('YYYY-MM-DD');\r\n            if (!data) {\r\n              // check if match time is over\r\n              if (row.end_time_short == 'TBD') {\r\n                return `<span class=\"badge badge-secondary text-capitalize\">TBD</span>`;\r\n              } else {\r\n                if (\r\n                  (row.date == today && row.end_time_short < currentTime) ||\r\n                  row.date < today\r\n                ) {\r\n                  return `<span class=\"badge badge-success text-capitalize\">${this._translateService.instant(\r\n                    'Finished'\r\n                  )}</span>`;\r\n                } else if (\r\n                  row.date == today &&\r\n                  row.start_time_short <= currentTime &&\r\n                  row.end_time_short >= currentTime\r\n                ) {\r\n                  return `<span class=\"badge badge-warning text-capitalize\">${this._translateService.instant(\r\n                    'In Progress'\r\n                  )}</span>`;\r\n                } else if (\r\n                  row.date > today ||\r\n                  (row.date == today && row.start_time_short > currentTime)\r\n                ) {\r\n                  return `<span class=\"badge badge-info text-capitalize\">${this._translateService.instant(\r\n                    'Upcoming'\r\n                  )}</span>`;\r\n                }\r\n              }\r\n            } else {\r\n              if (AppConfig.CANCEL_MATCH_TYPES.includes(data)) {\r\n                return `<span class=\"badge badge-danger text-capitalize\">${data}</span>`;\r\n              } else {\r\n                return `<span class=\"badge badge-secondary text-capitalize\">${data}</span>`;\r\n              }\r\n            }\r\n          },\r\n          className: 'text-center p-1',\r\n          orderable: false\r\n        }\r\n      ],\r\n      lengthMenu: [\r\n        [-1],\r\n        ['All']\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: btns\r\n      }\r\n    };\r\n\r\n    switch (this.stage.type) {\r\n      case AppConfig.TOURNAMENT_TYPES.knockout:\r\n        this.dtOptions.order = [[0, 'asc']];\r\n        // add rowGroup\r\n        this.dtOptions.rowGroup = { dataSrc: 'round_name' };\r\n        // insert round_name column at index 6\r\n        this.dtOptions.columns.splice(7, 0, {\r\n          title: this._translateService.instant('Name'),\r\n          data: 'round_name',\r\n          className: 'text-center',\r\n          render: function (data) {\r\n            return `<p style=\"margin:0; min-width: max-content\">${data}</p>`;\r\n          }\r\n        });\r\n\r\n        break;\r\n      case AppConfig.TOURNAMENT_TYPES.groups:\r\n        this.dtOptions.order = [[2, 'asc']];\r\n\r\n        // insert round_name column at index 5\r\n        this.dtOptions.columns.splice(7, 0, {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          className: 'text-center',\r\n          render: function (data, type, row) {\r\n            // split round_name by - and get the last item\r\n            if (!data) return '';\r\n            let round_name = data.split('-').pop();\r\n            return `<p style=\"margin:0; min-width: max-content\">${round_name}</p>`;\r\n          }\r\n        });\r\n        break;\r\n      case AppConfig.TOURNAMENT_TYPES.league:\r\n        // clear rowGroup\r\n        this.dtOptions.rowGroup = null;\r\n        this.dtOptions.columns.splice(7, 0, {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          className: 'text-center',\r\n          render: function (data) {\r\n            console.log('data', data);\r\n            return `<p style=\"margin:0; min-width: max-content\">${data ?? ''}</p>`;\r\n          }\r\n        });\r\n        // order by round_level\r\n        this.dtOptions.order = [[5, 'asc']];\r\n        break;\r\n\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  addOrRemoveRoundLevel(is_friendly: boolean) {\r\n    const round_level = {\r\n      key: 'round_level',\r\n      type: 'select',\r\n      props: {\r\n        label: this._translateService.instant('Round level'),\r\n        placeholder: this._translateService.instant('Select round level'),\r\n        // required: true,\r\n        options: this.roundLevelOpts\r\n      }\r\n    };\r\n\r\n    if (is_friendly) {\r\n      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {\r\n        // remove round_level from editMatch\r\n        const round_level_index = (this.editMatch as any).findIndex(\r\n          (item) => item.key === 'round_level'\r\n        );\r\n        // check if round_level exist\r\n        if (round_level_index > -1) {\r\n          (this.editMatch as any).splice(round_level_index, 1);\r\n        }\r\n      }\r\n    } else {\r\n      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {\r\n        const round_level_index = (this.editMatch as any).findIndex(\r\n          (item) => item.key === 'round_level'\r\n        );\r\n        // check if round_level not exist\r\n        if (round_level_index < 0) {\r\n          (this.editMatch as any).splice(2, 0, {\r\n            ...round_level\r\n          });\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  filterFriendlyMatches(active) {\r\n    this.friendlyMatchesActive = active;\r\n    this.addOrRemoveRoundLevel(active);\r\n    // deselect all rows\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api | any) => {\r\n      // round level backup\r\n      dtInstance.rows().deselect();\r\n      if (!active) {\r\n        // show column round name\r\n        dtInstance.column(5).visible(true);\r\n        dtInstance.column(1).search(1).draw();\r\n        // disable button auto generate\r\n        let btns = dtInstance.button('#auto-generate-btn');\r\n        console.log(btns);\r\n        if (btns) {\r\n          btns.enable();\r\n        }\r\n      } else {\r\n        // hide column round name\r\n        dtInstance.column(5).visible(true);\r\n        dtInstance.column(1).search(2).draw();\r\n        // enable button auto generate\r\n        let btns = dtInstance.button('#auto-generate-btn');\r\n        console.log(btns);\r\n        if (btns) {\r\n          btns.disable();\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  checkRowIsCancelled(row) {\r\n    let hasCancel = false;\r\n    // if row has status in AppConfig.CANCEL_MATCH_TYPES\r\n    if (AppConfig.CANCEL_MATCH_TYPES.includes(row.status)) {\r\n      hasCancel = true;\r\n    }\r\n    return hasCancel;\r\n  }\r\n\r\n  private _generateMatches(confirmOverride: boolean = false) {\r\n     const formData = new FormData();\r\n    formData.append('stage_id', this.stage.id.toString());\r\n    formData.append('confirm_override', confirmOverride ? '1' : '0');\r\n    this._stageService.autoGenerateMatches(formData).subscribe(\r\n      (resp: any) => {\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n          this.onDataChange.emit(resp);\r\n        });\r\n      },\r\n      (error) => {\r\n        if (error.warning) {\r\n          Swal.fire({\r\n            title: this._translateService.instant('Cannot Auto Generate'),\r\n            html: error.warning.replace(/\\n/g, '<br>'),\r\n            icon: 'warning',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary'\r\n            }\r\n          });\r\n          return;\r\n        } else {\r\n          Swal.fire({\r\n            title: this._translateService.instant('Error'),\r\n            text: error.message,\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary'\r\n            }\r\n          });\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  autoGenerateMatches() {\r\n    if (this.hasMatch) {\r\n      this._loadingService.show();\r\n\r\n      Swal.fire({\r\n        title: `${this._translateService.instant('Are you sure?')}`,\r\n        html: `\r\n            <p>${this._translateService.instant(\r\n              'All current matches will be deleted unless they contain information'\r\n            )}.</p>\r\n            <div class=\"swal2-checkbox-container\" style=\"display:flex;justify-content:center;gap: 10px;\">\r\n                <input type=\"checkbox\" id=\"confirmCheckbox\" >\r\n                <label for=\"confirmCheckbox\" class=\"swal2-label\">${this._translateService.instant(\r\n                  'I confirm that I want to generate all current matches'\r\n                )}</label>\r\n            </div>\r\n        `,\r\n        icon: 'warning',\r\n        showCancelButton: true,\r\n        confirmButtonText: this._translateService.instant('Yes'),\r\n        cancelButtonText: this._translateService.instant('No')\r\n      }).then((result) => {\r\n        if (result.isConfirmed) {\r\n          const checkbox = document.getElementById('confirmCheckbox') as HTMLInputElement;\r\n          const isChecked = checkbox?.checked ?? false;\r\n          Swal.fire({\r\n            title: this._translateService.instant('Match number will be reset'),\r\n            html: `<p>${this._translateService.instant(\r\n              'All match numbers in the tournament will be reset and regenerated. Are you sure you want to continue?'\r\n            )}</p>`,\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            confirmButtonText: this._translateService.instant('Yes, continue'),\r\n            cancelButtonText: this._translateService.instant('Cancel')\r\n          }).then((subResult) => {\r\n            if (subResult.isConfirmed) {\r\n              this._generateMatches(isChecked);\r\n            } else {\r\n              this._loadingService.dismiss();\r\n            }\r\n          });\r\n        } else {\r\n          this._loadingService.dismiss();\r\n        }\r\n      });\r\n    } else {\r\n      if (this.isComplete) {\r\n        this._generateMatches(false);\r\n      }\r\n    }\r\n  }\r\n\r\n  resetScore(stage_match_id) {\r\n    console.log(`reset score for ${stage_match_id}`);\r\n    this._loadingService.show();\r\n    this._stageService.resetScore(stage_match_id).subscribe(\r\n      (resp: any) => {\r\n        console.log(resp);\r\n\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n          this.onDataChange.emit(resp);\r\n        });\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: this._translateService.instant('Error'),\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK'),\r\n          customClass: {\r\n            confirmButton: 'btn btn-primary'\r\n          }\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  swapTeams(stage_match: StageMatch) {\r\n    this._loadingService.show();\r\n    this._stageService.swapTeams(stage_match).subscribe((resp: any) => {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n        this.onDataChange.emit(resp);\r\n      });\r\n    });\r\n  }\r\n\r\n  onSuccess($event) {\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      dtInstance.ajax.reload();\r\n    });\r\n    if (this.stage.type === AppConfig.TOURNAMENT_TYPES.groups) {\r\n      this._stageService.checkMatchScore(this.stage.id).subscribe(\r\n        (resp: any) => {\r\n          console.log('checkMatchScore', resp);\r\n        },\r\n        (error) => {\r\n          console.log('checkMatchScore', error);\r\n        }\r\n      );\r\n    }\r\n\r\n    if (this.is_score_updated) {\r\n      this.onUpdateScore.emit($event);\r\n    }\r\n    this.onDataChange.emit($event);\r\n  }\r\n\r\n  checkValidateUpdateScore(row) {\r\n    return !(!row.date || !row.start_time || !row.end_time || !row.location);\r\n  }\r\n\r\n  editor(action, fields, row?) {\r\n    this.params.use_data = false;\r\n    this.paramsToPost = {\r\n      stage_type: this.stage.type,\r\n      type_action: fields\r\n    };\r\n    // check has row with status is pass in selected rows\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      let selectedRows = dtInstance.rows({ selected: true }).data();\r\n      console.log('selectedRows', selectedRows);\r\n\r\n      switch (fields) {\r\n        case 'editMatch':\r\n          this.params.title.edit = this._translateService.instant('Edit match');\r\n\r\n          this.fields = [...this.editMatch, {\r\n            key: 'match_status',\r\n            type: 'input',\r\n            props: {\r\n              type: 'hidden'\r\n            },\r\n            defaultValue: selectedRows[0]?.status\r\n          }].map((item) => ({ ...item }));\r\n          console.log('fields', this.fields);\r\n          this.is_score_updated = false;\r\n\r\n          this._http\r\n            .post<any>(`${environment.apiUrl}/stage-matches/options/${this.stage.id}`, {})\r\n            .subscribe((resp: any) => {\r\n              this.fields.forEach((item) => {\r\n                if (item.key === 'home_team_id' || item.key === 'away_team_id') {\r\n                  item.props.options = resp.options.teams;\r\n                }\r\n                if (item.key === 'location_id') {\r\n                  item.props.options = resp.options.location;\r\n                }\r\n              });\r\n\r\n              this.fields.forEach((item) => {\r\n                if (item.key === 'referee') {\r\n                  item.value = selectedRows[0]?.referees?.map((r) => r.id) || [];\r\n                }\r\n              });\r\n\r\n              this.fields_subject.next(this.fields);\r\n              this.params.action = action;\r\n              this.params.row = row ? row : null;\r\n              this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n            });\r\n\r\n          return;\r\n        case 'updateScore':\r\n          this.params.title.edit =\r\n            this._translateService.instant('Update Score');\r\n          this.fields = this.updateScore;\r\n          this.is_score_updated = true;\r\n          break;\r\n        case 'cancelMatch':\r\n          this.params.title.edit =\r\n            this._translateService.instant('Cancel Match');\r\n          this.fields = this.cancelMatch;\r\n          this.is_score_updated = false;\r\n          break;\r\n        case 'addReplaceMatch':\r\n          console.log(row);\r\n\r\n          let paramsPost = {\r\n            'data[0][round_name]': row.round_name,\r\n            'data[0][round_level]': row.round_level,\r\n            'data[0][match_id]': row.id,\r\n            'data[0][match_number]': row.match_number || 0\r\n          };\r\n          // merge paramsToPost and paramsPost\r\n          this.paramsToPost = { ...this.paramsToPost, ...paramsPost };\r\n          this.params.title.edit =\r\n            this._translateService.instant('Add Replace Match');\r\n          this.fields = this.editMatch;\r\n          this.params.use_data = true;\r\n          break;\r\n        case 'updateRankMatch':\r\n          this.params.title.edit = this._translateService.instant('Edit Rank');\r\n          this.fields = this.updateRankMatch;\r\n          this.is_score_updated = false;\r\n          break;\r\n        default:\r\n          this.params.title.edit = this._translateService.instant('Add Match');\r\n          this.fields = this.editMatch.map((item) => {\r\n            if (item.key === 'referee') {\r\n              item['defaultValue'] = [];\r\n              item['value'] = [];\r\n            }\r\n            return item;\r\n          });\r\n          this.is_score_updated = false;\r\n          break;\r\n      }\r\n      this.fields_subject.next(this.fields);\r\n      this.params.action = action;\r\n      this.params.row = row ? row : null;\r\n\r\n      this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n    });\r\n  }\r\n\r\n  onCloseSidebar(event) {\r\n    this.editMatch.forEach((e) => {\r\n      if (e.key === 'referee') {\r\n        e['defaultValue'] = [];\r\n        e['value'] = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\r\n      this.dtTrigger.next(this.dtOptions);\r\n    }, 500);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n  }\r\n\r\n  @ViewChild('modalAssignReferee')\r\n  modalAssignReferee!: TemplateRef<any>;\r\n\r\n  public isMultipleAssign = true;\r\n  public selectedIds = [];\r\n  public assignRefereeForm = new FormGroup({});\r\n  public assignRefereeModel = {};\r\n  public assignRefereeFields = [\r\n    {\r\n      key: 'list_referees',\r\n      type: 'ng-select',\r\n      props: {\r\n        multiple: true,\r\n        hideOnMultiple: true,\r\n        defaultValue: [],\r\n        label: this._translateService.instant('Match Referee'),\r\n        placeholder: this._translateService.instant('Select match referees'),\r\n        options: []\r\n      },\r\n      hooks: {}\r\n    }\r\n  ];\r\n\r\n\r\n  openModal() {\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api | any) => {\r\n      let selectedRows = dtInstance.rows({ selected: true }).data();\r\n\r\n      this.selectedIds = [];\r\n      selectedRows.map((row) => {\r\n        return this.selectedIds.push(row.id);\r\n      });\r\n\r\n      if (selectedRows.length > 1) {\r\n        this.isMultipleAssign = true;\r\n        this.assignRefereeModel = {\r\n          ...this.assignRefereeModel,\r\n          list_referees: []\r\n        };\r\n      } else {\r\n        this.isMultipleAssign = false;\r\n        this.assignRefereeModel = {\r\n          ...this.assignRefereeModel,\r\n          list_referees: selectedRows[0]?.referees?.map((r) => r.id) || []\r\n        };\r\n      }\r\n\r\n      this._modalService.open(this.modalAssignReferee, {\r\n        centered: true,\r\n        size: 'lg',\r\n        beforeDismiss: () => {\r\n          this.assignRefereeModel = {};\r\n          this.assignRefereeFields.forEach((item) => {\r\n            if (item.key === 'list_referees') {\r\n              item['defaultValue'] = [];\r\n            }\r\n          });\r\n          return true;\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  getListReferee() {\r\n    this._stageService.getListRefereesByStageId(this.stage.id).subscribe((response) => {\r\n      this.listReferees = response['data'];\r\n      this.assignRefereeFields.forEach((item) => {\r\n        if (item.key === 'list_referees') {\r\n          item.props.options = response['data'].map((referee) => {\r\n            console.log(referee);\r\n            return {\r\n              label: referee.user\r\n                ? `${referee.user.first_name} ${referee.user.last_name}`\r\n                : referee.referee_name,\r\n              value: referee.id\r\n            };\r\n          });\r\n        }\r\n      });\r\n    }\r\n    );\r\n  }\r\n\r\n  onSubmitAssignReferee(event) {\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      dtInstance.ajax.reload();\r\n    });\r\n  }\r\n\r\n  autoschedule() {\r\n    this._stageService.autoSchedule(this.stage.id).subscribe((response) => {\r\n      console.log(response);\r\n    })\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <div class=\"card\">\r\n      <div class=\"card-header px-0 pt-0\">\r\n        <ul\r\n          ngbNav\r\n          #nav=\"ngbNav\"\r\n          class=\"nav-tabs\"\r\n          *ngIf=\"stage.type === AppConfig.TOURNAMENT_TYPES.league\"\r\n        >\r\n          <li ngbNavItem>\r\n            <a ngbNavLink (click)=\"filterFriendlyMatches(false)\">{{\r\n                'All Matches' | translate\r\n              }}</a>\r\n          </li>\r\n          <li ngbNavItem>\r\n            <a ngbNavLink (click)=\"filterFriendlyMatches(true)\">{{\r\n                'Friendly Matches' | translate\r\n              }}</a>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n\r\n      <table\r\n        datatable\r\n        [dtOptions]=\"dtOptions\"\r\n        [dtTrigger]=\"dtTrigger\"\r\n        class=\"table row-border hover\"\r\n      ></table>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<core-sidebar\r\n  class=\"modal modal-slide-in sidebar-todo-modal fade\"\r\n  [name]=\"table_name\"\r\n  overlayClass=\"modal-backdrop\"\r\n>\r\n  <app-editor-sidebar\r\n    [table]=\"dtElement\"\r\n    [fields]=\"fields\"\r\n    [params]=\"params\"\r\n    (onSuccess)=\"onSuccess($event)\"\r\n    (onClose)=\"onCloseSidebar($event)\"\r\n    [paramsToPost]=\"paramsToPost\"\r\n    [fields_subject]=\"fields_subject\"\r\n  >\r\n  </app-editor-sidebar>\r\n</core-sidebar>\r\n\r\n<ng-template #modalAssignReferee let-modal>\r\n  <app-modal-assign-referees\r\n    [isMultipleAssign]=\"isMultipleAssign\"\r\n    [selectedIds]=\"selectedIds\"\r\n    [listReferees]=\"listReferees\"\r\n    [assignRefereeForm]=\"assignRefereeForm\"\r\n    [assignRefereeFields]=\"assignRefereeFields\"\r\n    [assignRefereeModel]=\"assignRefereeModel\"\r\n    (onSubmit)=\"onSubmitAssignReferee($event)\"\r\n  />\r\n</ng-template>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}