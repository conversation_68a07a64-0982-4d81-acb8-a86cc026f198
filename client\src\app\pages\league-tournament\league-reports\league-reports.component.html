<div class="content-wrapper container-xxl p-0">
	<div class="content-body">
		<!-- content-header component -->
		<app-content-header [contentHeader]="contentHeader"></app-content-header>

		

		<div class="row">
			<div class="col-12">
					<div class="bg-white">
						<nav>	
							<div class="nav nav-tabs" id="nav-tab" role="tablist">
								<button 

									*ngFor="let type of viewTypes; let i = index" 
									class="nav-link" 
									[class.active]="viewType === type.value"
									id="nav-{{type.value}}-tab" 
									data-bs-toggle="tab" 
									type="button" 
									role="tab"
									(click)="onSelectViewType(type.value)"
									aria-controls="nav-{{type.value}}" 
									[attr.aria-selected]="viewType === type.value">
									<i [ngClass]="type.icon_name"></i>
									{{ type.label | translate }}
								</button>
							</div>
						</nav>
					</div>
				
				<div class="card">
					<!-- Content for League Table tab -->
					<div *ngIf="viewType === 'league_table'" class="p-1">
						<div class="row mb-1">
							<div class="col-12">
								<label for="season" class="form-label">{{'Season'|translate}}</label>
								<ng-select 
									[searchable]="true" 
									[clearable]="false" 
									placeholder="{{'Select Season'|translate}}"
									[(ngModel)]="seasonId" 
									(change)="onSelectSeason($event)">
									<ng-option *ngFor="let season of seasons" [value]="season.id">
										{{ season.name | translate }}
									</ng-option>
								</ng-select>
							</div>
						</div>
						<div class="row mb-1">
							<div class="col-md-3">
								<label for="tournament" class="form-label">{{'Tournament'|translate}}</label>
								<ng-select 
									[searchable]="true" 
									[clearable]="true" 
									placeholder="{{'Select Tournament'|translate}}"
									[(ngModel)]="tournamentId" 
									(change)="onSelectTournament($event)">
									<ng-option *ngFor="let tournament of tournaments" [value]="tournament.id">
										{{ tournament.name | translate }}
									</ng-option>
								</ng-select>
							</div>
							<div class="col-md-3">
								<label for="club" class="form-label">{{'Club'|translate}}</label>
								<ng-select 
									[searchable]="true" 
									[clearable]="true" 
									placeholder="{{'Select Club'|translate}}" 
									[(ngModel)]="clubId"
									(change)="onSelectClub($event)">
									<ng-option *ngFor="let club of clubs" [value]="club.id">
										{{ club.code | translate }}
									</ng-option>
								</ng-select>
							</div>
							<div class="col-md-3">
								<label for="dateRange" class="form-label">{{'Date Range'|translate}}</label>
								<div class="input-group">
									<input
										name="daterange"
										class="form-control"
										placeholder="{{'Select Date Range'|translate}}"
										ngbDatepicker
										readonly
										#dateRangePicker="ngbDatepicker"
										[value]="formatter.format(fromDate) + (toDate ? ' - ' + formatter.format(toDate) : '')"
										(click)="openDatePicker()"
										[dayTemplate]="dayTemplate"
										[footerTemplate]="footerTemplate"
										(dateSelect)="onDateSelection($event)"
										[firstDayOfWeek]="1"
										[displayMonths]="2"
										outsideDays="hidden"
										[autoClose]="false"
									/>
									<div class="input-group-append">
										<button 
											class="btn btn-outline-secondary" 
											(click)="openDatePicker()" 
											type="button">
											<i class="feather icon-calendar"></i>
										</button>
									</div>
								</div>
								
								<ng-template #dayTemplate let-date="date" let-focused="focused">
									<span
										class="custom-day"
										[class.focused]="focused"
										[class.range]="isRange(date)"
										[class.faded]="isHovered(date) || isInside(date)"
										(mouseenter)="hoveredDate = date"
										(mouseleave)="hoveredDate = null"
									>
										{{ date.day }}
									</span>
								</ng-template>
								
								<ng-template #footerTemplate>
									<hr class="my-0">
									<div class="d-flex justify-content-between p-2">
										<button
											type="button"
											class="btn btn-outline-secondary btn-sm"
											(click)="clearDateRange(); dateRangePicker.close()"
										>
											Clear
										</button>
										<button
											type="button"
											class="btn btn-primary btn-sm"
											(click)="dateRangePicker.close()"
										>
											Close
										</button>
									</div>
								</ng-template>
							</div>
							<div class="col-md-3">
								<label for="matchStatus" class="form-label">{{'Match Status'|translate}}</label>
								<ng-select 
									[searchable]="false" 
									[clearable]="false" 
									placeholder="{{'Select Status'|translate}}"
									[(ngModel)]="matchStatus" 
									(change)="onSelectMatchStatus($event)">
									<ng-option *ngFor="let status of matchStatusOptions" [value]="status.value">
										{{ status.label | translate }}
									</ng-option>
								</ng-select>
							</div>
						</div>
						<div class="row mb-1">
							<div class="col-12">
								<table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="table border row-border hover">
								</table>
							</div>
						</div>
					</div>

					<div *ngIf="viewType === 'schedule_matches'" class="p-2">
						<div class="text-center">
							<h4>{{'Schedule Matches Content'|translate}}</h4>
							<p>{{'Coming soon...'|translate}}</p>
						</div>
					</div>

					<div *ngIf="viewType === 'bracket'" class="p-2">
						<div class="text-center">
							<h4>{{'Bracket Content'|translate}}</h4>
							<p>{{'Coming soon...'|translate}}</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<core-sidebar class="modal modal-slide-in sidebar-todo-modal fade" [name]="table_name" overlayClass="modal-backdrop">
	<app-editor-sidebar [table]="dtElement" [fields]="fields" [params]="params">
	</app-editor-sidebar>
</core-sidebar>

<ng-template #rowActionBtn let-data="adtData" let-emitter="captureEvents">
	<app-btn-dropdown-action [actions]="rowActions" [data]="data" (emitter)="emitter($event)"
		btnStyle="font-size:15px;color:black!important"></app-btn-dropdown-action>
</ng-template>