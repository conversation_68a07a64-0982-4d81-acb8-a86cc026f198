import { HttpClient } from '@angular/common/http';
import {
  Component,
  OnInit,
  Renderer2,
  ViewChild,
  AfterViewInit,
  ViewEncapsulation,
  TemplateRef,
  HostListener,
  OnDestroy
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { DataTableDirective } from 'angular-datatables';
import { CommonsService } from 'app/services/commons.service';
import { LoadingService } from 'app/services/loading.service';
import { TeamService } from 'app/services/team.service';
import { ExportService } from 'app/services/export.service';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'environments/environment';
import { RegistrationService } from 'app/services/registration.service';
import { TournamentService } from 'app/services/tournament.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import Swal from 'sweetalert2';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { EditorSidebarParams } from 'app/interfaces/editor-sidebar';
import { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';
import { AppConfig } from 'app/app-config';
import { EZBtnActions } from 'app/components/btn-dropdown-action/btn-dropdown-action.component';
import { ClubService } from 'app/services/club.service';
import moment from 'moment';
import { Title } from '@angular/platform-browser';
import { NgbCalendar, NgbDate, NgbDateStruct, NgbDateParserFormatter, NgbDatepicker } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-league-reports',
  templateUrl: './league-reports.component.html',
  styleUrls: ['./league-reports.component.scss'],
})
export class LeagueReportsComponent
  implements AfterViewInit, OnInit, OnDestroy
{
  @ViewChild('rowActionBtn') rowActionBtn: TemplateRef<any>;
  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;
  @ViewChild(DataTableDirective, { static: false })
  dtElement: any = DataTableDirective;
  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
  dtOptions: any = {};
  private unlistener: () => void;
  public currentTeam: any = {};
  public seasonId: any;
  public clubId: any = null;
  public tournamentId: any = null;
  public dateRange: any = { start_date: null, end_date: null };
  public dateRangeValue: string = '';
  hoveredDate: NgbDate | null = null;
  fromDate: NgbDate | null = null;
  toDate: NgbDate | null = null;
  isSelecting: boolean = false; // Track if we're in the middle of selecting range
  private clickOutsideTimeout: any;
  public matchStatus: string = 'all';
  public isTableLoading: boolean = false;
  public modalRef: any;
  public contentHeader: object;
  public seasons;
  public clubs;
  public tournaments;
  public table_name = 'team-table';

  viewTypes = [
    { label: 'Table View', value: 'league_table', icon_name: 'fa-light fa-table-list' },
    { label: 'Schedule View', value: 'schedule_matches', icon_name: 'fa-light fa-calendar' },
    // { label: 'Bracket View', value: 'bracket', icon_name: 'grid' },
  ];

  viewType: string = 'league_table';

  matchStatusOptions = [
    { label: 'All Status', value: 'all' },
    { label: 'Upcoming', value: 'upcoming' },
    { label: 'Active', value: 'active' },
    { label: 'Completed', value: 'completed' },
    { label: 'Cancelled', value: 'cancelled' },
  ];

  public params: EditorSidebarParams = {
    editor_id: this.table_name,
    title: {
      create: this._translateService.instant('Create New Tournament'),
      edit: 'Edit team',
      remove: 'Delete team'
    },
    url: `${environment.apiUrl}/teams/editor`,
    method: 'POST',
    action: 'create'
  };

  public fields: any[] = [
    {
      key: 'home_score',
      type: 'number',
      props: {
        label: this._translateService.instant('Home team score'),
        placeholder: this._translateService.instant('Enter score of team'),
        required: true,
      }
    },
    {
      key: 'away_score',
      type: 'number',
      props: {
        label: this._translateService.instant('Away team score'),
        placeholder: this._translateService.instant('Enter score of team'),
        required: true,
      }
    }
  ];

  // private variables
  private _unsubscribeAll: Subject<any>;

  constructor(
    private route: ActivatedRoute,
    public _router: Router,
    public _commonsService: CommonsService,
    public _http: HttpClient,
    public _trans: TranslateService,
    public renderer: Renderer2,
    public _teamService: TeamService,
    public _modalService: NgbModal,
    public _loadingService: LoadingService,
    public _toastService: ToastrService,
    public _registrationService: RegistrationService,
    public _clubService: ClubService,
    public _translateService: TranslateService,
    public _coreSidebarService: CoreSidebarService,
    public _titleService: Title,
    private _exportService: ExportService,
    public _tournamentService: TournamentService,
    private calendar: NgbCalendar,
    public formatter: NgbDateParserFormatter
  ) {
    this._titleService.setTitle('Matches Report');
  }

  _getCurrentSeason() {
    this._loadingService.show();
    this._registrationService.getAllSeasonActive().subscribe(
      (data) => {
        this.seasons = data;
        this.seasonId = this.seasons[0].id;
        this._getTournaments(); // Fetch tournaments after getting seasons
        if (this.dtElement.dtInstance) {
          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
          });
        }
        this.dtTrigger.next(this.dtOptions);
      },
      (error) => {
        Swal.fire({
          title: 'Error',
          text: error.message,
          icon: 'error',
          confirmButtonText: this._translateService.instant('OK')
        });
      },
      () => {
        this._loadingService.dismiss();
      }
    );
  }

  _getClubs() {
    this._clubService.getAllClubs().subscribe(
      (res) => {
        this.clubs = res.data;
        // this.clubId = this.clubs[0];
        if (this.dtElement.dtInstance) {
          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
          });
        }
      },
      (error) => {
        Swal.fire({
          title: 'Error',
          text: error.message,
          icon: 'error',
          confirmButtonText: this._translateService.instant('OK')
        });
      }
    );
  }

  onSelectSeason($event) {
    return new Promise((resolve, reject) => {
      this.seasonId = $event;
      this._getTournaments(); // Fetch tournaments when season changes
      // Reset tournament selection when season changes
      this.tournamentId = null;
      this.refreshDataTable();
      resolve(true);
    });
  }

  onSelectClub($event) {
    console.log(`onSelectClub: ${$event}`);
    this.clubId = $event;
    this.refreshDataTable();
  }

  onSelectTournament($event) {
    console.log(`onSelectTournament: ${$event}`);
    this.tournamentId = $event;
    this.refreshDataTable();
  }

  onSelectMatchStatus($event) {
    console.log(`onSelectMatchStatus: ${$event}`);
    this.matchStatus = $event;
    this.refreshDataTable();
  }

  onDateSelection(date: NgbDate) {

    if (!this.fromDate && !this.toDate) {
      console.log('Setting From Date');
      this.fromDate = date;
      this.isSelecting = true;
    } else if (
      this.fromDate &&
      !this.toDate &&
      date &&
      date.after(this.fromDate)
    ) {
      this.toDate = date;
      this.isSelecting = false;
      this.updateDateRange();
      setTimeout(() => {
        if (this.dateRangePicker && this.dateRangePicker.close) {
          this.dateRangePicker.close();
        }
      }, 0);
    } else {
      console.log('reset date form');
      this.toDate = null;
      this.fromDate = date;
      this.isSelecting = true;
    }
  }

  openDatePicker() {
    this.isSelecting = false; // Reset selection state when opening
    if (this.dateRangePicker) {
      this.dateRangePicker.open();
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    if (this.clickOutsideTimeout) {
      clearTimeout(this.clickOutsideTimeout);
    }

    this.clickOutsideTimeout = setTimeout(() => {
      this.handleClickOutside(event);
    }, 50);
  }

  private handleClickOutside(event: Event) {
    // Check if datepicker is open
    if (
      !this.dateRangePicker ||
      !this.dateRangePicker.isOpen ||
      !this.dateRangePicker.isOpen()
    ) {
      return;
    }

    if (this.isSelecting) {
      return;
    }

    const target = event.target as HTMLElement;

    if (!target) return;

    if (
      target.closest('.btn') ||
      target.classList.contains('feather') ||
      target.classList.contains('icon-calendar')
    ) {
      return;
    }

    if (
      target.tagName === 'INPUT' &&
      target.getAttribute('name') === 'daterange'
    ) {
      return;
    }

    const datepickerElement = document.querySelector('ngb-datepicker');
    if (datepickerElement && datepickerElement.contains(target)) {
      return;
    }

    this.dateRangePicker.close();
  }

  clearDateRange() {
    this.fromDate = null;
    this.toDate = null;
    this.isSelecting = false;
    this.updateDateRange();
  }

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    const parsed = this.formatter.parse(input);
    return parsed && this.calendar.isValid(NgbDate.from(parsed))
      ? NgbDate.from(parsed)
      : currentValue;
  }

  isHovered(date: NgbDate) {
    return (
      this.fromDate &&
      !this.toDate &&
      this.hoveredDate &&
      date.after(this.fromDate) &&
      date.before(this.hoveredDate)
    );
  }

  isInside(date: NgbDate) {
    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);
  }

  isRange(date: NgbDate) {
    return (
      date.equals(this.fromDate) ||
      (this.toDate && date.equals(this.toDate)) ||
      this.isInside(date) ||
      this.isHovered(date)
    );
  }

  onDateRangeChange() {
    console.log('onDateRangeChange:', this.dateRange);
    this.refreshDataTable();
  }

  updateDateRange() {
    if (this.fromDate && this.toDate) {
      const fromMoment = moment(
        `${this.fromDate.year}-${this.fromDate.month
          .toString()
          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`
      );
      const toMoment = moment(
        `${this.toDate.year}-${this.toDate.month
          .toString()
          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`
      );

      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');
      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');
      this.dateRangeValue = `${fromMoment.format(
        'MMM DD, YYYY'
      )} - ${toMoment.format('MMM DD, YYYY')}`;

      this.onDateRangeChange();
    } else if (this.fromDate) {
      const fromMoment = moment(
        `${this.fromDate.year}-${this.fromDate.month
          .toString()
          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`
      );
      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');
      this.dateRange.end_date = null;
      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');
    } else {
      this.dateRange.start_date = null;
      this.dateRange.end_date = null;
      this.dateRangeValue = '';
      this.onDateRangeChange();
    }
  }

  _getTournaments() {
    if (this.seasonId) {
      this._http
        .get<any>(
          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`
        )
        .subscribe(
          (data) => {
            this.tournaments = data.data;
          },
          (error) => {
            Swal.fire({
              title: 'Error',
              text: error.message,
              icon: 'error',
              confirmButtonText: this._translateService.instant('OK'),
            });
          }
        );
    }
  }

  ngOnInit(): void {
    this.contentHeader = {
      headerTitle: this._trans.instant('Matches Report'),
      actionButton: false,
      breadcrumb: {
        type: '',
        links: [
          {
            name: this._trans.instant('Home'),
            isLink: false
          },
          {
            name: this._trans.instant('Tournaments'),
            isLink: false
          },
          {
            name: this._trans.instant('Matches Report'),
            isLink: false
          }
        ]
      }
    };

    this._getCurrentSeason();
    this._getClubs();
    this.dtOptions = {
      dom: this._commonsService.dataTableDefaults.dom,
      select: 'single',
      rowId: 'id',
      processing: true,
      language: {
        ...this._commonsService.dataTableDefaults.lang,
        // processing: `<div class="d-flex justify-content-center align-items-center" style="height: 200px;">
        //               <div class="spinner-border text-primary" role="status">
        //                 <span class="sr-only">Loading...</span>
        //               </div>
                      
        //             </div>`,
        processing: ``,
      },
      ajax: (dataTablesParameters: any, callback) => {
        // Build query parameters for all filters
        let params = new URLSearchParams();

        if (this.clubId != undefined && this.clubId !== null) {
          params.append('club_id', this.clubId);
        }

        if (this.tournamentId != undefined && this.tournamentId !== null) {
          params.append('tournament_id', this.tournamentId);
        }

        if (this.dateRange.start_date) {
          params.append('start_date', this.dateRange.start_date);
        }

        if (this.dateRange.end_date) {
          params.append('end_date', this.dateRange.end_date);
        }

        if (this.matchStatus && this.matchStatus !== 'all') {
          params.append('match_status', this.matchStatus);
        }

        const queryString = params.toString();
        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${
          queryString ? '?' + queryString : ''
        }`;

        this.isTableLoading = true;
        this._http.post<any>(url, dataTablesParameters).subscribe(
          (resp: any) => {
            this.isTableLoading = false;
            callback({
              recordsTotal: resp.recordsTotal,
              recordsFiltered: resp.recordsFiltered,
              data: resp.data,
            });
          },
          (error) => {
            this.isTableLoading = false;
            console.error('Error loading table data:', error);
            callback({
              recordsTotal: 0,
              recordsFiltered: 0,
              data: [],
            });
          }
        );
      },
      responsive: false,
      scrollX: true,
      columnDefs: [{ responsivePriority: 1, targets: -1 }],
      columns: [
        {
          title: this._translateService.instant('No.'),
          data: null,
          className: 'font-weight-bolder',
          type: 'any-number',
          render: function (data, type, row, metadata) {

            return metadata.row + 1
          }
        },
        {
          title: this._translateService.instant('Tournament'),
          data: 'name',
          className: 'font-weight-bolder',
          type: 'any-number',
          render: {
            display: (data, type, row) => {
              return data ?? 'TBD';
            },
            filter: (data, type, row) => {
              return data;
            }
          }
        },
        {
          title: this._translateService.instant('Round'),
          data: 'round_name',
          render: function (data) {
            const displayData = data || 'TBD';
            return `<div class="text-center" style="width:max-content;">${displayData}</div>`;
          },
        },
        {
          title: this._translateService.instant('Date'),
          data: 'start_time',
          className: 'text-center',
          render: function (data, type, row) {
            const displayDate =
              !data || data === 'TBD'
                ? 'TBD'
                : moment(data).format('YYYY-MM-DD');
            return `<div class="text-center" style="min-width: max-content;">${displayDate}</div>`;
          },
        },
        {
          title: this._translateService.instant('Start time'),
          data: 'start_time',
          className: 'text-center',

          render: function(data, type, row) {
            if (!data || data == 'TBD') {
              return 'TBD';
            }
            // format to HH:mm from ISO 8601
            return moment(data).format('HH:mm');
          }
        },
        {
          title: this._translateService.instant('End time'),
          data: 'end_time',
          className: 'text-center',
          render: function(data, type, row) {
            if (!data || data == 'TBD') {
              return 'TBD';
            }
            // format to HH:mm from ISO 8601
            return moment(data).format('HH:mm');
          }
        },
        {
          title: this._translateService.instant('Location'),
          data: 'location',
          render: function(data, type, row) {
            if (!data || data == 'TBD') {
              return 'TBD';
            }
            // format to HH:mm from ISO 8601
            return data;
          }
        },
        {
          title: this._translateService.instant('Home Team'),
          data: 'home_team_name',
          className: 'text-center'
        },
        {
          title: 'VS',
          data: null,
          className: 'text-center',
          render: function(data, type, row) {
            return 'vs';
          }
        },
        {
          title: this._translateService.instant('Away Team'),
          data: 'away_team_name',
          className: 'text-center'
        },
        {
          title: this._translateService.instant('Home score'),
          data: 'home_score',
          className: 'text-center'
        },
        {
          data: null,
          className: 'text-center',
          render: function(data, type, row) {
            return '-';
          }
        },
        {
          title: this._translateService.instant('Away score'),
          data: 'away_score',
          className: 'text-center'
        }
      ],
      buttons: {
        dom: this._commonsService.dataTableDefaults.buttons.dom,
        buttons: [
          {
            text: `<i class="fa-solid fa-file-csv mr-1"></i> ${this._translateService.instant(
              'Export CSV'
            )}`,
            extend: 'csv',
            action: async (e: any, dt: any, button: any, config: any) => {
              const data = dt.buttons.exportData();
              
              // Generate filename with current filter information
              let filename = 'Matches_Report';
              
              if (this.seasonId) {
                const season = this.seasons?.find(s => s.id == this.seasonId);
                if (season) {
                  filename += `_${season.name.replace(/\s+/g, '_')}`;
                }
              }
              
              if (this.tournamentId) {
                const tournament = this.tournaments?.find(t => t.id == this.tournamentId);
                if (tournament) {
                  filename += `_${tournament.name.replace(/\s+/g, '_')}`;
                }
              }
              
              if (this.clubId) {
                const club = this.clubs?.find(c => c.id == this.clubId);
                if (club) {
                  filename += `_${club.code.replace(/\s+/g, '_')}`;
                }
              }
              
              if (this.matchStatus && this.matchStatus !== 'all') {
                filename += `_${this.matchStatus}`;
              }
              
              if (this.dateRange.start_date && this.dateRange.end_date) {
                filename += `_${this.dateRange.start_date}_to_${this.dateRange.end_date}`;
              } else if (this.dateRange.start_date) {
                filename += `_from_${this.dateRange.start_date}`;
              }
              
              filename += '.csv';
              
              await this._exportService.exportCsv(data, filename);
            }
          }
        ]
      }
    };
  }

  setClubs(data) {
    this.clubs = data;
    // get field has key club_id
    const clubField = this.fields.find((field) => field.key === 'club_id');
    // set options for club field
    let current_clubs = [];
    data.forEach((club) => {
      let club_name = this._translateService.instant(club.name);
      current_clubs.push({
        label: club_name,
        value: club.id
      });
    });
    clubField.props.options = current_clubs;
  }

  onCaptureEvent(event: any) {
    // console.log(event);
  }

  editor(action, row?) {
    this.fields[0].defaultValue = this.clubId;
    switch (action) {
      case 'create':
        this.fields[2].props.disabled = false;
        break;
      case 'edit':
        this.fields[2].props.disabled = true;
        break;
      case 'remove':
        break;
      default:
        break;
    }
    this.params.action = action;
    this.params.row = row ? row : null;
    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();
  }

  ngAfterViewInit(): void {
    this.unlistener = this.renderer.listen('document', 'click', (event) => {
      if (event.target.hasAttribute('tournament_id')) {
        let tournament_id = event.target.getAttribute('tournament_id');
        let stage_id = event.target.getAttribute('stage_id');
        //  navigate to path ':tournament_id/stages/:stage_id'
        this._router.navigate([tournament_id, 'stages', stage_id], {
          relativeTo: this.route
        });
      }
    });
  }

  onSelectViewType(event: any) {
    this.viewType = event;
    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.ajax.reload();
      });
    }
  }

  private refreshDataTable() {
    if (this.dtElement.dtInstance) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.ajax.reload();
      });
    }
  }

  ngOnDestroy(): void {
    this.dtTrigger.unsubscribe();
    this.unlistener();
    if (this.clickOutsideTimeout) {
      clearTimeout(this.clickOutsideTimeout);
    }
  }
}
