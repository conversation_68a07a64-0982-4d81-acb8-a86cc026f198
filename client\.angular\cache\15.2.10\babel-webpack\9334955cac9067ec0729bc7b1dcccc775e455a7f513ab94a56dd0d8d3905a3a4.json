{"ast": null, "code": "import { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { AppConfig } from 'app/app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/team.service\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@ngx-translate/core\";\nfunction ModalAddNumberTeamComponent_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"This field is required.\"), \" \");\n  }\n}\nfunction ModalAddNumberTeamComponent_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Minimum is 2 teams.\"), \" \");\n  }\n}\nfunction ModalAddNumberTeamComponent_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Maximum is 128 teams.\"), \" \");\n  }\n}\nfunction ModalAddNumberTeamComponent_div_22_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Only numbers are allowed.\"), \" \");\n  }\n}\nfunction ModalAddNumberTeamComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, ModalAddNumberTeamComponent_div_22_div_1_Template, 3, 3, \"div\", 13);\n    i0.ɵɵtemplate(2, ModalAddNumberTeamComponent_div_22_div_2_Template, 3, 3, \"div\", 13);\n    i0.ɵɵtemplate(3, ModalAddNumberTeamComponent_div_22_div_3_Template, 3, 3, \"div\", 13);\n    i0.ɵɵtemplate(4, ModalAddNumberTeamComponent_div_22_div_4_Template, 3, 3, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.teamMatchForm.get(\"teamNumb_name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.teamMatchForm.get(\"teamNumb_name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"min\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.teamMatchForm.get(\"teamNumb_name\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"max\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.teamMatchForm.get(\"teamNumb_name\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"pattern\"]);\n  }\n}\nexport class ModalAddNumberTeamComponent {\n  constructor(_teamService, _activeModal, _translateService) {\n    this._teamService = _teamService;\n    this._activeModal = _activeModal;\n    this._translateService = _translateService;\n    this.team_number = 0;\n    this.AppConfig = AppConfig;\n    // validate type number\n    this.teamMatchForm = new FormGroup({\n      teamNumb_name: new FormControl(null, [Validators.required, Validators.min(2), Validators.max(128), Validators.pattern('^[0-9]+$')])\n    });\n  }\n  ngOnInit() {\n    if (this.team_number || this.team_number === 0) {\n      this.teamMatchForm.get('teamNumb_name')?.setValue(this.team_number);\n    }\n  }\n  onClose() {\n    this.teams = [];\n    this._activeModal.close();\n  }\n  onSubmit() {\n    if (this.teamMatchForm.valid) {\n      const teamNumber = this.teamMatchForm.get('teamNumb_name')?.value;\n      this._activeModal.close(teamNumber);\n    }\n  }\n  static #_ = this.ɵfac = function ModalAddNumberTeamComponent_Factory(t) {\n    return new (t || ModalAddNumberTeamComponent)(i0.ɵɵdirectiveInject(i1.TeamService), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalAddNumberTeamComponent,\n    selectors: [[\"app-modal-add-number-team\"]],\n    inputs: {\n      teams: \"teams\",\n      team_number: \"team_number\"\n    },\n    decls: 30,\n    vars: 23,\n    consts: [[1, \"modal-header\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [1, \"pb-1\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"teamNumb_name\"], [\"type\", \"number\", \"id\", \"teamNumb_name\", \"formControlName\", \"teamNumb_name\", \"min\", \"2\", \"max\", \"128\", 1, \"form-control\", 3, \"placeholder\"], [\"class\", \"text-danger mt-1\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn\", \"btn-secondary\", \"mx-1\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mx-1\"], [1, \"text-danger\", \"mt-1\"], [4, \"ngIf\"]],\n    template: function ModalAddNumberTeamComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0)(2, \"button\", 1);\n        i0.ɵɵlistener(\"click\", function ModalAddNumberTeamComponent_Template_button_click_2_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(3, \"span\", 2);\n        i0.ɵɵtext(4, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\")(7, \"h4\")(8, \"span\");\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵpipe(11, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"p\");\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalAddNumberTeamComponent_Template_form_ngSubmit_15_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(16, \"div\", 5)(17, \"label\", 6);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(20, \"input\", 7);\n        i0.ɵɵpipe(21, \"translate\");\n        i0.ɵɵtemplate(22, ModalAddNumberTeamComponent_div_22_Template, 5, 4, \"div\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 9)(24, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function ModalAddNumberTeamComponent_Template_button_click_24_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵtext(25);\n        i0.ɵɵpipe(26, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"button\", 11);\n        i0.ɵɵtext(28);\n        i0.ɵɵpipe(29, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        let tmp_5_0;\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(10, 9, \"Set number of team\"), \" \", i0.ɵɵpipeBind1(11, 11, \"Groups\"), \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 13, \"Specify the number of teams (between 2 and 128) for the schedule.\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.teamMatchForm);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 15, \"Number of Teams\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(21, 17, \"Enter team number\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.teamMatchForm.get(\"teamNumb_name\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.teamMatchForm.get(\"teamNumb_name\")) == null ? null : tmp_5_0.touched));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 19, \"Cancel\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(29, 21, \"Submit\"), \" \");\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAGnE,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;ICoBhCC,EAAA,CAAAC,cAAA,UAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,uCACF;;;;;IACAN,EAAA,CAAAC,cAAA,UAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,mCACF;;;;;IACAN,EAAA,CAAAC,cAAA,UAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,qCACF;;;;;IACAN,EAAA,CAAAC,cAAA,UAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,yCACF;;;;;IAZFN,EAAA,CAAAC,cAAA,cAAiI;IAC/HD,EAAA,CAAAO,UAAA,IAAAC,iDAAA,kBAEM;IACNR,EAAA,CAAAO,UAAA,IAAAE,iDAAA,kBAEM;IACNT,EAAA,CAAAO,UAAA,IAAAG,iDAAA,kBAEM;IACNV,EAAA,CAAAO,UAAA,IAAAI,iDAAA,kBAEM;IACRX,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IAZEH,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAY,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,aAAA,CAAAC,GAAA,oCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA8D;IAG9DjB,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAY,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,aAAA,CAAAC,GAAA,oCAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,QAAyD;IAGzDjB,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAY,UAAA,UAAAO,OAAA,GAAAL,MAAA,CAAAC,aAAA,CAAAC,GAAA,oCAAAG,OAAA,CAAAF,MAAA,kBAAAE,OAAA,CAAAF,MAAA,QAAyD;IAGzDjB,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAY,UAAA,UAAAQ,OAAA,GAAAN,MAAA,CAAAC,aAAA,CAAAC,GAAA,oCAAAI,OAAA,CAAAH,MAAA,kBAAAG,OAAA,CAAAH,MAAA,YAA6D;;;ADrB7E,OAAM,MAAOI,2BAA2B;EAetCC,YACUC,YAAyB,EACzBC,YAA4B,EAC5BC,iBAAmC;IAFnC,KAAAF,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAhBlB,KAAAC,WAAW,GAAW,CAAC;IAChC,KAAA3B,SAAS,GAAGA,SAAS;IAErB;IACA,KAAAgB,aAAa,GAAG,IAAIlB,SAAS,CAAC;MAC5B8B,aAAa,EAAE,IAAI/B,WAAW,CAAC,IAAI,EAAE,CACnCE,UAAU,CAAC8B,QAAQ,EACnB9B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,EACjB/B,UAAU,CAACgC,GAAG,CAAC,GAAG,CAAC,EACnBhC,UAAU,CAACiC,OAAO,CAAC,UAAU,CAAC,CAC/B;KACF,CAAC;EAOF;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACX,aAAa,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEiB,QAAQ,CAAC,IAAI,CAACP,WAAW,CAAC;;EAEvE;EAEAQ,OAAOA,CAAA;IACL,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACX,YAAY,CAACY,KAAK,EAAE;EAC3B;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtB,aAAa,CAACuB,KAAK,EAAE;MAC5B,MAAMC,UAAU,GAAG,IAAI,CAACxB,aAAa,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEwB,KAAK;MACjE,IAAI,CAAChB,YAAY,CAACY,KAAK,CAACG,UAAU,CAAC;;EAEvC;EAAC,QAAAE,CAAA;qBAtCUpB,2BAA2B,EAAArB,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA9C,EAAA,CAAA0C,iBAAA,CAAAK,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;UAA3B5B,2BAA2B;IAAA6B,SAAA;IAAAC,MAAA;MAAAhB,KAAA;MAAAT,WAAA;IAAA;IAAA0B,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZxCzD,EAAA,CAAAC,cAAA,UAAK;QAE8ED,EAAA,CAAA2D,UAAA,mBAAAC,6DAAA;UAAA,OAASF,GAAA,CAAAxB,OAAA,EAAS;QAAA,EAAC;QAChGlC,EAAA,CAAAC,cAAA,cAAyB;QAAAD,EAAA,CAAAE,MAAA,aAAO;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAG3CH,EAAA,CAAAC,cAAA,aAAkD;QAGtCD,EAAA,CAAAE,MAAA,GAAiE;;;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAEhFH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGNH,EAAA,CAAAC,cAAA,eAAuE;QAArCD,EAAA,CAAA2D,UAAA,sBAAAE,+DAAA;UAAA,OAAYH,GAAA,CAAArB,QAAA,EAAU;QAAA,EAAC;QACvDrC,EAAA,CAAAC,cAAA,cAAwB;QAEpBD,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAA8D,SAAA,gBAC0E;;QAC1E9D,EAAA,CAAAO,UAAA,KAAAwD,2CAAA,iBAaM;QACR/D,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAA2C;QAEvCD,EAAA,CAAA2D,UAAA,mBAAAK,8DAAA;UAAA,OAASN,GAAA,CAAAxB,OAAA,EAAS;QAAA,EAAC;QACnBlC,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAmD;QACjDD,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;QArCHH,EAAA,CAAAI,SAAA,GAAiE;QAAjEJ,EAAA,CAAAiE,kBAAA,KAAAjE,EAAA,CAAAM,WAAA,oCAAAN,EAAA,CAAAM,WAAA,uBAAiE;QAGvEN,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,mFACF;QAGIN,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAAY,UAAA,cAAA8C,GAAA,CAAA3C,aAAA,CAA2B;QAG3Bf,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,iCACF;QAEEN,EAAA,CAAAI,SAAA,GAAmD;QAAnDJ,EAAA,CAAAkE,qBAAA,gBAAAlE,EAAA,CAAAM,WAAA,8BAAmD;QAC/CN,EAAA,CAAAI,SAAA,GAAgG;QAAhGJ,EAAA,CAAAY,UAAA,WAAAuD,OAAA,GAAAT,GAAA,CAAA3C,aAAA,CAAAC,GAAA,oCAAAmD,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAT,GAAA,CAAA3C,aAAA,CAAAC,GAAA,oCAAAmD,OAAA,CAAAE,OAAA,EAAgG;QAmBpGrE,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,wBACF;QAEEN,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,wBACF", "names": ["FormControl", "FormGroup", "Validators", "AppConfig", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtemplate", "ModalAddNumberTeamComponent_div_22_div_1_Template", "ModalAddNumberTeamComponent_div_22_div_2_Template", "ModalAddNumberTeamComponent_div_22_div_3_Template", "ModalAddNumberTeamComponent_div_22_div_4_Template", "ɵɵproperty", "tmp_0_0", "ctx_r0", "teamMatchForm", "get", "errors", "tmp_1_0", "tmp_2_0", "tmp_3_0", "ModalAddNumberTeamComponent", "constructor", "_teamService", "_activeModal", "_translateService", "team_number", "teamNumb_name", "required", "min", "max", "pattern", "ngOnInit", "setValue", "onClose", "teams", "close", "onSubmit", "valid", "teamNumber", "value", "_", "ɵɵdirectiveInject", "i1", "TeamService", "i2", "NgbActiveModal", "i3", "TranslateService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "ModalAddNumberTeamComponent_Template", "rf", "ctx", "ɵɵlistener", "ModalAddNumberTeamComponent_Template_button_click_2_listener", "ModalAddNumberTeamComponent_Template_form_ngSubmit_15_listener", "ɵɵelement", "ModalAddNumberTeamComponent_div_22_Template", "ModalAddNumberTeamComponent_Template_button_click_24_listener", "ɵɵtextInterpolate2", "ɵɵpropertyInterpolate", "tmp_5_0", "invalid", "touched"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-teams\\modal-add-number-team\\modal-add-number-team.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-teams\\modal-add-number-team\\modal-add-number-team.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { TeamService } from 'app/services/team.service';\r\n\r\n@Component({\r\n  selector: 'app-modal-add-number-team',\r\n  templateUrl: './modal-add-number-team.component.html',\r\n  styleUrls: ['./modal-add-number-team.component.scss']\r\n})\r\nexport class ModalAddNumberTeamComponent {\r\n  @Input() teams: any;\r\n  @Input() team_number: number = 0;\r\n  AppConfig = AppConfig;\r\n  \r\n  // validate type number\r\n  teamMatchForm = new FormGroup({\r\n    teamNumb_name: new FormControl(null, [\r\n      Validators.required,\r\n      Validators.min(2),\r\n      Validators.max(128),\r\n      Validators.pattern('^[0-9]+$'),\r\n    ]),\r\n  });\r\n  \r\n  constructor(\r\n    private _teamService: TeamService,\r\n    private _activeModal: NgbActiveModal,\r\n    private _translateService: TranslateService\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    if (this.team_number || this.team_number === 0) {\r\n      this.teamMatchForm.get('teamNumb_name')?.setValue(this.team_number);\r\n    }\r\n  }\r\n\r\n  onClose() {\r\n    this.teams = [];\r\n    this._activeModal.close();\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.teamMatchForm.valid) {\r\n      const teamNumber = this.teamMatchForm.get('teamNumb_name')?.value;\r\n      this._activeModal.close(teamNumber);\r\n    }\r\n  }\r\n}\r\n", "<div>\r\n  <div class=\"modal-header\">\r\n    <button type=\"button\" class=\"close\" data-bs-dismiss=\"modal\" aria-label=\"Close\" (click)=\"onClose()\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <div>\r\n      <h4>\r\n        <span>{{ 'Set number of team' | translate }} {{ 'Groups' | translate }}</span>\r\n      </h4>\r\n      <p>\r\n        {{ 'Specify the number of teams (between 2 and 128) for the schedule.' | translate }}\r\n      </p>\r\n    </div>\r\n\r\n    <form [formGroup]=\"teamMatchForm\" (ngSubmit)=\"onSubmit()\" class=\"pb-1\">\r\n      <div class=\"form-group\">\r\n        <label for=\"teamNumb_name\">\r\n          {{ 'Number of Teams' | translate }}\r\n        </label>\r\n        <input type=\"number\" class=\"form-control\" id=\"teamNumb_name\" formControlName=\"teamNumb_name\"\r\n          placeholder=\"{{ 'Enter team number' | translate }}\"  min=\"2\" max=\"128\"/>\r\n        <div *ngIf=\"teamMatchForm.get('teamNumb_name')?.invalid && teamMatchForm.get('teamNumb_name')?.touched\" class=\"text-danger mt-1\">\r\n          <div *ngIf=\"teamMatchForm.get('teamNumb_name')?.errors?.['required']\">\r\n            {{ 'This field is required.' | translate }}\r\n          </div>\r\n          <div *ngIf=\"teamMatchForm.get('teamNumb_name')?.errors?.['min']\">\r\n            {{ 'Minimum is 2 teams.' | translate }}\r\n          </div>\r\n          <div *ngIf=\"teamMatchForm.get('teamNumb_name')?.errors?.['max']\">\r\n            {{ 'Maximum is 128 teams.' | translate }}\r\n          </div>\r\n          <div *ngIf=\"teamMatchForm.get('teamNumb_name')?.errors?.['pattern']\">\r\n            {{ 'Only numbers are allowed.' | translate }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button type=\"button\" class=\"btn btn-secondary mx-1\" data-bs-dismiss=\"modal\" aria-label=\"Close\"\r\n          (click)=\"onClose()\">\r\n          {{ 'Cancel' | translate }}\r\n        </button>\r\n        <button type=\"submit\" class=\"btn btn-primary mx-1\">\r\n          {{ 'Submit' | translate }}\r\n        </button>\r\n      </div>\r\n    </form>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}