<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" height="480" width="640">
  <g fill-rule="evenodd" stroke-width="1pt">
    <path fill="#00c4ff" d="M0-.01h640.003v160.43H0z"/>
    <path fill="#fff" d="M0 159.763h640.003v160.43H0z"/>
    <path fill="#00c4ff" d="M0 319.536h640.003v160.43H0z"/>
  </g>
  <path d="M382.49 221.33c0 14.564-11.864 26.37-26.5 26.37s-26.498-11.806-26.498-26.37 11.864-26.37 26.5-26.37 26.498 11.806 26.498 26.37z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(1.6452 0 0 1.62 -265.894 -116.567)" stroke="#000" stroke-width=".625" fill="#ffd600"/>
  <path d="M364.43 195.28c-4.34-1.05-8.785.422-10.185.318-1.925 0-6.79-1.68-10.185 0M338.71 200.49c4.305-3.01 9.115 1.086 10.394.315 3.492-2.294 6.736-1.868 10.08.21 2.155 1.272 5.914-3.71 10.29.315" stroke-opacity=".387" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".5" fill="none"/>
  <path d="M333.88 205.63c2.275-1.855 9.694-1.925 17.324 2.414 1.155-.28 1.89-1.084.945-2.204-5.74-1.995-12.425-4.515-18.585-2.625-1.68 1.19-1.26 1.96.315 2.415z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".5" fill="#efc000"/>
  <path d="M333.88 205.63c2.275-1.855 9.694-1.925 17.324 2.414 1.155-.28 1.89-1.084.945-2.204-5.74-1.995-12.425-4.515-18.585-2.625-1.68 1.19-1.26 1.96.315 2.415z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-1.2703 0 0 1.231 768.984 -28.912)" stroke="#000" stroke-width=".5" fill="#efc000"/>
  <path stroke-linejoin="round" d="M330.84 211.83c7.525-4.83 17.464-2.31 21.63.315-6.09-1.155-6.196-1.68-10.606-1.785-3.115.106-7.7-.21-11.024 1.47z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path d="M348.06 211.3c-3.675 7.665-10.08 7.77-14.594-.42" stroke-opacity=".387" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".625" fill="none"/>
  <path d="M308.78 234.09c-1.383 1.514-4.77 2.413-7.564 2.01s-3.937-1.96-2.553-3.474c1.384-1.514 4.77-2.413 7.565-2.01s3.937 1.96 2.553 3.474z" fill-rule="evenodd" fill-opacity=".368"/>
  <path d="M301.898 232.147c.143.353-.297.82-.984 1.045s-1.358.12-1.5-.23c-.144-.354.297-.822.983-1.046s1.36-.12 1.5.23z" fill-rule="evenodd" fill="gold"/>
  <path d="M349.18 224.5c-4.24 7.127 1.537 2.1 2.475 4.164 1.65 1.913 3.3 1.462 4.276 0 .977-1.65 7.128 3.113 2.927-3.938" stroke-opacity=".387" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M341.64 236.31c3.638-.413 9.753-3.188 11.93-.9 1.874-2.063 8.476.6 12.714.9-3.076 1.875-9.302.6-12.265 2.588-2.89-1.763-9.267-.15-12.38-2.588z" stroke-opacity=".387" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-linecap="round" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M347.5 239.58c5.514 2.25 6.752 1.913 12.716.225-1.238 3.264-4.398 3.95-6.19 3.826-1.857-.12-4.388.114-6.526-4.05z" stroke-opacity=".387" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(1.2703 0 0 1.231 -119.922 -28.068)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" transform="matrix(-.908 -.861 -.8884 .88 837.014 353.18)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" transform="matrix(.889 .8794 -.9075 .8614 204.616 -258.71)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" transform="matrix(-.0073 -1.231 -1.2703 .007 601.74 676.9)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" transform="matrix(-1.1653 -.49 -.5057 1.1292 835.787 164.23)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" transform="matrix(-.905 -.864 -.8915 .877 827.91 348.79)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" transform="matrix(-.4964 -1.133 -1.1693 .481 748.115 528.492)" stroke="#000" stroke-width=".625" fill="none"/>
  <path d="M349.18 224.5c-4.24 7.127 1.537 2.1 2.475 4.164 1.65 1.913 3.3 1.462 4.276 0 .977-1.65 7.128 3.113 2.927-3.938" stroke-opacity=".082" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M341.64 236.31c3.638-.413 9.753-3.188 11.93-.9 1.874-2.063 8.476.6 12.714.9-3.076 1.875-9.302.6-12.265 2.588-2.89-1.763-9.267-.15-12.38-2.588z" stroke-opacity=".082" fill-rule="evenodd" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-linecap="round" stroke-width=".625" fill="#f0bf00"/>
  <path stroke-linejoin="round" d="M347.5 239.58c5.514 2.25 6.752 1.913 12.716.225-1.238 3.264-4.398 3.95-6.19 3.826-1.857-.12-4.388.114-6.526-4.05z" stroke-opacity=".082" fill-rule="evenodd" transform="matrix(1.2703 0 0 1.231 -130.873 -28.912)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.908 -.861 -.8884 .88 837.014 353.18)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.889 .8794 -.9075 .8614 204.616 -258.71)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.0073 -1.231 -1.2703 .007 601.74 676.9)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-1.1653 -.49 -.5057 1.1292 835.787 164.23)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.905 -.864 -.8915 .877 827.91 348.79)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.4964 -1.133 -1.1693 .481 748.115 528.492)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(0 -1.231 1.2703 0 41.183 668.378)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-1.2703 0 0 1.231 770.68 -28.276)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.908 -.861 .8884 .88 -196.843 353.81)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.889 .8794 .9075 .8614 435.564 -258.39)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.0073 -1.231 1.2703 .007 38.068 676.9)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(1.1653 -.49 .5057 1.1292 -195.68 164.874)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.905 -.864 .8915 .877 -188.602 348.79)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.4964 -1.133 1.1693 .481 -107.76 528.492)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.908 -.861 .8884 .88 -196.843 353.81)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.889 .8794 .9075 .8614 435.564 -258.39)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.0073 -1.231 1.2703 .007 38.068 676.9)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(1.1653 -.49 .5057 1.1292 -195.68 164.874)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.905 -.864 .8915 .877 -188.602 348.79)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.4964 -1.133 1.1693 .481 -107.76 528.492)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(0 1.231 -1.2703 0 598.48 -184.42)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(1.2703 0 0 -1.231 -131 512.223)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.908 .861 -.8884 -.88 836.514 130.14)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.889 -.8794 -.9075 -.8614 204.116 742.347)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.0073 1.231 -1.2703 -.007 601.612 -192.956)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-1.1653 .49 -.5057 -1.1292 835.352 319.092)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.905 .864 -.8915 -.877 828.282 135.16)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.4964 1.133 -1.1693 -.481 747.437 -44.55)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.908 .861 -.8884 -.88 836.514 130.14)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.889 -.8794 -.9075 -.8614 204.116 742.347)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.0073 1.231 -1.2703 -.007 601.612 -192.956)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-1.1653 .49 -.5057 -1.1292 835.352 319.092)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.905 .864 -.8915 -.877 828.282 135.16)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.4964 1.133 -1.1693 -.481 747.437 -44.55)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-1.2703 0 0 -1.231 759.526 511.997)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(0 1.231 1.2703 0 40.634 -194.91)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.8884 -.88 .908 -.861 434.918 742.67)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.9075 .8614 .889 -.8794 -196.835 129.834)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-1.2703 -.007 .0073 -1.231 768.322 515.032)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.5057 -1.1292 1.1653 -.49 239.93 741.54)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.8915 -.877 .905 -.864 429.72 734.68)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-1.1693 -.481 .4964 -1.133 615.186 656.337)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.8884 -.88 .908 -.861 434.918 742.67)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.9075 .8614 .889 -.8794 -196.835 129.834)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-1.2703 -.007 .0073 -1.231 768.322 515.032)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.5057 -1.1292 1.1653 -.49 239.93 741.54)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.8915 -.877 .905 -.864 429.72 734.68)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-1.1693 -.481 .4964 -1.133 615.186 656.337)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.2703 0 0 1.231 -130.216 -27.957)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.2703 0 0 1.231 -131.217 -25.465)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.4064 0 0 1.231 -176.307 -23.11)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.4064 0 0 1.231 -177.588 -20.892)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.6786 0 0 1.231 -267.194 -18.537)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.86 0 0 1.231 -326.593 -16.183)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.7693 0 0 1.231 -297.676 -13.552)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.724 0 0 1.231 -282.503 -10.92)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.7693 0 0 1.231 -297.39 -8.426)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(2.223 0 0 1.231 -444.825 -5.517)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.9053 0 0 1.231 -341.188 -2.192)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -341.045 .162)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -340.044 3.348)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -339.902 6.12)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -338.186 9.167)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -336.47 11.66)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -334.9 14.57)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -333.04 17.34)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -331.753 19.97)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -329.038 23.165)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.225 0 0 1.231 -104.597 26.07)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -341.045 .162)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -340.044 3.348)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -339.902 6.12)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -338.186 9.167)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -336.47 11.66)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -334.9 14.57)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -333.04 17.34)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -331.753 19.97)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.9053 0 0 1.231 -329.038 23.165)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.225 0 0 1.231 -103.975 25.828)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.2703 0 0 1.231 769.71 -28.594)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.2703 0 0 1.231 770.71 -26.1)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4064 0 0 1.231 815.79 -23.746)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4064 0 0 1.231 817.08 -21.53)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.6786 0 0 1.231 906.69 -19.175)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.86 0 0 1.231 966.086 -16.82)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.7693 0 0 1.231 937.163 -14.188)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.724 0 0 1.231 921.99 -11.555)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.7693 0 0 1.231 936.888 -9.062)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-2.223 0 0 1.231 1084.31 -6.153)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 980.676 -2.828)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 980.53 -.474)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 979.53 2.712)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 979.385 5.482)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 977.674 8.53)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 975.963 11.025)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 974.382 13.933)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 972.525 16.71)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 971.25 19.34)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.9053 0 0 1.231 968.523 22.52)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.225 0 0 1.231 744.08 25.425)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 980.53 -.474)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 979.53 2.712)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 979.385 5.482)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 977.674 8.53)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 975.963 11.025)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 974.382 13.933)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 972.525 16.71)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 971.25 19.34)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.9053 0 0 1.231 968.523 22.52)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.225 0 0 1.231 744.08 25.425)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path stroke-linejoin="round" d="M330.84 211.83c7.525-4.83 17.464-2.31 21.63.315-6.09-1.155-6.196-1.68-10.606-1.785-3.115.106-7.7-.21-11.024 1.47z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-1.2703 0 0 1.231 770.29 -29.23)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path d="M348.06 211.3c-3.675 7.665-10.08 7.77-14.594-.42" stroke-opacity=".387" transform="matrix(1.2703 0 0 1.231 -97.703 -29.548)" stroke="#000" stroke-width=".625" fill="none"/>
  <path d="M341.925 233.537c-1.43 1.45-4.93 2.31-7.815 1.924s-4.067-1.874-2.637-3.324c1.43-1.45 4.928-2.31 7.815-1.924s4.067 1.876 2.637 3.325z" fill-rule="evenodd" fill-opacity=".368"/>
  <path d="M335.073 231.518c.142.352-.298.82-.985 1.045s-1.358.12-1.5-.232c-.144-.35.297-.82.983-1.044s1.36-.12 1.503.232z" fill-rule="evenodd" fill="gold"/>
</svg>
