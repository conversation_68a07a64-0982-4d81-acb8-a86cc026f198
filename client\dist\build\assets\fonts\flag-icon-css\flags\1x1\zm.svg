<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg id="svg548" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3819">
  <rdf:RDF>
   <cc:Work rdf:about="">
  <dc:format>image/svg+xml</dc:format>
  <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs550">
  <clipPath id="clipPath4044" clipPathUnits="userSpaceOnUse">
   <rect id="rect4046" fill-opacity="0.67" height="496.06" width="496.06" y=".00013924" x="248.03"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath4044)" transform="matrix(1.0321 0 0 1.0321 -256 -.00014371)">
  <rect id="rect626" height="496.06" width="744.09" y=".000153" x="-0.000099" stroke-width="1pt" fill="#198a00"/>
  <rect id="rect616" height="317.82" width="87.321" y="178.21" x="656.77" stroke-width="1pt" fill="#ef7d00"/>
  <rect id="rect617" height="317.89" width="87.227" y="178.06" x="569.56" stroke-width="1pt"/>
  <rect id="rect618" height="317.81" width="89.074" y="178.26" x="480.51" stroke-width="1pt" fill="#de2010"/>
  <g id="g600" stroke="#000" fill="#ef7d00" transform="matrix(.96652 0 0 .96652 32.506 4.4393)">
   <path id="path564" stroke-linejoin="round" d="m292.6 63.483s45.525-19.453 49.936-23.464c2.006 2.4066-18.25 26.873-57.557 35.898 35.697-8.423 66.181-34.895 72.398-34.093 1.805 0.4011 1.403 25.871-80.42 47.931 56.554-14.841 89.444-42.115 89.043-39.107 0.401 0.6016-5.615 21.458-55.15 37.703 13.838-3.2092 51.34-26.071 50.939-21.659 1.203 1.8049-36.299 49.936-104.08 34.294 54.148 14.038 89.243-19.855 94.257-19.253 1.003 0.2005-10.028 29.681-78.213 31.486 32.69-3.409 23.263-0.201 23.263-0.201s-19.253 15.041-42.516 4.814c18.25 5.014 20.255 5.214 20.656 6.818-1.203 2.005-16.244 5.014-30.683-2.808 11.631 4.813 22.06 5.616 22.26 7.421-0.2 0.601-7.62 4.813-14.238 1.805-6.618-3.009-67.585-40.511-67.585-40.511l103.88-28.478 3.81 1.4038z" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-width="1.25"/>
   <path id="path560" stroke-linejoin="round" d="m170.47 164.93c-9.135 0-9.387 8.129-9.387 8.129s-0.754 0.587-0.251 4.527c1.508-2.683 2.263-3.521 2.263-3.521 1.006 0.168 5.196 1.425 11.817-3.771-6.035 6.285-2.43 8.632-2.43 8.632s-1.425 4.945 3.184 5.951c-1.424-2.012-0.586-3.772-0.586-3.772s6.453-0.67 6.034-8.548c0.252 7.123 4.107 8.883 4.107 8.883s0 3.939 4.19 4.275c-2.262-2.012-1.76-5.113-1.76-5.113s5.448-3.939 0.838-10.644c2.766-1.593 5.029-6.034 5.029-6.034s-3.771-1.593-5.531-2.933c-0.839-1.761-0.084-11.399-0.084-11.399l-2.263-12.488-6.454 19.445c0.251-2.766 0.419 8.381-8.716 8.381z" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-width="1.25"/>
   <path id="path563" stroke-linejoin="round" d="m215.59 136.88c0.2 0.201 6.819 7.622 13.236 7.22 2.406-2.005-5.014-6.417-5.014-7.42 2.607 2.407 13.637 11.832 20.657 8.423 2.807-4.011-5.014-3.409-13.638-14.239 6.016 4.011 21.058 12.835 28.077 9.426 3.009-3.209-15.643-13.237-21.86-21.058l-16.043-8.222-21.86 17.849 16.445 8.021z" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-width="1.25"/>
   <path id="path562" stroke-linejoin="round" d="m190.92 75.917s7.42-4.412 37.101-2.206c3.209 0.401 20.255-5.8158 25.67-7.6207 8.623-1.6044 36.098-7.6209 43.519-13.236 5.214-0.6016-1.405 9.6263-8.022 12.033-7.02 3.2088-32.489 12.033-41.514 10.83 10.83 0.2006 4.814 9.0246-13.236 4.8131 8.624 4.8131 5.415 5.6153 5.415 5.6153s-15.241 0.8022-19.252-2.6071c10.228 3.8104 6.016 5.2142 6.016 5.2142s-10.428 1.0027-15.242-1.2033c7.42 2.206 3.61 3.6099 3.61 3.6099s-6.016 1.0027-11.03-0.8022c-5.013-1.8049-12.634-14.439-13.035-14.439z" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1.25"/>
   <path id="path561" stroke-linejoin="round" d="m202.82 141.04 1.09 19.612s-0.587 1.006-1.09 1.593c-0.503 0.586-13.913-2.096-12.069 8.129 0 4.191 0.084 5.28 2.934 7.627-0.755-2.766-0.503-4.693-0.503-4.693s3.771 2.263 7.459-3.939c-2.514 6.118-0.755 8.297-0.168 8.465 0.587 1.09-1.005 6.034 4.023 5.95-1.927-1.844-1.005-4.526-1.005-4.526s5.28-0.837 3.436-10.392c1.928-1.928 2.682-0.084 2.682-0.084s0.586 5.699 5.196 4.526c2.096 1.173-0.335 4.274-0.335 4.274s3.352 0.084 4.358-2.766c1.006-2.849 2.179-7.962-3.353-10.057-0.67-2.011 2.18-2.179 2.18-2.179s3.52 1.173 4.525 2.933c1.006 1.76 0.671-4.609-3.687-5.28-5.364-0.168-5.699-1.257-5.699-1.508 0-0.252-0.922-14.248-1.341-18.271l-8.633 0.586z" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-width="1.25"/>
   <path id="path553" stroke-linejoin="round" d="m221.48 157.75c0.066-0.899-9.15-13.308-6.812-14.912 2.337 0.481 6.17 6.125 9.275 4.293-0.941-2.2-3.715-0.961-6.865-6.593-3.149-6.367-3.609-15.925-14.639-26.351 7.141 11.033 23.34 17.5 24.026 14.283s-14.264-15.382-13.498-18.314c2.973 6.021 17.984 19.437 29.044 18.326 0.758-2.583-9.057-7.796-11.583-12.664-7.065-4.746-25.972-21.044-26.351-24.823-6.88-10.034-11.175-13.07-13.175-14.226-0.773-0.9105-0.967-1.7809-1.118-2.3343-4.271-10.069 1.386-13.338 4.697-13.822 2.725-0.3028 3.516 0.1051 5.477-0.8445-2.322-0.9083-4.643-1.7578-6.964-2.6662 3.027 2.2204 10.759 0.2656 9.083 6.661 3.39-1.2898 10.603-9.8713-8.175-11.808-6.111-7.0106-31.236-11.04-37.525 19.558 0.502 0.429 0.765 0.7815 2.363 1.9749-7.94-3.837-29.34-6.803-37.05-8.049-20.84-6.012-42.48-20.5-44.56-18.718-2.782 1.234 12.589 15.536 11.79 15.852-13.328-7.771-25.505-13.277-36.834-18.288-7.971-2.979-15.954-9.916-16.835-8.596-2.933 6.076 12.779 21.352 15.702 23.148 2.923 1.719 25.91 12.382 25.571 12.515-34.622-15.715-40.427-18.564-42.164-20.375-3.052-0.688-10.486-10.724-12.557-10.079-1.1341 1.1142 1.1405 16.973 17.662 24.092 2.6929 1.9626 35.414 13.701 35.093 14.621-0.08 0.23-37.477-15.435-38.93-15.995-7.21-2.828-16.057-12.738-17.711-11.623-1.5787 1.0112 4.0784 11.738 11.364 15.102 3.8188 1.6853 18.467 8.9405 31.665 13.558 0.8423 0.3046-23.722-9.3154-35.533-14.251-5.3549-2.9721-7.8767-6.6767-8.6924-5.8796-1.2065 0.797 1.9493 16.643 39.902 25.584 0.9999 0.4256 13.36-2.928 12.749-2.4832-0.1528 0.1112-12.273 2.7499-13.453 2.6597-1.0503-0.1604-7.6904 0.7861-7.8105 1.1302-0.4012 1.0719 2.4058 6.7457 22.002 5.5625 2.5052-0.1506 16.1-4.9509 14.958-3.9183-0.5711 0.5166-18.513 6.3466-19.879 6.4616-1.1868 0.1677-7.4897 0.8268-7.7227 1.4972-0.2846 0.9143 4.6653 4.9524 15.189 5.5994 9.3176 0.495 27.023-5.6933 26.668-5.2921-0.3552 0.4011-17.068 6.1281-17.506 6.4771-0.6048 0.342-6.7248 0.674-6.9396 1.144-0.4987 1.169 8.8282 9.891 36.412 0.583-2.771 3.743-16.141 6.303-16.122 6.977-0.062 0.489 2.9457 3.395 7.6202 4.435 2.337 0.52 5.537 0.414 8.46-0.048 5.25-1.051 10.783-3.128 17.813-9.625 0.84 2.039-17.561 11.351-16.697 12.239 3.99 3.7 17.244-0.477 18.112-0.962 0.869-0.486 25.523-14.388 25.438-15.997 0.383 2.062-31.785 19.541-31.438 20.258 2.065 3.295 13.054-0.173 13.579-0.476 0.524-0.302 14.024-7.595 14.394-7.81 0.369-0.215-15.866 9.502-14.42 10.981-0.699 6.31 25.355-4.355 27.397-5.683 1.021-0.664-12.516 5.952-12.552 9.095 4.359 7.37 18.317 5.032 20.47 3.411 1.077-0.811-1.267 5.475-0.512 4.904 0.263-0.118 3.036-3.397 4.102-5.77-0.34 2.059-1.87 5.303-2.763 9.025-0.894 3.722-1.15 7.922-2.377 12.384-0.281 1.96 8.601-3.832 7.311-17.678 0.76 7.202-2.805 20.669-1.872 21.369 1.866 1.4 7.025-6.707 7.546-12.189 1.532 2.769 4.747 8.549 7.513 9.879-0.301-4.189 0.053-3.997-1.192-8.114 0.718-5.466 0.818-13.031 0.992-21.573 7.516 14.398 10.514 20.478 8.414 31.504 2.235 0.932 5.805-7.696 5.517-12.137 2.87 12.34 15.258 14.19 15.455 13.831z" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1pt"/>
   <path id="path555" stroke-linejoin="round" d="m96.289 66.342s-5.1157 3.8652-11.482 3.6379c1.5915 6.3662 14.779 1.4779 14.779 1.4779s-6.3662 7.0483-10.914 8.1851c2.7284 2.2736 14.21 1.4779 16.143 0.3411 1.932-1.1369 5.343-5.5705 5.343-5.5705s-11.823 13.642-13.529 13.528c-0.2274 1.5915 12.392 1.0231 15.007-1.3642 2.614-2.3874 9.208-5.9115 9.208-5.9115s-17.28 12.05-18.076 12.05c6.48 1.7053 19.44-2.0463 27.739-7.162-12.278 8.1852-13.414 9.663-19.667 12.391 5.571 1.2506 8.412 5.0022 27.284-3.1831 10.8-5.1156 16.938-14.438 16.938-14.438-6.707 8.981-17.28 16.598-28.988 24.101-0.683 1.364 12.504 6.934 29.329-10.573" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1pt"/>
   <path id="path556" d="m187.69 104.31s1.137 5.343 5.684 9.436c4.548 4.092 4.889 8.071 4.889 8.071" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1pt"/>
   <path id="path557" d="m185.42 69.525s1.591 4.7746 6.252 7.8441c4.775 3.0694 11.938 12.732 12.506 14.438 0.55 2.0639 3.296 16.939 3.069 18.53" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1pt"/>
   <path id="path558" d="m156.43 76.914c0.341 1.8189-6.708 11.027 2.955 21.827-8.754 10.685-8.753 12.05-8.753 12.05s4.888 2.728 13.642-4.775c14.437 17.735 9.626 25.851 9.626 25.851" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1pt"/>
   <path id="path559" stroke-linejoin="round" d="m172.75 119.35s-1.33-1.905 1.041-9.019c2.135 2.572 4.509 3.121 5.55 4.162 1.04 1.041 11.941 2.619 12.634 9.904" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1pt"/>
   <path id="path552" d="m185.56 52.574c0-0.5352-2.195-5.2452-10.384 0.6423 4.389 0.2675 8.724 2.7831 10.384-0.6423z" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-width="1pt"/>
   <path id="path565" stroke-linejoin="round" d="m263.72 74.112c0.401 0.2005 24.467 5.4148 32.489 2.8077-10.228 13.637-30.684 4.412-30.684 4.412 10.027 3.0082 10.429 2.8077 13.637 5.6153 1.003 2.6071-18.45 1.4038-24.868-2.206 17.849 5.6153 18.25 5.2142 18.852 7.4202 0.803 3.0082-29.28-0.8022-31.887-4.8131 8.022 6.217 12.434 7.4203 16.846 10.228-5.415 2.4066-15.442 4.8127-33.893-8.2224 24.266 22.06 46.126 20.656 49.536 24.667-8.423 12.434-41.113-7.42-56.154-17.448-15.041-10.027 32.89 24.266 37.303 23.664-2.207 3.409-18.05 0.401-19.053-1.203" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-width="1pt"/>
   <path id="path566" d="m269.94 97.175c-2.807 0.4011-11.23 0.4011-12.433 0.2005" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1pt"/>
   <path id="path554" stroke-linejoin="round" d="m89.241 62.704s18.303 9.663 26.033 8.9809c-2.046 1.9326-5.116 2.7284-5.116 2.7284 1.933 0.7958 7.276 3.6379 15.348 1.8189-1.933 1.9326-4.434 4.0927-4.434 4.0927s6.935 2.6146 14.779-1.478c-2.387 2.9557-3.752 5.1158-3.752 5.1158l5.457 0.341" transform="matrix(.74683 0 0 .74683 457.18 13.745)" stroke-linecap="round" stroke-width="1pt"/>
  </g>
 </g>
</svg>
