{"ast": null, "code": "import { environment } from 'environments/environment';\nimport moment from 'moment';\nimport { NgbDate } from '@ng-bootstrap/ng-bootstrap';\nimport Swal from 'sweetalert2';\nimport { LeagueTableViewComponent } from './league-table-view/league-table-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"app/services/registration.service\";\nimport * as i6 from \"app/services/club.service\";\nimport * as i7 from \"@angular/platform-browser\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"app/layout/components/content-header/content-header.component\";\nimport * as i12 from \"@ng-select/ng-select\";\nimport * as i13 from \"./league-table-view/league-table-view.component\";\nimport * as i14 from \"./schedule-view/schedule-view.component\";\nconst _c0 = [\"dateRangePicker\"];\nfunction LeagueReportsComponent_ng_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, season_r12.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tournament_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", tournament_r13.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, tournament_r13.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const club_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", club_r14.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, club_r14.code), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵlistener(\"mouseenter\", function LeagueReportsComponent_ng_template_42_Template_span_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const date_r15 = restoredCtx.date;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.hoveredDate = date_r15);\n    })(\"mouseleave\", function LeagueReportsComponent_ng_template_42_Template_span_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.hoveredDate = null);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r15 = ctx.date;\n    const focused_r16 = ctx.focused;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"focused\", focused_r16)(\"range\", ctx_r5.isRange(date_r15))(\"faded\", ctx_r5.isHovered(date_r15) || ctx_r5.isInside(date_r15));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", date_r15.day, \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelement(0, \"hr\", 37);\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_ng_template_44_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      const _r3 = i0.ɵɵreference(37);\n      ctx_r20.clearDateRange();\n      return i0.ɵɵresetView(_r3.close());\n    });\n    i0.ɵɵtext(3, \" Clear \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_ng_template_44_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r21);\n      i0.ɵɵnextContext();\n      const _r3 = i0.ɵɵreference(37);\n      return i0.ɵɵresetView(_r3.close());\n    });\n    i0.ɵɵtext(5, \" Close \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LeagueReportsComponent_ng_option_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, status_r23.label), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-league-table-view\", 41);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"seasonId\", ctx_r10.seasonId)(\"clubId\", ctx_r10.clubId)(\"tournamentId\", ctx_r10.tournamentId)(\"dateRange\", ctx_r10.dateRange)(\"matchStatus\", ctx_r10.matchStatus)(\"seasons\", ctx_r10.seasons)(\"clubs\", ctx_r10.clubs)(\"tournaments\", ctx_r10.tournaments);\n  }\n}\nfunction LeagueReportsComponent_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-schedule-view\", 41);\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"seasonId\", ctx_r11.seasonId)(\"clubId\", ctx_r11.clubId)(\"tournamentId\", ctx_r11.tournamentId)(\"dateRange\", ctx_r11.dateRange)(\"matchStatus\", ctx_r11.matchStatus)(\"seasons\", ctx_r11.seasons)(\"clubs\", ctx_r11.clubs)(\"tournaments\", ctx_r11.tournaments);\n  }\n}\nexport class LeagueReportsComponent {\n  constructor(route, _router, _http, _trans, renderer, _loadingService, _registrationService, _clubService, _translateService, _titleService, calendar, formatter) {\n    this.route = route;\n    this._router = _router;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._loadingService = _loadingService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._titleService = _titleService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false; // Track if we're in the middle of selecting range\n    this.matchStatus = 'all';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshChildDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshChildDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n  }\n  ngOnDestroy() {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function LeagueReportsComponent_Factory(t) {\n    return new (t || LeagueReportsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.RegistrationService), i0.ɵɵdirectiveInject(i6.ClubService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i7.Title), i0.ɵɵdirectiveInject(i8.NgbCalendar), i0.ɵɵdirectiveInject(i8.NgbDateParserFormatter));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueReportsComponent,\n    selectors: [[\"app-league-reports\"]],\n    viewQuery: function LeagueReportsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(LeagueTableViewComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dateRangePicker = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leagueTableViewComponent = _t.first);\n      }\n    },\n    hostBindings: function LeagueReportsComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function LeagueReportsComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 69,\n    vars: 60,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"league-reports-page\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\", \"mb-2\"], [1, \"card-body\"], [1, \"row\", \"mb-1\"], [\"for\", \"season\", 1, \"form-label\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\"], [\"for\", \"tournament\", 1, \"form-label\"], [\"for\", \"club\", 1, \"form-label\"], [\"for\", \"dateRange\", 1, \"form-label\"], [1, \"input-group\"], [\"name\", \"daterange\", \"ngbDatepicker\", \"\", \"readonly\", \"\", \"outsideDays\", \"hidden\", 1, \"form-control\", 3, \"placeholder\", \"value\", \"dayTemplate\", \"footerTemplate\", \"firstDayOfWeek\", \"displayMonths\", \"autoClose\", \"click\", \"dateSelect\"], [\"dateRangePicker\", \"ngbDatepicker\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"feather\", \"icon-calendar\"], [\"dayTemplate\", \"\"], [\"footerTemplate\", \"\"], [\"for\", \"matchStatus\", 1, \"form-label\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"league_table\"], [\"ngbNavLink\", \"\"], [1, \"fa-light\", \"fa-table-list\", \"mr-1\"], [\"ngbNavContent\", \"\"], [\"ngbNavItem\", \"schedule_matches\"], [1, \"fa-light\", \"fa-calendar\", \"mr-1\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [3, \"value\"], [1, \"custom-day\", 3, \"mouseenter\", \"mouseleave\"], [1, \"my-0\"], [1, \"d-flex\", \"justify-content-between\", \"p-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [3, \"seasonId\", \"clubId\", \"tournamentId\", \"dateRange\", \"matchStatus\", \"seasons\", \"clubs\", \"tournaments\"]],\n    template: function LeagueReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 5)(10, \"label\", 9);\n        i0.ɵɵtext(11);\n        i0.ɵɵpipe(12, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"ng-select\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_13_listener($event) {\n          return ctx.seasonId = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_13_listener($event) {\n          return ctx.onSelectSeason($event);\n        });\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵtemplate(15, LeagueReportsComponent_ng_option_15_Template, 3, 4, \"ng-option\", 11);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(16, \"div\", 8)(17, \"div\", 12)(18, \"label\", 13);\n        i0.ɵɵtext(19);\n        i0.ɵɵpipe(20, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"ng-select\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_21_listener($event) {\n          return ctx.tournamentId = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_21_listener($event) {\n          return ctx.onSelectTournament($event);\n        });\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵtemplate(23, LeagueReportsComponent_ng_option_23_Template, 3, 4, \"ng-option\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"div\", 12)(25, \"label\", 14);\n        i0.ɵɵtext(26);\n        i0.ɵɵpipe(27, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"ng-select\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_28_listener($event) {\n          return ctx.clubId = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_28_listener($event) {\n          return ctx.onSelectClub($event);\n        });\n        i0.ɵɵpipe(29, \"translate\");\n        i0.ɵɵtemplate(30, LeagueReportsComponent_ng_option_30_Template, 3, 4, \"ng-option\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"div\", 12)(32, \"label\", 15);\n        i0.ɵɵtext(33);\n        i0.ɵɵpipe(34, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"div\", 16)(36, \"input\", 17, 18);\n        i0.ɵɵlistener(\"click\", function LeagueReportsComponent_Template_input_click_36_listener() {\n          return ctx.openDatePicker();\n        })(\"dateSelect\", function LeagueReportsComponent_Template_input_dateSelect_36_listener($event) {\n          return ctx.onDateSelection($event);\n        });\n        i0.ɵɵpipe(38, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"div\", 19)(40, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function LeagueReportsComponent_Template_button_click_40_listener() {\n          return ctx.openDatePicker();\n        });\n        i0.ɵɵelement(41, \"i\", 21);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(42, LeagueReportsComponent_ng_template_42_Template, 2, 7, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(44, LeagueReportsComponent_ng_template_44_Template, 6, 0, \"ng-template\", null, 23, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"div\", 12)(47, \"label\", 24);\n        i0.ɵɵtext(48);\n        i0.ɵɵpipe(49, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"ng-select\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_50_listener($event) {\n          return ctx.matchStatus = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_50_listener($event) {\n          return ctx.onSelectMatchStatus($event);\n        });\n        i0.ɵɵpipe(51, \"translate\");\n        i0.ɵɵtemplate(52, LeagueReportsComponent_ng_option_52_Template, 3, 4, \"ng-option\", 11);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(53, \"div\", 25)(54, \"ul\", 26, 27)(56, \"li\", 28)(57, \"a\", 29);\n        i0.ɵɵelement(58, \"i\", 30);\n        i0.ɵɵtext(59);\n        i0.ɵɵpipe(60, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(61, LeagueReportsComponent_ng_template_61_Template, 1, 8, \"ng-template\", 31);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"li\", 32)(63, \"a\", 29);\n        i0.ɵɵelement(64, \"i\", 33);\n        i0.ɵɵtext(65);\n        i0.ɵɵpipe(66, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(67, LeagueReportsComponent_ng_template_67_Template, 1, 8, \"ng-template\", 31);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(68, \"div\", 34);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r4 = i0.ɵɵreference(43);\n        const _r6 = i0.ɵɵreference(45);\n        const _r9 = i0.ɵɵreference(55);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 36, \"Season\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(14, 38, \"Select Season\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.seasonId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 40, \"Tournament\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(22, 42, \"Select Tournament\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx.tournamentId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.tournaments);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 44, \"Club\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(29, 46, \"Select Club\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx.clubId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.clubs);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(34, 48, \"Date Range\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(38, 50, \"Select Date Range\"));\n        i0.ɵɵproperty(\"value\", ctx.formatter.format(ctx.fromDate) + (ctx.toDate ? \" - \" + ctx.formatter.format(ctx.toDate) : \"\"))(\"dayTemplate\", _r4)(\"footerTemplate\", _r6)(\"firstDayOfWeek\", 1)(\"displayMonths\", 2)(\"autoClose\", false);\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(49, 52, \"Match Status\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(51, 54, \"Select Status\"));\n        i0.ɵɵproperty(\"searchable\", false)(\"clearable\", false)(\"ngModel\", ctx.matchStatus);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.matchStatusOptions);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(60, 56, \"Table View\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(66, 58, \"Schedule View\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r9);\n      }\n    },\n    dependencies: [i9.NgForOf, i10.NgControlStatus, i10.NgModel, i8.NgbInputDatepicker, i8.NgbNavContent, i8.NgbNav, i8.NgbNavItem, i8.NgbNavLink, i8.NgbNavOutlet, i11.ContentHeaderComponent, i12.NgSelectComponent, i12.ɵr, i13.LeagueTableViewComponent, i14.ScheduleViewComponent, i3.TranslatePipe],\n    styles: [\".min-w-max[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n\\n.custom-day[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 0.185rem 0.25rem;\\n  border-radius: 0.25rem;\\n  display: inline-block;\\n  width: 2rem;\\n  cursor: pointer;\\n}\\n\\n.custom-day[_ngcontent-%COMP%]:hover, .custom-day.focused[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n\\n.custom-day.range[_ngcontent-%COMP%], .custom-day[_ngcontent-%COMP%]:hover {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.custom-day.faded[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 123, 255, 0.5);\\n  color: white;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlLXJlcG9ydHMvbGVhZ3VlLXJlcG9ydHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUdBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFGOztBQUdBOztFQUVFLHlCQUFBO0FBQUY7O0FBR0E7O0VBRUUseUJBQUE7RUFDQSxZQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtFQUNBLFlBQUE7QUFBRjs7QUFHQTtFQUNFLHVCQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIubWluLXctbWF4IHtcclxuICBtaW4td2lkdGg6IG1heC1jb250ZW50O1xyXG59XHJcblxyXG4vLyBEYXRlIHJhbmdlIHBpY2tlciBzdHlsZXNcclxuLmN1c3RvbS1kYXkge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBwYWRkaW5nOiAwLjE4NXJlbSAwLjI1cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHdpZHRoOiAycmVtO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmN1c3RvbS1kYXk6aG92ZXIsXHJcbi5jdXN0b20tZGF5LmZvY3VzZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LnJhbmdlLFxyXG4uY3VzdG9tLWRheTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LmZhZGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDEyMywgMjU1LCAwLjUpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLm5hdi1saW5rIHtcclxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAYA,SAASA,WAAW,QAAQ,0BAA0B;AAGtD,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAAsBC,OAAO,QAAgC,4BAA4B;AACzF,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,wBAAwB,QAAQ,iDAAiD;;;;;;;;;;;;;;;;;;;ICCtEC,EAAA,CAAAC,cAAA,oBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,EAAA,CAAmB;IAC3DN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAJ,UAAA,CAAAK,IAAA,OACF;;;;;IAaAV,EAAA,CAAAC,cAAA,oBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAO,cAAA,CAAAL,EAAA,CAAuB;IACvEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAE,cAAA,CAAAD,IAAA,OACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAQ,QAAA,CAAAN,EAAA,CAAiB;IACrDN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAG,QAAA,CAAAC,IAAA,OACF;;;;;;IAkCAb,EAAA,CAAAC,cAAA,eAOC;IAFCD,EAAA,CAAAc,UAAA,wBAAAC,0EAAA;MAAA,MAAAC,WAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAF,OAAA,CAAAG,WAAA,GAAAL,QAAA;IAAA,EAAiC,wBAAAM,0EAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA1B,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAG,OAAA,CAAAF,WAAA,GACL,IAAI;IAAA,EADC;IAGjCxB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAPLH,EAAA,CAAA2B,WAAA,YAAAC,WAAA,CAAyB,UAAAC,MAAA,CAAAC,OAAA,CAAAX,QAAA,YAAAU,MAAA,CAAAE,SAAA,CAAAZ,QAAA,KAAAU,MAAA,CAAAG,QAAA,CAAAb,QAAA;IAMzBnB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAW,QAAA,CAAAc,GAAA,MACF;;;;;;IAIAjC,EAAA,CAAAkC,SAAA,aAAiB;IACjBlC,EAAA,CAAAC,cAAA,cAAgD;IAI5CD,EAAA,CAAAc,UAAA,mBAAAqB,uEAAA;MAAAnC,EAAA,CAAAiB,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAsB,aAAA;MAAA,MAAAgB,GAAA,GAAAtC,EAAA,CAAAuC,WAAA;MAASF,OAAA,CAAAG,cAAA,EAAgB;MAAA,OAAExC,EAAA,CAAAuB,WAAA,CAAAe,GAAA,CAAAG,KAAA,EAAuB;IAAA,EAAC;IAEnDzC,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAc,UAAA,mBAAA4B,uEAAA;MAAA1C,EAAA,CAAAiB,aAAA,CAAAmB,IAAA;MAAApC,EAAA,CAAAsB,aAAA;MAAA,MAAAgB,GAAA,GAAAtC,EAAA,CAAAuC,WAAA;MAAA,OAASvC,EAAA,CAAAuB,WAAA,CAAAe,GAAA,CAAAG,KAAA,EAAuB;IAAA,EAAC;IAEjCzC,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAYXH,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAuC,UAAA,CAAAC,KAAA,CAAsB;IACzE5C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAkC,UAAA,CAAAE,KAAA,OACF;;;;;IAgBF7C,EAAA,CAAAkC,SAAA,gCASyB;;;;IARvBlC,EAAA,CAAAI,UAAA,aAAA0C,OAAA,CAAAC,QAAA,CAAqB,WAAAD,OAAA,CAAAE,MAAA,kBAAAF,OAAA,CAAAG,YAAA,eAAAH,OAAA,CAAAI,SAAA,iBAAAJ,OAAA,CAAAK,WAAA,aAAAL,OAAA,CAAAM,OAAA,WAAAN,OAAA,CAAAO,KAAA,iBAAAP,OAAA,CAAAQ,WAAA;;;;;IAiBvBtD,EAAA,CAAAkC,SAAA,4BASqB;;;;IARnBlC,EAAA,CAAAI,UAAA,aAAAmD,OAAA,CAAAR,QAAA,CAAqB,WAAAQ,OAAA,CAAAP,MAAA,kBAAAO,OAAA,CAAAN,YAAA,eAAAM,OAAA,CAAAL,SAAA,iBAAAK,OAAA,CAAAJ,WAAA,aAAAI,OAAA,CAAAH,OAAA,WAAAG,OAAA,CAAAF,KAAA,iBAAAE,OAAA,CAAAD,WAAA;;;ADvIzC,OAAM,MAAOE,sBAAsB;EA6BjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,eAA+B,EAC/BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,aAAoB,EACnBC,QAAqB,EACtBC,SAAiC;IAXhC,KAAAX,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IAnCX,KAAArB,MAAM,GAAQ,IAAI;IAClB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,SAAS,GAAQ;MAAEoB,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAhD,WAAW,GAAmB,IAAI;IAClC,KAAAiD,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAC,WAAW,GAAY,KAAK,CAAC,CAAC;IAEvB,KAAAxB,WAAW,GAAW,KAAK;IAMlC,KAAAyB,kBAAkB,GAAG,CACnB;MAAE/B,KAAK,EAAE,YAAY;MAAED,KAAK,EAAE;IAAK,CAAE,EACrC;MAAEC,KAAK,EAAE,UAAU;MAAED,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEC,KAAK,EAAE,QAAQ;MAAED,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEC,KAAK,EAAE,WAAW;MAAED,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEC,KAAK,EAAE,WAAW;MAAED,KAAK,EAAE;IAAW,CAAE,CAC3C;IAgBC,IAAI,CAACuB,aAAa,CAACU,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACf,eAAe,CAACgB,IAAI,EAAE;IAC3B,IAAI,CAACf,oBAAoB,CAACgB,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAAC9B,OAAO,GAAG8B,IAAI;MACnB,IAAI,CAACnC,QAAQ,GAAG,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC9C,EAAE;MAClC,IAAI,CAAC6E,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EACAC,KAAK,IAAI;MACRtF,IAAI,CAACuF,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACxB,iBAAiB,CAACyB,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAAC5B,eAAe,CAAC6B,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAAC5B,YAAY,CAAC6B,WAAW,EAAE,CAACb,SAAS,CACtCc,GAAG,IAAI;MACN,IAAI,CAAC1C,KAAK,GAAG0C,GAAG,CAACb,IAAI;IACvB,CAAC,EACAE,KAAK,IAAI;MACRtF,IAAI,CAACuF,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACxB,iBAAiB,CAACyB,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAK,cAAcA,CAACC,MAAW;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACrD,QAAQ,GAAGkD,MAAM;MACtB,IAAI,CAACd,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAAClC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACoD,qBAAqB,EAAE;MAC5BF,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACL,MAAW;IACtBM,OAAO,CAACC,GAAG,CAAC,iBAAiBP,MAAM,EAAE,CAAC;IACtC,IAAI,CAACjD,MAAM,GAAGiD,MAAM;IACpB,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAI,kBAAkBA,CAACR,MAAW;IAC5BM,OAAO,CAACC,GAAG,CAAC,uBAAuBP,MAAM,EAAE,CAAC;IAC5C,IAAI,CAAChD,YAAY,GAAGgD,MAAM;IAC1B,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAK,mBAAmBA,CAACT,MAAW;IAC7BM,OAAO,CAACC,GAAG,CAAC,wBAAwBP,MAAM,EAAE,CAAC;IAC7C,IAAI,CAAC9C,WAAW,GAAG8C,MAAM;IACzB,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAM,eAAeA,CAACvF,IAAa;IAE3B,IAAI,CAAC,IAAI,CAACqD,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClC6B,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAAC/B,QAAQ,GAAGrD,IAAI;MACpB,IAAI,CAACuD,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZtD,IAAI,IACJA,IAAI,CAACwF,KAAK,CAAC,IAAI,CAACnC,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGtD,IAAI;MAClB,IAAI,CAACuD,WAAW,GAAG,KAAK;MACxB,IAAI,CAACkC,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAACtE,KAAK,EAAE;UACtD,IAAI,CAACsE,eAAe,CAACtE,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACL8D,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAAC9B,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAGrD,IAAI;MACpB,IAAI,CAACuD,WAAW,GAAG,IAAI;;EAE3B;EAEAqC,cAAcA,CAAA;IACZ,IAAI,CAACrC,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACoC,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACE,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGN,UAAU,CAAC,MAAK;MACzC,IAAI,CAACQ,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACJ,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACQ,MAAM,IAC5B,CAAC,IAAI,CAACR,eAAe,CAACQ,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAAC5C,WAAW,EAAE;MACpB;;IAGF,MAAM6C,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACT,eAAe,CAACtE,KAAK,EAAE;EAC9B;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAACiC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACkC,eAAe,EAAE;EACxB;EAEAoB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAAC/D,SAAS,CAACgE,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAAChE,QAAQ,CAACkE,OAAO,CAACzI,OAAO,CAAC0I,IAAI,CAACH,MAAM,CAAC,CAAC,GACxDvI,OAAO,CAAC0I,IAAI,CAACH,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEAnG,SAASA,CAACX,IAAa;IACrB,OACE,IAAI,CAACqD,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAAClD,WAAW,IAChBJ,IAAI,CAACwF,KAAK,CAAC,IAAI,CAACnC,QAAQ,CAAC,IACzBrD,IAAI,CAACoH,MAAM,CAAC,IAAI,CAAChH,WAAW,CAAC;EAEjC;EAEAQ,QAAQA,CAACZ,IAAa;IACpB,OAAO,IAAI,CAACsD,MAAM,IAAItD,IAAI,CAACwF,KAAK,CAAC,IAAI,CAACnC,QAAQ,CAAC,IAAIrD,IAAI,CAACoH,MAAM,CAAC,IAAI,CAAC9D,MAAM,CAAC;EAC7E;EAEA5C,OAAOA,CAACV,IAAa;IACnB,OACEA,IAAI,CAACqH,MAAM,CAAC,IAAI,CAAChE,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAItD,IAAI,CAACqH,MAAM,CAAC,IAAI,CAAC/D,MAAM,CAAE,IACzC,IAAI,CAAC1C,QAAQ,CAACZ,IAAI,CAAC,IACnB,IAAI,CAACW,SAAS,CAACX,IAAI,CAAC;EAExB;EAEAsH,iBAAiBA,CAAA;IACfnC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACtD,SAAS,CAAC;EACnD;EAEA2D,eAAeA,CAAA;IACb,IAAI,IAAI,CAACpC,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAMiE,UAAU,GAAG/I,MAAM,CACvB,GAAG,IAAI,CAAC6E,QAAQ,CAACmE,IAAI,IAAI,IAAI,CAACnE,QAAQ,CAACoE,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACtE,QAAQ,CAACxC,GAAG,CAAC6G,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAMC,QAAQ,GAAGpJ,MAAM,CACrB,GAAG,IAAI,CAAC8E,MAAM,CAACkE,IAAI,IAAI,IAAI,CAAClE,MAAM,CAACmE,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACrE,MAAM,CAACzC,GAAG,CAAC6G,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAAC7F,SAAS,CAACoB,UAAU,GAAGqE,UAAU,CAACM,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAAC/F,SAAS,CAACqB,QAAQ,GAAGyE,QAAQ,CAACC,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAACzE,cAAc,GAAG,GAAGmE,UAAU,CAACM,MAAM,CACxC,cAAc,CACf,MAAMD,QAAQ,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACP,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAACjE,QAAQ,EAAE;MACxB,MAAMkE,UAAU,GAAG/I,MAAM,CACvB,GAAG,IAAI,CAAC6E,QAAQ,CAACmE,IAAI,IAAI,IAAI,CAACnE,QAAQ,CAACoE,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACtE,QAAQ,CAACxC,GAAG,CAAC6G,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAAC7F,SAAS,CAACoB,UAAU,GAAGqE,UAAU,CAACM,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAAC/F,SAAS,CAACqB,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAGmE,UAAU,CAACM,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAAC/F,SAAS,CAACoB,UAAU,GAAG,IAAI;MAChC,IAAI,CAACpB,SAAS,CAACqB,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACkE,iBAAiB,EAAE;;EAE5B;EAEAvD,eAAeA,CAAA;IACb,IAAI,IAAI,CAACpC,QAAQ,EAAE;MACjB,IAAI,CAACa,KAAK,CACPsF,GAAG,CACF,GAAGvJ,WAAW,CAACwJ,MAAM,YAAY,IAAI,CAACpG,QAAQ,+BAA+B,CAC9E,CACAkC,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAAC5B,WAAW,GAAG4B,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAE,KAAK,IAAI;QACRtF,IAAI,CAACuF,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACxB,iBAAiB,CAACyB,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAyD,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACzF,MAAM,CAAC8B,OAAO,CAAC,gBAAgB,CAAC;MAClD4D,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEhJ,IAAI,EAAE,IAAI,CAACmD,MAAM,CAAC8B,OAAO,CAAC,MAAM,CAAC;UACjCgE,MAAM,EAAE;SACT,EACD;UACEjJ,IAAI,EAAE,IAAI,CAACmD,MAAM,CAAC8B,OAAO,CAAC,aAAa,CAAC;UACxCgE,MAAM,EAAE;SACT,EACD;UACEjJ,IAAI,EAAE,IAAI,CAACmD,MAAM,CAAC8B,OAAO,CAAC,gBAAgB,CAAC;UAC3CgE,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAAC7E,iBAAiB,EAAE;IACxB,IAAI,CAACe,SAAS,EAAE;EAClB;EAEA+D,WAAWA,CAAA;IACT,IAAI,IAAI,CAACxC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;EAAC,QAAAyC,CAAA;qBA3UUrG,sBAAsB,EAAAxD,EAAA,CAAA8J,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAhK,EAAA,CAAA8J,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAjK,EAAA,CAAA8J,iBAAA,CAAAI,EAAA,CAAAC,UAAA,GAAAnK,EAAA,CAAA8J,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAArK,EAAA,CAAA8J,iBAAA,CAAA9J,EAAA,CAAAsK,SAAA,GAAAtK,EAAA,CAAA8J,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAxK,EAAA,CAAA8J,iBAAA,CAAAW,EAAA,CAAAC,mBAAA,GAAA1K,EAAA,CAAA8J,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAA5K,EAAA,CAAA8J,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAArK,EAAA,CAAA8J,iBAAA,CAAAe,EAAA,CAAAC,KAAA,GAAA9K,EAAA,CAAA8J,iBAAA,CAAAiB,EAAA,CAAAC,WAAA,GAAAhL,EAAA,CAAA8J,iBAAA,CAAAiB,EAAA,CAAAE,sBAAA;EAAA;EAAA,QAAAC,EAAA;UAAtB1H,sBAAsB;IAAA2H,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;uBAGtBvL,wBAAwB;;;;;;;;;;;iBAHxBwL,GAAA,CAAArE,eAAA,CAAAjB,MAAA,CAAuB;QAAA,UAAAjG,EAAA,CAAAwL,iBAAA;;;;;;;;QC1BpCxL,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAAkC,SAAA,4BAAyE;QAEzElC,EAAA,CAAAC,cAAA,iBAAkC;QAQmBD,EAAA,CAAAE,MAAA,IAAsB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrEH,EAAA,CAAAC,cAAA,qBAKoC;QADlCD,EAAA,CAAAc,UAAA,2BAAA2K,oEAAAxF,MAAA;UAAA,OAAAsF,GAAA,CAAAxI,QAAA,GAAAkD,MAAA;QAAA,EAAsB,oBAAAyF,6DAAAzF,MAAA;UAAA,OACZsF,GAAA,CAAAvF,cAAA,CAAAC,MAAA,CAAsB;QAAA,EADV;;QAEtBjG,EAAA,CAAA2L,UAAA,KAAAC,4CAAA,wBAEY;QACd5L,EAAA,CAAAG,YAAA,EAAY;QAGhBH,EAAA,CAAAC,cAAA,cAAsB;QAEyBD,EAAA,CAAAE,MAAA,IAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7EH,EAAA,CAAAC,cAAA,qBAKwC;QADtCD,EAAA,CAAAc,UAAA,2BAAA+K,oEAAA5F,MAAA;UAAA,OAAAsF,GAAA,CAAAtI,YAAA,GAAAgD,MAAA;QAAA,EAA0B,oBAAA6F,6DAAA7F,MAAA;UAAA,OAChBsF,GAAA,CAAA9E,kBAAA,CAAAR,MAAA,CAA0B;QAAA,EADV;;QAE1BjG,EAAA,CAAA2L,UAAA,KAAAI,4CAAA,wBAEY;QACd/L,EAAA,CAAAG,YAAA,EAAY;QAEdH,EAAA,CAAAC,cAAA,eAAsB;QACiBD,EAAA,CAAAE,MAAA,IAAoB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjEH,EAAA,CAAAC,cAAA,qBAKkC;QADhCD,EAAA,CAAAc,UAAA,2BAAAkL,oEAAA/F,MAAA;UAAA,OAAAsF,GAAA,CAAAvI,MAAA,GAAAiD,MAAA;QAAA,EAAoB,oBAAAgG,6DAAAhG,MAAA;UAAA,OACVsF,GAAA,CAAAjF,YAAA,CAAAL,MAAA,CAAoB;QAAA,EADV;;QAEpBjG,EAAA,CAAA2L,UAAA,KAAAO,4CAAA,wBAEY;QACdlM,EAAA,CAAAG,YAAA,EAAY;QAEdH,EAAA,CAAAC,cAAA,eAAsB;QACsBD,EAAA,CAAAE,MAAA,IAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC5EH,EAAA,CAAAC,cAAA,eAAyB;QASrBD,EAAA,CAAAc,UAAA,mBAAAqL,wDAAA;UAAA,OAASZ,GAAA,CAAAvE,cAAA,EAAgB;QAAA,EAAC,wBAAAoF,6DAAAnG,MAAA;UAAA,OAGZsF,GAAA,CAAA5E,eAAA,CAAAV,MAAA,CAAuB;QAAA,EAHX;;QAR5BjG,EAAA,CAAAG,YAAA,EAgBE;QACFH,EAAA,CAAAC,cAAA,eAAgC;QAG5BD,EAAA,CAAAc,UAAA,mBAAAuL,yDAAA;UAAA,OAASd,GAAA,CAAAvE,cAAA,EAAgB;QAAA,EAAC;QAE1BhH,EAAA,CAAAkC,SAAA,aAAqC;QACvClC,EAAA,CAAAG,YAAA,EAAS;QAIbH,EAAA,CAAA2L,UAAA,KAAAW,8CAAA,iCAAAtM,EAAA,CAAAuM,sBAAA,CAWc;QAEdvM,EAAA,CAAA2L,UAAA,KAAAa,8CAAA,iCAAAxM,EAAA,CAAAuM,sBAAA,CAkBc;QAChBvM,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,eAAsB;QACwBD,EAAA,CAAAE,MAAA,IAA4B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAChFH,EAAA,CAAAC,cAAA,qBAKyC;QADvCD,EAAA,CAAAc,UAAA,2BAAA2L,oEAAAxG,MAAA;UAAA,OAAAsF,GAAA,CAAApI,WAAA,GAAA8C,MAAA;QAAA,EAAyB,oBAAAyG,6DAAAzG,MAAA;UAAA,OACfsF,GAAA,CAAA7E,mBAAA,CAAAT,MAAA,CAA2B;QAAA,EADZ;;QAEzBjG,EAAA,CAAA2L,UAAA,KAAAgB,4CAAA,wBAEY;QACd3M,EAAA,CAAAG,YAAA,EAAY;QAOpBH,EAAA,CAAAC,cAAA,eAAkB;QAIVD,EAAA,CAAAkC,SAAA,aAA2C;QAC3ClC,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAA2L,UAAA,KAAAiB,8CAAA,0BAWc;QAChB5M,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAkC;QAE9BD,EAAA,CAAAkC,SAAA,aAAyC;QACzClC,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAA2L,UAAA,KAAAkB,8CAAA,0BAWc;QAChB7M,EAAA,CAAAG,YAAA,EAAK;QAGTH,EAAA,CAAAkC,SAAA,eAA6C;QAC/ClC,EAAA,CAAAG,YAAA,EAAM;;;;;;QA5KUH,EAAA,CAAAO,SAAA,GAA+B;QAA/BP,EAAA,CAAAI,UAAA,kBAAAmL,GAAA,CAAAlC,aAAA,CAA+B;QAUErJ,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAA8M,iBAAA,CAAA9M,EAAA,CAAAS,WAAA,mBAAsB;QAI3DT,EAAA,CAAAO,SAAA,GAA2C;QAA3CP,EAAA,CAAA+M,qBAAA,gBAAA/M,EAAA,CAAAS,WAAA,0BAA2C;QAF3CT,EAAA,CAAAI,UAAA,oBAAmB,gCAAAmL,GAAA,CAAAxI,QAAA;QAKW/C,EAAA,CAAAO,SAAA,GAAU;QAAVP,EAAA,CAAAI,UAAA,YAAAmL,GAAA,CAAAnI,OAAA,CAAU;QAQCpD,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAA8M,iBAAA,CAAA9M,EAAA,CAAAS,WAAA,uBAA0B;QAInET,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAA+M,qBAAA,gBAAA/M,EAAA,CAAAS,WAAA,8BAA+C;QAF/CT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAmL,GAAA,CAAAtI,YAAA;QAKejD,EAAA,CAAAO,SAAA,GAAc;QAAdP,EAAA,CAAAI,UAAA,YAAAmL,GAAA,CAAAjI,WAAA,CAAc;QAMbtD,EAAA,CAAAO,SAAA,GAAoB;QAApBP,EAAA,CAAA8M,iBAAA,CAAA9M,EAAA,CAAAS,WAAA,iBAAoB;QAIvDT,EAAA,CAAAO,SAAA,GAAyC;QAAzCP,EAAA,CAAA+M,qBAAA,gBAAA/M,EAAA,CAAAS,WAAA,wBAAyC;QAFzCT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAmL,GAAA,CAAAvI,MAAA;QAKShD,EAAA,CAAAO,SAAA,GAAQ;QAARP,EAAA,CAAAI,UAAA,YAAAmL,GAAA,CAAAlI,KAAA,CAAQ;QAMIrD,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAA8M,iBAAA,CAAA9M,EAAA,CAAAS,WAAA,uBAA0B;QAKhET,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAA+M,qBAAA,gBAAA/M,EAAA,CAAAS,WAAA,8BAA+C;QAI/CT,EAAA,CAAAI,UAAA,UAAAmL,GAAA,CAAAlH,SAAA,CAAA4E,MAAA,CAAAsC,GAAA,CAAA9G,QAAA,KAAA8G,GAAA,CAAA7G,MAAA,WAAA6G,GAAA,CAAAlH,SAAA,CAAA4E,MAAA,CAAAsC,GAAA,CAAA7G,MAAA,QAAuF,gBAAAsI,GAAA,oBAAAC,GAAA;QAsD/CjN,EAAA,CAAAO,SAAA,IAA4B;QAA5BP,EAAA,CAAA8M,iBAAA,CAAA9M,EAAA,CAAAS,WAAA,yBAA4B;QAItET,EAAA,CAAAO,SAAA,GAA2C;QAA3CP,EAAA,CAAA+M,qBAAA,gBAAA/M,EAAA,CAAAS,WAAA,0BAA2C;QAF3CT,EAAA,CAAAI,UAAA,qBAAoB,gCAAAmL,GAAA,CAAApI,WAAA;QAKUnD,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAI,UAAA,YAAAmL,GAAA,CAAA3G,kBAAA,CAAqB;QAerD5E,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,4BACF;QAiBET,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,+BACF;QAgBDT,EAAA,CAAAO,SAAA,GAAoB;QAApBP,EAAA,CAAAI,UAAA,iBAAA8M,GAAA,CAAoB", "names": ["environment", "moment", "NgbDate", "<PERSON><PERSON>", "LeagueTableViewComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "season_r12", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "name", "tournament_r13", "club_r14", "code", "ɵɵlistener", "LeagueReportsComponent_ng_template_42_Template_span_mouseenter_0_listener", "restoredCtx", "ɵɵrestoreView", "_r18", "date_r15", "date", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "hoveredDate", "LeagueReportsComponent_ng_template_42_Template_span_mouseleave_0_listener", "ctx_r19", "ɵɵclassProp", "focused_r16", "ctx_r5", "isRange", "isHovered", "isInside", "day", "ɵɵelement", "LeagueReportsComponent_ng_template_44_Template_button_click_2_listener", "_r21", "ctx_r20", "_r3", "ɵɵreference", "clearDateRange", "close", "LeagueReportsComponent_ng_template_44_Template_button_click_4_listener", "status_r23", "value", "label", "ctx_r10", "seasonId", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "matchStatus", "seasons", "clubs", "tournaments", "ctx_r11", "LeagueReportsComponent", "constructor", "route", "_router", "_http", "_trans", "renderer", "_loadingService", "_registrationService", "_clubService", "_translateService", "_titleService", "calendar", "formatter", "start_date", "end_date", "dateRangeValue", "fromDate", "toDate", "isSelecting", "matchStatusOptions", "setTitle", "_getCurrentSeason", "show", "getAllSeasonActive", "subscribe", "data", "_getTournaments", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "instant", "dismiss", "_getClubs", "getAllClubs", "res", "onSelectSeason", "$event", "Promise", "resolve", "reject", "refreshChildDataTable", "onSelectClub", "console", "log", "onSelectTournament", "onSelectMatchStatus", "onDateSelection", "after", "updateDateRange", "setTimeout", "dateRangePicker", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "from", "before", "equals", "onDateRangeChange", "fromMoment", "year", "month", "toString", "padStart", "toMoment", "format", "get", "apiUrl", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "isLink", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "HttpClient", "i3", "TranslateService", "Renderer2", "i4", "LoadingService", "i5", "RegistrationService", "i6", "ClubService", "i7", "Title", "i8", "NgbCalendar", "NgbDateParserFormatter", "_2", "selectors", "viewQuery", "LeagueReportsComponent_Query", "rf", "ctx", "ɵɵresolveDocument", "LeagueReportsComponent_Template_ng_select_ngModelChange_13_listener", "LeagueReportsComponent_Template_ng_select_change_13_listener", "ɵɵtemplate", "LeagueReportsComponent_ng_option_15_Template", "LeagueReportsComponent_Template_ng_select_ngModelChange_21_listener", "LeagueReportsComponent_Template_ng_select_change_21_listener", "LeagueReportsComponent_ng_option_23_Template", "LeagueReportsComponent_Template_ng_select_ngModelChange_28_listener", "LeagueReportsComponent_Template_ng_select_change_28_listener", "LeagueReportsComponent_ng_option_30_Template", "LeagueReportsComponent_Template_input_click_36_listener", "LeagueReportsComponent_Template_input_dateSelect_36_listener", "LeagueReportsComponent_Template_button_click_40_listener", "LeagueReportsComponent_ng_template_42_Template", "ɵɵtemplateRefExtractor", "LeagueReportsComponent_ng_template_44_Template", "LeagueReportsComponent_Template_ng_select_ngModelChange_50_listener", "LeagueReportsComponent_Template_ng_select_change_50_listener", "LeagueReportsComponent_ng_option_52_Template", "LeagueReportsComponent_ng_template_61_Template", "LeagueReportsComponent_ng_template_67_Template", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate", "_r4", "_r6", "_r9"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  HostListener,\r\n  OnD<PERSON>roy\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\r\nimport Swal from 'sweetalert2';\r\nimport { LeagueTableViewComponent } from './league-table-view/league-table-view.component';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit, OnD<PERSON>roy\r\n{\r\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\r\n  @ViewChild(LeagueTableViewComponent, { static: false }) leagueTableViewComponent: LeagueTableViewComponent;\r\n\r\n  public seasonId: any;\r\n  public clubId: any = null;\r\n  public tournamentId: any = null;\r\n  public dateRange: any = { start_date: null, end_date: null };\r\n  public dateRangeValue: string = '';\r\n  hoveredDate: NgbDate | null = null;\r\n  fromDate: NgbDate | null = null;\r\n  toDate: NgbDate | null = null;\r\n  isSelecting: boolean = false; // Track if we're in the middle of selecting range\r\n  private clickOutsideTimeout: any;\r\n  public matchStatus: string = 'all';\r\n  public contentHeader: object;\r\n  public seasons: any[];\r\n  public clubs: any[];\r\n  public tournaments: any[];\r\n\r\n  matchStatusOptions = [\r\n    { label: 'All Status', value: 'all' },\r\n    { label: 'Upcoming', value: 'upcoming' },\r\n    { label: 'Active', value: 'active' },\r\n    { label: 'Completed', value: 'completed' },\r\n    { label: 'Cancelled', value: 'cancelled' },\r\n  ];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _loadingService: LoadingService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _titleService: Title,\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event: any) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshChildDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event: any) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectTournament($event: any) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event: any) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <section id=\"league-reports-page\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <!-- Filters Section -->\r\n          <div class=\"card mb-2\">\r\n            <div class=\"card-body\">\r\n              <div class=\"row mb-1\">\r\n                <div class=\"col-12\">\r\n                  <label for=\"season\" class=\"form-label\">{{'Season'|translate}}</label>\r\n                  <ng-select\r\n                    [searchable]=\"true\"\r\n                    [clearable]=\"false\"\r\n                    placeholder=\"{{'Select Season'|translate}}\"\r\n                    [(ngModel)]=\"seasonId\"\r\n                    (change)=\"onSelectSeason($event)\">\r\n                    <ng-option *ngFor=\"let season of seasons\" [value]=\"season.id\">\r\n                      {{ season.name | translate }}\r\n                    </ng-option>\r\n                  </ng-select>\r\n                </div>\r\n              </div>\r\n              <div class=\"row mb-1\">\r\n                <div class=\"col-md-3\">\r\n                  <label for=\"tournament\" class=\"form-label\">{{'Tournament'|translate}}</label>\r\n                  <ng-select\r\n                    [searchable]=\"true\"\r\n                    [clearable]=\"true\"\r\n                    placeholder=\"{{'Select Tournament'|translate}}\"\r\n                    [(ngModel)]=\"tournamentId\"\r\n                    (change)=\"onSelectTournament($event)\">\r\n                    <ng-option *ngFor=\"let tournament of tournaments\" [value]=\"tournament.id\">\r\n                      {{ tournament.name | translate }}\r\n                    </ng-option>\r\n                  </ng-select>\r\n                </div>\r\n                <div class=\"col-md-3\">\r\n                  <label for=\"club\" class=\"form-label\">{{'Club'|translate}}</label>\r\n                  <ng-select\r\n                    [searchable]=\"true\"\r\n                    [clearable]=\"true\"\r\n                    placeholder=\"{{'Select Club'|translate}}\"\r\n                    [(ngModel)]=\"clubId\"\r\n                    (change)=\"onSelectClub($event)\">\r\n                    <ng-option *ngFor=\"let club of clubs\" [value]=\"club.id\">\r\n                      {{ club.code | translate }}\r\n                    </ng-option>\r\n                  </ng-select>\r\n                </div>\r\n                <div class=\"col-md-3\">\r\n                  <label for=\"dateRange\" class=\"form-label\">{{'Date Range'|translate}}</label>\r\n                  <div class=\"input-group\">\r\n                    <input\r\n                      name=\"daterange\"\r\n                      class=\"form-control\"\r\n                      placeholder=\"{{'Select Date Range'|translate}}\"\r\n                      ngbDatepicker\r\n                      readonly\r\n                      #dateRangePicker=\"ngbDatepicker\"\r\n                      [value]=\"formatter.format(fromDate) + (toDate ? ' - ' + formatter.format(toDate) : '')\"\r\n                      (click)=\"openDatePicker()\"\r\n                      [dayTemplate]=\"dayTemplate\"\r\n                      [footerTemplate]=\"footerTemplate\"\r\n                      (dateSelect)=\"onDateSelection($event)\"\r\n                      [firstDayOfWeek]=\"1\"\r\n                      [displayMonths]=\"2\"\r\n                      outsideDays=\"hidden\"\r\n                      [autoClose]=\"false\"\r\n                    />\r\n                    <div class=\"input-group-append\">\r\n                      <button\r\n                        class=\"btn btn-outline-secondary\"\r\n                        (click)=\"openDatePicker()\"\r\n                        type=\"button\">\r\n                        <i class=\"feather icon-calendar\"></i>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <ng-template #dayTemplate let-date=\"date\" let-focused=\"focused\">\r\n                    <span\r\n                      class=\"custom-day\"\r\n                      [class.focused]=\"focused\"\r\n                      [class.range]=\"isRange(date)\"\r\n                      [class.faded]=\"isHovered(date) || isInside(date)\"\r\n                      (mouseenter)=\"hoveredDate = date\"\r\n                      (mouseleave)=\"hoveredDate = null\"\r\n                    >\r\n                      {{ date.day }}\r\n                    </span>\r\n                  </ng-template>\r\n\r\n                  <ng-template #footerTemplate>\r\n                    <hr class=\"my-0\">\r\n                    <div class=\"d-flex justify-content-between p-2\">\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"btn btn-outline-secondary btn-sm\"\r\n                        (click)=\"clearDateRange(); dateRangePicker.close()\"\r\n                      >\r\n                        Clear\r\n                      </button>\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"btn btn-primary btn-sm\"\r\n                        (click)=\"dateRangePicker.close()\"\r\n                      >\r\n                        Close\r\n                      </button>\r\n                    </div>\r\n                  </ng-template>\r\n                </div>\r\n                <div class=\"col-md-3\">\r\n                  <label for=\"matchStatus\" class=\"form-label\">{{'Match Status'|translate}}</label>\r\n                  <ng-select\r\n                    [searchable]=\"false\"\r\n                    [clearable]=\"false\"\r\n                    placeholder=\"{{'Select Status'|translate}}\"\r\n                    [(ngModel)]=\"matchStatus\"\r\n                    (change)=\"onSelectMatchStatus($event)\">\r\n                    <ng-option *ngFor=\"let status of matchStatusOptions\" [value]=\"status.value\">\r\n                      {{ status.label | translate }}\r\n                    </ng-option>\r\n                  </ng-select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Tabs Section -->\r\n          <div class=\"card\">\r\n            <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs m-0\">\r\n              <li ngbNavItem=\"league_table\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-table-list mr-1\"></i>\r\n                  {{ 'Table View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-league-table-view\r\n                    [seasonId]=\"seasonId\"\r\n                    [clubId]=\"clubId\"\r\n                    [tournamentId]=\"tournamentId\"\r\n                    [dateRange]=\"dateRange\"\r\n                    [matchStatus]=\"matchStatus\"\r\n                    [seasons]=\"seasons\"\r\n                    [clubs]=\"clubs\"\r\n                    [tournaments]=\"tournaments\"\r\n                  ></app-league-table-view>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem=\"schedule_matches\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-calendar mr-1\"></i>\r\n                  {{ 'Schedule View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-schedule-view\r\n                    [seasonId]=\"seasonId\"\r\n                    [clubId]=\"clubId\"\r\n                    [tournamentId]=\"tournamentId\"\r\n                    [dateRange]=\"dateRange\"\r\n                    [matchStatus]=\"matchStatus\"\r\n                    [seasons]=\"seasons\"\r\n                    [clubs]=\"clubs\"\r\n                    [tournaments]=\"tournaments\"\r\n                  ></app-schedule-view>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}