{"ast": null, "code": "import { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport Swal from 'sweetalert2';\nimport { AppConfig } from '../../../app-config';\nimport moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/tournament.service\";\nimport * as i3 from \"../../../services/stage.service\";\nimport * as i4 from \"../../../services/loading.service\";\nimport * as i5 from \"@angular/platform-browser\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"../../../services/auto-schedule.service\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"ngx-toastr\";\nconst _c0 = [\"modalSetupSchedule\"];\nconst _c1 = [\"modalEditSchedule\"];\nconst _c2 = [\"modalCrudBreak\"];\nconst _c3 = [\"modalUpdateMatch\"];\nconst _c4 = [\"modalRefereeList\"];\nfunction AutoScheduleComponent_ng_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", date_r27);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", date_r27, \" \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"button\", 51);\n    i0.ɵɵelement(2, \"i\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 53)(4, \"a\", 54);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_7_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const locationKey_r29 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r33.editSchedule(locationKey_r29, (tmp_b_0 = ctx_r33.getConfig(ctx_r33.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵtext(5, \" Edit Plan \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 54);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_7_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const locationKey_r29 = i0.ɵɵnextContext().$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.deletePlan(locationKey_r29));\n    });\n    i0.ɵɵtext(7, \" Delete Plan \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_16_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_16_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 64);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r38 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(3);\n    const _r25 = i0.ɵɵreference(56);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r25)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, item_r38));\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 65)(2, \"p\", 66);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 67);\n    i0.ɵɵelement(5, \"i\", 68);\n    i0.ɵɵelementStart(6, \"p\", 69);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r38 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r38.description || \"Break time\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r38.break_durations, \" mins \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const item_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r48.onEditMatch(item_r38));\n    });\n    i0.ɵɵelement(2, \"i\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const item_r38 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r29 = i0.ɵɵnextContext().$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r51.onUnscheduleTimeSlot(item_r38, (tmp_b_0 = ctx_r51.getConfig(ctx_r51.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵelement(6, \"i\", 72);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"Update Match Referees\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"Unschedule Match\"), \" \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const item_r38 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r29 = i0.ɵɵnextContext().$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r54.onEditEventTime(item_r38, (tmp_b_0 = ctx_r54.getConfig(ctx_r54.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵelement(2, \"i\", 73);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const item_r38 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r29 = i0.ɵɵnextContext().$implicit;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r58.onUnscheduleTimeSlot(item_r38, (tmp_b_0 = ctx_r58.getConfig(ctx_r58.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵelement(6, \"i\", 72);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"Edit Event Time\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"Delete Event\"), \" \");\n  }\n}\nconst _c6 = function (a0, a1) {\n  return {\n    \"conflict-border\": a0,\n    \"warning-border\": a1\n  };\n};\nfunction AutoScheduleComponent_div_22_ng_container_2_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_16_Template_div_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const item_r38 = restoredCtx.$implicit;\n      const ctx_r61 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(!ctx_r61.isLock && ctx_r61.toggleDropdown($event, item_r38));\n    });\n    i0.ɵɵtemplate(1, AutoScheduleComponent_div_22_ng_container_2_div_16_button_1_Template, 2, 0, \"button\", 56);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_div_22_ng_container_2_div_16_button_2_Template, 2, 0, \"button\", 57);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_3_Template, 2, 4, \"ng-container\", 19);\n    i0.ɵɵtemplate(4, AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_4_Template, 8, 2, \"ng-container\", 19);\n    i0.ɵɵelementStart(5, \"div\", 58)(6, \"div\", 59);\n    i0.ɵɵtemplate(7, AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_7_Template, 9, 6, \"ng-container\", 19);\n    i0.ɵɵtemplate(8, AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_8_Template, 9, 6, \"ng-container\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r38 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    let tmp_4_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c6, ctx_r31.isMatchHasConflict(item_r38.id, \"match\"), ctx_r31.isWarningMatch(item_r38.id) && !ctx_r31.isMatchHasConflict(item_r38.id, \"match\")))(\"data_stage_id\", item_r38.stage_id)(\"data_time_slot_id\", item_r38.time_slot_id)(\"data_type\", item_r38.type)(\"data_break_durations\", (tmp_4_0 = item_r38.break_durations) !== null && tmp_4_0 !== undefined ? tmp_4_0 : 0)(\"cdkDragDisabled\", ctx_r31.isLock);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.isMatchHasConflict(item_r38.id, \"match\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.isWarningMatch(item_r38.id) && !ctx_r31.isMatchHasConflict(item_r38.id, \"match\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r38.type === \"match\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r38.type === \"break\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"visible\", ctx_r31.isDropdownOpen(item_r38));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r38.type === \"match\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r38.type === \"break\");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_footer_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"footer\", 74)(1, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_footer_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const locationKey_r29 = i0.ɵɵnextContext().$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r63.openModalAddBreak(locationKey_r29, ctx_r63.listMatches[locationKey_r29][ctx_r63.listMatches[locationKey_r29].length - 1] == null ? null : ctx_r63.listMatches[locationKey_r29][ctx_r63.listMatches[locationKey_r29].length - 1].time_slot_id, (tmp_b_0 = ctx_r63.getConfig(ctx_r63.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵelement(2, \"i\", 76);\n    i0.ɵɵtext(3, \" Add event / break \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 41)(2, \"div\", 42)(3, \"header\", 23)(4, \"div\", 43)(5, \"p\", 44);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AutoScheduleComponent_div_22_ng_container_2_div_7_Template, 8, 0, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 46)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 47);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function AutoScheduleComponent_div_22_ng_container_2_Template_div_cdkDropListDropped_15_listener($event) {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r66.drop($event));\n    });\n    i0.ɵɵtemplate(16, AutoScheduleComponent_div_22_ng_container_2_div_16_Template, 9, 17, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, AutoScheduleComponent_div_22_ng_container_2_footer_17_Template, 4, 0, \"footer\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const locationKey_r29 = ctx.$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    let tmp_2_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", locationKey_r29, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r28.isLock);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r28.getShortTime(((tmp_2_0 = ctx_r28.getConfig(ctx_r28.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_2_0.begin_date) + \" \" + ((tmp_2_0 = ctx_r28.getConfig(ctx_r28.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_2_0.begin_time) || \"\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r28.getShortTime(((tmp_3_0 = ctx_r28.getConfig(ctx_r28.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_3_0.begin_date) + \" \" + ((tmp_3_0 = ctx_r28.getConfig(ctx_r28.responseMetadata[\"locations\"][locationKey_r29].id)) == null ? null : tmp_3_0.end_time) || \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r28.listMatches[locationKey_r29])(\"id\", locationKey_r29)(\"data_location_id\", ctx_r28.responseMetadata[\"locations\"][locationKey_r29].id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r28.listMatches[locationKey_r29]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r28.isLock);\n  }\n}\nfunction AutoScheduleComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_div_22_ng_container_2_Template, 18, 9, \"ng-container\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.listLocationIds);\n  }\n}\nfunction AutoScheduleComponent_ng_container_23_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_23_ng_container_1_Template, 1, 0, \"ng-container\", 77);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r17 = i0.ɵɵreference(48);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r17);\n  }\n}\nfunction AutoScheduleComponent_ng_container_24_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_24_ng_container_1_Template, 1, 0, \"ng-container\", 77);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r19 = i0.ɵɵreference(50);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r19);\n  }\n}\nfunction AutoScheduleComponent_ng_container_25_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_25_ng_container_1_Template, 1, 0, \"ng-container\", 77);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r21 = i0.ɵɵreference(52);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r21);\n  }\n}\nfunction AutoScheduleComponent_ng_container_35_div_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_35_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_35_div_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 64);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r72 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(2);\n    const _r23 = i0.ɵɵreference(54);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r23)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, item_r72));\n  }\n}\nfunction AutoScheduleComponent_ng_container_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_35_div_1_ng_container_1_Template, 2, 4, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r72 = ctx.$implicit;\n    const ctx_r71 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data_stage_id\", item_r72.stage_id)(\"data_time_slot_id\", item_r72.time_slot_id)(\"data_type\", item_r72.type)(\"cdkDragDisabled\", ctx_r71.isLock);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r72.type === \"match\");\n  }\n}\nfunction AutoScheduleComponent_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_35_div_1_Template, 2, 5, \"div\", 78);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.listUnScheduledMatches);\n  }\n}\nfunction AutoScheduleComponent_ng_container_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 80)(2, \"p\", 81);\n    i0.ɵɵtext(3, \" No matches found for this tournament. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_container_36_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.autoGenerate());\n    });\n    i0.ɵɵelement(5, \"i\", 83);\n    i0.ɵɵtext(6, \" Auto Generate \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AutoScheduleComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-setup-schedule\", 84);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_37_Template_app_modal_setup_schedule_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r80);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"seasonId\", ctx_r8.seasonId)(\"tournamentId\", ctx_r8.tournamentId)(\"tournamentInfo\", ctx_r8.tournamentInfo);\n  }\n}\nfunction AutoScheduleComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-update-config\", 85);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_39_Template_app_modal_update_config_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selectedConfig\", ctx_r10.selectedConfig);\n  }\n}\nfunction AutoScheduleComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-crud-break\", 86);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_41_Template_app_modal_crud_break_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r86);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"breakModalParams\", ctx_r12.breakModalParams);\n  }\n}\nfunction AutoScheduleComponent_ng_template_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-update-match\", 87);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_43_Template_app_modal_update_match_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r88 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r88.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"timeSlotInfo\", ctx_r14.selectedItem)(\"seasonId\", ctx_r14.seasonId);\n  }\n}\nconst _c7 = function (a0) {\n  return {\n    \"text-danger\": a0\n  };\n};\nfunction AutoScheduleComponent_ng_template_45_div_9_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r92 = i0.ɵɵnextContext().$implicit;\n    const ctx_r93 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c7, ctx_r93.isMatchHasConflict(ctx_r93.selectedMatchId, \"referee\", ref_r92.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ref_r92.user == null ? null : ref_r92.user.first_name, \" \", ref_r92.user == null ? null : ref_r92.user.last_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_45_div_9_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r92 = i0.ɵɵnextContext().$implicit;\n    const ctx_r94 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c7, ctx_r94.isMatchHasConflict(ctx_r94.selectedMatchId, \"referee\", ref_r92.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ref_r92.referee_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_45_div_9_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 99);\n    i0.ɵɵelement(1, \"i\", 100);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutoScheduleComponent_ng_template_45_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_ng_template_45_div_9_span_2_Template, 2, 5, \"span\", 97);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_ng_template_45_div_9_span_3_Template, 2, 4, \"span\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AutoScheduleComponent_ng_template_45_div_9_button_4_Template, 2, 0, \"button\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r92 = ctx.$implicit;\n    const ctx_r91 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ref_r92.referee_type == \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ref_r92.referee_type == \"freetext\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r91.isMatchHasConflict(ctx_r91.selectedMatchId, \"referee\", ref_r92.id));\n  }\n}\nfunction AutoScheduleComponent_ng_template_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"h4\", 89);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_45_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r99);\n      const modal_r90 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(modal_r90.dismiss());\n    });\n    i0.ɵɵelementStart(5, \"span\", 91);\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 92)(8, \"div\", 93);\n    i0.ɵɵtemplate(9, AutoScheduleComponent_ng_template_45_div_9_Template, 5, 3, \"div\", 94);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"Match Referees\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.selectedMatchReferees);\n  }\n}\nfunction AutoScheduleComponent_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"div\", 102)(2, \"p\", 103);\n    i0.ɵɵtext(3, \"No Plan Created\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 104);\n    i0.ɵɵtext(5, \" Please enter the necessary information to allow the system to generate an accurate schedule based on your requirements. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_47_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r101);\n      const ctx_r100 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r100.openModalSetupSchedule());\n    });\n    i0.ɵɵtext(7, \" Setup \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_ng_template_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 102)(2, \"p\", 103);\n    i0.ɵɵtext(3, \"Fetching\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 104);\n    i0.ɵɵtext(5, \" Waiting for getting schedule data... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"div\", 102)(2, \"p\", 103);\n    i0.ɵɵtext(3, \"Scheduling...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 104);\n    i0.ɵɵtext(5, \" Scheduling is being processed in the background due to the large number of matches. Please wait approximately 5 to 15 minutes for completion. You will be notified when the process is finished. \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r104 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"p\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 108)(4, \"div\", 109);\n    i0.ɵɵelement(5, \"img\", 110);\n    i0.ɵɵelementStart(6, \"span\", 111);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_53_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r104);\n      const item_r102 = restoredCtx.$implicit;\n      const ctx_r103 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r103.swapTeam(item_r102.match));\n    });\n    i0.ɵɵelement(9, \"i\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 114)(11, \"span\", 111);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"img\", 115);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r102 = ctx.$implicit;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r102.match.round_name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_1_0 = item_r102.match == null ? null : item_r102.match.home_team == null ? null : item_r102.match.home_team.name) !== null && tmp_1_0 !== undefined ? tmp_1_0 : \"TBD\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_2_0 = item_r102.match == null ? null : item_r102.match.away_team == null ? null : item_r102.match.away_team.name) !== null && tmp_2_0 !== undefined ? tmp_2_0 : \"TBD\");\n  }\n}\nfunction AutoScheduleComponent_ng_template_55_div_22_div_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r109 = i0.ɵɵnextContext().$implicit;\n    const item_r105 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r111 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c7, ctx_r111.isMatchHasConflict(item_r105.id, \"referee\", ref_r109.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ref_r109.user == null ? null : ref_r109.user.first_name, \" \", ref_r109.user == null ? null : ref_r109.user.last_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_55_div_22_div_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r109 = i0.ɵɵnextContext().$implicit;\n    const item_r105 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r112 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c7, ctx_r112.isMatchHasConflict(item_r105.id, \"referee\", ref_r109.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ref_r109.referee_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_55_div_22_div_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutoScheduleComponent_ng_template_55_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 124);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_ng_template_55_div_22_div_2_span_2_Template, 2, 5, \"span\", 97);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_ng_template_55_div_22_div_2_span_3_Template, 2, 4, \"span\", 97);\n    i0.ɵɵtemplate(4, AutoScheduleComponent_ng_template_55_div_22_div_2_span_4_Template, 2, 0, \"span\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ref_r109 = ctx.$implicit;\n    const last_r110 = ctx.last;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ref_r109.referee_type == \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ref_r109.referee_type == \"freetext\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r110);\n  }\n}\nfunction AutoScheduleComponent_ng_template_55_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r120 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"span\", 126);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_55_div_22_div_3_Template_span_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r120);\n      const item_r105 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r118 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r118.toggleRefereeExpansion(item_r105.id, $event));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r105 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r108 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r108.getRemainingRefereesCount(item_r105.referees, item_r105.id), \" more \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_55_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119);\n    i0.ɵɵelement(1, \"i\", 120);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_ng_template_55_div_22_div_2_Template, 5, 3, \"div\", 121);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_ng_template_55_div_22_div_3_Template, 3, 1, \"div\", 122);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r105 = i0.ɵɵnextContext().$implicit;\n    const ctx_r106 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r106.getDisplayedReferees(item_r105.referees, item_r105.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r106.getRemainingRefereesCount(item_r105.referees, item_r105.id) > 0);\n  }\n}\nfunction AutoScheduleComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r124 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"p\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 108)(4, \"div\", 109);\n    i0.ɵɵelement(5, \"img\", 110);\n    i0.ɵɵelementStart(6, \"span\", 116);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_55_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r124);\n      const item_r105 = restoredCtx.$implicit;\n      const ctx_r123 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r123.swapTeam(item_r105.match));\n    });\n    i0.ɵɵelement(9, \"i\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 114)(11, \"span\", 116);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"img\", 115);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 117);\n    i0.ɵɵelement(15, \"i\", 68);\n    i0.ɵɵelementStart(16, \"p\", 66);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 66);\n    i0.ɵɵtext(19, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 66);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, AutoScheduleComponent_ng_template_55_div_22_Template, 4, 2, \"div\", 118);\n  }\n  if (rf & 2) {\n    const item_r105 = ctx.$implicit;\n    const ctx_r26 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r105.match.round_name, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c7, ctx_r26.isMatchHasConflict(item_r105.id, \"team\", item_r105.match.home_team_id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = item_r105.match.home_team == null ? null : item_r105.match.home_team.name) !== null && tmp_2_0 !== undefined ? tmp_2_0 : \"TBD\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c7, ctx_r26.isMatchHasConflict(item_r105.id, \"team\", item_r105.match.away_team_id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_4_0 = item_r105.match.away_team == null ? null : item_r105.match.away_team.name) !== null && tmp_4_0 !== undefined ? tmp_4_0 : \"TBD\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.getShortTime(item_r105.start_time), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.getShortTime(item_r105.end_time), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r105.referees && item_r105.referees.length > 0);\n  }\n}\nconst _c8 = function (a0, a1) {\n  return {\n    \"btn-outline-primary\": a0,\n    \"btn-primary\": a1\n  };\n};\nexport class AutoScheduleComponent {\n  constructor(_route, _router, _tournamentService, _stageService, _loadingService, _titleService, _translateService, _autoScheduleService, _modalService, _toastService) {\n    this._route = _route;\n    this._router = _router;\n    this._tournamentService = _tournamentService;\n    this._stageService = _stageService;\n    this._loadingService = _loadingService;\n    this._titleService = _titleService;\n    this._translateService = _translateService;\n    this._autoScheduleService = _autoScheduleService;\n    this._modalService = _modalService;\n    this._toastService = _toastService;\n    this.leagueOrGroupStageId = null;\n    this.dateOptions = [];\n    this.listLocations = {};\n    this.listMatches = {};\n    this.listLocationIds = [];\n    this.responseMetadata = {};\n    this.responseMatches = {};\n    this.teamConflicts = {};\n    this.refereeConflicts = {};\n    this.stageConflicts = {};\n    this.overEndTime = [];\n    this.selectedConfig = null;\n    this.isFetching = true;\n    this.isLock = false;\n    this.hasMatches = false;\n    this.isScheduling = true;\n    // public listUnScheduledMatches = [\n    // ];\n    this.listUnScheduledMatches = [];\n    this.selectedDate = null;\n    this.hasPlan = false;\n    // Simple dropdown state - track which dropdown is open\n    this.activeDropdownId = null;\n    // Referee expansion state\n    this.expandedRefereeMatches = new Set();\n    this.selectedMatchReferees = [];\n    this.selectedMatchId = null;\n    this.breakModalParams = {\n      locationId: null,\n      tournamentId: null,\n      timeSlotId: null,\n      lastTimeSlotId: null,\n      configId: null,\n      maxBreakDuration: null\n    };\n    this.selectedItem = null;\n    this.location = location;\n    this.isFetching = true;\n    this.tournamentId = this._route.snapshot.paramMap.get('tournament_id');\n    this._tournamentService.getTournament(this.tournamentId).subscribe(res => {\n      this.tournamentInfo = res;\n      this.leagueOrGroupStageId = res.stages.find(stage => {\n        if (this.tournamentInfo && this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\n          return stage.type === AppConfig.TOURNAMENT_TYPES.groups;\n        } else {\n          return stage.type === AppConfig.TOURNAMENT_TYPES.league;\n        }\n      })?.id;\n      this.isLock = res.is_locked_schedule === 1;\n      this.seasonId = res.group.season.id;\n      _titleService.setTitle(res.name);\n      this.contentHeader = {\n        headerTitle: res.name,\n        actionButton: false,\n        breadcrumb: {\n          type: '',\n          links: [{\n            name: this._translateService.instant('Leagues'),\n            isLink: false\n          }, {\n            name: this._translateService.instant('Manage Leagues'),\n            isLink: true,\n            link: '/leagues/manage'\n          }, {\n            name: res.name,\n            isLink: false\n          }, {\n            name: this._translateService.instant('Auto Schedule'),\n            isLink: false\n          }]\n        }\n      };\n      this.getListDates();\n    });\n  }\n  ngOnInit() {}\n  ngAfterViewChecked() {\n    setTimeout(() => {\n      this.hasMatches = this.responseMatches && Object.keys(this.responseMatches).length > 0 || this.listUnScheduledMatches.length > 0;\n    }, 0);\n  }\n  getListDates() {\n    this._autoScheduleService.getListDates(this.tournamentId).subscribe(res => {\n      this.dateOptions = res['data'];\n      this.selectedDate = this.dateOptions.length > 0 ? this.dateOptions[0] : [];\n      this.hasPlan = this.dateOptions.length > 0;\n      this.onScheduleAction(null, true);\n    });\n  }\n  getScheduleMatches(showLoading = true) {\n    if (showLoading) {\n      this._loadingService.show();\n    }\n    if (this.selectedDate.length === 0) {\n      this._loadingService.dismiss();\n      this.isFetching = false;\n      this.isScheduling = false;\n      return;\n    }\n    this._autoScheduleService.getScheduleMatches(this.tournamentId, this.selectedDate).subscribe(res => {\n      this.isScheduling = res.schedule_status === 'scheduling';\n      console.log('this.isScheduling', this.isScheduling);\n      if (this.isScheduling) {\n        this._loadingService.dismiss();\n        this.listMatches = {};\n        this.listLocationIds = [];\n        this.overEndTime = [];\n        this.isFetching = false;\n        return;\n      }\n      this.responseMetadata = res.metadata;\n      this.responseMatches = Array.isArray(res.data) && res.data.length === 0 ? null : res.data;\n      this.overEndTime = res.over_end_time;\n      this.teamConflicts = res.conflicts['team_scheduling_conflict'];\n      this.refereeConflicts = res.conflicts['referee_scheduling_conflict'];\n      this.stageConflicts = res.conflicts['stage_scheduling_conflict'];\n      if (this.responseMatches) {\n        this.mapListLocations();\n      } else {\n        this.listLocationIds = [];\n        this.overEndTime = [];\n        this.listMatches = {};\n        this.selectedDate = null;\n      }\n      this._loadingService.dismiss();\n    }, () => {}, () => {\n      this.isFetching = false;\n    });\n  }\n  getUnScheduleMatches(showLoading = true) {\n    if (showLoading) {\n      this._loadingService.show();\n    }\n    this._autoScheduleService.getListUnScheduledMatches(this.tournamentId).subscribe(res => {\n      this.listUnScheduledMatches = res.data;\n      this._loadingService.dismiss();\n    });\n  }\n  mapListLocations() {\n    this.listLocationIds = [];\n    this.listMatches = {};\n    this.listLocations = this.responseMatches[this.selectedDate] || {};\n    if (!this.listLocations || !this.selectedDate) return;\n    Object.keys(this.listLocations).forEach(locationName => {\n      if (!this.listLocationIds.includes(`${locationName}`)) {\n        this.listLocationIds.push(`${locationName}`);\n      }\n      if (!this.listMatches[locationName]) {\n        this.listMatches[locationName] = [];\n      }\n      this.listMatches[locationName] = [...this.listMatches[locationName], ...this.listLocations[locationName]];\n    });\n  }\n  onSelectDate(event) {\n    this.selectedDate = event;\n    this.onScheduleAction(null, true);\n  }\n  openModalSetupSchedule() {\n    this._modalService.open(this.modalSetupSchedule, {\n      centered: true,\n      size: 'lg'\n    });\n  }\n  onScheduleAction(action, showLoading = true) {\n    if (action === 'fetchDates') {\n      this.getListDates();\n    } else {\n      this.getScheduleMatches(showLoading);\n      this.getUnScheduleMatches(showLoading);\n    }\n  }\n  unScheduleMatch(timeSlotId, configId, onError, onSuccess) {\n    this._autoScheduleService.unScheduleMatch(timeSlotId, configId).subscribe(res => {\n      this.onScheduleAction(null, false);\n      onSuccess && onSuccess();\n    }, error => {\n      onError();\n    });\n  }\n  mapNewSlotIndex(locationKey) {\n    const newSlotIndex = {};\n    this.listMatches[locationKey].forEach((item, index) => {\n      newSlotIndex[item.time_slot_id] = index;\n    });\n    return newSlotIndex;\n  }\n  updateLocationMatch(locationKey, updateData, onError, onSuccess) {\n    const newIndex = this.mapNewSlotIndex(locationKey);\n    this._autoScheduleService.updateLocationMatch(updateData).subscribe(res => {\n      this.onScheduleAction(null, true);\n      onSuccess && onSuccess();\n    }, error => {\n      onError();\n    });\n  }\n  drop(event) {\n    const targetContainer = event.container.element.nativeElement;\n    const prevContainer = event.previousContainer.element.nativeElement;\n    const dragItem = event.item.element.nativeElement;\n    const targetContainerId = targetContainer['id'];\n    const prevContainerId = prevContainer['id'];\n    const targetStageId = targetContainer['data_stage_id'];\n    const targetLocationId = targetContainer['data_location_id'];\n    const prevStageId = prevContainer['data_stage_id'];\n    const prevLocationId = prevContainer['data_location_id'];\n    const itemTimeSlotId = dragItem['data_time_slot_id'];\n    const itemType = dragItem['data_type'];\n    const itemBreakDurations = dragItem['data_break_durations'];\n    const itemNewIndex = event.currentIndex;\n    if (prevContainerId === targetContainerId && event.currentIndex === event.previousIndex) return;\n    const handleDrop = () => {\n      if (targetContainerId === 'unScheduleZone') {\n        transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n        if (targetContainerId === prevContainerId) return;\n        this.unScheduleMatch(itemTimeSlotId, this.getConfig(prevLocationId)?.id, () => {\n          transferArrayItem(event.container.data, event.previousContainer.data, event.currentIndex, event.previousIndex);\n          this._toastService.error(this._translateService.instant('Failed to unschedule match.'));\n        }, () => {\n          this._toastService.success(this._translateService.instant('Match unscheduled successfully.'));\n        });\n      } else {\n        if (event.previousContainer === event.container) {\n          moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n          this.updateLocationMatch(targetContainerId, {\n            new_index: this.mapNewSlotIndex(targetContainerId),\n            location_id: targetLocationId,\n            prev_location_id: prevLocationId,\n            stage_id: targetStageId,\n            prev_stage_id: prevStageId,\n            tournament_id: this.tournamentId,\n            config_id: this.getConfig(targetLocationId)?.id,\n            prev_config_id: this.getConfig(prevLocationId)?.id\n          }, () => {\n            moveItemInArray(event.container.data, event.currentIndex, event.previousIndex);\n            this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\n          }, () => {\n            this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\n          });\n        } else {\n          transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n          this.updateLocationMatch(targetContainerId, {\n            new_index: this.mapNewSlotIndex(targetContainerId),\n            location_id: targetLocationId,\n            prev_location_id: prevContainerId === 'unScheduleZone' ? null : prevLocationId,\n            stage_id: targetStageId,\n            prev_stage_id: prevContainerId === 'unScheduleZone' ? null : prevStageId,\n            tournament_id: this.tournamentId,\n            config_id: this.getConfig(targetLocationId)?.id,\n            prev_config_id: this.getConfig(prevLocationId)?.id\n          }, () => {\n            transferArrayItem(event.container.data, event.previousContainer.data, event.currentIndex, event.previousIndex);\n            this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\n          }, () => {\n            this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\n          });\n        }\n      }\n    };\n    if (targetContainerId !== 'unScheduleZone') {\n      if (itemType === 'match') {\n        if (!this.checkValidUpdateMatch(targetContainerId, prevContainerId, event.currentIndex, event.previousIndex)) {\n          Swal.fire({\n            title: this._translateService.instant('Warning'),\n            text: this._translateService.instant(`${targetContainerId !== prevContainerId ? 'Adding' : 'Updating'} this match will exceed the location configured end time. Do you want to continue?`),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Continue'),\n            showCancelButton: true,\n            reverseButtons: true\n          }).then(result => {\n            if (result.isConfirmed) {\n              handleDrop();\n            }\n          });\n        } else {\n          handleDrop();\n        }\n      } else if (itemType === 'break') {\n        // -1 :block\n        // 0: need show warning\n        // 1: good\n        const validUpdateStatus = this.checkValidUpdateEvent(targetContainerId, prevContainerId, event.currentIndex, event.previousIndex, itemBreakDurations);\n        if (validUpdateStatus === -1) {\n          return Swal.fire({\n            title: this._translateService.instant('Error'),\n            text: this._translateService.instant('Cannot add this event to this location because there is not enough time space to add.'),\n            icon: 'error',\n            confirmButtonText: this._translateService.instant('OK')\n          });\n        } else if (validUpdateStatus === 0) {\n          Swal.fire({\n            title: this._translateService.instant('Warning'),\n            text: this._translateService.instant('Updating the index of this event may cause some matches to exceed the configured end time for the location. Do you want to continue?'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Continue'),\n            showCancelButton: true,\n            reverseButtons: true\n          }).then(result => {\n            if (result.isConfirmed) {\n              handleDrop();\n            }\n          });\n        } else {\n          handleDrop();\n        }\n      } else {\n        handleDrop();\n      }\n    } else {\n      handleDrop();\n    }\n  }\n  getShortTime(time) {\n    // handle return ISO string to short time format HH:mm and format 24 hours\n    if (!time) return '';\n    const date = new Date(time);\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n  getConfig(location_id) {\n    return this.responseMetadata['configs'].find(item => {\n      return item.tournament_id === +this.tournamentId && item.location_id === +location_id && item.begin_date === this.selectedDate;\n    });\n  }\n  editSchedule(locationKey, configId) {\n    this.selectedConfig = {\n      tournamentId: this.tournamentId,\n      location: this.responseMetadata['locations'][locationKey].id,\n      date: this.selectedDate,\n      configId,\n      timeSlotIds: this.listMatches[locationKey].map(item => item.time_slot_id)\n    };\n    this._modalService.open(this.modalEditSchedule, {\n      centered: true\n    });\n  }\n  deletePlan(locationKey) {\n    const locationId = this.responseMetadata['locations'][locationKey].id;\n    const configId = this.getConfig(locationId)?.id;\n    const timeSlotIds = this.listMatches[locationKey].map(item => item.time_slot_id);\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant('You will not be able to recover this schedule!'),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._autoScheduleService.deleteSchedule(this.tournamentId, locationId, timeSlotIds, configId).subscribe(res => {\n          this.onScheduleAction(null, false);\n          this._toastService.success(this._translateService.instant('Schedule deleted successfully.'));\n        });\n      }\n    });\n  }\n  openModalAddBreak(locationKey, lastTimeSlotId, configId) {\n    const locationId = this.responseMetadata['locations'][locationKey].id;\n    const config = this.getConfig(locationId);\n    const lastTimeSlotEndTime = this.listMatches[locationKey][this.listMatches[locationKey].length - 1].end_time;\n    // get time from lastTimeSlotEndTime to 23:59 same day\n    const maxBreakDuration = moment(lastTimeSlotEndTime).endOf('day').diff(moment(lastTimeSlotEndTime), 'minutes');\n    this.breakModalParams = {\n      ...this.breakModalParams,\n      timeSlotId: null,\n      locationId,\n      tournamentId: this.tournamentId,\n      lastTimeSlotId,\n      configId,\n      maxBreakDuration\n    };\n    this._modalService.open(this.modalCrudBreak, {\n      centered: true\n    });\n  }\n  // Simple dropdown methods\n  toggleDropdown(event, item) {\n    event.preventDefault();\n    event.stopPropagation();\n    if (event.target['className'] === 'swap-button') {\n      return;\n    }\n    const dropdownId = this.getDropdownId(item);\n    // Close dropdown if clicking on the same item\n    if (this.activeDropdownId === dropdownId) {\n      this.activeDropdownId = null;\n    } else {\n      // Open new dropdown (close any existing one)\n      this.activeDropdownId = dropdownId;\n    }\n  }\n  isDropdownOpen(item) {\n    const dropdownId = this.getDropdownId(item);\n    return this.activeDropdownId === dropdownId;\n  }\n  getDropdownId(item) {\n    // Create unique ID for each item\n    return `${item.time_slot_id}_${item.type}_${item.stage_id || 'unscheduled'}`;\n  }\n  closeAllDropdowns() {\n    this.activeDropdownId = null;\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    // Close dropdown if clicking outside of dropdown or dnd-item\n    if (!target.closest('.item-dropdown') && !target.closest('.dnd-item')) {\n      this.closeAllDropdowns();\n    }\n  }\n  // Dropdown action handlers\n  onEditMatch(item) {\n    this.closeAllDropdowns();\n    this.selectedItem = item;\n    this._modalService.open(this.modalUpdateMatch, {\n      centered: true\n    });\n  }\n  onUnscheduleTimeSlot(item, configId) {\n    const description = {\n      match: 'This match will be moved to unscheduled matches.',\n      break: 'This break will be removed.'\n    };\n    const successMessage = {\n      match: 'Match unscheduled successfully.',\n      break: 'Break removed successfully.'\n    };\n    const errorMessage = {\n      match: 'Failed to unschedule match.',\n      break: 'Failed to remove break.'\n    };\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant(description[item.type]),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('Cancel')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.unScheduleMatch(item.time_slot_id, configId, () => {\n          this._toastService.error(this._translateService.instant(errorMessage[item.type]));\n        }, () => {\n          this._toastService.success(this._translateService.instant(successMessage[item.type]));\n        });\n      }\n    });\n    this.closeAllDropdowns();\n  }\n  onEditEventTime(item, configId) {\n    this.closeAllDropdowns();\n    this.breakModalParams = {\n      timeSlotId: item.time_slot_id,\n      locationId: item.location_id,\n      tournamentId: item.tournament_id,\n      description: item.description,\n      breakDurations: item.break_durations,\n      configId: configId\n    };\n    this._modalService.open(this.modalCrudBreak, {\n      centered: true\n    });\n  }\n  clearSchedule() {\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant('This action will clear all schedule.'),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true,\n      confirmButtonText: this._translateService.instant('Yes, clear it!'),\n      cancelButtonText: this._translateService.instant('Cancel')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._loadingService.show();\n        this._autoScheduleService.clearSchedule(this.tournamentId).subscribe(res => {\n          Swal.fire({\n            title: this._translateService.instant('Success!'),\n            text: this._translateService.instant('Schedule has been cleared.'),\n            icon: 'success'\n          });\n          // this.\n          this.onScheduleAction('fetchDates', true);\n        }, error => {\n          Swal.fire({\n            title: this._translateService.instant('Error!'),\n            text: this._translateService.instant('Schedule has been cleared.'),\n            icon: 'success'\n          });\n        }, () => {\n          this._loadingService.dismiss();\n        });\n      }\n    });\n  }\n  isMatchHasConflict(scheduleMatchId, type, itemCheckId) {\n    switch (type) {\n      case 'team':\n        return this.teamConflicts[scheduleMatchId]?.find(item => item.team_id === itemCheckId);\n      case 'referee':\n        return this.refereeConflicts[scheduleMatchId]?.find(item => item.referee_id === itemCheckId);\n      case 'match':\n        return this.teamConflicts[scheduleMatchId] || this.refereeConflicts[scheduleMatchId];\n    }\n  }\n  onClickLock() {\n    this.isLock = !this.isLock;\n    this._autoScheduleService.updateTournamentScheduleStatus(this.tournamentId).subscribe(res => {\n      this._toastService.success(res.message);\n    }, error => {\n      this.isLock = !this.isLock;\n      this._toastService.error(error.message);\n    });\n  }\n  swapTeam(matchInfo) {\n    this.closeAllDropdowns();\n    this._stageService.swapTeams(matchInfo).subscribe(res => {\n      this._toastService.success('Swap teams successfully.');\n      this.onScheduleAction(null, false);\n    }, error => {\n      this._toastService.error(error.message || 'Failed to swap teams.');\n    });\n  }\n  autoGenerate() {\n    const formData = new FormData();\n    formData.append('stage_id', this.leagueOrGroupStageId.toString());\n    this._stageService.autoGenerateMatches(formData).subscribe(res => {\n      this._toastService.success('Auto generate matches successfully.');\n      this.onScheduleAction(null, false);\n    }, error => {\n      Swal.fire({\n        title: 'Cannot Auto Generate',\n        icon: error.warning ? 'warning' : 'error',\n        text: error.warning || error.message || 'Failed to auto generate matches.',\n        confirmButtonText: 'Go to Stage Details'\n      }).then(result => {\n        if (result.isConfirmed) {\n          this._router.navigate(['leagues', 'manage', this.tournamentId, 'stages', this.leagueOrGroupStageId]);\n        }\n      });\n    });\n  }\n  getRefereeDisplayLimit(referees, matchId) {\n    if (this.expandedRefereeMatches.has(matchId)) {\n      return referees.length; // Show all if expanded\n    }\n    // Calculate average name length\n    const totalNameLength = referees.reduce((sum, ref) => {\n      const name = ref.referee_type === 'user' ? `${ref.user?.first_name || ''} ${ref.user?.last_name || ''}`.trim() : ref.referee_name || '';\n      return sum + name.length;\n    }, 0);\n    const averageLength = totalNameLength / referees.length;\n    // If average name length is long (>15 chars), show 3; otherwise show 4\n    return averageLength > 15 ? 3 : 4;\n  }\n  getDisplayedReferees(referees, matchId) {\n    if (!referees || referees.length === 0) {\n      return [];\n    }\n    const limit = this.getRefereeDisplayLimit(referees, matchId);\n    return referees.slice(0, limit);\n  }\n  getRemainingRefereesCount(referees, matchId) {\n    if (!referees || referees.length === 0 || this.expandedRefereeMatches.has(matchId)) {\n      return 0;\n    }\n    const limit = this.getRefereeDisplayLimit(referees, matchId);\n    return Math.max(0, referees.length - limit);\n  }\n  toggleRefereeExpansion(matchId, event) {\n    event.stopPropagation();\n    event.preventDefault();\n    const matchItem = this.findMatchById(matchId);\n    if (!matchItem || !matchItem.referees || matchItem.referees.length === 0) {\n      return;\n    }\n    this.showRefereeModal(matchItem.referees, matchId);\n  }\n  showRefereeModal(referees, matchId) {\n    this.selectedMatchReferees = referees;\n    this.selectedMatchId = matchId;\n    this._modalService.open(this.modalRefereeList, {\n      centered: true,\n      size: 'md'\n    });\n  }\n  findMatchById(matchId) {\n    for (const locationKey of this.listLocationIds) {\n      const matches = this.listMatches[locationKey] || [];\n      const match = matches.find(item => item.id === matchId);\n      if (match) {\n        return match;\n      }\n    }\n    // Also check unscheduled matches\n    return this.listUnScheduledMatches.find(item => item.id === matchId) || null;\n  }\n  getConfigEndTime(beginDate, beginTime, endTime) {\n    const parseBeginTime = moment(`${beginDate} ${beginTime}`);\n    const parseEndTime = moment(`${beginDate} ${endTime}`);\n    if (parseEndTime.isBefore(parseBeginTime)) {\n      parseEndTime.add(1, 'day');\n    }\n    return parseEndTime.toDate();\n  }\n  checkValidUpdateMatch(locationKey, prevLocationKey, currentIndex, prevIndex) {\n    if (locationKey === prevLocationKey) {\n      return true;\n    }\n    const locationId = this.responseMetadata['locations'][locationKey].id;\n    const lastTimeSlotOfLocation = this.listMatches[locationKey][this.listMatches[locationKey].length - 1];\n    const lastTimeSlotType = lastTimeSlotOfLocation.type;\n    const lastTimeSlotEndTime = moment(lastTimeSlotOfLocation.end_time).toDate();\n    const config = this.getConfig(locationId);\n    const breakDuration = config.break_match_duration;\n    const matchDuration = config.match_duration;\n    const configLatestEndTime = this.getConfigEndTime(config.begin_date, config.begin_time, config.end_time);\n    let estimateNewMatchEndTime;\n    if (lastTimeSlotType === 'match') {\n      estimateNewMatchEndTime = moment(lastTimeSlotEndTime).add(breakDuration, 'minutes').add(matchDuration, 'minutes').toDate();\n    } else {\n      estimateNewMatchEndTime = moment(lastTimeSlotEndTime).add(matchDuration, 'minutes').toDate();\n    }\n    return estimateNewMatchEndTime <= configLatestEndTime;\n  }\n  checkValidUpdateEvent(locationKey, prevLocationKey, currentIndex, prevIndex, eventDuration) {\n    if (currentIndex === prevIndex && locationKey === prevLocationKey) {\n      return true;\n    }\n    const locationId = this.responseMetadata['locations'][locationKey].id;\n    const totalTimeSlotOfLocation = this.listMatches[locationKey].length - 1;\n    let lastTimeSlotOfLocation;\n    if (locationKey === prevLocationKey && currentIndex === totalTimeSlotOfLocation || locationKey !== prevLocationKey && currentIndex > totalTimeSlotOfLocation) {\n      return true;\n    }\n    if (prevIndex === totalTimeSlotOfLocation) {\n      lastTimeSlotOfLocation = this.listMatches[locationKey][this.listMatches[locationKey].length - 2];\n    } else {\n      lastTimeSlotOfLocation = this.listMatches[locationKey][this.listMatches[locationKey].length - 1];\n    }\n    console.log('lastTimeSlotOfLocation', lastTimeSlotOfLocation);\n    const lastTimeSlotEndTime = moment(lastTimeSlotOfLocation.end_time).toDate();\n    console.log('🚀 ~ checkValidUpdateEvent ~ lastTimeSlotEndTime: ', lastTimeSlotEndTime);\n    const config = this.getConfig(locationId);\n    const breakDuration = config.break_match_duration;\n    const configLatestEndTime = this.getConfigEndTime(config.begin_date, config.begin_time, config.end_time);\n    const estimateNewEventEndTime = moment(lastTimeSlotEndTime).add(eventDuration, 'minutes').toDate();\n    const maxEventDuration = moment(lastTimeSlotEndTime).endOf('day').diff(moment(lastTimeSlotEndTime), 'minutes');\n    if (eventDuration > maxEventDuration) {\n      return -1;\n    }\n    console.log('estimateNewEventEndTime', estimateNewEventEndTime);\n    console.log('configLatestEndTime', configLatestEndTime);\n    if (estimateNewEventEndTime > configLatestEndTime) {\n      return 0;\n    }\n    return 1;\n  }\n  isWarningMatch(matchId) {\n    return this.overEndTime.includes(+matchId);\n  }\n  static #_ = this.ɵfac = function AutoScheduleComponent_Factory(t) {\n    return new (t || AutoScheduleComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TournamentService), i0.ɵɵdirectiveInject(i3.StageService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.Title), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i7.AutoScheduleService), i0.ɵɵdirectiveInject(i8.NgbModal), i0.ɵɵdirectiveInject(i9.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AutoScheduleComponent,\n    selectors: [[\"app-auto-schedule\"]],\n    viewQuery: function AutoScheduleComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n        i0.ɵɵviewQuery(_c4, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalSetupSchedule = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalEditSchedule = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalCrudBreak = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalUpdateMatch = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalRefereeList = _t.first);\n      }\n    },\n    hostBindings: function AutoScheduleComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 57,\n    vars: 27,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"top-header\"], [1, \"row\", \"mb-1\"], [1, \"d-flex\", \"flex-column\", 2, \"gap\", \"2px\"], [\"for\", \"selectDate\"], [\"id\", \"selectDate\", 2, \"min-width\", \"200px\", 3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"disabled\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", 2, \"gap\", \"8px\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"data-feather\", \"settings\", 1, \"mr-25\"], [1, \"btn\", \"btn-icon\", 3, \"ngClass\", \"disabled\", \"click\"], [3, \"ngClass\"], [1, \"btn\", \"btn-icon\", \"btn-outline-danger\", 3, \"disabled\", \"click\"], [\"data-feather\", \"trash-2\"], [\"cdkDropListGroup\", \"\", 1, \"grid\", \"grid-cols-12\"], [\"id\", \"listLocationZone\", 1, \"col-xl-9\", \"col-lg-8\", \"col-sm-6\", \"col-12\"], [\"class\", \"horizontal-scroll-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"listUnScheduleZone\", 1, \"col-xl-3\", \"col-lg-4\", \"col-sm-6\", \"col-12\"], [1, \"unschedule-container\"], [1, \"location-column\", \"unplanned-matches-container\"], [1, \"location-header\"], [1, \"h4\"], [1, \"small\"], [\"cdkDropList\", \"\", \"id\", \"unScheduleZone\", 1, \"dnd-zone\", 3, \"cdkDropListData\", \"cdkDropListDropped\"], [\"modalSetupSchedule\", \"\"], [\"modalEditSchedule\", \"\"], [\"modalCrudBreak\", \"\"], [\"modalUpdateMatch\", \"\"], [\"modalRefereeList\", \"\"], [\"noSchedule\", \"\"], [\"fetchingState\", \"\"], [\"schedulingState\", \"\"], [\"matchNotScheduledTemplate\", \"\"], [\"matchScheduledTemplate\", \"\"], [3, \"value\"], [1, \"horizontal-scroll-container\"], [1, \"location-columns-wrapper\"], [4, \"ngFor\", \"ngForOf\"], [1, \"location-column\", \"mb-2\"], [1, \"bg-white\", \"shadow-sm\"], [1, \"d-flex\", \"align-items-start\", \"justify-content-between\"], [1, \"location-name\", \"h4\"], [\"class\", \"\", \"ngbDropdown\", \"\", 4, \"ngIf\"], [1, \"location-time\"], [\"cdkDropList\", \"\", 1, \"dnd-zone\", 3, \"cdkDropListData\", \"id\", \"data_location_id\", \"cdkDropListDropped\"], [\"class\", \"dnd-item location-match-row\", \"cdkDrag\", \"\", 3, \"ngClass\", \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"data_break_durations\", \"cdkDragDisabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"location-footer\", 4, \"ngIf\"], [\"ngbDropdown\", \"\", 1, \"\"], [\"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-link\", \"dropdown-toggle\"], [1, \"fa-solid\", \"fa-ellipsis-vertical\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"dropdownMenuButton\"], [\"ngbDropdownItem\", \"\", 3, \"click\"], [\"cdkDrag\", \"\", 1, \"dnd-item\", \"location-match-row\", 3, \"ngClass\", \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"data_break_durations\", \"cdkDragDisabled\", \"click\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"class\", \"conflict-tooltip btn btn-link\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This match has conflict\", 4, \"ngIf\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"class\", \"warning-tooltip btn btn-link\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This match has exceeded the end time configuration of this location\", 4, \"ngIf\"], [1, \"item-dropdown\"], [1, \"dropdown-content\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This match has conflict\", 1, \"conflict-tooltip\", \"btn\", \"btn-link\"], [1, \"fa-light\", \"fa-circle-exclamation\", 2, \"font-size\", \"16px\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This match has exceeded the end time configuration of this location\", 1, \"warning-tooltip\", \"btn\", \"btn-link\"], [1, \"fa-light\", \"fa-exclamation-triangle\", 2, \"font-size\", \"16px\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"break-info-header\"], [1, \"text-center\", \"m-0\"], [1, \"break-row\"], [\"aria-hidden\", \"true\", 1, \"fa-regular\", \"fa-clock\"], [1, \"break-time\", \"m-0\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"fa-regular\", \"fa-whistle\", \"mr-2\", 2, \"rotate\", \"-45deg\"], [1, \"fa\", \"fa-trash\", \"mr-2\"], [1, \"fa\", \"fa-clock\", \"mr-2\"], [1, \"location-footer\"], [1, \"btn\", \"btn-link\", \"w-100\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-plus\"], [4, \"ngTemplateOutlet\"], [\"class\", \"dnd-item location-match-row\", \"cdkDrag\", \"\", 3, \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\", 4, \"ngFor\", \"ngForOf\"], [\"cdkDrag\", \"\", 1, \"dnd-item\", \"location-match-row\", 3, \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\"], [\"id\", \"notHaveMatches\"], [1, \"text-center\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-wand-magic-sparkles\"], [3, \"seasonId\", \"tournamentId\", \"tournamentInfo\", \"onSubmit\"], [3, \"selectedConfig\", \"onSubmit\"], [3, \"breakModalParams\", \"onSubmit\"], [3, \"timeSlotInfo\", \"seasonId\", \"onSubmit\"], [1, \"modal-header\"], [\"id\", \"modal-title\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"referee-list\"], [\"class\", \"referee-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"referee-item\"], [1, \"referee-info\"], [3, \"ngClass\", 4, \"ngIf\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"class\", \"conflict-tooltip btn btn-link p-0\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This referee has a conflict\", 4, \"ngIf\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This referee has a conflict\", 1, \"conflict-tooltip\", \"btn\", \"btn-link\", \"p-0\"], [1, \"fa-light\", \"fa-circle-exclamation\", \"text-danger\", 2, \"font-size\", \"16px\"], [\"id\", \"noSchedule\"], [1, \"col\", \"d-flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"g-2\", 2, \"height\", \"500px\"], [1, \"h5\"], [1, \"w-75\", \"text-center\", 2, \"color\", \"rgba(168, 170, 174, 1)\"], [\"id\", \"fetchingState\"], [\"id\", \"schedulingState\"], [1, \"match-info-header\"], [1, \"team-row\"], [1, \"home-team\"], [\"src\", \"https://assets.codepen.io/285131/whufc.svg\", \"alt\", \"Home Team Logo\", 1, \"team-logo\"], [1, \"h6\", \"team-name\"], [1, \"swap-button\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-right-left\"], [1, \"away-team\"], [\"src\", \"https://assets.codepen.io/285131/whufc.svg\", \"alt\", \"Away Team Logo\", 1, \"team-logo\"], [1, \"h6\", \"team-name\", 3, \"ngClass\"], [1, \"match-date\"], [\"class\", \"referees-row\", 4, \"ngIf\"], [1, \"referees-row\"], [1, \"fa-regular\", \"fa-whistle\", 2, \"rotate\", \"-45deg\"], [\"class\", \"referee-names\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"referee-more\", 4, \"ngIf\"], [1, \"referee-names\"], [1, \"referee-name\"], [1, \"referee-more\"], [1, \"referee-expand-btn\", 3, \"click\"]],\n    template: function AutoScheduleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"label\", 6);\n        i0.ɵɵtext(7);\n        i0.ɵɵpipe(8, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"ng-select\", 7);\n        i0.ɵɵlistener(\"ngModelChange\", function AutoScheduleComponent_Template_ng_select_ngModelChange_9_listener($event) {\n          return ctx.selectedDate = $event;\n        })(\"change\", function AutoScheduleComponent_Template_ng_select_change_9_listener($event) {\n          return ctx.onSelectDate($event);\n        });\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵtemplate(11, AutoScheduleComponent_ng_option_11_Template, 2, 2, \"ng-option\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 9)(13, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_13_listener() {\n          return ctx.openModalSetupSchedule();\n        });\n        i0.ɵɵelement(14, \"i\", 11);\n        i0.ɵɵtext(15, \" Add Schedule \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_16_listener() {\n          return ctx.onClickLock();\n        });\n        i0.ɵɵelement(17, \"i\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_18_listener() {\n          return ctx.clearSchedule();\n        });\n        i0.ɵɵelement(19, \"i\", 15);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(20, \"div\", 16)(21, \"div\", 17);\n        i0.ɵɵtemplate(22, AutoScheduleComponent_div_22_Template, 3, 1, \"div\", 18);\n        i0.ɵɵtemplate(23, AutoScheduleComponent_ng_container_23_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵtemplate(24, AutoScheduleComponent_ng_container_24_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵtemplate(25, AutoScheduleComponent_ng_container_25_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"div\", 22)(29, \"div\", 23)(30, \"p\", 24);\n        i0.ɵɵtext(31, \"Not Planned\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"p\", 25);\n        i0.ɵɵtext(33, \" You can add unscheduled matches to the calendar by drag and drop them. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"div\", 26);\n        i0.ɵɵlistener(\"cdkDropListDropped\", function AutoScheduleComponent_Template_div_cdkDropListDropped_34_listener($event) {\n          return ctx.drop($event);\n        });\n        i0.ɵɵtemplate(35, AutoScheduleComponent_ng_container_35_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵtemplate(36, AutoScheduleComponent_ng_container_36_Template, 7, 0, \"ng-container\", 19);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵtemplate(37, AutoScheduleComponent_ng_template_37_Template, 1, 3, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(39, AutoScheduleComponent_ng_template_39_Template, 1, 1, \"ng-template\", null, 28, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(41, AutoScheduleComponent_ng_template_41_Template, 1, 1, \"ng-template\", null, 29, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(43, AutoScheduleComponent_ng_template_43_Template, 1, 2, \"ng-template\", null, 30, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(45, AutoScheduleComponent_ng_template_45_Template, 10, 4, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(47, AutoScheduleComponent_ng_template_47_Template, 8, 0, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(49, AutoScheduleComponent_ng_template_49_Template, 6, 0, \"ng-template\", null, 33, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(51, AutoScheduleComponent_ng_template_51_Template, 6, 0, \"ng-template\", null, 34, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(53, AutoScheduleComponent_ng_template_53_Template, 14, 3, \"ng-template\", null, 35, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(55, AutoScheduleComponent_ng_template_55_Template, 23, 12, \"ng-template\", null, 36, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 20, \"Date\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(10, 22, \"Select Date\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.selectedDate)(\"disabled\", ctx.isScheduling || ctx.isFetching);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.dateOptions);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.isLock || ctx.isScheduling || ctx.isFetching);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(24, _c8, !ctx.isLock, ctx.isLock))(\"disabled\", ctx.isScheduling || ctx.isFetching);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", ctx.isLock ? \"fa-regular fa-lock\" : \"fa-regular fa-unlock\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.isLock || ctx.isScheduling || ctx.isFetching);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasPlan);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasPlan && !ctx.isFetching && !ctx.isLock);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isFetching);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isScheduling && !ctx.isFetching);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"cdkDropListData\", ctx.listUnScheduledMatches);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMatches);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasMatches && !ctx.isScheduling);\n      }\n    },\n    styles: [\".home-team[_ngcontent-%COMP%], .away-team[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n\\nimg.team-logo[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n}\\n\\n.swap-button[_ngcontent-%COMP%] {\\n  color: rgb(45, 103, 241);\\n  background: rgba(45, 103, 241, 0.2);\\n  border-width: 0;\\n  padding: 0.25rem 1rem;\\n  border-radius: 0.5rem;\\n}\\n\\n.top-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 15;\\n  background-color: white;\\n  padding: 1rem;\\n  margin: 0;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  width: 100%;\\n  min-width: 100%;\\n}\\n.top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin: 0;\\n  width: 100%;\\n  min-width: 100%;\\n}\\n\\n.dnd-zone[_ngcontent-%COMP%] {\\n  background: #fff;\\n  min-height: 10rem;\\n}\\n\\n.dnd-item[_ngcontent-%COMP%] {\\n  -webkit-user-select: none;\\n          user-select: none;\\n  border-top: 1px solid rgba(168, 170, 174, 0.25);\\n  padding: 1rem 0.25rem;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  gap: 4px;\\n  min-height: 10rem;\\n  cursor: grab;\\n  position: relative;\\n  background: #fff;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .conflict-tooltip[_ngcontent-%COMP%] {\\n  padding: 0;\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n  color: rgba(255, 0, 0, 0.75) !important;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .warning-tooltip[_ngcontent-%COMP%] {\\n  padding: 0;\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n  color: rgba(255, 200, 36, 0.75) !important;\\n}\\n.dnd-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(45, 103, 241, 0.05);\\n}\\n.dnd-item[_ngcontent-%COMP%]:active {\\n  cursor: grabbing;\\n}\\n.dnd-item.cdk-drag-preview[_ngcontent-%COMP%] {\\n  z-index: 999;\\n  border-radius: 8px;\\n  border: 1px solid #a8aaae;\\n  scale: 0.95;\\n}\\n.dnd-item.cdk-drag-preview[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n.dnd-item.cdk-drag-preview.conflict-border[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 0, 0, 0.5) !important;\\n}\\n.dnd-item.location-match-row[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .match-info-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: rgb(75, 70, 92);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 100%;\\n  margin-left: -2rem;\\n  z-index: 1000;\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  border: 1px solid rgba(168, 170, 174, 0.25);\\n  min-width: 180px;\\n  overflow: hidden;\\n  display: none;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown.visible[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0.75rem 1rem;\\n  border: none;\\n  background: none;\\n  text-align: left;\\n  color: rgb(75, 70, 92);\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  white-space: nowrap;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(45, 103, 241, 0.1);\\n  color: rgb(45, 103, 241);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  background-color: rgba(45, 103, 241, 0.1);\\n  color: rgb(45, 103, 241);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 16px;\\n  text-align: center;\\n  margin-right: 0.5rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .team-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .team-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .match-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  justify-content: center;\\n  align-items: center;\\n  color: rgb(75, 70, 92);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: rgb(75, 70, 92);\\n  flex-wrap: wrap;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-names[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-name[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  align-items: center;\\n  white-space: nowrap;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-more[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-expand-btn[_ngcontent-%COMP%] {\\n  color: rgb(45, 103, 241);\\n  cursor: pointer;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  text-decoration: underline;\\n  transition: color 0.2s ease;\\n  padding: 0.5rem 0;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .break-info-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: rgb(16, 15, 15);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .break-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: rgb(75, 70, 92);\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  overflow: visible !important;\\n  position: relative;\\n}\\n\\n.content-body[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n  width: 100%;\\n}\\n\\n#listLocationZone[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  overflow-x: auto;\\n  padding: 0 !important;\\n  margin: 1rem 0;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%] {\\n  padding-bottom: 1rem;\\n  scroll-behavior: smooth;\\n  width: 100%;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(45, 103, 241, 0.5);\\n  border-radius: 4px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(45, 103, 241, 0.7);\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]   .location-columns-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  min-width: max-content;\\n  width: max-content;\\n  position: relative;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]   .location-columns-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  display: block;\\n  min-width: 28rem;\\n  flex-shrink: 0;\\n  height: 1px;\\n}\\n\\n#listUnScheduleZone[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: absolute;\\n  top: calc(10rem - 2px);\\n  right: 0;\\n  height: 100%;\\n  z-index: 10;\\n  margin: 1rem 0;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%] {\\n  position: sticky !important;\\n  top: 8rem;\\n  height: max-content;\\n  z-index: 10;\\n  width: 100%;\\n  right: 0;\\n  margin: 1rem 0;\\n  background: white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 1200px) {\\n  #listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 100%;\\n    right: 0;\\n    margin: 1rem 0;\\n    top: 12rem;\\n  }\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   #unScheduleZone[_ngcontent-%COMP%] {\\n  z-index: 100;\\n  max-height: 50vh;\\n  overflow-y: auto;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container[_ngcontent-%COMP%] {\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  height: max-content;\\n  max-height: calc(100vh - 15rem);\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container.location-column[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%] {\\n  background-color: white !important;\\n  border-bottom: 1px solid rgba(168, 170, 174, 0.25);\\n}\\n\\n.location-column[_ngcontent-%COMP%] {\\n  width: 26rem;\\n  height: max-content;\\n  overflow: visible;\\n  border: 1px solid #eee;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  padding: 1rem 0.5rem;\\n  background: rgba(45, 103, 241, 0.25);\\n  justify-content: space-between;\\n  height: 8rem;\\n  position: relative;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .btn.btn-link[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%] {\\n  border: none;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%]::after {\\n  width: 0;\\n  background-image: none;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin: 0;\\n  width: 90%;\\n  color: rgb(45, 103, 241);\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-time[_ngcontent-%COMP%] {\\n  color: rgb(45, 103, 241);\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-time[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-footer[_ngcontent-%COMP%] {\\n  border-top: 1px solid rgba(168, 170, 174, 0.25);\\n  padding: 1rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .top-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: stretch;\\n    padding-left: 0.5rem;\\n    padding-right: 0.5rem;\\n  }\\n  #listUnScheduleZone[_ngcontent-%COMP%] {\\n    position: relative !important;\\n    left: auto;\\n    right: auto;\\n    top: auto;\\n    width: 100%;\\n    max-width: 100%;\\n    margin-top: 2rem;\\n  }\\n  .location-column[_ngcontent-%COMP%] {\\n    min-width: 250px;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 0.25rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-names[_ngcontent-%COMP%] {\\n    gap: 0.15rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-expand-btn[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .location-column[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n  }\\n  .top-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    padding-left: 0.25rem;\\n    padding-right: 0.25rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-name[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-expand-btn[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0;\\n  }\\n  .referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%]   .referee-type[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n    left: 0;\\n    top: 100%;\\n    margin-left: 0;\\n    margin-top: 5px;\\n    min-width: 200px;\\n    max-width: 90vw;\\n  }\\n}\\n.horizontal-scroll-container[_ngcontent-%COMP%] {\\n  overflow: visible;\\n}\\n\\n.conflict-border[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 0, 0, 0.5) !important;\\n}\\n\\n.warning-border[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 200, 36, 0.75) !important;\\n}\\n\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.cdk-drag-disabled[_ngcontent-%COMP%] {\\n  background: rgba(220, 220, 220, 0.25);\\n  cursor: default;\\n}\\n.cdk-drag-disabled[_ngcontent-%COMP%]:hover {\\n  background: rgba(220, 220, 220, 0.25);\\n}\\n\\n#fetchingState[_ngcontent-%COMP%], #noSchedule[_ngcontent-%COMP%] {\\n  width: 75%;\\n}\\n\\n#notHaveMatches[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 10rem;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  z-index: 1000;\\n}\\n\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid rgba(168, 170, 174, 0.25);\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 0.95rem;\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%]   .referee-type[_ngcontent-%COMP%] {\\n  color: rgba(75, 70, 92, 0.7);\\n  font-size: 0.8rem;\\n  font-style: italic;\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   i.fa-exclamation-triangle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.5rem;\\n  cursor: pointer;\\n  color: rgb(75, 70, 92);\\n}\\n.modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\\n  color: rgb(45, 103, 241);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAOA,SAAsBA,eAAe,EAAEC,iBAAiB,QAAQ,wBAAwB;AAExF,OAAOC,IAAI,MAAM,aAAa;AAG9B,SAASC,SAAS,QAAQ,qBAAqB;AAG/C,OAAOC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;ICIHC,EAAA,CAAAC,cAAA,oBAA2D;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAtCH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAc;IAACL,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAO,kBAAA,KAAAF,QAAA,MAAW;;;;;;IAqClDL,EAAA,CAAAC,cAAA,cAA0C;IAElCD,EAAA,CAAAQ,SAAA,YAA6C;IACjDR,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAA0D;IAG9CD,EAAA,CAAAS,UAAA,mBAAAC,8EAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAC,eAAA,GAAAb,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAc,aAAA;MAAA,IAAAG,OAAA;MAAA,OAE5CjB,EAAA,CAAAkB,WAAA,CAAAF,OAAA,CAAAG,YAAA,CAAAN,eAAA,GAAAI,OAAA,GAECD,OAAA,CAAAI,SAAA,CAAAJ,OAAA,CAAAK,gBAAA,CAA8D,WACtE,EAAAR,eAAA,EAAAS,EAAA,CACY,mBAAAL,OAAA,CAAAK,EAAA,CACG;IAAA,EAAK;IAE2BtB,EAAA,CAAAE,MAAA,kBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAAqD;IAAlCD,EAAA,CAAAS,UAAA,mBAAAc,8EAAA;MAAAvB,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAC,eAAA,GAAAb,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAS,OAAA,GAAAxB,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAkB,WAAA,CAAAM,OAAA,CAAAC,UAAA,CAAAZ,eAAA,CAAuB;IAAA,EAAC;IAChDb,EAAA,CAAAE,MAAA,oBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAmCZH,EAAA,CAAAC,cAAA,iBAA8L;IAC1LD,EAAA,CAAAQ,SAAA,YAAuE;IAC3ER,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAsQ;IAClQD,EAAA,CAAAQ,SAAA,YAAyE;IAC7ER,EAAA,CAAAG,YAAA,EAAS;;;;;IAGLH,EAAA,CAAA0B,kBAAA,GAKgB;;;;;;;;;;IANpB1B,EAAA,CAAA2B,uBAAA,GAA4C;IACxC3B,EAAA,CAAA4B,UAAA,IAAAC,yFAAA,2BAKgB;IACpB7B,EAAA,CAAA8B,qBAAA,EAAe;;;;;;IALF9B,EAAA,CAAAM,SAAA,GAED;IAFCN,EAAA,CAAAI,UAAA,qBAAA2B,IAAA,CAED,4BAAA/B,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAAC,QAAA;;;;;IAIZlC,EAAA,CAAA2B,uBAAA,GAA4C;IACxC3B,EAAA,CAAAC,cAAA,cAA+B;IAEvBD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAERH,EAAA,CAAAC,cAAA,cAAuB;IACnBD,EAAA,CAAAQ,SAAA,YAAsD;IACtDR,EAAA,CAAAC,cAAA,YAA0B;IACtBD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEZH,EAAA,CAAA8B,qBAAA,EAAe;;;;IATH9B,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAA2B,QAAA,CAAAC,WAAA,sBACJ;IAKInC,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAA2B,QAAA,CAAAE,eAAA,WACJ;;;;;;IAQApC,EAAA,CAAA2B,uBAAA,GAA4C;IACxC3B,EAAA,CAAAC,cAAA,iBAA0D;IAA5BD,EAAA,CAAAS,UAAA,mBAAA4B,mGAAA;MAAArC,EAAA,CAAAW,aAAA,CAAA2B,IAAA;MAAA,MAAAJ,QAAA,GAAAlC,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAwB,OAAA,GAAAvC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAkB,WAAA,CAAAqB,OAAA,CAAAC,WAAA,CAAAN,QAAA,CAAiB;IAAA,EAAC;IACrDlC,EAAA,CAAAQ,SAAA,YAAkE;IAClER,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAWC;IATOD,EAAA,CAAAS,UAAA,mBAAAgC,mGAAA;MAAAzC,EAAA,CAAAW,aAAA,CAAA2B,IAAA;MAAA,MAAAJ,QAAA,GAAAlC,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAF,eAAA,GAAAb,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAA2B,OAAA,GAAA1C,EAAA,CAAAc,aAAA;MAAA,IAAAG,OAAA;MAAA,OAEtDjB,EAAA,CAAAkB,WAAA,CAAAwB,OAAA,CAAAC,oBAAA,CAAAT,QAAA,GAAAjB,OAAA,GAESyB,OAAA,CAAAtB,SAAA,CAAAsB,OAAA,CAAArB,gBAAA,CAChB,WAAW,EAAAR,eAAA,EAAAS,EAAA,CAEQ,mBAAAL,OAAA,CAAAK,EAAA,CACG;IAAA,EAAM;IAE6BtB,EAAA,CAAAQ,SAAA,YAAgC;IAChCR,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACbH,EAAA,CAAA8B,qBAAA,EAAe;;;IAjBP9B,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA4C,WAAA,qCACJ;IAcI5C,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA4C,WAAA,gCACJ;;;;;;IAIJ5C,EAAA,CAAA2B,uBAAA,GAA4C;IACxC3B,EAAA,CAAAC,cAAA,iBAWC;IATOD,EAAA,CAAAS,UAAA,mBAAAoC,mGAAA;MAAA7C,EAAA,CAAAW,aAAA,CAAAmC,IAAA;MAAA,MAAAZ,QAAA,GAAAlC,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAF,eAAA,GAAAb,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAgC,OAAA,GAAA/C,EAAA,CAAAc,aAAA;MAAA,IAAAG,OAAA;MAAA,OAEjDjB,EAAA,CAAAkB,WAAA,CAAA6B,OAAA,CAAAC,eAAA,CAAAd,QAAA,GAAAjB,OAAA,GAEI8B,OAAA,CAAA3B,SAAA,CAAA2B,OAAA,CAAA1B,gBAAA,CAChB,WAAW,EAAAR,eAAA,EAAAS,EAAA,CAEQ,mBAAAL,OAAA,CAAAK,EAAA,CACG;IAAA,EAAM;IAE6BtB,EAAA,CAAAQ,SAAA,YAAgC;IAChCR,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAWC;IATOD,EAAA,CAAAS,UAAA,mBAAAwC,mGAAA;MAAAjD,EAAA,CAAAW,aAAA,CAAAmC,IAAA;MAAA,MAAAZ,QAAA,GAAAlC,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAF,eAAA,GAAAb,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAmC,OAAA,GAAAlD,EAAA,CAAAc,aAAA;MAAA,IAAAG,OAAA;MAAA,OAEtDjB,EAAA,CAAAkB,WAAA,CAAAgC,OAAA,CAAAP,oBAAA,CAAAT,QAAA,GAAAjB,OAAA,GAESiC,OAAA,CAAA9B,SAAA,CAAA8B,OAAA,CAAA7B,gBAAA,CAChB,WAAW,EAAAR,eAAA,EAAAS,EAAA,CAEQ,mBAAAL,OAAA,CAAAK,EAAA,CACG;IAAA,EAAM;IAE6BtB,EAAA,CAAAQ,SAAA,YAAgC;IAChCR,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACbH,EAAA,CAAA8B,qBAAA,EAAe;;;IAjBP9B,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA4C,WAAA,+BACJ;IAcI5C,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA4C,WAAA,4BACJ;;;;;;;;;;;;IArGhB5C,EAAA,CAAAC,cAAA,cAcC;IADOD,EAAA,CAAAS,UAAA,mBAAA0C,iFAAAC,MAAA;MAAA,MAAAC,WAAA,GAAArD,EAAA,CAAAW,aAAA,CAAA2C,IAAA;MAAA,MAAApB,QAAA,GAAAmB,WAAA,CAAAtC,SAAA;MAAA,MAAAwC,OAAA,GAAAvD,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAkB,WAAA,EAAAqC,OAAA,CAAAC,MAAA,IAAWD,OAAA,CAAAE,cAAA,CAAAL,MAAA,EAAAlB,QAAA,CAA4B;IAAA,EAAC;IAErDlC,EAAA,CAAA4B,UAAA,IAAA8B,oEAAA,qBAES;IACT1D,EAAA,CAAA4B,UAAA,IAAA+B,oEAAA,qBAES;IAET3D,EAAA,CAAA4B,UAAA,IAAAgC,0EAAA,2BAOe;IACf5D,EAAA,CAAA4B,UAAA,IAAAiC,0EAAA,2BAYe;IAGf7D,EAAA,CAAAC,cAAA,cAAkE;IAG1DD,EAAA,CAAA4B,UAAA,IAAAkC,0EAAA,2BAoBe;IAGf9D,EAAA,CAAA4B,UAAA,IAAAmC,0EAAA,2BA+Be;IACnB/D,EAAA,CAAAG,YAAA,EAAM;;;;;;IApGNH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgE,eAAA,KAAAC,GAAA,EAAAC,OAAA,CAAAC,kBAAA,CAAAjC,QAAA,CAAAZ,EAAA,YAAA4C,OAAA,CAAAE,cAAA,CAAAlC,QAAA,CAAAZ,EAAA,MAAA4C,OAAA,CAAAC,kBAAA,CAAAjC,QAAA,CAAAZ,EAAA,YAGxB,kBAAAY,QAAA,CAAAmC,QAAA,uBAAAnC,QAAA,CAAAoC,YAAA,eAAApC,QAAA,CAAAqC,IAAA,2BAAAC,OAAA,GAAAtC,QAAA,CAAAE,eAAA,cAAAoC,OAAA,KAAAC,SAAA,GAAAD,OAAA,yBAAAN,OAAA,CAAAV,MAAA;IASsKxD,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAI,UAAA,SAAA8D,OAAA,CAAAC,kBAAA,CAAAjC,QAAA,CAAAZ,EAAA,WAA0C;IAGCtB,EAAA,CAAAM,SAAA,GAAuE;IAAvEN,EAAA,CAAAI,UAAA,SAAA8D,OAAA,CAAAE,cAAA,CAAAlC,QAAA,CAAAZ,EAAA,MAAA4C,OAAA,CAAAC,kBAAA,CAAAjC,QAAA,CAAAZ,EAAA,WAAuE;IAIrPtB,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAA8B,QAAA,CAAAqC,IAAA,aAA2B;IAQ3BvE,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAA8B,QAAA,CAAAqC,IAAA,aAA2B;IAefvE,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAA0E,WAAA,YAAAR,OAAA,CAAAS,cAAA,CAAAzC,QAAA,EAAsC;IAG1ClC,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAA8B,QAAA,CAAAqC,IAAA,aAA2B;IAuB3BvE,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAA8B,QAAA,CAAAqC,IAAA,aAA2B;;;;;;IAoC1DvE,EAAA,CAAAC,cAAA,iBAAgD;IAGpCD,EAAA,CAAAS,UAAA,mBAAAmE,uFAAA;MAAA5E,EAAA,CAAAW,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,eAAA,GAAAb,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAA+D,OAAA,GAAA9E,EAAA,CAAAc,aAAA;MAAA,IAAAG,OAAA;MAAA,OAE3CjB,EAAA,CAAAkB,WAAA,CAAA4D,OAAA,CAAAC,iBAAA,CAAAlE,eAAA,EAAAiE,OAAA,CAAAE,WAAA,CAAAnE,eAAA,EAAAiE,OAAA,CAAAE,WAAA,CAAAnE,eAAA,EAAAoE,MAAA,GAGc,CAAC,mBAAAH,OAAA,CAAAE,WAAA,CAAAnE,eAAA,EAAAiE,OAAA,CAAAE,WAAA,CAAAnE,eAAA,EAAAoE,MAAA,GAAD,CAAC,EAAAX,YAAA,GAAArD,OAAA,GAEZ6D,OAAA,CAAA1D,SAAA,CAAA0D,OAAA,CAAAzD,gBAAA,CAAwD,WAChE,EAAAR,eAAA,EAAAS,EAAA,CACY,mBAAAL,OAAA,CAAAK,EAAA,CACG;IAAA,EAAQ;IAEqBtB,EAAA,CAAAQ,SAAA,YAA6C;IAC7CR,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA5KzBH,EAAA,CAAA2B,uBAAA,GAA0D;IACtD3B,EAAA,CAAAC,cAAA,cAAkC;IAKdD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAA4B,UAAA,IAAAsD,0DAAA,kBAsBM;IACVlF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA2B;IACpBD,EAAA,CAAAE,MAAA,IAAiL;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxLH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACRH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAyK;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGxLH,EAAA,CAAAC,cAAA,eASC;IALOD,EAAA,CAAAS,UAAA,gCAAA0E,wFAAA/B,MAAA;MAAApD,EAAA,CAAAW,aAAA,CAAAyE,IAAA;MAAA,MAAAC,OAAA,GAAArF,EAAA,CAAAc,aAAA;MAAA,OAAsBd,EAAA,CAAAkB,WAAA,CAAAmE,OAAA,CAAAC,IAAA,CAAAlC,MAAA,CAAY;IAAA,EAAC;IAMvCpD,EAAA,CAAA4B,UAAA,KAAA2D,2DAAA,mBAyGM;IACVvF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA4B,UAAA,KAAA4D,8DAAA,qBAkBS;IACbxF,EAAA,CAAAG,YAAA,EAAM;IAEdH,EAAA,CAAA8B,qBAAA,EAAe;;;;;;;IA1KS9B,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAM,eAAA,MACJ;IAC2Bb,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,UAAAqF,OAAA,CAAAjC,MAAA,CAAa;IAyBrCxD,EAAA,CAAAM,SAAA,GAAiL;IAAjLN,EAAA,CAAA0F,iBAAA,CAAAD,OAAA,CAAAE,YAAA,GAAAC,OAAA,GAAAH,OAAA,CAAArE,SAAA,CAAAqE,OAAA,CAAApE,gBAAA,cAAAR,eAAA,EAAAS,EAAA,oBAAAsE,OAAA,CAAAC,UAAA,YAAAD,OAAA,GAAAH,OAAA,CAAArE,SAAA,CAAAqE,OAAA,CAAApE,gBAAA,cAAAR,eAAA,EAAAS,EAAA,oBAAAsE,OAAA,CAAAE,UAAA,SAAiL;IAEjL9F,EAAA,CAAAM,SAAA,GAAyK;IAAzKN,EAAA,CAAA0F,iBAAA,CAAAD,OAAA,CAAAE,YAAA,GAAAI,OAAA,GAAAN,OAAA,CAAArE,SAAA,CAAAqE,OAAA,CAAApE,gBAAA,cAAAR,eAAA,EAAAS,EAAA,oBAAAyE,OAAA,CAAAF,UAAA,YAAAE,OAAA,GAAAN,OAAA,CAAArE,SAAA,CAAAqE,OAAA,CAAApE,gBAAA,cAAAR,eAAA,EAAAS,EAAA,oBAAAyE,OAAA,CAAAC,QAAA,SAAyK;IAM5KhG,EAAA,CAAAM,SAAA,GAA4C;IAA5CN,EAAA,CAAAI,UAAA,oBAAAqF,OAAA,CAAAT,WAAA,CAAAnE,eAAA,EAA4C,OAAAA,eAAA,sBAAA4E,OAAA,CAAApE,gBAAA,cAAAR,eAAA,EAAAS,EAAA;IAQvBtB,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAqF,OAAA,CAAAT,WAAA,CAAAnE,eAAA,EAA2B;IA0GvBb,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,UAAAqF,OAAA,CAAAjC,MAAA,CAAa;;;;;IA7JlExD,EAAA,CAAAC,cAAA,cAAyD;IAEjDD,EAAA,CAAA4B,UAAA,IAAAqE,oDAAA,4BAgLe;IACnBjG,EAAA,CAAAG,YAAA,EAAM;;;;IAjLoCH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAA8F,MAAA,CAAAC,eAAA,CAAkB;;;;;IAoL5DnG,EAAA,CAAA0B,kBAAA,GAA4D;;;;;IADhE1B,EAAA,CAAA2B,uBAAA,GAAyD;IACrD3B,EAAA,CAAA4B,UAAA,IAAAwE,6DAAA,2BAA4D;IAChEpG,EAAA,CAAA8B,qBAAA,EAAe;;;;;IADI9B,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,qBAAAiG,IAAA,CAA4B;;;;;IAG3CrG,EAAA,CAAA0B,kBAAA,GAA+D;;;;;IADnE1B,EAAA,CAAA2B,uBAAA,GAAiC;IAC7B3B,EAAA,CAAA4B,UAAA,IAAA0E,6DAAA,2BAA+D;IACnEtG,EAAA,CAAA8B,qBAAA,EAAe;;;;;IADI9B,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,qBAAAmG,IAAA,CAA+B;;;;;IAG9CvG,EAAA,CAAA0B,kBAAA,GAAiE;;;;;IADrE1B,EAAA,CAAA2B,uBAAA,GAAkD;IAC9C3B,EAAA,CAAA4B,UAAA,IAAA4E,6DAAA,2BAAiE;IACrExG,EAAA,CAAA8B,qBAAA,EAAe;;;;;IADI9B,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAI,UAAA,qBAAAqG,IAAA,CAAiC;;;;;IAgB5BzG,EAAA,CAAA0B,kBAAA,GAKgB;;;;;IANpB1B,EAAA,CAAA2B,uBAAA,GAA4C;IACxC3B,EAAA,CAAA4B,UAAA,IAAA8E,kFAAA,2BAKgB;IACpB1G,EAAA,CAAA8B,qBAAA,EAAe;;;;;;IALF9B,EAAA,CAAAM,SAAA,GAED;IAFCN,EAAA,CAAAI,UAAA,qBAAAuG,IAAA,CAED,4BAAA3G,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAA2E,QAAA;;;;;IALhB5G,EAAA,CAAAC,cAAA,cAAwN;IACpND,EAAA,CAAA4B,UAAA,IAAAiF,mEAAA,2BAOe;IACnB7G,EAAA,CAAAG,YAAA,EAAM;;;;;IATuFH,EAAA,CAAAI,UAAA,kBAAAwG,QAAA,CAAAvC,QAAA,CAA+B,sBAAAuC,QAAA,CAAAtC,YAAA,eAAAsC,QAAA,CAAArC,IAAA,qBAAAuC,OAAA,CAAAtD,MAAA;IACzGxD,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAAwG,QAAA,CAAArC,IAAA,aAA2B;;;;;IAFlDvE,EAAA,CAAA2B,uBAAA,GAAiC;IAC7B3B,EAAA,CAAA4B,UAAA,IAAAmF,oDAAA,kBASM;IACV/G,EAAA,CAAA8B,qBAAA,EAAe;;;;IAVW9B,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,YAAA4G,MAAA,CAAAC,sBAAA,CAAyB;;;;;;IAWnDjH,EAAA,CAAA2B,uBAAA,GAAmD;IAC/C3B,EAAA,CAAAC,cAAA,cAAyB;IAEjBD,EAAA,CAAAE,MAAA,8CACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,iBAAyD;IAAjDD,EAAA,CAAAS,UAAA,mBAAAyG,uEAAA;MAAAlH,EAAA,CAAAW,aAAA,CAAAwG,IAAA;MAAA,MAAAC,OAAA,GAAApH,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAkB,WAAA,CAAAkG,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC5BrH,EAAA,CAAAQ,SAAA,YAA+C;IAC/CR,EAAA,CAAAE,MAAA,sBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEjBH,EAAA,CAAA8B,qBAAA,EAAe;;;;;;IASvC9B,EAAA,CAAAC,cAAA,mCAAwJ;IAAxCD,EAAA,CAAAS,UAAA,sBAAA6G,2FAAAlE,MAAA;MAAApD,EAAA,CAAAW,aAAA,CAAA4G,IAAA;MAAA,MAAAC,OAAA,GAAAxH,EAAA,CAAAc,aAAA;MAAA,OAAYd,EAAA,CAAAkB,WAAA,CAAAsG,OAAA,CAAAC,gBAAA,CAAArE,MAAA,CAAwB;IAAA,EAAC;IAArJpD,EAAA,CAAAG,YAAA,EAAwJ;;;;IAA9HH,EAAA,CAAAI,UAAA,aAAAsH,MAAA,CAAAC,QAAA,CAAqB,iBAAAD,MAAA,CAAAE,YAAA,oBAAAF,MAAA,CAAAG,cAAA;;;;;;IAG/C7H,EAAA,CAAAC,cAAA,kCAAmG;IAAxCD,EAAA,CAAAS,UAAA,sBAAAqH,0FAAA1E,MAAA;MAAApD,EAAA,CAAAW,aAAA,CAAAoH,IAAA;MAAA,MAAAC,OAAA,GAAAhI,EAAA,CAAAc,aAAA;MAAA,OAAYd,EAAA,CAAAkB,WAAA,CAAA8G,OAAA,CAAAP,gBAAA,CAAArE,MAAA,CAAwB;IAAA,EAAC;IAAhGpD,EAAA,CAAAG,YAAA,EAAmG;;;;IAA1EH,EAAA,CAAAI,UAAA,mBAAA6H,OAAA,CAAAC,cAAA,CAAiC;;;;;;IAG1DlI,EAAA,CAAAC,cAAA,+BAAoG;IAAxCD,EAAA,CAAAS,UAAA,sBAAA0H,uFAAA/E,MAAA;MAAApD,EAAA,CAAAW,aAAA,CAAAyH,IAAA;MAAA,MAAAC,OAAA,GAAArI,EAAA,CAAAc,aAAA;MAAA,OAAYd,EAAA,CAAAkB,WAAA,CAAAmH,OAAA,CAAAZ,gBAAA,CAAArE,MAAA,CAAwB;IAAA,EAAC;IAAjGpD,EAAA,CAAAG,YAAA,EAAoG;;;;IAA9EH,EAAA,CAAAI,UAAA,qBAAAkI,OAAA,CAAAC,gBAAA,CAAqC;;;;;;IAI3DvI,EAAA,CAAAC,cAAA,iCAAoH;IAAxCD,EAAA,CAAAS,UAAA,sBAAA+H,yFAAApF,MAAA;MAAApD,EAAA,CAAAW,aAAA,CAAA8H,IAAA;MAAA,MAAAC,OAAA,GAAA1I,EAAA,CAAAc,aAAA;MAAA,OAAYd,EAAA,CAAAkB,WAAA,CAAAwH,OAAA,CAAAjB,gBAAA,CAAArE,MAAA,CAAwB;IAAA,EAAC;IAAjHpD,EAAA,CAAAG,YAAA,EAAoH;;;;IAA5FH,EAAA,CAAAI,UAAA,iBAAAuI,OAAA,CAAAC,YAAA,CAA6B,aAAAD,OAAA,CAAAhB,QAAA;;;;;;;;;;IAc/C3H,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IALCH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgC,eAAA,IAAA6G,GAAA,EAAAC,OAAA,CAAA3E,kBAAA,CAAA2E,OAAA,CAAAC,eAAA,aAAAC,OAAA,CAAA1H,EAAA,GAEJ;IAEFtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAiJ,kBAAA,MAAAD,OAAA,CAAAE,IAAA,kBAAAF,OAAA,CAAAE,IAAA,CAAAC,UAAA,OAAAH,OAAA,CAAAE,IAAA,kBAAAF,OAAA,CAAAE,IAAA,CAAAE,SAAA,MACF;;;;;IACUpJ,EAAA,CAAAC,cAAA,eAKC;IACTD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IALWH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgC,eAAA,IAAA6G,GAAA,EAAAQ,OAAA,CAAAlF,kBAAA,CAAAkF,OAAA,CAAAN,eAAA,aAAAC,OAAA,CAAA1H,EAAA,GAEd;IAEFtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAyI,OAAA,CAAAM,YAAA,MACF;;;;;IAEMtJ,EAAA,CAAAC,cAAA,iBAAwN;IACpND,EAAA,CAAAQ,SAAA,aAAmF;IACvFR,EAAA,CAAAG,YAAA,EAAS;;;;;IArBbH,EAAA,CAAAC,cAAA,cAAoE;IAEtED,EAAA,CAAA4B,UAAA,IAAA2H,0DAAA,mBAOO;IACGvJ,EAAA,CAAA4B,UAAA,IAAA4H,0DAAA,mBAOH;IACDxJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA4B,UAAA,IAAA6H,4DAAA,qBAES;IACbzJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAnBCH,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAA4I,OAAA,CAAAU,YAAA,WAAgC;IAQtB1J,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAI,UAAA,SAAA4I,OAAA,CAAAU,YAAA,eAAoC;IAQyG1J,EAAA,CAAAM,SAAA,GAA4D;IAA5DN,EAAA,CAAAI,UAAA,SAAAuJ,OAAA,CAAAxF,kBAAA,CAAAwF,OAAA,CAAAZ,eAAA,aAAAC,OAAA,CAAA1H,EAAA,EAA4D;;;;;;IA3BlOtB,EAAA,CAAAC,cAAA,cAA0B;IACmBD,EAAA,CAAAE,MAAA,GAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChFH,EAAA,CAAAC,cAAA,iBAAiF;IAA1BD,EAAA,CAAAS,UAAA,mBAAAmJ,sEAAA;MAAA,MAAAvG,WAAA,GAAArD,EAAA,CAAAW,aAAA,CAAAkJ,IAAA;MAAA,MAAAC,SAAA,GAAAzG,WAAA,CAAAtC,SAAA;MAAA,OAASf,EAAA,CAAAkB,WAAA,CAAA4I,SAAA,CAAAC,OAAA,EAAe;IAAA,EAAC;IAC5E/J,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,aAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG/CH,EAAA,CAAAC,cAAA,cAAwB;IAEhBD,EAAA,CAAA4B,UAAA,IAAAoI,mDAAA,kBAsBM;IACVhK,EAAA,CAAAG,YAAA,EAAM;;;;IA9BmCH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAA0F,iBAAA,CAAA1F,EAAA,CAAA4C,WAAA,yBAAkC;IAO7B5C,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,YAAA6J,OAAA,CAAAC,qBAAA,CAAwB;;;;;;IA4B1ElK,EAAA,CAAAC,cAAA,eAAqB;IAECD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjCH,EAAA,CAAAC,cAAA,aAAmE;IAC/DD,EAAA,CAAAE,MAAA,gIACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAAmE;IAAnCD,EAAA,CAAAS,UAAA,mBAAA0J,sEAAA;MAAAnK,EAAA,CAAAW,aAAA,CAAAyJ,KAAA;MAAA,MAAAC,QAAA,GAAArK,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAkB,WAAA,CAAAmJ,QAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAC9DtK,EAAA,CAAAE,MAAA,cACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAMjBH,EAAA,CAAAC,cAAA,eAAwB;IAEFD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1BH,EAAA,CAAAC,cAAA,aAAmE;IAC/DD,EAAA,CAAAE,MAAA,6CACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMZH,EAAA,CAAAC,cAAA,eAA0B;IAEJD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/BH,EAAA,CAAAC,cAAA,aAAmE;IAC/DD,EAAA,CAAAE,MAAA,yMACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAMZH,EAAA,CAAAC,cAAA,eAA+B;IACAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9DH,EAAA,CAAAC,cAAA,eAAsB;IAEdD,EAAA,CAAAQ,SAAA,eAA+F;IAC/FR,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEhFH,EAAA,CAAAC,cAAA,kBAA2D;IAA/BD,EAAA,CAAAS,UAAA,mBAAA8J,sEAAA;MAAA,MAAAlH,WAAA,GAAArD,EAAA,CAAAW,aAAA,CAAA6J,KAAA;MAAA,MAAAC,SAAA,GAAApH,WAAA,CAAAtC,SAAA;MAAA,MAAA2J,QAAA,GAAA1K,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAkB,WAAA,CAAAwJ,QAAA,CAAAC,QAAA,CAAAF,SAAA,CAAAG,KAAA,CAAoB;IAAA,EAAC;IACtD5K,EAAA,CAAAQ,SAAA,aAAmD;IACvDR,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,gBAAuB;IACQD,EAAA,CAAAE,MAAA,IAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5EH,EAAA,CAAAQ,SAAA,gBAA+F;IACnGR,EAAA,CAAAG,YAAA,EAAM;;;;;;IAbqBH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA0F,iBAAA,CAAA+E,SAAA,CAAAG,KAAA,CAAAC,UAAA,CAA2B;IAKvB7K,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAA0F,iBAAA,EAAAoF,OAAA,GAAAL,SAAA,CAAAG,KAAA,kBAAAH,SAAA,CAAAG,KAAA,CAAAG,SAAA,kBAAAN,SAAA,CAAAG,KAAA,CAAAG,SAAA,CAAAC,IAAA,cAAAF,OAAA,KAAArG,SAAA,GAAAqG,OAAA,SAA0C;IAM1C9K,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAA0F,iBAAA,EAAAE,OAAA,GAAA6E,SAAA,CAAAG,KAAA,kBAAAH,SAAA,CAAAG,KAAA,CAAAK,SAAA,kBAAAR,SAAA,CAAAG,KAAA,CAAAK,SAAA,CAAAD,IAAA,cAAApF,OAAA,KAAAnB,SAAA,GAAAmB,OAAA,SAA0C;;;;;IA+DzE5F,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IALCH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgC,eAAA,IAAA6G,GAAA,EAAAqC,QAAA,CAAA/G,kBAAA,CAAAgH,SAAA,CAAA7J,EAAA,aAAA8J,QAAA,CAAA9J,EAAA,GAEJ;IAEFtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAiJ,kBAAA,MAAAmC,QAAA,CAAAlC,IAAA,kBAAAkC,QAAA,CAAAlC,IAAA,CAAAC,UAAA,OAAAiC,QAAA,CAAAlC,IAAA,kBAAAkC,QAAA,CAAAlC,IAAA,CAAAE,SAAA,MACF;;;;;IACQpJ,EAAA,CAAAC,cAAA,eAKC;IACPD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IALSH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgC,eAAA,IAAA6G,GAAA,EAAAwC,QAAA,CAAAlH,kBAAA,CAAAgH,SAAA,CAAA7J,EAAA,aAAA8J,QAAA,CAAA9J,EAAA,GAEZ;IAEFtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAA6K,QAAA,CAAA9B,YAAA,MACF;;;;;IACQtJ,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAlBpCH,EAAA,CAAAC,cAAA,eAA6G;IAE7GD,EAAA,CAAA4B,UAAA,IAAA0J,iEAAA,mBAOO;IACCtL,EAAA,CAAA4B,UAAA,IAAA2J,iEAAA,mBAOD;IACCvL,EAAA,CAAA4B,UAAA,IAAA4J,iEAAA,mBAA4B;IAChCxL,EAAA,CAAAG,YAAA,EAAM;;;;;IAhBDH,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAgL,QAAA,CAAA1B,YAAA,WAAgC;IAQxB1J,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAI,UAAA,SAAAgL,QAAA,CAAA1B,YAAA,eAAoC;IAOtC1J,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAI,UAAA,UAAAqL,SAAA,CAAW;;;;;;IAK1BzL,EAAA,CAAAC,cAAA,eAAwF;IACzDD,EAAA,CAAAS,UAAA,mBAAAiL,iFAAAtI,MAAA;MAAApD,EAAA,CAAAW,aAAA,CAAAgL,KAAA;MAAA,MAAAR,SAAA,GAAAnL,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAA6K,QAAA,GAAA5L,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAkB,WAAA,CAAA0K,QAAA,CAAAC,sBAAA,CAAAV,SAAA,CAAA7J,EAAA,EAAA8B,MAAA,CAAuC;IAAA,EAAC;IAChFpD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,OAAAuL,QAAA,CAAAC,yBAAA,CAAAZ,SAAA,CAAAa,QAAA,EAAAb,SAAA,CAAA7J,EAAA,YACF;;;;;IA9BFtB,EAAA,CAAAC,cAAA,eAA4E;IACxED,EAAA,CAAAQ,SAAA,aAA6D;IAG7DR,EAAA,CAAA4B,UAAA,IAAAqK,0DAAA,mBAoBM;IAGNjM,EAAA,CAAA4B,UAAA,IAAAsK,0DAAA,mBAIM;IACVlM,EAAA,CAAAG,YAAA,EAAM;;;;;IA5ByCH,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAI,UAAA,YAAA+L,QAAA,CAAAC,oBAAA,CAAAjB,SAAA,CAAAa,QAAA,EAAAb,SAAA,CAAA7J,EAAA,EAAiD;IAuBjEtB,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAI,UAAA,SAAA+L,QAAA,CAAAJ,yBAAA,CAAAZ,SAAA,CAAAa,QAAA,EAAAb,SAAA,CAAA7J,EAAA,MAA2D;;;;;;IA7E1FtB,EAAA,CAAAC,cAAA,eAA+B;IAEvBD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAERH,EAAA,CAAAC,cAAA,eAAsB;IAEdD,EAAA,CAAAQ,SAAA,eAA+F;IAC/FR,EAAA,CAAAC,cAAA,gBASC;IACLD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAELH,EAAA,CAAAC,cAAA,kBAA2D;IAA/BD,EAAA,CAAAS,UAAA,mBAAA4L,sEAAA;MAAA,MAAAhJ,WAAA,GAAArD,EAAA,CAAAW,aAAA,CAAA2L,KAAA;MAAA,MAAAnB,SAAA,GAAA9H,WAAA,CAAAtC,SAAA;MAAA,MAAAwL,QAAA,GAAAvM,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAkB,WAAA,CAAAqL,QAAA,CAAA5B,QAAA,CAAAQ,SAAA,CAAAP,KAAA,CAAoB;IAAA,EAAC;IACtD5K,EAAA,CAAAQ,SAAA,aAAmD;IACvDR,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,gBAAuB;IAWvBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACDH,EAAA,CAAAQ,SAAA,gBAA+F;IACnGR,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAC,cAAA,gBAAwB;IACpBD,EAAA,CAAAQ,SAAA,aAAsD;IACtDR,EAAA,CAAAC,cAAA,aAA2B;IACvBD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChCH,EAAA,CAAAC,cAAA,aAA2B;IACvBD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAERH,EAAA,CAAA4B,UAAA,KAAA4K,oDAAA,mBAgCM;;;;;;;IAhFExM,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAA4K,SAAA,CAAAP,KAAA,CAAAC,UAAA,MACJ;IAOY7K,EAAA,CAAAM,SAAA,GAMV;IANUN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgC,eAAA,IAAA6G,GAAA,EAAA4D,OAAA,CAAAtI,kBAAA,CAAAgH,SAAA,CAAA7J,EAAA,UAAA6J,SAAA,CAAAP,KAAA,CAAA8B,YAAA,GAMV;IAEF1M,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,OAAAqF,OAAA,GAAAuF,SAAA,CAAAP,KAAA,CAAAG,SAAA,kBAAAI,SAAA,CAAAP,KAAA,CAAAG,SAAA,CAAAC,IAAA,cAAApF,OAAA,KAAAnB,SAAA,GAAAmB,OAAA,cACF;IAQQ5F,EAAA,CAAAM,SAAA,GAMJ;IANIN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgC,eAAA,KAAA6G,GAAA,EAAA4D,OAAA,CAAAtI,kBAAA,CAAAgH,SAAA,CAAA7J,EAAA,UAAA6J,SAAA,CAAAP,KAAA,CAAA+B,YAAA,GAMJ;IAEF3M,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,OAAAiE,OAAA,GAAA2G,SAAA,CAAAP,KAAA,CAAAK,SAAA,kBAAAE,SAAA,CAAAP,KAAA,CAAAK,SAAA,CAAAD,IAAA,cAAAxG,OAAA,KAAAC,SAAA,GAAAD,OAAA,cACF;IAOMxE,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAkM,OAAA,CAAA9G,YAAA,CAAAwF,SAAA,CAAAyB,UAAA,OACJ;IAGI5M,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAkM,OAAA,CAAA9G,YAAA,CAAAwF,SAAA,CAAAnF,QAAA,OACJ;IAEuBhG,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAI,UAAA,SAAA+K,SAAA,CAAAa,QAAA,IAAAb,SAAA,CAAAa,QAAA,CAAA/G,MAAA,KAA+C;;;;;;;;;AD3Z9E,OAAM,MAAO4H,qBAAqB;EA6D9BC,YACWC,MAAsB,EACtBC,OAAe,EACfC,kBAAqC,EACrCC,aAA2B,EAC3BC,eAA+B,EAC/BC,aAAoB,EACpBC,iBAAmC,EACnCC,oBAAyC,EACxCC,aAAuB,EACvBC,aAA4B;IAT7B,KAAAT,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAlElB,KAAAC,oBAAoB,GAAkB,IAAI;IAG1C,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAA3I,WAAW,GAAG,EAAE;IAChB,KAAAmB,eAAe,GAAG,EAAE;IACpB,KAAA9E,gBAAgB,GAAG,EAAE;IACrB,KAAAuM,eAAe,GAAc,EAAE;IAE/B,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,WAAW,GAAG,EAAE;IAGhB,KAAA9F,cAAc,GAA8B,IAAI;IAEhD,KAAA+F,UAAU,GAAG,IAAI;IACjB,KAAAzK,MAAM,GAAG,KAAK;IACd,KAAA0K,UAAU,GAAG,KAAK;IAClB,KAAAC,YAAY,GAAG,IAAI;IAiB1B;IAEA;IAEO,KAAAlH,sBAAsB,GAAG,EAAE;IAE3B,KAAAmH,YAAY,GAAG,IAAI;IAEnB,KAAAC,OAAO,GAAG,KAAK;IAEtB;IACO,KAAAC,gBAAgB,GAAkB,IAAI;IAE7C;IACO,KAAAC,sBAAsB,GAAgB,IAAIC,GAAG,EAAE;IAC/C,KAAAtE,qBAAqB,GAAU,EAAE;IACjC,KAAAnB,eAAe,GAAkB,IAAI;IA0arC,KAAAR,gBAAgB,GAAqB;MACxCkG,UAAU,EAAE,IAAI;MAChB7G,YAAY,EAAE,IAAI;MAClB8G,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAE;KACrB;IA0EM,KAAAjG,YAAY,GAAG,IAAI;IA8TP,KAAAkG,QAAQ,GAAGA,QAAQ;IA3yBlC,IAAI,CAACb,UAAU,GAAG,IAAI;IACtB,IAAI,CAACrG,YAAY,GAAG,IAAI,CAACmF,MAAM,CAACgC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC;IACtE,IAAI,CAAChC,kBAAkB,CAACiC,aAAa,CAAC,IAAI,CAACtH,YAAY,CAAC,CAACuH,SAAS,CAC7DC,GAAG,IAAI;MACJ,IAAI,CAACvH,cAAc,GAAGuH,GAAG;MAEzB,IAAI,CAAC3B,oBAAoB,GAAG2B,GAAG,CAACC,MAAM,CAACC,IAAI,CAAEC,KAAK,IAAI;QAClD,IAAI,IAAI,CAAC1H,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC,MAAM,CAAC,KAAK/H,SAAS,CAAC0P,gBAAgB,CAACC,gBAAgB,EAAE;UACpG,OAAOF,KAAK,CAAChL,IAAI,KAAKzE,SAAS,CAAC0P,gBAAgB,CAACE,MAAM;SAC1D,MAAM;UACH,OAAOH,KAAK,CAAChL,IAAI,KAAKzE,SAAS,CAAC0P,gBAAgB,CAACG,MAAM;;MAE/D,CAAC,CAAC,EAAErO,EAAE;MAEN,IAAI,CAACkC,MAAM,GAAG4L,GAAG,CAACQ,kBAAkB,KAAK,CAAC;MAC1C,IAAI,CAACjI,QAAQ,GAAGyH,GAAG,CAACS,KAAK,CAACC,MAAM,CAACxO,EAAE;MACnC8L,aAAa,CAAC2C,QAAQ,CAACX,GAAG,CAACpE,IAAI,CAAC;MAEhC,IAAI,CAACgF,aAAa,GAAG;QACjBC,WAAW,EAAEb,GAAG,CAACpE,IAAI;QACrBkF,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;UACR5L,IAAI,EAAE,EAAE;UACR6L,KAAK,EAAE,CACH;YACIpF,IAAI,EAAE,IAAI,CAACqC,iBAAiB,CAACgD,OAAO,CAAC,SAAS,CAAC;YAC/CC,MAAM,EAAE;WACX,EACD;YACItF,IAAI,EAAE,IAAI,CAACqC,iBAAiB,CAACgD,OAAO,CAAC,gBAAgB,CAAC;YACtDC,MAAM,EAAE,IAAI;YACZC,IAAI,EAAE;WACT,EACD;YACIvF,IAAI,EAAEoE,GAAG,CAACpE,IAAI;YACdsF,MAAM,EAAE;WACX,EACD;YACItF,IAAI,EAAE,IAAI,CAACqC,iBAAiB,CAACgD,OAAO,CAAC,eAAe,CAAC;YACrDC,MAAM,EAAE;WACX;;OAGZ;MAED,IAAI,CAACE,YAAY,EAAE;IACvB,CAAC,CAAC;EACV;EAEAC,QAAQA,CAAA,GACR;EAEAC,kBAAkBA,CAAA;IACdC,UAAU,CAAC,MAAK;MACZ,IAAI,CAACzC,UAAU,GAAI,IAAI,CAACN,eAAe,IAAIgD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjD,eAAe,CAAC,CAAC3I,MAAM,GAAG,CAAC,IAAK,IAAI,CAACgC,sBAAsB,CAAChC,MAAM,GAAG,CAAC;IACtI,CAAC,EAAE,CAAC,CAAC;EACT;EAEAuL,YAAYA,CAAA;IACR,IAAI,CAAClD,oBAAoB,CAACkD,YAAY,CAAC,IAAI,CAAC5I,YAAY,CAAC,CAACuH,SAAS,CAAEC,GAAG,IAAI;MACxE,IAAI,CAAC1B,WAAW,GAAG0B,GAAG,CAAC,MAAM,CAAC;MAC9B,IAAI,CAAChB,YAAY,GAAG,IAAI,CAACV,WAAW,CAACzI,MAAM,GAAG,CAAC,GAAG,IAAI,CAACyI,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE;MAC1E,IAAI,CAACW,OAAO,GAAG,IAAI,CAACX,WAAW,CAACzI,MAAM,GAAG,CAAC;MAC1C,IAAI,CAACwC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;IACrC,CAAC,CAAC;EACN;EAEAqJ,kBAAkBA,CAACC,WAAA,GAAuB,IAAI;IAC1C,IAAIA,WAAW,EAAE;MACb,IAAI,CAAC5D,eAAe,CAAC6D,IAAI,EAAE;;IAG/B,IAAI,IAAI,CAAC5C,YAAY,CAACnJ,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACkI,eAAe,CAACpD,OAAO,EAAE;MAC9B,IAAI,CAACkE,UAAU,GAAG,KAAK;MACvB,IAAI,CAACE,YAAY,GAAG,KAAK;MACzB;;IAGJ,IAAI,CAACb,oBAAoB,CAACwD,kBAAkB,CAAC,IAAI,CAAClJ,YAAY,EAAE,IAAI,CAACwG,YAAY,CAAC,CAC7Ee,SAAS,CAAEC,GAAG,IAAI;MAEf,IAAI,CAACjB,YAAY,GAAGiB,GAAG,CAAC6B,eAAe,KAAK,YAAY;MACxDC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAChD,YAAY,CAAC;MACnD,IAAI,IAAI,CAACA,YAAY,EAAE;QACnB,IAAI,CAAChB,eAAe,CAACpD,OAAO,EAAE;QAE9B,IAAI,CAAC/E,WAAW,GAAG,EAAE;QACrB,IAAI,CAACmB,eAAe,GAAG,EAAE;QACzB,IAAI,CAAC6H,WAAW,GAAG,EAAE;QACrB,IAAI,CAACC,UAAU,GAAG,KAAK;QAEvB;;MAGJ,IAAI,CAAC5M,gBAAgB,GAAG+N,GAAG,CAACgC,QAAQ;MACpC,IAAI,CAACxD,eAAe,GAAGyD,KAAK,CAACC,OAAO,CAAClC,GAAG,CAACmC,IAAI,CAAC,IAAInC,GAAG,CAACmC,IAAI,CAACtM,MAAM,KAAK,CAAC,GAAG,IAAI,GAAGmK,GAAG,CAACmC,IAAI;MAEzF,IAAI,CAACvD,WAAW,GAAGoB,GAAG,CAACoC,aAAa;MACpC,IAAI,CAAC3D,aAAa,GAAGuB,GAAG,CAACqC,SAAS,CAAC,0BAA0B,CAAC;MAC9D,IAAI,CAAC3D,gBAAgB,GAAGsB,GAAG,CAACqC,SAAS,CAAC,6BAA6B,CAAC;MACpE,IAAI,CAAC1D,cAAc,GAAGqB,GAAG,CAACqC,SAAS,CAAC,2BAA2B,CAAC;MAEhE,IAAI,IAAI,CAAC7D,eAAe,EAAE;QACtB,IAAI,CAAC8D,gBAAgB,EAAE;OAC1B,MAAM;QACH,IAAI,CAACvL,eAAe,GAAG,EAAE;QACzB,IAAI,CAAC6H,WAAW,GAAG,EAAE;QACrB,IAAI,CAAChJ,WAAW,GAAG,EAAE;QACrB,IAAI,CAACoJ,YAAY,GAAG,IAAI;;MAG5B,IAAI,CAACjB,eAAe,CAACpD,OAAO,EAAE;IAClC,CAAC,EAAE,MAAK,CACR,CAAC,EAAE,MAAK;MACJ,IAAI,CAACkE,UAAU,GAAG,KAAK;IAE3B,CAAC,CAAC;EAEV;EAEA0D,oBAAoBA,CAACZ,WAAW,GAAG,IAAI;IACnC,IAAIA,WAAW,EAAE;MACb,IAAI,CAAC5D,eAAe,CAAC6D,IAAI,EAAE;;IAE/B,IAAI,CAAC1D,oBAAoB,CAACsE,yBAAyB,CAAC,IAAI,CAAChK,YAAY,CAAC,CACjEuH,SAAS,CAAEC,GAAG,IAAI;MACf,IAAI,CAACnI,sBAAsB,GAAGmI,GAAG,CAACmC,IAAI;MAEtC,IAAI,CAACpE,eAAe,CAACpD,OAAO,EAAE;IAClC,CAAC,CAAC;EACV;EAEA2H,gBAAgBA,CAAA;IACZ,IAAI,CAACvL,eAAe,GAAG,EAAE;IACzB,IAAI,CAACnB,WAAW,GAAG,EAAE;IAErB,IAAI,CAAC2I,aAAa,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACQ,YAAY,CAAC,IAAI,EAAE;IAElE,IAAI,CAAC,IAAI,CAACT,aAAa,IAAI,CAAC,IAAI,CAACS,YAAY,EAAE;IAE/CwC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClD,aAAa,CAAC,CAACkE,OAAO,CAAEC,YAAoB,IAAI;MAE7D,IAAI,CAAC,IAAI,CAAC3L,eAAe,CAAC4L,QAAQ,CAAC,GAAGD,YAAY,EAAE,CAAC,EAAE;QACnD,IAAI,CAAC3L,eAAe,CAAC6L,IAAI,CAAC,GAAGF,YAAY,EAAE,CAAC;;MAGhD,IAAI,CAAC,IAAI,CAAC9M,WAAW,CAAC8M,YAAY,CAAC,EAAE;QACjC,IAAI,CAAC9M,WAAW,CAAC8M,YAAY,CAAC,GAAG,EAAE;;MAGvC,IAAI,CAAC9M,WAAW,CAAC8M,YAAY,CAAC,GAAG,CAC7B,GAAG,IAAI,CAAC9M,WAAW,CAAC8M,YAAY,CAAC,EACjC,GAAG,IAAI,CAACnE,aAAa,CAACmE,YAAY,CAAC,CACtC;IACL,CAAC,CAAC;EACN;EAEAG,YAAYA,CAACC,KAAK;IACd,IAAI,CAAC9D,YAAY,GAAG8D,KAAK;IACzB,IAAI,CAACzK,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;EACrC;EAEA6C,sBAAsBA,CAAA;IAClB,IAAI,CAACiD,aAAa,CAAC4E,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;MAC7CC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACT,CAAC;EACN;EAEA7K,gBAAgBA,CAAC8K,MAAM,EAAExB,WAAA,GAAuB,IAAI;IAChD,IAAIwB,MAAM,KAAK,YAAY,EAAE;MACzB,IAAI,CAAC/B,YAAY,EAAE;KACtB,MAAM;MACH,IAAI,CAACM,kBAAkB,CAACC,WAAW,CAAC;MACpC,IAAI,CAACY,oBAAoB,CAACZ,WAAW,CAAC;;EAE9C;EAEAyB,eAAeA,CAAC9D,UAA2B,EAAEE,QAAyB,EAAE6D,OAAmB,EAAEC,SAAqB;IAC9G,IAAI,CAACpF,oBAAoB,CAACkF,eAAe,CAAC9D,UAAU,EAAEE,QAAQ,CAAC,CAACO,SAAS,CAAEC,GAAG,IAAI;MAC9E,IAAI,CAAC3H,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;MAClCiL,SAAS,IAAIA,SAAS,EAAE;IAC5B,CAAC,EAAGC,KAAK,IAAI;MACTF,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EAGAG,eAAeA,CAACC,WAAW;IAEvB,MAAMC,YAAY,GAAG,EAAE;IACvB,IAAI,CAAC9N,WAAW,CAAC6N,WAAW,CAAC,CAAChB,OAAO,CAAC,CAACkB,IAAI,EAAEC,KAAK,KAAI;MAElDF,YAAY,CAACC,IAAI,CAACzO,YAAY,CAAC,GAAG0O,KAAK;IAC3C,CAAC,CAAC;IACF,OAAOF,YAAY;EACvB;EAEAG,mBAAmBA,CAACJ,WAAmB,EAAEK,UAAqC,EAAET,OAAmB,EAAEC,SAAqB;IACtH,MAAMS,QAAQ,GAAG,IAAI,CAACP,eAAe,CAACC,WAAW,CAAC;IAClD,IAAI,CAACvF,oBAAoB,CAAC2F,mBAAmB,CAACC,UAAU,CAAC,CAAC/D,SAAS,CAAEC,GAAG,IAAI;MACxE,IAAI,CAAC3H,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;MACjCiL,SAAS,IAAIA,SAAS,EAAE;IAC5B,CAAC,EAAGC,KAAK,IAAI;MACTF,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EAGAnN,IAAIA,CAAC4M,KAA4B;IAC7B,MAAMkB,eAAe,GAAGlB,KAAK,CAACmB,SAAS,CAACC,OAAO,CAACC,aAAa;IAC7D,MAAMC,aAAa,GAAGtB,KAAK,CAACuB,iBAAiB,CAACH,OAAO,CAACC,aAAa;IACnE,MAAMG,QAAQ,GAAGxB,KAAK,CAACa,IAAI,CAACO,OAAO,CAACC,aAAa;IAEjD,MAAMI,iBAAiB,GAAGP,eAAe,CAAC,IAAI,CAAC;IAC/C,MAAMQ,eAAe,GAAGJ,aAAa,CAAC,IAAI,CAAC;IAE3C,MAAMK,aAAa,GAAGT,eAAe,CAAC,eAAe,CAAC;IACtD,MAAMU,gBAAgB,GAAGV,eAAe,CAAC,kBAAkB,CAAC;IAE5D,MAAMW,WAAW,GAAGP,aAAa,CAAC,eAAe,CAAC;IAClD,MAAMQ,cAAc,GAAGR,aAAa,CAAC,kBAAkB,CAAC;IAExD,MAAMS,cAAc,GAAGP,QAAQ,CAAC,mBAAmB,CAAC;IACpD,MAAMQ,QAAQ,GAAGR,QAAQ,CAAC,WAAW,CAAC;IACtC,MAAMS,kBAAkB,GAAGT,QAAQ,CAAC,sBAAsB,CAAC;IAC3D,MAAMU,YAAY,GAAGlC,KAAK,CAACmC,YAAY;IAEvC,IAAIT,eAAe,KAAKD,iBAAiB,IAAIzB,KAAK,CAACmC,YAAY,KAAKnC,KAAK,CAACoC,aAAa,EAAE;IAEzF,MAAMC,UAAU,GAAGA,CAAA,KAAK;MAEpB,IAAIZ,iBAAiB,KAAK,gBAAgB,EAAE;QACxC/T,iBAAiB,CACbsS,KAAK,CAACuB,iBAAiB,CAAClC,IAAI,EAC5BW,KAAK,CAACmB,SAAS,CAAC9B,IAAI,EACpBW,KAAK,CAACoC,aAAa,EACnBpC,KAAK,CAACmC,YAAY,CACrB;QAED,IAAIV,iBAAiB,KAAKC,eAAe,EAAE;QAE3C,IAAI,CAACpB,eAAe,CAACyB,cAAc,EAAE,IAAI,CAAC7S,SAAS,CAAC4S,cAAc,CAAC,EAAE1S,EAAE,EAAE,MAAK;UAC1E1B,iBAAiB,CACbsS,KAAK,CAACmB,SAAS,CAAC9B,IAAI,EACpBW,KAAK,CAACuB,iBAAiB,CAAClC,IAAI,EAC5BW,KAAK,CAACmC,YAAY,EAClBnC,KAAK,CAACoC,aAAa,CACtB;UACD,IAAI,CAAC9G,aAAa,CAACmF,KAAK,CAAC,IAAI,CAACtF,iBAAiB,CAACgD,OAAO,CAAC,6BAA6B,CAAC,CAAC;QAC3F,CAAC,EAAE,MAAK;UACJ,IAAI,CAAC7C,aAAa,CAACgH,OAAO,CAAC,IAAI,CAACnH,iBAAiB,CAACgD,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACjG,CAAC,CAAC;OACL,MAAM;QACH,IAAI6B,KAAK,CAACuB,iBAAiB,KAAKvB,KAAK,CAACmB,SAAS,EAAE;UAC7C1T,eAAe,CAACuS,KAAK,CAACmB,SAAS,CAAC9B,IAAI,EAAEW,KAAK,CAACoC,aAAa,EAAEpC,KAAK,CAACmC,YAAY,CAAC;UAC9E,IAAI,CAACpB,mBAAmB,CAACU,iBAAiB,EAAE;YACxCc,SAAS,EAAE,IAAI,CAAC7B,eAAe,CAACe,iBAAiB,CAAC;YAClDe,WAAW,EAAEZ,gBAAgB;YAC7Ba,gBAAgB,EAAEX,cAAc;YAChC3P,QAAQ,EAAEwP,aAAa;YACvBe,aAAa,EAAEb,WAAW;YAC1Bc,aAAa,EAAE,IAAI,CAACjN,YAAY;YAChCkN,SAAS,EAAE,IAAI,CAAC1T,SAAS,CAAC0S,gBAAgB,CAAC,EAAExS,EAAE;YAC/CyT,cAAc,EAAE,IAAI,CAAC3T,SAAS,CAAC4S,cAAc,CAAC,EAAE1S;WACnD,EAAE,MAAK;YACJ3B,eAAe,CAACuS,KAAK,CAACmB,SAAS,CAAC9B,IAAI,EAAEW,KAAK,CAACmC,YAAY,EAAEnC,KAAK,CAACoC,aAAa,CAAC;YAC9E,IAAI,CAAC9G,aAAa,CAACmF,KAAK,CAAC,IAAI,CAACtF,iBAAiB,CAACgD,OAAO,CAAC,kCAAkC,CAAC,CAAC;UAChG,CAAC,EAAE,MAAK;YACJ,IAAI,CAAC7C,aAAa,CAACgH,OAAO,CAAC,IAAI,CAACnH,iBAAiB,CAACgD,OAAO,CAAC,sCAAsC,CAAC,CAAC;UACtG,CAAC,CAAC;SACL,MAAM;UACHzQ,iBAAiB,CACbsS,KAAK,CAACuB,iBAAiB,CAAClC,IAAI,EAC5BW,KAAK,CAACmB,SAAS,CAAC9B,IAAI,EACpBW,KAAK,CAACoC,aAAa,EACnBpC,KAAK,CAACmC,YAAY,CACrB;UAED,IAAI,CAACpB,mBAAmB,CAACU,iBAAiB,EACtC;YACIc,SAAS,EAAE,IAAI,CAAC7B,eAAe,CAACe,iBAAiB,CAAC;YAClDe,WAAW,EAAEZ,gBAAgB;YAC7Ba,gBAAgB,EAAEf,eAAe,KAAK,gBAAgB,GAAG,IAAI,GAAGI,cAAc;YAC9E3P,QAAQ,EAAEwP,aAAa;YACvBe,aAAa,EAAEhB,eAAe,KAAK,gBAAgB,GAAG,IAAI,GAAGG,WAAW;YACxEc,aAAa,EAAE,IAAI,CAACjN,YAAY;YAChCkN,SAAS,EAAE,IAAI,CAAC1T,SAAS,CAAC0S,gBAAgB,CAAC,EAAExS,EAAE;YAC/CyT,cAAc,EAAE,IAAI,CAAC3T,SAAS,CAAC4S,cAAc,CAAC,EAAE1S;WACnD,EACD,MAAK;YACD1B,iBAAiB,CACbsS,KAAK,CAACmB,SAAS,CAAC9B,IAAI,EACpBW,KAAK,CAACuB,iBAAiB,CAAClC,IAAI,EAC5BW,KAAK,CAACmC,YAAY,EAClBnC,KAAK,CAACoC,aAAa,CACtB;YACD,IAAI,CAAC9G,aAAa,CAACmF,KAAK,CAAC,IAAI,CAACtF,iBAAiB,CAACgD,OAAO,CAAC,kCAAkC,CAAC,CAAC;UAChG,CAAC,EAAE,MAAK;YACJ,IAAI,CAAC7C,aAAa,CAACgH,OAAO,CAAC,IAAI,CAACnH,iBAAiB,CAACgD,OAAO,CAAC,sCAAsC,CAAC,CAAC;UACtG,CAAC,CAAC;;;IAGlB,CAAC;IAED,IAAIsD,iBAAiB,KAAK,gBAAgB,EAAE;MACxC,IAAIO,QAAQ,KAAK,OAAO,EAAE;QACtB,IAAI,CAAC,IAAI,CAACc,qBAAqB,CAACrB,iBAAiB,EAAEC,eAAe,EAAE1B,KAAK,CAACmC,YAAY,EAAEnC,KAAK,CAACoC,aAAa,CAAC,EAAE;UAC1GzU,IAAI,CAACoV,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,CAACgD,OAAO,CAAC,SAAS,CAAC;YAChD8E,IAAI,EAAE,IAAI,CAAC9H,iBAAiB,CAACgD,OAAO,CAAC,GAAGsD,iBAAiB,KAAKC,eAAe,GAAG,QAAQ,GAAG,UAAU,oFAAoF,CAAC;YAC1LwB,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAChI,iBAAiB,CAACgD,OAAO,CAAC,UAAU,CAAC;YAC7DiF,gBAAgB,EAAE,IAAI;YACtBC,cAAc,EAAE;WACnB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;YACf,IAAIA,MAAM,CAACC,WAAW,EAAE;cACpBnB,UAAU,EAAE;;UAEpB,CAAC,CAAC;SACL,MAAM;UACHA,UAAU,EAAE;;OAGnB,MAAM,IAAIL,QAAQ,KAAK,OAAO,EAAE;QAC7B;QACA;QACA;QAEA,MAAMyB,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAACjC,iBAAiB,EAAEC,eAAe,EAAE1B,KAAK,CAACmC,YAAY,EAAEnC,KAAK,CAACoC,aAAa,EAAEH,kBAAkB,CAAC;QACrJ,IAAIwB,iBAAiB,KAAK,CAAC,CAAC,EAAE;UAC1B,OAAO9V,IAAI,CAACoV,IAAI,CAAC;YACbC,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,CAACgD,OAAO,CAAC,OAAO,CAAC;YAC9C8E,IAAI,EAAE,IAAI,CAAC9H,iBAAiB,CAACgD,OAAO,CAAC,uFAAuF,CAAC;YAC7H+E,IAAI,EAAE,OAAO;YACbC,iBAAiB,EAAE,IAAI,CAAChI,iBAAiB,CAACgD,OAAO,CAAC,IAAI;WACzD,CAAC;SACL,MAAM,IAAIsF,iBAAiB,KAAK,CAAC,EAAE;UAChC9V,IAAI,CAACoV,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,CAACgD,OAAO,CAAC,SAAS,CAAC;YAChD8E,IAAI,EAAE,IAAI,CAAC9H,iBAAiB,CAACgD,OAAO,CAAC,sIAAsI,CAAC;YAC5K+E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAAChI,iBAAiB,CAACgD,OAAO,CAAC,UAAU,CAAC;YAC7DiF,gBAAgB,EAAE,IAAI;YACtBC,cAAc,EAAE;WACnB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;YACf,IAAIA,MAAM,CAACC,WAAW,EAAE;cACpBnB,UAAU,EAAE;;UAEpB,CAAC,CAAC;SACL,MAAM;UACHA,UAAU,EAAE;;OAEnB,MAAM;QACHA,UAAU,EAAE;;KAEnB,MAAM;MACHA,UAAU,EAAE;;EAEpB;EAEA5O,YAAYA,CAACkQ,IAAY;IACrB;IACA,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IAC3B,MAAMG,KAAK,GAAGF,IAAI,CAACG,QAAQ,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAMC,OAAO,GAAGN,IAAI,CAACO,UAAU,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,OAAO,GAAGH,KAAK,IAAII,OAAO,EAAE;EAChC;EAEAhV,SAASA,CAACsT,WAAW;IACjB,OAAO,IAAI,CAACrT,gBAAgB,CAAC,SAAS,CAAC,CAACiO,IAAI,CAAEyD,IAAI,IAAI;MAClD,OAAOA,IAAI,CAAC8B,aAAa,KAAK,CAAC,IAAI,CAACjN,YAAY,IAAImL,IAAI,CAAC2B,WAAW,KAAK,CAACA,WAAW,IAAI3B,IAAI,CAAClN,UAAU,KAAK,IAAI,CAACuI,YAAY;IAClI,CAAC,CAAC;EACN;EAEAjN,YAAYA,CAAC0R,WAAW,EAAEjE,QAAQ;IAC9B,IAAI,CAAC1G,cAAc,GAAG;MAClBN,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BkH,QAAQ,EAAE,IAAI,CAACzN,gBAAgB,CAAC,WAAW,CAAC,CAACwR,WAAW,CAAC,CAACvR,EAAE;MAC5DwU,IAAI,EAAE,IAAI,CAAC1H,YAAY;MACvBQ,QAAQ;MACR0H,WAAW,EAAE,IAAI,CAACtR,WAAW,CAAC6N,WAAW,CAAC,CAAC0D,GAAG,CAAExD,IAAI,IAAKA,IAAI,CAACzO,YAAY;KAC7E;IACD,IAAI,CAACiJ,aAAa,CAAC4E,IAAI,CAAC,IAAI,CAACqE,iBAAiB,EAAE;MAC5CnE,QAAQ,EAAE;KACb,CAAC;EACN;EAEA5Q,UAAUA,CAACoR,WAAW;IAElB,MAAMpE,UAAU,GAAG,IAAI,CAACpN,gBAAgB,CAAC,WAAW,CAAC,CAACwR,WAAW,CAAC,CAACvR,EAAE;IACrE,MAAMsN,QAAQ,GAAG,IAAI,CAACxN,SAAS,CAACqN,UAAU,CAAC,EAAEnN,EAAE;IAC/C,MAAMgV,WAAW,GAAG,IAAI,CAACtR,WAAW,CAAC6N,WAAW,CAAC,CAAC0D,GAAG,CAAExD,IAAI,IAAKA,IAAI,CAACzO,YAAY,CAAC;IAElFzE,IAAI,CAACoV,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,CAACgD,OAAO,CAAC,eAAe,CAAC;MACtD8E,IAAI,EAAE,IAAI,CAAC9H,iBAAiB,CAACgD,OAAO,CAAC,gDAAgD,CAAC;MACtF+E,IAAI,EAAE,SAAS;MACfE,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE;KACnB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAACpI,oBAAoB,CAACmJ,cAAc,CAAC,IAAI,CAAC7O,YAAY,EAAE6G,UAAU,EAAE6H,WAAW,EAAE1H,QAAQ,CAAC,CAACO,SAAS,CAAEC,GAAG,IAAI;UAC7G,IAAI,CAAC3H,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;UAClC,IAAI,CAAC+F,aAAa,CAACgH,OAAO,CAAC,IAAI,CAACnH,iBAAiB,CAACgD,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAChG,CAAC,CAAC;;IAEV,CAAC,CAAC;EACN;EAWAtL,iBAAiBA,CAAC8N,WAAW,EAAElE,cAAc,EAAEC,QAAQ;IAEnD,MAAMH,UAAU,GAAG,IAAI,CAACpN,gBAAgB,CAAC,WAAW,CAAC,CAACwR,WAAW,CAAC,CAACvR,EAAE;IACrE,MAAMoV,MAAM,GAAG,IAAI,CAACtV,SAAS,CAACqN,UAAU,CAAC;IACzC,MAAMkI,mBAAmB,GAAG,IAAI,CAAC3R,WAAW,CAAC6N,WAAW,CAAC,CAAC,IAAI,CAAC7N,WAAW,CAAC6N,WAAW,CAAC,CAAC5N,MAAM,GAAG,CAAC,CAAC,CAACe,QAAQ;IAE5G;IACA,MAAM6I,gBAAgB,GAAG9O,MAAM,CAAC4W,mBAAmB,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,IAAI,CAAC9W,MAAM,CAAC4W,mBAAmB,CAAC,EAAE,SAAS,CAAC;IAE9G,IAAI,CAACpO,gBAAgB,GAAG;MACpB,GAAG,IAAI,CAACA,gBAAgB;MACxBmG,UAAU,EAAE,IAAI;MAChBD,UAAU;MACV7G,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/B+G,cAAc;MACdC,QAAQ;MACRC;KACH;IAED,IAAI,CAACtB,aAAa,CAAC4E,IAAI,CAAC,IAAI,CAAC2E,cAAc,EAAE;MACzCzE,QAAQ,EAAE;KAEb,CAAC;EAEN;EAEA;EACA5O,cAAcA,CAACyO,KAAiB,EAAEa,IAAS;IAEvCb,KAAK,CAAC6E,cAAc,EAAE;IACtB7E,KAAK,CAAC8E,eAAe,EAAE;IAEvB,IAAI9E,KAAK,CAAC+E,MAAM,CAAC,WAAW,CAAC,KAAK,aAAa,EAAE;MAC7C;;IAGJ,MAAMC,UAAU,GAAG,IAAI,CAACC,aAAa,CAACpE,IAAI,CAAC;IAE3C;IACA,IAAI,IAAI,CAACzE,gBAAgB,KAAK4I,UAAU,EAAE;MACtC,IAAI,CAAC5I,gBAAgB,GAAG,IAAI;KAC/B,MAAM;MACH;MACA,IAAI,CAACA,gBAAgB,GAAG4I,UAAU;;EAE1C;EAEAvS,cAAcA,CAACoO,IAAS;IACpB,MAAMmE,UAAU,GAAG,IAAI,CAACC,aAAa,CAACpE,IAAI,CAAC;IAC3C,OAAO,IAAI,CAACzE,gBAAgB,KAAK4I,UAAU;EAC/C;EAEAC,aAAaA,CAACpE,IAAS;IACnB;IACA,OAAO,GAAGA,IAAI,CAACzO,YAAY,IAAIyO,IAAI,CAACxO,IAAI,IAAIwO,IAAI,CAAC1O,QAAQ,IAAI,aAAa,EAAE;EAChF;EAEA+S,iBAAiBA,CAAA;IACb,IAAI,CAAC9I,gBAAgB,GAAG,IAAI;EAChC;EAGA+I,eAAeA,CAACnF,KAAiB;IAC7B,MAAM+E,MAAM,GAAG/E,KAAK,CAAC+E,MAAqB;IAE1C;IACA,IAAI,CAACA,MAAM,CAACK,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAACL,MAAM,CAACK,OAAO,CAAC,WAAW,CAAC,EAAE;MACnE,IAAI,CAACF,iBAAiB,EAAE;;EAEhC;EAKA;EACA5U,WAAWA,CAACuQ,IAAS;IACjB,IAAI,CAACqE,iBAAiB,EAAE;IAExB,IAAI,CAACxO,YAAY,GAAGmK,IAAI;IAExB,IAAI,CAACxF,aAAa,CAAC4E,IAAI,CAAC,IAAI,CAACoF,gBAAgB,EAAE;MAC3ClF,QAAQ,EAAE;KACb,CAAC;EAEN;EAEA1P,oBAAoBA,CAACoQ,IAAS,EAAEnE,QAAQ;IACpC,MAAMzM,WAAW,GAAG;MAChByI,KAAK,EAAE,kDAAkD;MACzD4M,KAAK,EAAE;KACV;IAED,MAAMC,cAAc,GAAG;MACnB7M,KAAK,EAAE,iCAAiC;MACxC4M,KAAK,EAAE;KACV;IAED,MAAME,YAAY,GAAG;MACjB9M,KAAK,EAAE,6BAA6B;MACpC4M,KAAK,EAAE;KACV;IAGD3X,IAAI,CAACoV,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,CAACgD,OAAO,CAAC,eAAe,CAAC;MACtD8E,IAAI,EAAE,IAAI,CAAC9H,iBAAiB,CAACgD,OAAO,CAAClO,WAAW,CAAC4Q,IAAI,CAACxO,IAAI,CAAC,CAAC;MAC5D6Q,IAAI,EAAE,SAAS;MACfE,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpBF,iBAAiB,EAAE,IAAI,CAAChI,iBAAiB,CAACgD,OAAO,CAAC,KAAK,CAAC;MACxDsH,gBAAgB,EAAE,IAAI,CAACtK,iBAAiB,CAACgD,OAAO,CAAC,QAAQ;KAC5D,CAAC,CAACmF,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAAClD,eAAe,CAACO,IAAI,CAACzO,YAAY,EAAEsK,QAAQ,EAAE,MAAK;UACnD,IAAI,CAACpB,aAAa,CAACmF,KAAK,CAAC,IAAI,CAACtF,iBAAiB,CAACgD,OAAO,CAACqH,YAAY,CAAC3E,IAAI,CAACxO,IAAI,CAAC,CAAC,CAAC;QACrF,CAAC,EAAE,MAAK;UACJ,IAAI,CAACiJ,aAAa,CAACgH,OAAO,CAAC,IAAI,CAACnH,iBAAiB,CAACgD,OAAO,CAACoH,cAAc,CAAC1E,IAAI,CAACxO,IAAI,CAAC,CAAC,CAAC;QACzF,CAAC,CAAC;;IAEV,CAAC,CAAC;IAEF,IAAI,CAAC6S,iBAAiB,EAAE;EAC5B;EAEApU,eAAeA,CAAC+P,IAAS,EAAEnE,QAAgB;IAEvC,IAAI,CAACwI,iBAAiB,EAAE;IAExB,IAAI,CAAC7O,gBAAgB,GAAG;MACpBmG,UAAU,EAAEqE,IAAI,CAACzO,YAAY;MAC7BmK,UAAU,EAAEsE,IAAI,CAAC2B,WAAW;MAC5B9M,YAAY,EAAEmL,IAAI,CAAC8B,aAAa;MAChC1S,WAAW,EAAE4Q,IAAI,CAAC5Q,WAAW;MAC7ByV,cAAc,EAAE7E,IAAI,CAAC3Q,eAAe;MACpCwM,QAAQ,EAAEA;KACb;IAED,IAAI,CAACrB,aAAa,CAAC4E,IAAI,CAAC,IAAI,CAAC2E,cAAc,EAAE;MACzCzE,QAAQ,EAAE;KACb,CAAC;EAEN;EAEAwF,aAAaA,CAAA;IAEThY,IAAI,CAACoV,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,CAACgD,OAAO,CAAC,eAAe,CAAC;MACtD8E,IAAI,EAAE,IAAI,CAAC9H,iBAAiB,CAACgD,OAAO,CAAC,sCAAsC,CAAC;MAC5E+E,IAAI,EAAE,SAAS;MACfE,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpBF,iBAAiB,EAAE,IAAI,CAAChI,iBAAiB,CAACgD,OAAO,CAAC,gBAAgB,CAAC;MACnEsH,gBAAgB,EAAE,IAAI,CAACtK,iBAAiB,CAACgD,OAAO,CAAC,QAAQ;KAC5D,CAAC,CAACmF,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAACvI,eAAe,CAAC6D,IAAI,EAAE;QAC3B,IAAI,CAAC1D,oBAAoB,CAACuK,aAAa,CAAC,IAAI,CAACjQ,YAAY,CAAC,CAACuH,SAAS,CAAEC,GAAG,IAAI;UACzEvP,IAAI,CAACoV,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,CAACgD,OAAO,CAAC,UAAU,CAAC;YACjD8E,IAAI,EAAE,IAAI,CAAC9H,iBAAiB,CAACgD,OAAO,CAAC,4BAA4B,CAAC;YAClE+E,IAAI,EAAE;WACT,CAAC;UACF;UACA,IAAI,CAAC3N,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC;QAC7C,CAAC,EAAGkL,KAAK,IAAI;UACT9S,IAAI,CAACoV,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAAC7H,iBAAiB,CAACgD,OAAO,CAAC,QAAQ,CAAC;YAC/C8E,IAAI,EAAE,IAAI,CAAC9H,iBAAiB,CAACgD,OAAO,CAAC,4BAA4B,CAAC;YAClE+E,IAAI,EAAE;WACT,CAAC;QACN,CAAC,EAAE,MAAK;UACJ,IAAI,CAACjI,eAAe,CAACpD,OAAO,EAAE;QAClC,CAAC,CAAC;;IAEV,CAAC,CAAC;EACN;EAEA5F,kBAAkBA,CAAC2T,eAAuB,EAAEvT,IAAkC,EAAEwT,WAAoB;IAChG,QAAQxT,IAAI;MACR,KAAK,MAAM;QACP,OAAO,IAAI,CAACsJ,aAAa,CAACiK,eAAe,CAAC,EAAExI,IAAI,CAACyD,IAAI,IAAIA,IAAI,CAACiF,OAAO,KAAKD,WAAW,CAAC;MAC1F,KAAK,SAAS;QACV,OAAO,IAAI,CAACjK,gBAAgB,CAACgK,eAAe,CAAC,EAAExI,IAAI,CAACyD,IAAI,IAAIA,IAAI,CAACkF,UAAU,KAAKF,WAAW,CAAC;MAChG,KAAK,OAAO;QACR,OAAO,IAAI,CAAClK,aAAa,CAACiK,eAAe,CAAC,IAAI,IAAI,CAAChK,gBAAgB,CAACgK,eAAe,CAAC;;EAEhG;EAEAI,WAAWA,CAAA;IACP,IAAI,CAAC1U,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC1B,IAAI,CAAC8J,oBAAoB,CAAC6K,8BAA8B,CAAC,IAAI,CAACvQ,YAAY,CAAC,CAACuH,SAAS,CAAEC,GAAG,IAAI;MAC1F,IAAI,CAAC5B,aAAa,CAACgH,OAAO,CAACpF,GAAG,CAACgJ,OAAO,CAAC;IAC3C,CAAC,EAAGzF,KAAK,IAAI;MAET,IAAI,CAACnP,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1B,IAAI,CAACgK,aAAa,CAACmF,KAAK,CAACA,KAAK,CAACyF,OAAO,CAAC;IAC3C,CAAC,CAAC;EACN;EAEAzN,QAAQA,CAAC0N,SAAS;IACd,IAAI,CAACjB,iBAAiB,EAAE;IACxB,IAAI,CAAClK,aAAa,CAACoL,SAAS,CAACD,SAAS,CAAC,CAAClJ,SAAS,CAAEC,GAAG,IAAI;MACtD,IAAI,CAAC5B,aAAa,CAACgH,OAAO,CAAC,0BAA0B,CAAC;MACtD,IAAI,CAAC/M,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;IACtC,CAAC,EAAGkL,KAAK,IAAI;MACT,IAAI,CAACnF,aAAa,CAACmF,KAAK,CAACA,KAAK,CAACyF,OAAO,IAAI,uBAAuB,CAAC;IACtE,CAAC,CAAC;EACN;EAEA/Q,YAAYA,CAAA;IACP,MAAMkR,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAChCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAAChL,oBAAoB,CAACyI,QAAQ,EAAE,CAAC;IACjE,IAAI,CAAChJ,aAAa,CAACwL,mBAAmB,CAACH,QAAQ,CAAC,CAACpJ,SAAS,CAAEC,GAAG,IAAI;MAC/D,IAAI,CAAC5B,aAAa,CAACgH,OAAO,CAAC,qCAAqC,CAAC;MACjE,IAAI,CAAC/M,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;IACtC,CAAC,EAAGkL,KAAK,IAAI;MACT9S,IAAI,CAACoV,IAAI,CAAC;QACNC,KAAK,EAAE,sBAAsB;QAC7BE,IAAI,EAAEzC,KAAK,CAACgG,OAAO,GAAG,SAAS,GAAG,OAAO;QACzCxD,IAAI,EAAExC,KAAK,CAACgG,OAAO,IAAIhG,KAAK,CAACyF,OAAO,IAAI,kCAAkC;QAC1E/C,iBAAiB,EAAE;OACtB,CAAC,CAACG,IAAI,CAAEC,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,WAAW,EAAE;UACpB,IAAI,CAAC1I,OAAO,CAAC4L,QAAQ,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAChR,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC6F,oBAAoB,CAAC,CAAC;;MAE5G,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAoL,sBAAsBA,CAAC7M,QAAe,EAAE8M,OAAe;IACnD,IAAI,IAAI,CAACvK,sBAAsB,CAACwK,GAAG,CAACD,OAAO,CAAC,EAAE;MAC1C,OAAO9M,QAAQ,CAAC/G,MAAM,CAAC,CAAC;;IAG5B;IACA,MAAM+T,eAAe,GAAGhN,QAAQ,CAACiN,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;MACjD,MAAMnO,IAAI,GAAGmO,GAAG,CAACzP,YAAY,KAAK,MAAM,GAClC,GAAGyP,GAAG,CAACjQ,IAAI,EAAEC,UAAU,IAAI,EAAE,IAAIgQ,GAAG,CAACjQ,IAAI,EAAEE,SAAS,IAAI,EAAE,EAAE,CAACgQ,IAAI,EAAE,GACnED,GAAG,CAAC7P,YAAY,IAAI,EAAE;MAC5B,OAAO4P,GAAG,GAAGlO,IAAI,CAAC/F,MAAM;IAC5B,CAAC,EAAE,CAAC,CAAC;IAEL,MAAMoU,aAAa,GAAGL,eAAe,GAAGhN,QAAQ,CAAC/G,MAAM;IAEvD;IACA,OAAOoU,aAAa,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;EACrC;EAEAjN,oBAAoBA,CAACJ,QAAe,EAAE8M,OAAe;IACjD,IAAI,CAAC9M,QAAQ,IAAIA,QAAQ,CAAC/G,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,EAAE;;IAGb,MAAMqU,KAAK,GAAG,IAAI,CAACT,sBAAsB,CAAC7M,QAAQ,EAAE8M,OAAO,CAAC;IAC5D,OAAO9M,QAAQ,CAACuN,KAAK,CAAC,CAAC,EAAED,KAAK,CAAC;EACnC;EAEAvN,yBAAyBA,CAACC,QAAe,EAAE8M,OAAe;IACtD,IAAI,CAAC9M,QAAQ,IAAIA,QAAQ,CAAC/G,MAAM,KAAK,CAAC,IAAI,IAAI,CAACsJ,sBAAsB,CAACwK,GAAG,CAACD,OAAO,CAAC,EAAE;MAChF,OAAO,CAAC;;IAGZ,MAAMQ,KAAK,GAAG,IAAI,CAACT,sBAAsB,CAAC7M,QAAQ,EAAE8M,OAAO,CAAC;IAC5D,OAAOU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzN,QAAQ,CAAC/G,MAAM,GAAGqU,KAAK,CAAC;EAC/C;EAEAzN,sBAAsBA,CAACiN,OAAe,EAAE5G,KAAY;IAChDA,KAAK,CAAC8E,eAAe,EAAE;IACvB9E,KAAK,CAAC6E,cAAc,EAAE;IAEtB,MAAM2C,SAAS,GAAG,IAAI,CAACC,aAAa,CAACb,OAAO,CAAC;IAC7C,IAAI,CAACY,SAAS,IAAI,CAACA,SAAS,CAAC1N,QAAQ,IAAI0N,SAAS,CAAC1N,QAAQ,CAAC/G,MAAM,KAAK,CAAC,EAAE;MACtE;;IAIJ,IAAI,CAAC2U,gBAAgB,CAACF,SAAS,CAAC1N,QAAQ,EAAE8M,OAAO,CAAC;EACtD;EAEAc,gBAAgBA,CAAC5N,QAAe,EAAE8M,OAAe;IAC7C,IAAI,CAAC5O,qBAAqB,GAAG8B,QAAQ;IACrC,IAAI,CAACjD,eAAe,GAAG+P,OAAO;IAE9B,IAAI,CAACvL,aAAa,CAAC4E,IAAI,CAAC,IAAI,CAAC0H,gBAAgB,EAAE;MAC3CxH,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACT,CAAC;EACN;EAEAqH,aAAaA,CAACb,OAAe;IACzB,KAAK,MAAMjG,WAAW,IAAI,IAAI,CAAC1M,eAAe,EAAE;MAC5C,MAAM2T,OAAO,GAAG,IAAI,CAAC9U,WAAW,CAAC6N,WAAW,CAAC,IAAI,EAAE;MACnD,MAAMjI,KAAK,GAAGkP,OAAO,CAACxK,IAAI,CAACyD,IAAI,IAAIA,IAAI,CAACzR,EAAE,KAAKwX,OAAO,CAAC;MACvD,IAAIlO,KAAK,EAAE;QACP,OAAOA,KAAK;;;IAIpB;IACA,OAAO,IAAI,CAAC3D,sBAAsB,CAACqI,IAAI,CAACyD,IAAI,IAAIA,IAAI,CAACzR,EAAE,KAAKwX,OAAO,CAAC,IAAI,IAAI;EAChF;EAEAiB,gBAAgBA,CAACC,SAAiB,EAAEC,SAAiB,EAAEC,OAAe;IAClE,MAAMC,cAAc,GAAGpa,MAAM,CAAC,GAAGia,SAAS,IAAIC,SAAS,EAAE,CAAC;IAC1D,MAAMG,YAAY,GAAGra,MAAM,CAAC,GAAGia,SAAS,IAAIE,OAAO,EAAE,CAAC;IAEtD,IAAIE,YAAY,CAACC,QAAQ,CAACF,cAAc,CAAC,EAAE;MACvCC,YAAY,CAACE,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;;IAE9B,OAAOF,YAAY,CAACG,MAAM,EAAE;EAChC;EAEAvF,qBAAqBA,CAACnC,WAAW,EAAE2H,eAAe,EAAEnG,YAAY,EAAEoG,SAAS;IACvE,IAAI5H,WAAW,KAAK2H,eAAe,EAAE;MACjC,OAAO,IAAI;;IAGf,MAAM/L,UAAU,GAAG,IAAI,CAACpN,gBAAgB,CAAC,WAAW,CAAC,CAACwR,WAAW,CAAC,CAACvR,EAAE;IACrE,MAAMoZ,sBAAsB,GAAG,IAAI,CAAC1V,WAAW,CAAC6N,WAAW,CAAC,CAAC,IAAI,CAAC7N,WAAW,CAAC6N,WAAW,CAAC,CAAC5N,MAAM,GAAG,CAAC,CAAC;IACtG,MAAM0V,gBAAgB,GAAGD,sBAAsB,CAACnW,IAAI;IACpD,MAAMoS,mBAAmB,GAAG5W,MAAM,CAAC2a,sBAAsB,CAAC1U,QAAQ,CAAC,CAACuU,MAAM,EAAE;IAE5E,MAAM7D,MAAM,GAAG,IAAI,CAACtV,SAAS,CAACqN,UAAU,CAAC;IACzC,MAAMmM,aAAa,GAAGlE,MAAM,CAACmE,oBAAoB;IACjD,MAAMC,aAAa,GAAGpE,MAAM,CAACqE,cAAc;IAC3C,MAAMC,mBAAmB,GAAG,IAAI,CAACjB,gBAAgB,CAACrD,MAAM,CAAC7Q,UAAU,EAAE6Q,MAAM,CAAC5Q,UAAU,EAAE4Q,MAAM,CAAC1Q,QAAQ,CAAC;IAExG,IAAIiV,uBAAuB;IAC3B,IAAIN,gBAAgB,KAAK,OAAO,EAAE;MAC9BM,uBAAuB,GAAGlb,MAAM,CAAC4W,mBAAmB,CAAC,CAAC2D,GAAG,CAACM,aAAa,EAAE,SAAS,CAAC,CAACN,GAAG,CAACQ,aAAa,EAAE,SAAS,CAAC,CAACP,MAAM,EAAE;KAC7H,MAAM;MACHU,uBAAuB,GAAGlb,MAAM,CAAC4W,mBAAmB,CAAC,CAAC2D,GAAG,CAACQ,aAAa,EAAE,SAAS,CAAC,CAACP,MAAM,EAAE;;IAGhG,OAAOU,uBAAuB,IAAID,mBAAmB;EACzD;EAEApF,qBAAqBA,CAAC/C,WAAW,EAAE2H,eAAe,EAAEnG,YAAY,EAAEoG,SAAS,EAAES,aAAa;IACtF,IAAI7G,YAAY,KAAKoG,SAAS,IAAI5H,WAAW,KAAK2H,eAAe,EAAE;MAC/D,OAAO,IAAI;;IAGf,MAAM/L,UAAU,GAAG,IAAI,CAACpN,gBAAgB,CAAC,WAAW,CAAC,CAACwR,WAAW,CAAC,CAACvR,EAAE;IAErE,MAAM6Z,uBAAuB,GAAG,IAAI,CAACnW,WAAW,CAAC6N,WAAW,CAAC,CAAC5N,MAAM,GAAG,CAAC;IAExE,IAAIyV,sBAAsB;IAE1B,IAAI7H,WAAW,KAAK2H,eAAe,IAAInG,YAAY,KAAK8G,uBAAuB,IAAItI,WAAW,KAAK2H,eAAe,IAAInG,YAAY,GAAG8G,uBAAuB,EAAE;MAC1J,OAAO,IAAI;;IAGf,IAAIV,SAAS,KAAKU,uBAAuB,EAAE;MACvCT,sBAAsB,GAAG,IAAI,CAAC1V,WAAW,CAAC6N,WAAW,CAAC,CAAC,IAAI,CAAC7N,WAAW,CAAC6N,WAAW,CAAC,CAAC5N,MAAM,GAAG,CAAC,CAAC;KACnG,MAAM;MACHyV,sBAAsB,GAAG,IAAI,CAAC1V,WAAW,CAAC6N,WAAW,CAAC,CAAC,IAAI,CAAC7N,WAAW,CAAC6N,WAAW,CAAC,CAAC5N,MAAM,GAAG,CAAC,CAAC;;IAEpGiM,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuJ,sBAAsB,CAAC;IAE7D,MAAM/D,mBAAmB,GAAG5W,MAAM,CAAC2a,sBAAsB,CAAC1U,QAAQ,CAAC,CAACuU,MAAM,EAAE;IAC5ErJ,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEwF,mBAAmB,CAAC;IAEtF,MAAMD,MAAM,GAAG,IAAI,CAACtV,SAAS,CAACqN,UAAU,CAAC;IACzC,MAAMmM,aAAa,GAAGlE,MAAM,CAACmE,oBAAoB;IACjD,MAAMG,mBAAmB,GAAG,IAAI,CAACjB,gBAAgB,CAACrD,MAAM,CAAC7Q,UAAU,EAAE6Q,MAAM,CAAC5Q,UAAU,EAAE4Q,MAAM,CAAC1Q,QAAQ,CAAC;IAExG,MAAMoV,uBAAuB,GAAGrb,MAAM,CAAC4W,mBAAmB,CAAC,CAAC2D,GAAG,CAACY,aAAa,EAAE,SAAS,CAAC,CAACX,MAAM,EAAE;IAElG,MAAMc,gBAAgB,GAAGtb,MAAM,CAAC4W,mBAAmB,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,IAAI,CAAC9W,MAAM,CAAC4W,mBAAmB,CAAC,EAAE,SAAS,CAAC;IAE9G,IAAIuE,aAAa,GAAGG,gBAAgB,EAAE;MAClC,OAAO,CAAC,CAAC;;IAGbnK,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEiK,uBAAuB,CAAC;IAC/DlK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6J,mBAAmB,CAAC;IACvD,IAAII,uBAAuB,GAAGJ,mBAAmB,EAAE;MAC/C,OAAO,CAAC;;IAGZ,OAAO,CAAC;EACZ;EAGA5W,cAAcA,CAAC0U,OAAO;IAElB,OAAO,IAAI,CAAC9K,WAAW,CAAC+D,QAAQ,CAAC,CAAC+G,OAAO,CAAC;EAE9C;EAAC,QAAAwC,CAAA;qBAl3BQzO,qBAAqB,EAAA7M,EAAA,CAAAub,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzb,EAAA,CAAAub,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1b,EAAA,CAAAub,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAA5b,EAAA,CAAAub,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAA9b,EAAA,CAAAub,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAhc,EAAA,CAAAub,iBAAA,CAAAU,EAAA,CAAAC,KAAA,GAAAlc,EAAA,CAAAub,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAApc,EAAA,CAAAub,iBAAA,CAAAc,EAAA,CAAAC,mBAAA,GAAAtc,EAAA,CAAAub,iBAAA,CAAAgB,EAAA,CAAAC,QAAA,GAAAxc,EAAA,CAAAub,iBAAA,CAAAkB,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAArB9P,qBAAqB;IAAA+P,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;iBAArBC,GAAA,CAAA3F,eAAA,CAAAjU,MAAA,CAAuB;QAAA,UAAApD,EAAA,CAAAid,iBAAA;;;;;;;;QCvBpCjd,EAAA,CAAAC,cAAA,aAA+C;QAGvCD,EAAA,CAAAQ,SAAA,4BAAyE;QACzER,EAAA,CAAAC,cAAA,aAAwB;QAIYD,EAAA,CAAAE,MAAA,GAAwB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxDH,EAAA,CAAAC,cAAA,mBASC;QAJOD,EAAA,CAAAS,UAAA,2BAAAyc,kEAAA9Z,MAAA;UAAA,OAAA4Z,GAAA,CAAA5O,YAAA,GAAAhL,MAAA;QAAA,EAA0B,oBAAA+Z,2DAAA/Z,MAAA;UAAA,OAChB4Z,GAAA,CAAA/K,YAAA,CAAA7O,MAAA,CAAoB;QAAA,EADJ;;QAK9BpD,EAAA,CAAA4B,UAAA,KAAAwb,2CAAA,uBAAkF;QACtFpd,EAAA,CAAAG,YAAA,EAAY;QAEhBH,EAAA,CAAAC,cAAA,cAAyD;QACrBD,EAAA,CAAAS,UAAA,mBAAA4c,wDAAA;UAAA,OAASL,GAAA,CAAA1S,sBAAA,EAAwB;QAAA,EAAC;QAC9DtK,EAAA,CAAAQ,SAAA,aAA6C;QAC7CR,EAAA,CAAAE,MAAA,sBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAQC;QANOD,EAAA,CAAAS,UAAA,mBAAA6c,wDAAA;UAAA,OAASN,GAAA,CAAA9E,WAAA,EAAa;QAAA,EAAC;QAO3BlY,EAAA,CAAAQ,SAAA,aAA0E;QAC9ER,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAA4H;QAA5ED,EAAA,CAAAS,UAAA,mBAAA8c,wDAAA;UAAA,OAASP,GAAA,CAAAnF,aAAA,EAAe;QAAA,EAAC;QACrE7X,EAAA,CAAAQ,SAAA,aAA8B;QAClCR,EAAA,CAAAG,YAAA,EAAS;QAIrBH,EAAA,CAAAC,cAAA,eAAgD;QAExCD,EAAA,CAAA4B,UAAA,KAAA4b,qCAAA,kBAoLM;QACNxd,EAAA,CAAA4B,UAAA,KAAA6b,8CAAA,2BAEe;QACfzd,EAAA,CAAA4B,UAAA,KAAA8b,8CAAA,2BAEe;QACf1d,EAAA,CAAA4B,UAAA,KAAA+b,8CAAA,2BAEe;QACnB3d,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,eAAuE;QAIzCD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAC7BH,EAAA,CAAAC,cAAA,aAAiB;QACbD,EAAA,CAAAE,MAAA,gFACJ;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAERH,EAAA,CAAAC,cAAA,eAAqI;QAAxDD,EAAA,CAAAS,UAAA,gCAAAmd,kEAAAxa,MAAA;UAAA,OAAsB4Z,GAAA,CAAA1X,IAAA,CAAAlC,MAAA,CAAY;QAAA,EAAC;QAC5GpD,EAAA,CAAA4B,UAAA,KAAAic,8CAAA,2BAWe;QACf7d,EAAA,CAAA4B,UAAA,KAAAkc,8CAAA,2BAWe;QACnB9d,EAAA,CAAAG,YAAA,EAAM;QAO9BH,EAAA,CAAA4B,UAAA,KAAAmc,6CAAA,iCAAA/d,EAAA,CAAAge,sBAAA,CAEc;QACdhe,EAAA,CAAA4B,UAAA,KAAAqc,6CAAA,iCAAAje,EAAA,CAAAge,sBAAA,CAEc;QACdhe,EAAA,CAAA4B,UAAA,KAAAsc,6CAAA,iCAAAle,EAAA,CAAAge,sBAAA,CAEc;QAEdhe,EAAA,CAAA4B,UAAA,KAAAuc,6CAAA,iCAAAne,EAAA,CAAAge,sBAAA,CAEc;QAEdhe,EAAA,CAAA4B,UAAA,KAAAwc,6CAAA,kCAAApe,EAAA,CAAAge,sBAAA,CAkCc;QAEdhe,EAAA,CAAA4B,UAAA,KAAAyc,6CAAA,iCAAAre,EAAA,CAAAge,sBAAA,CAYc;QAEdhe,EAAA,CAAA4B,UAAA,KAAA0c,6CAAA,iCAAAte,EAAA,CAAAge,sBAAA,CASc;QAEdhe,EAAA,CAAA4B,UAAA,KAAA2c,6CAAA,iCAAAve,EAAA,CAAAge,sBAAA,CASc;QAEdhe,EAAA,CAAA4B,UAAA,KAAA4c,6CAAA,kCAAAxe,EAAA,CAAAge,sBAAA,CAiBc;QAEdhe,EAAA,CAAA4B,UAAA,KAAA6c,6CAAA,mCAAAze,EAAA,CAAAge,sBAAA,CAoFc;;;QAhdche,EAAA,CAAAM,SAAA,GAA+B;QAA/BN,EAAA,CAAAI,UAAA,kBAAA4c,GAAA,CAAAhN,aAAA,CAA+B;QAKfhQ,EAAA,CAAAM,SAAA,GAAwB;QAAxBN,EAAA,CAAA0F,iBAAA,CAAA1F,EAAA,CAAA4C,WAAA,gBAAwB;QAKxC5C,EAAA,CAAAM,SAAA,GAA6C;QAA7CN,EAAA,CAAA0e,qBAAA,gBAAA1e,EAAA,CAAA4C,WAAA,wBAA6C;QAF7C5C,EAAA,CAAAI,UAAA,oBAAmB,gCAAA4c,GAAA,CAAA5O,YAAA,cAAA4O,GAAA,CAAA7O,YAAA,IAAA6O,GAAA,CAAA/O,UAAA;QAQKjO,EAAA,CAAAM,SAAA,GAAc;QAAdN,EAAA,CAAAI,UAAA,YAAA4c,GAAA,CAAAtP,WAAA,CAAc;QAIqB1N,EAAA,CAAAM,SAAA,GAAiD;QAAjDN,EAAA,CAAAI,UAAA,aAAA4c,GAAA,CAAAxZ,MAAA,IAAAwZ,GAAA,CAAA7O,YAAA,IAAA6O,GAAA,CAAA/O,UAAA,CAAiD;QAO5GjO,EAAA,CAAAM,SAAA,GAGd;QAHcN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgE,eAAA,KAAA2a,GAAA,GAAA3B,GAAA,CAAAxZ,MAAA,EAAAwZ,GAAA,CAAAxZ,MAAA,EAGd,aAAAwZ,GAAA,CAAA7O,YAAA,IAAA6O,GAAA,CAAA/O,UAAA;QAGajO,EAAA,CAAAM,SAAA,GAAkE;QAAlEN,EAAA,CAAAI,UAAA,YAAA4c,GAAA,CAAAxZ,MAAA,iDAAkE;QAECxD,EAAA,CAAAM,SAAA,GAAiD;QAAjDN,EAAA,CAAAI,UAAA,aAAA4c,GAAA,CAAAxZ,MAAA,IAAAwZ,GAAA,CAAA7O,YAAA,IAAA6O,GAAA,CAAA/O,UAAA,CAAiD;QAQrFjO,EAAA,CAAAM,SAAA,GAAa;QAAbN,EAAA,CAAAI,UAAA,SAAA4c,GAAA,CAAA3O,OAAA,CAAa;QAqLxCrO,EAAA,CAAAM,SAAA,GAAwC;QAAxCN,EAAA,CAAAI,UAAA,UAAA4c,GAAA,CAAA3O,OAAA,KAAA2O,GAAA,CAAA/O,UAAA,KAAA+O,GAAA,CAAAxZ,MAAA,CAAwC;QAGxCxD,EAAA,CAAAM,SAAA,GAAgB;QAAhBN,EAAA,CAAAI,UAAA,SAAA4c,GAAA,CAAA/O,UAAA,CAAgB;QAGhBjO,EAAA,CAAAM,SAAA,GAAiC;QAAjCN,EAAA,CAAAI,UAAA,SAAA4c,GAAA,CAAA7O,YAAA,KAAA6O,GAAA,CAAA/O,UAAA,CAAiC;QAaNjO,EAAA,CAAAM,SAAA,GAA0C;QAA1CN,EAAA,CAAAI,UAAA,oBAAA4c,GAAA,CAAA/V,sBAAA,CAA0C;QACzDjH,EAAA,CAAAM,SAAA,GAAgB;QAAhBN,EAAA,CAAAI,UAAA,SAAA4c,GAAA,CAAA9O,UAAA,CAAgB;QAYhBlO,EAAA,CAAAM,SAAA,GAAkC;QAAlCN,EAAA,CAAAI,UAAA,UAAA4c,GAAA,CAAA9O,UAAA,KAAA8O,GAAA,CAAA7O,YAAA,CAAkC", "names": ["moveItemInArray", "transferArrayItem", "<PERSON><PERSON>", "AppConfig", "moment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "date_r27", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵelement", "ɵɵlistener", "AutoScheduleComponent_div_22_ng_container_2_div_7_Template_a_click_4_listener", "ɵɵrestoreView", "_r35", "locationKey_r29", "ɵɵnextContext", "$implicit", "ctx_r33", "tmp_b_0", "ɵɵresetView", "editSchedule", "getConfig", "responseMetadata", "id", "AutoScheduleComponent_div_22_ng_container_2_div_7_Template_a_click_6_listener", "ctx_r36", "deletePlan", "ɵɵelementContainer", "ɵɵelementContainerStart", "ɵɵtemplate", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_3_ng_container_1_Template", "ɵɵelementContainerEnd", "_r25", "ɵɵpureFunction1", "_c5", "item_r38", "description", "break_durations", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_7_Template_button_click_1_listener", "_r50", "ctx_r48", "onEditMatch", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_7_Template_button_click_5_listener", "ctx_r51", "onUnscheduleTimeSlot", "ɵɵpipeBind1", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_8_Template_button_click_1_listener", "_r56", "ctx_r54", "onEditEventTime", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_8_Template_button_click_5_listener", "ctx_r58", "AutoScheduleComponent_div_22_ng_container_2_div_16_Template_div_click_0_listener", "$event", "restoredCtx", "_r62", "ctx_r61", "isLock", "toggleDropdown", "AutoScheduleComponent_div_22_ng_container_2_div_16_button_1_Template", "AutoScheduleComponent_div_22_ng_container_2_div_16_button_2_Template", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_3_Template", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_4_Template", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_7_Template", "AutoScheduleComponent_div_22_ng_container_2_div_16_ng_container_8_Template", "ɵɵpureFunction2", "_c6", "ctx_r31", "isMatchHasConflict", "isWarningMatch", "stage_id", "time_slot_id", "type", "tmp_4_0", "undefined", "ɵɵclassProp", "isDropdownOpen", "AutoScheduleComponent_div_22_ng_container_2_footer_17_Template_button_click_1_listener", "_r65", "ctx_r63", "openModalAddBreak", "listMatches", "length", "AutoScheduleComponent_div_22_ng_container_2_div_7_Template", "AutoScheduleComponent_div_22_ng_container_2_Template_div_cdkDropListDropped_15_listener", "_r67", "ctx_r66", "drop", "AutoScheduleComponent_div_22_ng_container_2_div_16_Template", "AutoScheduleComponent_div_22_ng_container_2_footer_17_Template", "ctx_r28", "ɵɵtextInterpolate", "getShortTime", "tmp_2_0", "begin_date", "begin_time", "tmp_3_0", "end_time", "AutoScheduleComponent_div_22_ng_container_2_Template", "ctx_r1", "listLocationIds", "AutoScheduleComponent_ng_container_23_ng_container_1_Template", "_r17", "AutoScheduleComponent_ng_container_24_ng_container_1_Template", "_r19", "AutoScheduleComponent_ng_container_25_ng_container_1_Template", "_r21", "AutoScheduleComponent_ng_container_35_div_1_ng_container_1_ng_container_1_Template", "_r23", "item_r72", "AutoScheduleComponent_ng_container_35_div_1_ng_container_1_Template", "ctx_r71", "AutoScheduleComponent_ng_container_35_div_1_Template", "ctx_r5", "listUnScheduledMatches", "AutoScheduleComponent_ng_container_36_Template_button_click_4_listener", "_r77", "ctx_r76", "autoGenerate", "AutoScheduleComponent_ng_template_37_Template_app_modal_setup_schedule_onSubmit_0_listener", "_r80", "ctx_r79", "onScheduleAction", "ctx_r8", "seasonId", "tournamentId", "tournamentInfo", "AutoScheduleComponent_ng_template_39_Template_app_modal_update_config_onSubmit_0_listener", "_r83", "ctx_r82", "ctx_r10", "selectedConfig", "AutoScheduleComponent_ng_template_41_Template_app_modal_crud_break_onSubmit_0_listener", "_r86", "ctx_r85", "ctx_r12", "breakModalParams", "AutoScheduleComponent_ng_template_43_Template_app_modal_update_match_onSubmit_0_listener", "_r89", "ctx_r88", "ctx_r14", "selectedItem", "_c7", "ctx_r93", "selectedMatchId", "ref_r92", "ɵɵtextInterpolate2", "user", "first_name", "last_name", "ctx_r94", "referee_name", "AutoScheduleComponent_ng_template_45_div_9_span_2_Template", "AutoScheduleComponent_ng_template_45_div_9_span_3_Template", "AutoScheduleComponent_ng_template_45_div_9_button_4_Template", "referee_type", "ctx_r91", "AutoScheduleComponent_ng_template_45_Template_button_click_4_listener", "_r99", "modal_r90", "dismiss", "AutoScheduleComponent_ng_template_45_div_9_Template", "ctx_r16", "selectedMatchReferees", "AutoScheduleComponent_ng_template_47_Template_button_click_6_listener", "_r101", "ctx_r100", "openModalSetupSchedule", "AutoScheduleComponent_ng_template_53_Template_button_click_8_listener", "_r104", "item_r102", "ctx_r103", "swapTeam", "match", "round_name", "tmp_1_0", "home_team", "name", "away_team", "ctx_r111", "item_r105", "ref_r109", "ctx_r112", "AutoScheduleComponent_ng_template_55_div_22_div_2_span_2_Template", "AutoScheduleComponent_ng_template_55_div_22_div_2_span_3_Template", "AutoScheduleComponent_ng_template_55_div_22_div_2_span_4_Template", "last_r110", "AutoScheduleComponent_ng_template_55_div_22_div_3_Template_span_click_1_listener", "_r120", "ctx_r118", "toggleRefereeExpansion", "ctx_r108", "getRemainingRefereesCount", "referees", "AutoScheduleComponent_ng_template_55_div_22_div_2_Template", "AutoScheduleComponent_ng_template_55_div_22_div_3_Template", "ctx_r106", "getDisplayedReferees", "AutoScheduleComponent_ng_template_55_Template_button_click_8_listener", "_r124", "ctx_r123", "AutoScheduleComponent_ng_template_55_div_22_Template", "ctx_r26", "home_team_id", "away_team_id", "start_time", "AutoScheduleComponent", "constructor", "_route", "_router", "_tournamentService", "_stageService", "_loadingService", "_titleService", "_translateService", "_autoScheduleService", "_modalService", "_toastService", "leagueOrGroupStageId", "dateOptions", "listLocations", "responseMatches", "teamConflicts", "referee<PERSON><PERSON><PERSON><PERSON><PERSON>", "stageConflicts", "overEndTime", "isFetching", "hasMatches", "isScheduling", "selectedDate", "hasPlan", "activeDropdownId", "expandedRefereeMatches", "Set", "locationId", "timeSlotId", "lastTimeSlotId", "configId", "maxBreakDuration", "location", "snapshot", "paramMap", "get", "getTournament", "subscribe", "res", "stages", "find", "stage", "TOURNAMENT_TYPES", "groups_knockouts", "groups", "league", "is_locked_schedule", "group", "season", "setTitle", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "instant", "isLink", "link", "getListDates", "ngOnInit", "ngAfterViewChecked", "setTimeout", "Object", "keys", "getScheduleMatches", "showLoading", "show", "schedule_status", "console", "log", "metadata", "Array", "isArray", "data", "over_end_time", "conflicts", "mapListLocations", "getUnScheduleMatches", "getListUnScheduledMatches", "for<PERSON>ach", "locationName", "includes", "push", "onSelectDate", "event", "open", "modalSetupSchedule", "centered", "size", "action", "unScheduleMatch", "onError", "onSuccess", "error", "mapNewSlotIndex", "locationKey", "newSlotIndex", "item", "index", "updateLocationMatch", "updateData", "newIndex", "targetContainer", "container", "element", "nativeElement", "prevContainer", "previousContainer", "dragItem", "targetContainerId", "prevContainerId", "targetStageId", "targetLocationId", "prevStageId", "prevLocationId", "itemTimeSlotId", "itemType", "itemBreakDurations", "itemNewIndex", "currentIndex", "previousIndex", "handleDrop", "success", "new_index", "location_id", "prev_location_id", "prev_stage_id", "tournament_id", "config_id", "prev_config_id", "checkValidUpdateMatch", "fire", "title", "text", "icon", "confirmButtonText", "showCancelButton", "reverseButtons", "then", "result", "isConfirmed", "validUpdateStatus", "checkValidUpdateEvent", "time", "date", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "timeSlotIds", "map", "modalEditSchedule", "deleteSchedule", "config", "lastTimeSlotEndTime", "endOf", "diff", "modalCrudBreak", "preventDefault", "stopPropagation", "target", "dropdownId", "getDropdownId", "closeAllDropdowns", "onDocumentClick", "closest", "modalUpdateMatch", "break", "successMessage", "errorMessage", "cancelButtonText", "breakDurations", "clearSchedule", "scheduleMatchId", "itemCheckId", "team_id", "referee_id", "onClickLock", "updateTournamentScheduleStatus", "message", "matchInfo", "swapTeams", "formData", "FormData", "append", "autoGenerateMatches", "warning", "navigate", "getRefereeDisplayLimit", "matchId", "has", "totalNameLength", "reduce", "sum", "ref", "trim", "averageLength", "limit", "slice", "Math", "max", "matchItem", "findMatchById", "showRefereeModal", "modalRefereeList", "matches", "getConfigEndTime", "beginDate", "beginTime", "endTime", "parseBeginTime", "parseEndTime", "isBefore", "add", "toDate", "prevLocationKey", "prevIndex", "lastTimeSlotOfLocation", "lastTimeSlotType", "breakDuration", "break_match_duration", "matchDuration", "match_duration", "configLatestEndTime", "estimateNewMatchEndTime", "eventDuration", "totalTimeSlotOfLocation", "estimateNewEventEndTime", "maxEventDuration", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "TournamentService", "i3", "StageService", "i4", "LoadingService", "i5", "Title", "i6", "TranslateService", "i7", "AutoScheduleService", "i8", "NgbModal", "i9", "ToastrService", "_2", "selectors", "viewQuery", "AutoScheduleComponent_Query", "rf", "ctx", "ɵɵresolveDocument", "AutoScheduleComponent_Template_ng_select_ngModelChange_9_listener", "AutoScheduleComponent_Template_ng_select_change_9_listener", "AutoScheduleComponent_ng_option_11_Template", "AutoScheduleComponent_Template_button_click_13_listener", "AutoScheduleComponent_Template_button_click_16_listener", "AutoScheduleComponent_Template_button_click_18_listener", "AutoScheduleComponent_div_22_Template", "AutoScheduleComponent_ng_container_23_Template", "AutoScheduleComponent_ng_container_24_Template", "AutoScheduleComponent_ng_container_25_Template", "AutoScheduleComponent_Template_div_cdkDropListDropped_34_listener", "AutoScheduleComponent_ng_container_35_Template", "AutoScheduleComponent_ng_container_36_Template", "AutoScheduleComponent_ng_template_37_Template", "ɵɵtemplateRefExtractor", "AutoScheduleComponent_ng_template_39_Template", "AutoScheduleComponent_ng_template_41_Template", "AutoScheduleComponent_ng_template_43_Template", "AutoScheduleComponent_ng_template_45_Template", "AutoScheduleComponent_ng_template_47_Template", "AutoScheduleComponent_ng_template_49_Template", "AutoScheduleComponent_ng_template_51_Template", "AutoScheduleComponent_ng_template_53_Template", "AutoScheduleComponent_ng_template_55_Template", "ɵɵpropertyInterpolate", "_c8"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\auto-schedule.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\auto-schedule.component.html"], "sourcesContent": ["import { Component, HostListener, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TournamentService } from '../../../services/tournament.service';\r\nimport { LoadingService } from '../../../services/loading.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AutoScheduleService, UpdateLocationMatchParams } from '../../../services/auto-schedule.service';\r\nimport { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport Swal from 'sweetalert2';\r\nimport { BreakModalParams } from './modal-crud-break/modal-crud-break.component';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { AppConfig } from '../../../app-config';\r\nimport { StageService } from '../../../services/stage.service';\r\nimport { UpdateConfigParams } from './modal-update-config/modal-update-config.component';\r\nimport moment from 'moment';\r\n\r\n\r\n@Component({\r\n    selector: 'app-auto-schedule',\r\n    templateUrl: './auto-schedule.component.html',\r\n    styleUrls: ['./auto-schedule.component.scss']\r\n})\r\nexport class AutoScheduleComponent {\r\n    \r\n    public contentHeader: object;\r\n    public tournamentId: any;\r\n    public seasonId: any;\r\n    public leagueOrGroupStageId: number | null = null;\r\n    public tournamentInfo: null;\r\n    \r\n    public dateOptions = [];\r\n    public listLocations = {};\r\n    public listMatches = {};\r\n    public listLocationIds = [];\r\n    public responseMetadata = {};\r\n    public responseMatches: {} | null = {};\r\n    \r\n    public teamConflicts = {};\r\n    public refereeConflicts = {};\r\n    public stageConflicts = {};\r\n    public overEndTime = [];\r\n    \r\n    \r\n    public selectedConfig: UpdateConfigParams | null = null;\r\n    \r\n    public isFetching = true;\r\n    public isLock = false;\r\n    public hasMatches = false;\r\n    public isScheduling = true;\r\n    \r\n    \r\n    @ViewChild('modalSetupSchedule')\r\n    modalSetupSchedule!: TemplateRef<any>;\r\n    \r\n    @ViewChild('modalEditSchedule')\r\n    modalEditSchedule!: TemplateRef<any>;\r\n    \r\n    @ViewChild('modalCrudBreak')\r\n    modalCrudBreak!: TemplateRef<any>;\r\n    @ViewChild('modalUpdateMatch')\r\n    modalUpdateMatch: TemplateRef<any>;\r\n    \r\n    @ViewChild('modalRefereeList')\r\n    modalRefereeList: TemplateRef<any>;\r\n    \r\n    // public listUnScheduledMatches = [\r\n    \r\n    // ];\r\n    \r\n    public listUnScheduledMatches = [];\r\n    \r\n    public selectedDate = null;\r\n    \r\n    public hasPlan = false;\r\n    \r\n    // Simple dropdown state - track which dropdown is open\r\n    public activeDropdownId: string | null = null;\r\n    \r\n    // Referee expansion state\r\n    public expandedRefereeMatches: Set<number> = new Set();\r\n    public selectedMatchReferees: any[] = [];\r\n    public selectedMatchId: number | null = null;\r\n    \r\n    constructor(\r\n        public _route: ActivatedRoute,\r\n        public _router: Router,\r\n        public _tournamentService: TournamentService,\r\n        public _stageService: StageService,\r\n        public _loadingService: LoadingService,\r\n        public _titleService: Title,\r\n        public _translateService: TranslateService,\r\n        public _autoScheduleService: AutoScheduleService,\r\n        private _modalService: NgbModal,\r\n        private _toastService: ToastrService\r\n    ) {\r\n        this.isFetching = true;\r\n        this.tournamentId = this._route.snapshot.paramMap.get('tournament_id');\r\n        this._tournamentService.getTournament(this.tournamentId).subscribe(\r\n            (res) => {\r\n                this.tournamentInfo = res;\r\n                \r\n                this.leagueOrGroupStageId = res.stages.find((stage) => {\r\n                    if (this.tournamentInfo && this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\r\n                        return stage.type === AppConfig.TOURNAMENT_TYPES.groups;\r\n                    } else {\r\n                        return stage.type === AppConfig.TOURNAMENT_TYPES.league;\r\n                    }\r\n                })?.id;\r\n                \r\n                this.isLock = res.is_locked_schedule === 1;\r\n                this.seasonId = res.group.season.id;\r\n                _titleService.setTitle(res.name);\r\n                \r\n                this.contentHeader = {\r\n                    headerTitle: res.name,\r\n                    actionButton: false,\r\n                    breadcrumb: {\r\n                        type: '',\r\n                        links: [\r\n                            {\r\n                                name: this._translateService.instant('Leagues'),\r\n                                isLink: false\r\n                            },\r\n                            {\r\n                                name: this._translateService.instant('Manage Leagues'),\r\n                                isLink: true,\r\n                                link: '/leagues/manage'\r\n                            },\r\n                            {\r\n                                name: res.name,\r\n                                isLink: false\r\n                            },\r\n                            {\r\n                                name: this._translateService.instant('Auto Schedule'),\r\n                                isLink: false\r\n                            }\r\n                        ]\r\n                    }\r\n                };\r\n                \r\n                this.getListDates();\r\n            });\r\n    }\r\n    \r\n    ngOnInit() {\r\n    }\r\n    \r\n    ngAfterViewChecked() {\r\n        setTimeout(() => {\r\n            this.hasMatches = (this.responseMatches && Object.keys(this.responseMatches).length > 0) || this.listUnScheduledMatches.length > 0;\r\n        }, 0);\r\n    }\r\n    \r\n    getListDates() {\r\n        this._autoScheduleService.getListDates(this.tournamentId).subscribe((res) => {\r\n            this.dateOptions = res['data'];\r\n            this.selectedDate = this.dateOptions.length > 0 ? this.dateOptions[0] : [];\r\n            this.hasPlan = this.dateOptions.length > 0;\r\n            this.onScheduleAction(null, true);\r\n        });\r\n    }\r\n    \r\n    getScheduleMatches(showLoading: boolean = true) {\r\n        if (showLoading) {\r\n            this._loadingService.show();\r\n        }\r\n        \r\n        if (this.selectedDate.length === 0) {\r\n            this._loadingService.dismiss();\r\n            this.isFetching = false;\r\n            this.isScheduling = false;\r\n            return;\r\n        }\r\n        \r\n        this._autoScheduleService.getScheduleMatches(this.tournamentId, this.selectedDate)\r\n            .subscribe((res) => {\r\n                \r\n                this.isScheduling = res.schedule_status === 'scheduling';\r\n                console.log('this.isScheduling', this.isScheduling);\r\n                if (this.isScheduling) {\r\n                    this._loadingService.dismiss();\r\n                    \r\n                    this.listMatches = {};\r\n                    this.listLocationIds = [];\r\n                    this.overEndTime = [];\r\n                    this.isFetching = false;\r\n                    \r\n                    return;\r\n                }\r\n                \r\n                this.responseMetadata = res.metadata;\r\n                this.responseMatches = Array.isArray(res.data) && res.data.length === 0 ? null : res.data;\r\n                \r\n                this.overEndTime = res.over_end_time;\r\n                this.teamConflicts = res.conflicts['team_scheduling_conflict'];\r\n                this.refereeConflicts = res.conflicts['referee_scheduling_conflict'];\r\n                this.stageConflicts = res.conflicts['stage_scheduling_conflict'];\r\n                \r\n                if (this.responseMatches) {\r\n                    this.mapListLocations();\r\n                } else {\r\n                    this.listLocationIds = [];\r\n                    this.overEndTime = [];\r\n                    this.listMatches = {};\r\n                    this.selectedDate = null;\r\n                }\r\n                \r\n                this._loadingService.dismiss();\r\n            }, () => {\r\n            }, () => {\r\n                this.isFetching = false;\r\n                \r\n            });\r\n        \r\n    }\r\n    \r\n    getUnScheduleMatches(showLoading = true) {\r\n        if (showLoading) {\r\n            this._loadingService.show();\r\n        }\r\n        this._autoScheduleService.getListUnScheduledMatches(this.tournamentId)\r\n            .subscribe((res) => {\r\n                this.listUnScheduledMatches = res.data;\r\n                \r\n                this._loadingService.dismiss();\r\n            });\r\n    }\r\n    \r\n    mapListLocations() {\r\n        this.listLocationIds = [];\r\n        this.listMatches = {};\r\n        \r\n        this.listLocations = this.responseMatches[this.selectedDate] || {};\r\n        \r\n        if (!this.listLocations || !this.selectedDate) return;\r\n        \r\n        Object.keys(this.listLocations).forEach((locationName: string) => {\r\n            \r\n            if (!this.listLocationIds.includes(`${locationName}`)) {\r\n                this.listLocationIds.push(`${locationName}`);\r\n            }\r\n            \r\n            if (!this.listMatches[locationName]) {\r\n                this.listMatches[locationName] = [];\r\n            }\r\n            \r\n            this.listMatches[locationName] = [\r\n                ...this.listMatches[locationName],\r\n                ...this.listLocations[locationName]\r\n            ];\r\n        });\r\n    }\r\n    \r\n    onSelectDate(event) {\r\n        this.selectedDate = event;\r\n        this.onScheduleAction(null, true);\r\n    }\r\n    \r\n    openModalSetupSchedule() {\r\n        this._modalService.open(this.modalSetupSchedule, {\r\n            centered: true,\r\n            size: 'lg'\r\n        });\r\n    }\r\n    \r\n    onScheduleAction(action, showLoading: boolean = true) {\r\n        if (action === 'fetchDates') {\r\n            this.getListDates();\r\n        } else {\r\n            this.getScheduleMatches(showLoading);\r\n            this.getUnScheduleMatches(showLoading);\r\n        }\r\n    }\r\n    \r\n    unScheduleMatch(timeSlotId: string | number, configId: string | number, onError: () => void, onSuccess: () => void) {\r\n        this._autoScheduleService.unScheduleMatch(timeSlotId, configId).subscribe((res) => {\r\n            this.onScheduleAction(null, false);\r\n            onSuccess && onSuccess();\r\n        }, (error) => {\r\n            onError();\r\n        });\r\n    }\r\n    \r\n    \r\n    mapNewSlotIndex(locationKey) {\r\n        \r\n        const newSlotIndex = {};\r\n        this.listMatches[locationKey].forEach((item, index) => {\r\n            \r\n            newSlotIndex[item.time_slot_id] = index;\r\n        });\r\n        return newSlotIndex;\r\n    }\r\n    \r\n    updateLocationMatch(locationKey: string, updateData: UpdateLocationMatchParams, onError: () => void, onSuccess: () => void) {\r\n        const newIndex = this.mapNewSlotIndex(locationKey);\r\n        this._autoScheduleService.updateLocationMatch(updateData).subscribe((res) => {\r\n            this.onScheduleAction(null, true);\r\n            onSuccess && onSuccess();\r\n        }, (error) => {\r\n            onError();\r\n        });\r\n    }\r\n    \r\n    \r\n    drop(event: CdkDragDrop<string[]>) {\r\n        const targetContainer = event.container.element.nativeElement;\r\n        const prevContainer = event.previousContainer.element.nativeElement;\r\n        const dragItem = event.item.element.nativeElement;\r\n        \r\n        const targetContainerId = targetContainer['id'];\r\n        const prevContainerId = prevContainer['id'];\r\n        \r\n        const targetStageId = targetContainer['data_stage_id'];\r\n        const targetLocationId = targetContainer['data_location_id'];\r\n        \r\n        const prevStageId = prevContainer['data_stage_id'];\r\n        const prevLocationId = prevContainer['data_location_id'];\r\n        \r\n        const itemTimeSlotId = dragItem['data_time_slot_id'];\r\n        const itemType = dragItem['data_type'];\r\n        const itemBreakDurations = dragItem['data_break_durations'];\r\n        const itemNewIndex = event.currentIndex;\r\n        \r\n        if (prevContainerId === targetContainerId && event.currentIndex === event.previousIndex) return;\r\n        \r\n        const handleDrop = () => {\r\n            \r\n            if (targetContainerId === 'unScheduleZone') {\r\n                transferArrayItem(\r\n                    event.previousContainer.data,\r\n                    event.container.data,\r\n                    event.previousIndex,\r\n                    event.currentIndex\r\n                );\r\n                \r\n                if (targetContainerId === prevContainerId) return;\r\n                \r\n                this.unScheduleMatch(itemTimeSlotId, this.getConfig(prevLocationId)?.id, () => {\r\n                    transferArrayItem(\r\n                        event.container.data,\r\n                        event.previousContainer.data,\r\n                        event.currentIndex,\r\n                        event.previousIndex\r\n                    );\r\n                    this._toastService.error(this._translateService.instant('Failed to unschedule match.'));\r\n                }, () => {\r\n                    this._toastService.success(this._translateService.instant('Match unscheduled successfully.'));\r\n                });\r\n            } else {\r\n                if (event.previousContainer === event.container) {\r\n                    moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\r\n                    this.updateLocationMatch(targetContainerId, {\r\n                        new_index: this.mapNewSlotIndex(targetContainerId),\r\n                        location_id: targetLocationId,\r\n                        prev_location_id: prevLocationId,\r\n                        stage_id: targetStageId,\r\n                        prev_stage_id: prevStageId,\r\n                        tournament_id: this.tournamentId,\r\n                        config_id: this.getConfig(targetLocationId)?.id,\r\n                        prev_config_id: this.getConfig(prevLocationId)?.id\r\n                    }, () => {\r\n                        moveItemInArray(event.container.data, event.currentIndex, event.previousIndex);\r\n                        this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\r\n                    }, () => {\r\n                        this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\r\n                    });\r\n                } else {\r\n                    transferArrayItem(\r\n                        event.previousContainer.data,\r\n                        event.container.data,\r\n                        event.previousIndex,\r\n                        event.currentIndex\r\n                    );\r\n                    \r\n                    this.updateLocationMatch(targetContainerId,\r\n                        {\r\n                            new_index: this.mapNewSlotIndex(targetContainerId),\r\n                            location_id: targetLocationId,\r\n                            prev_location_id: prevContainerId === 'unScheduleZone' ? null : prevLocationId,\r\n                            stage_id: targetStageId,\r\n                            prev_stage_id: prevContainerId === 'unScheduleZone' ? null : prevStageId,\r\n                            tournament_id: this.tournamentId,\r\n                            config_id: this.getConfig(targetLocationId)?.id,\r\n                            prev_config_id: this.getConfig(prevLocationId)?.id\r\n                        },\r\n                        () => {\r\n                            transferArrayItem(\r\n                                event.container.data,\r\n                                event.previousContainer.data,\r\n                                event.currentIndex,\r\n                                event.previousIndex\r\n                            );\r\n                            this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\r\n                        }, () => {\r\n                            this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\r\n                        });\r\n                }\r\n            }\r\n        };\r\n        \r\n        if (targetContainerId !== 'unScheduleZone') {\r\n            if (itemType === 'match') {\r\n                if (!this.checkValidUpdateMatch(targetContainerId, prevContainerId, event.currentIndex, event.previousIndex)) {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Warning'),\r\n                        text: this._translateService.instant(`${targetContainerId !== prevContainerId ? 'Adding' : 'Updating'} this match will exceed the location configured end time. Do you want to continue?`),\r\n                        icon: 'warning',\r\n                        confirmButtonText: this._translateService.instant('Continue'),\r\n                        showCancelButton: true,\r\n                        reverseButtons: true\r\n                    }).then((result) => {\r\n                        if (result.isConfirmed) {\r\n                            handleDrop();\r\n                        }\r\n                    });\r\n                } else {\r\n                    handleDrop();\r\n                }\r\n                \r\n            } else if (itemType === 'break') {\r\n                // -1 :block\r\n                // 0: need show warning\r\n                // 1: good\r\n                \r\n                const validUpdateStatus = this.checkValidUpdateEvent(targetContainerId, prevContainerId, event.currentIndex, event.previousIndex, itemBreakDurations);\r\n                if (validUpdateStatus === -1) {\r\n                    return Swal.fire({\r\n                        title: this._translateService.instant('Error'),\r\n                        text: this._translateService.instant('Cannot add this event to this location because there is not enough time space to add.'),\r\n                        icon: 'error',\r\n                        confirmButtonText: this._translateService.instant('OK')\r\n                    });\r\n                } else if (validUpdateStatus === 0) {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Warning'),\r\n                        text: this._translateService.instant('Updating the index of this event may cause some matches to exceed the configured end time for the location. Do you want to continue?'),\r\n                        icon: 'warning',\r\n                        confirmButtonText: this._translateService.instant('Continue'),\r\n                        showCancelButton: true,\r\n                        reverseButtons: true\r\n                    }).then((result) => {\r\n                        if (result.isConfirmed) {\r\n                            handleDrop();\r\n                        }\r\n                    });\r\n                } else {\r\n                    handleDrop();\r\n                }\r\n            } else {\r\n                handleDrop();\r\n            }\r\n        } else {\r\n            handleDrop();\r\n        }\r\n    }\r\n    \r\n    getShortTime(time: string): string {\r\n        // handle return ISO string to short time format HH:mm and format 24 hours\r\n        if (!time) return '';\r\n        const date = new Date(time);\r\n        const hours = date.getHours().toString().padStart(2, '0');\r\n        const minutes = date.getMinutes().toString().padStart(2, '0');\r\n        return `${hours}:${minutes}`;\r\n    }\r\n    \r\n    getConfig(location_id) {\r\n        return this.responseMetadata['configs'].find((item) => {\r\n            return item.tournament_id === +this.tournamentId && item.location_id === +location_id && item.begin_date === this.selectedDate;\r\n        });\r\n    }\r\n    \r\n    editSchedule(locationKey, configId) {\r\n        this.selectedConfig = {\r\n            tournamentId: this.tournamentId,\r\n            location: this.responseMetadata['locations'][locationKey].id,\r\n            date: this.selectedDate,\r\n            configId,\r\n            timeSlotIds: this.listMatches[locationKey].map((item) => item.time_slot_id)\r\n        };\r\n        this._modalService.open(this.modalEditSchedule, {\r\n            centered: true\r\n        });\r\n    }\r\n    \r\n    deletePlan(locationKey) {\r\n        \r\n        const locationId = this.responseMetadata['locations'][locationKey].id;\r\n        const configId = this.getConfig(locationId)?.id;\r\n        const timeSlotIds = this.listMatches[locationKey].map((item) => item.time_slot_id);\r\n        \r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant('You will not be able to recover this schedule!'),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this._autoScheduleService.deleteSchedule(this.tournamentId, locationId, timeSlotIds, configId).subscribe((res) => {\r\n                    this.onScheduleAction(null, false);\r\n                    this._toastService.success(this._translateService.instant('Schedule deleted successfully.'));\r\n                });\r\n            }\r\n        });\r\n    }\r\n    \r\n    public breakModalParams: BreakModalParams = {\r\n        locationId: null,\r\n        tournamentId: null,\r\n        timeSlotId: null,\r\n        lastTimeSlotId: null,\r\n        configId: null,\r\n        maxBreakDuration: null\r\n    };\r\n    \r\n    openModalAddBreak(locationKey, lastTimeSlotId, configId) {\r\n        \r\n        const locationId = this.responseMetadata['locations'][locationKey].id;\r\n        const config = this.getConfig(locationId);\r\n        const lastTimeSlotEndTime = this.listMatches[locationKey][this.listMatches[locationKey].length - 1].end_time;\r\n        \r\n        // get time from lastTimeSlotEndTime to 23:59 same day\r\n        const maxBreakDuration = moment(lastTimeSlotEndTime).endOf('day').diff(moment(lastTimeSlotEndTime), 'minutes');\r\n        \r\n        this.breakModalParams = {\r\n            ...this.breakModalParams,\r\n            timeSlotId: null,\r\n            locationId,\r\n            tournamentId: this.tournamentId,\r\n            lastTimeSlotId,\r\n            configId,\r\n            maxBreakDuration\r\n        };\r\n        \r\n        this._modalService.open(this.modalCrudBreak, {\r\n            centered: true\r\n            \r\n        });\r\n        \r\n    }\r\n    \r\n    // Simple dropdown methods\r\n    toggleDropdown(event: MouseEvent, item: any): void {\r\n        \r\n        event.preventDefault();\r\n        event.stopPropagation();\r\n        \r\n        if (event.target['className'] === 'swap-button') {\r\n            return;\r\n        }\r\n        \r\n        const dropdownId = this.getDropdownId(item);\r\n        \r\n        // Close dropdown if clicking on the same item\r\n        if (this.activeDropdownId === dropdownId) {\r\n            this.activeDropdownId = null;\r\n        } else {\r\n            // Open new dropdown (close any existing one)\r\n            this.activeDropdownId = dropdownId;\r\n        }\r\n    }\r\n    \r\n    isDropdownOpen(item: any): boolean {\r\n        const dropdownId = this.getDropdownId(item);\r\n        return this.activeDropdownId === dropdownId;\r\n    }\r\n    \r\n    getDropdownId(item: any): string {\r\n        // Create unique ID for each item\r\n        return `${item.time_slot_id}_${item.type}_${item.stage_id || 'unscheduled'}`;\r\n    }\r\n    \r\n    closeAllDropdowns(): void {\r\n        this.activeDropdownId = null;\r\n    }\r\n    \r\n    @HostListener('document:click', ['$event'])\r\n    onDocumentClick(event: MouseEvent): void {\r\n        const target = event.target as HTMLElement;\r\n        \r\n        // Close dropdown if clicking outside of dropdown or dnd-item\r\n        if (!target.closest('.item-dropdown') && !target.closest('.dnd-item')) {\r\n            this.closeAllDropdowns();\r\n        }\r\n    }\r\n    \r\n    \r\n    public selectedItem = null;\r\n    \r\n    // Dropdown action handlers\r\n    onEditMatch(item: any): void {\r\n        this.closeAllDropdowns();\r\n        \r\n        this.selectedItem = item;\r\n        \r\n        this._modalService.open(this.modalUpdateMatch, {\r\n            centered: true\r\n        });\r\n        \r\n    }\r\n    \r\n    onUnscheduleTimeSlot(item: any, configId): void {\r\n        const description = {\r\n            match: 'This match will be moved to unscheduled matches.',\r\n            break: 'This break will be removed.'\r\n        };\r\n        \r\n        const successMessage = {\r\n            match: 'Match unscheduled successfully.',\r\n            break: 'Break removed successfully.'\r\n        };\r\n        \r\n        const errorMessage = {\r\n            match: 'Failed to unschedule match.',\r\n            break: 'Failed to remove break.'\r\n        };\r\n        \r\n        \r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant(description[item.type]),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true,\r\n            confirmButtonText: this._translateService.instant('Yes'),\r\n            cancelButtonText: this._translateService.instant('Cancel')\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this.unScheduleMatch(item.time_slot_id, configId, () => {\r\n                    this._toastService.error(this._translateService.instant(errorMessage[item.type]));\r\n                }, () => {\r\n                    this._toastService.success(this._translateService.instant(successMessage[item.type]));\r\n                });\r\n            }\r\n        });\r\n        \r\n        this.closeAllDropdowns();\r\n    }\r\n    \r\n    onEditEventTime(item: any, configId: number): void {\r\n        \r\n        this.closeAllDropdowns();\r\n        \r\n        this.breakModalParams = {\r\n            timeSlotId: item.time_slot_id,\r\n            locationId: item.location_id,\r\n            tournamentId: item.tournament_id,\r\n            description: item.description,\r\n            breakDurations: item.break_durations,\r\n            configId: configId\r\n        };\r\n        \r\n        this._modalService.open(this.modalCrudBreak, {\r\n            centered: true\r\n        });\r\n        \r\n    }\r\n    \r\n    clearSchedule() {\r\n        \r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant('This action will clear all schedule.'),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true,\r\n            confirmButtonText: this._translateService.instant('Yes, clear it!'),\r\n            cancelButtonText: this._translateService.instant('Cancel')\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this._loadingService.show();\r\n                this._autoScheduleService.clearSchedule(this.tournamentId).subscribe((res) => {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Success!'),\r\n                        text: this._translateService.instant('Schedule has been cleared.'),\r\n                        icon: 'success'\r\n                    });\r\n                    // this.\r\n                    this.onScheduleAction('fetchDates', true);\r\n                }, (error) => {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Error!'),\r\n                        text: this._translateService.instant('Schedule has been cleared.'),\r\n                        icon: 'success'\r\n                    });\r\n                }, () => {\r\n                    this._loadingService.dismiss();\r\n                });\r\n            }\r\n        });\r\n    }\r\n    \r\n    isMatchHasConflict(scheduleMatchId: number, type: 'team' | 'referee' | 'match', itemCheckId?: number) {\r\n        switch (type) {\r\n            case 'team':\r\n                return this.teamConflicts[scheduleMatchId]?.find(item => item.team_id === itemCheckId);\r\n            case 'referee':\r\n                return this.refereeConflicts[scheduleMatchId]?.find(item => item.referee_id === itemCheckId);\r\n            case 'match':\r\n                return this.teamConflicts[scheduleMatchId] || this.refereeConflicts[scheduleMatchId];\r\n        }\r\n    }\r\n    \r\n    onClickLock() {\r\n        this.isLock = !this.isLock;\r\n        this._autoScheduleService.updateTournamentScheduleStatus(this.tournamentId).subscribe((res) => {\r\n            this._toastService.success(res.message);\r\n        }, (error) => {\r\n            \r\n            this.isLock = !this.isLock;\r\n            this._toastService.error(error.message);\r\n        });\r\n    }\r\n    \r\n    swapTeam(matchInfo) {\r\n        this.closeAllDropdowns();\r\n        this._stageService.swapTeams(matchInfo).subscribe((res) => {\r\n            this._toastService.success('Swap teams successfully.');\r\n            this.onScheduleAction(null, false);\r\n        }, (error) => {\r\n            this._toastService.error(error.message || 'Failed to swap teams.');\r\n        });\r\n    }\r\n    \r\n    autoGenerate() {\r\n         const formData = new FormData();\r\n        formData.append('stage_id', this.leagueOrGroupStageId.toString());\r\n        this._stageService.autoGenerateMatches(formData).subscribe((res) => {\r\n            this._toastService.success('Auto generate matches successfully.');\r\n            this.onScheduleAction(null, false);\r\n        }, (error) => {\r\n            Swal.fire({\r\n                title: 'Cannot Auto Generate',\r\n                icon: error.warning ? 'warning' : 'error',\r\n                text: error.warning || error.message || 'Failed to auto generate matches.',\r\n                confirmButtonText: 'Go to Stage Details'\r\n            }).then((result) => {\r\n                if (result.isConfirmed) {\r\n                    this._router.navigate(['leagues', 'manage', this.tournamentId, 'stages', this.leagueOrGroupStageId]);\r\n                }\r\n            });\r\n        });\r\n    }\r\n    \r\n    getRefereeDisplayLimit(referees: any[], matchId: number): number {\r\n        if (this.expandedRefereeMatches.has(matchId)) {\r\n            return referees.length; // Show all if expanded\r\n        }\r\n        \r\n        // Calculate average name length\r\n        const totalNameLength = referees.reduce((sum, ref) => {\r\n            const name = ref.referee_type === 'user'\r\n                ? `${ref.user?.first_name || ''} ${ref.user?.last_name || ''}`.trim()\r\n                : ref.referee_name || '';\r\n            return sum + name.length;\r\n        }, 0);\r\n        \r\n        const averageLength = totalNameLength / referees.length;\r\n        \r\n        // If average name length is long (>15 chars), show 3; otherwise show 4\r\n        return averageLength > 15 ? 3 : 4;\r\n    }\r\n    \r\n    getDisplayedReferees(referees: any[], matchId: number): any[] {\r\n        if (!referees || referees.length === 0) {\r\n            return [];\r\n        }\r\n        \r\n        const limit = this.getRefereeDisplayLimit(referees, matchId);\r\n        return referees.slice(0, limit);\r\n    }\r\n    \r\n    getRemainingRefereesCount(referees: any[], matchId: number): number {\r\n        if (!referees || referees.length === 0 || this.expandedRefereeMatches.has(matchId)) {\r\n            return 0;\r\n        }\r\n        \r\n        const limit = this.getRefereeDisplayLimit(referees, matchId);\r\n        return Math.max(0, referees.length - limit);\r\n    }\r\n    \r\n    toggleRefereeExpansion(matchId: number, event: Event): void {\r\n        event.stopPropagation();\r\n        event.preventDefault();\r\n        \r\n        const matchItem = this.findMatchById(matchId);\r\n        if (!matchItem || !matchItem.referees || matchItem.referees.length === 0) {\r\n            return;\r\n        }\r\n        \r\n        \r\n        this.showRefereeModal(matchItem.referees, matchId);\r\n    }\r\n    \r\n    showRefereeModal(referees: any[], matchId: number): void {\r\n        this.selectedMatchReferees = referees;\r\n        this.selectedMatchId = matchId;\r\n        \r\n        this._modalService.open(this.modalRefereeList, {\r\n            centered: true,\r\n            size: 'md'\r\n        });\r\n    }\r\n    \r\n    findMatchById(matchId: number): any {\r\n        for (const locationKey of this.listLocationIds) {\r\n            const matches = this.listMatches[locationKey] || [];\r\n            const match = matches.find(item => item.id === matchId);\r\n            if (match) {\r\n                return match;\r\n            }\r\n        }\r\n        \r\n        // Also check unscheduled matches\r\n        return this.listUnScheduledMatches.find(item => item.id === matchId) || null;\r\n    }\r\n    \r\n    getConfigEndTime(beginDate: string, beginTime: string, endTime: string) {\r\n        const parseBeginTime = moment(`${beginDate} ${beginTime}`);\r\n        const parseEndTime = moment(`${beginDate} ${endTime}`);\r\n        \r\n        if (parseEndTime.isBefore(parseBeginTime)) {\r\n            parseEndTime.add(1, 'day');\r\n        }\r\n        return parseEndTime.toDate();\r\n    }\r\n    \r\n    checkValidUpdateMatch(locationKey, prevLocationKey, currentIndex, prevIndex) {\r\n        if (locationKey === prevLocationKey) {\r\n            return true;\r\n        }\r\n        \r\n        const locationId = this.responseMetadata['locations'][locationKey].id;\r\n        const lastTimeSlotOfLocation = this.listMatches[locationKey][this.listMatches[locationKey].length - 1];\r\n        const lastTimeSlotType = lastTimeSlotOfLocation.type;\r\n        const lastTimeSlotEndTime = moment(lastTimeSlotOfLocation.end_time).toDate();\r\n        \r\n        const config = this.getConfig(locationId);\r\n        const breakDuration = config.break_match_duration;\r\n        const matchDuration = config.match_duration;\r\n        const configLatestEndTime = this.getConfigEndTime(config.begin_date, config.begin_time, config.end_time);\r\n        \r\n        let estimateNewMatchEndTime;\r\n        if (lastTimeSlotType === 'match') {\r\n            estimateNewMatchEndTime = moment(lastTimeSlotEndTime).add(breakDuration, 'minutes').add(matchDuration, 'minutes').toDate();\r\n        } else {\r\n            estimateNewMatchEndTime = moment(lastTimeSlotEndTime).add(matchDuration, 'minutes').toDate();\r\n        }\r\n        \r\n        return estimateNewMatchEndTime <= configLatestEndTime;\r\n    }\r\n    \r\n    checkValidUpdateEvent(locationKey, prevLocationKey, currentIndex, prevIndex, eventDuration) {\r\n        if (currentIndex === prevIndex && locationKey === prevLocationKey) {\r\n            return true;\r\n        }\r\n        \r\n        const locationId = this.responseMetadata['locations'][locationKey].id;\r\n        \r\n        const totalTimeSlotOfLocation = this.listMatches[locationKey].length - 1;\r\n        \r\n        let lastTimeSlotOfLocation;\r\n        \r\n        if (locationKey === prevLocationKey && currentIndex === totalTimeSlotOfLocation || locationKey !== prevLocationKey && currentIndex > totalTimeSlotOfLocation) {\r\n            return true;\r\n        }\r\n        \r\n        if (prevIndex === totalTimeSlotOfLocation) {\r\n            lastTimeSlotOfLocation = this.listMatches[locationKey][this.listMatches[locationKey].length - 2];\r\n        } else {\r\n            lastTimeSlotOfLocation = this.listMatches[locationKey][this.listMatches[locationKey].length - 1];\r\n        }\r\n        console.log('lastTimeSlotOfLocation', lastTimeSlotOfLocation);\r\n        \r\n        const lastTimeSlotEndTime = moment(lastTimeSlotOfLocation.end_time).toDate();\r\n        console.log('🚀 ~ checkValidUpdateEvent ~ lastTimeSlotEndTime: ', lastTimeSlotEndTime);\r\n        \r\n        const config = this.getConfig(locationId);\r\n        const breakDuration = config.break_match_duration;\r\n        const configLatestEndTime = this.getConfigEndTime(config.begin_date, config.begin_time, config.end_time);\r\n        \r\n        const estimateNewEventEndTime = moment(lastTimeSlotEndTime).add(eventDuration, 'minutes').toDate();\r\n        \r\n        const maxEventDuration = moment(lastTimeSlotEndTime).endOf('day').diff(moment(lastTimeSlotEndTime), 'minutes');\r\n        \r\n        if (eventDuration > maxEventDuration) {\r\n            return -1;\r\n        }\r\n        \r\n        console.log('estimateNewEventEndTime', estimateNewEventEndTime);\r\n        console.log('configLatestEndTime', configLatestEndTime);\r\n        if (estimateNewEventEndTime > configLatestEndTime) {\r\n            return 0;\r\n        }\r\n        \r\n        return 1;\r\n    }\r\n    \r\n    \r\n    isWarningMatch(matchId) {\r\n        \r\n        return this.overEndTime.includes(+matchId);\r\n        \r\n    }\r\n    \r\n    protected readonly location = location;\r\n    \r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n    <div class=\"content-body\">\r\n        <!-- content-header component -->\r\n        <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n        <div class=\"top-header\">\r\n            <div class=\"row mb-1\">\r\n                <!-- ng select season -->\r\n                <div class=\"d-flex flex-column\" style=\"gap: 2px;\">\r\n                    <label for=\"selectDate\">{{ 'Date' | translate }}</label>\r\n                    <ng-select\r\n                            id=\"selectDate\"\r\n                            [searchable]=\"true\"\r\n                            [clearable]=\"false\"\r\n                            placeholder=\"{{ 'Select Date' | translate }}\"\r\n                            [(ngModel)]=\"selectedDate\"\r\n                            (change)=\"onSelectDate($event)\"\r\n                            style=\"min-width: 200px;\"\r\n                            [disabled]=\"isScheduling || isFetching\"\r\n                    >\r\n                        <ng-option *ngFor=\"let date of dateOptions\" [value]=\"date\">{{ date }} </ng-option>\r\n                    </ng-select>\r\n                </div>\r\n                <div class=\"d-flex align-items-center\" style=\"gap: 8px;\">\r\n                    <button class=\"btn btn-primary\" (click)=\"openModalSetupSchedule()\" [disabled]=\"isLock || isScheduling || isFetching\">\r\n                        <i data-feather=\"settings\" class=\"mr-25\"></i>\r\n                        Add Schedule\r\n                    </button>\r\n                    <button\r\n                            class=\"btn btn-icon\"\r\n                            (click)=\"onClickLock()\"\r\n                            [ngClass]=\"{\r\n              'btn-outline-primary': !isLock,\r\n              'btn-primary': isLock\r\n            }\"\r\n                            [disabled]=\"isScheduling || isFetching\"\r\n                    >\r\n                        <i [ngClass]=\"isLock ? 'fa-regular fa-lock' : 'fa-regular fa-unlock'\"></i>\r\n                    </button>\r\n                    <button class=\"btn btn-icon btn-outline-danger\" (click)=\"clearSchedule()\" [disabled]=\"isLock || isScheduling || isFetching\">\r\n                        <i data-feather=\"trash-2\"></i>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div cdkDropListGroup class=\"grid grid-cols-12\">\r\n            <div class=\"col-xl-9 col-lg-8 col-sm-6 col-12\" id=\"listLocationZone\">\r\n                <div class=\"horizontal-scroll-container\" *ngIf=\"hasPlan\">\r\n                    <div class=\"location-columns-wrapper\">\r\n                        <ng-container *ngFor=\"let locationKey of listLocationIds\">\r\n                            <div class=\"location-column mb-2\">\r\n                                <div class=\"bg-white shadow-sm\">\r\n                                    <header class=\"location-header\">\r\n                                        <div class=\"d-flex align-items-start justify-content-between\">\r\n                                            <p class=\"location-name h4\">\r\n                                                {{ locationKey }}\r\n                                            </p>\r\n                                            <div class=\"\" ngbDropdown *ngIf=\"!isLock\">\r\n                                                <button class=\"btn btn-link dropdown-toggle\" ngbDropdownToggle>\r\n                                                    <i class=\"fa-solid fa-ellipsis-vertical\"></i>\r\n                                                </button>\r\n                                                <div ngbDropdownMenu aria-labelledby=\"dropdownMenuButton\">\r\n                                                    <a\r\n                                                            ngbDropdownItem\r\n                                                            (click)=\"\r\n                              editSchedule(\r\n                                locationKey,\r\n                                getConfig(\r\n                                  responseMetadata['locations'][locationKey].id\r\n                                )?.id\r\n                              )\r\n                            \"\r\n                                                    >\r\n                                                        Edit Plan\r\n                                                    </a>\r\n                                                    <a ngbDropdownItem (click)=\"deletePlan(locationKey)\">\r\n                                                        Delete Plan\r\n                                                    </a>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div class=\"location-time\">\r\n                                            <p>{{ getShortTime( getConfig( responseMetadata['locations'][locationKey].id )?.begin_date + ' ' + getConfig( responseMetadata['locations'][locationKey].id )?.begin_time || '' ) }}</p>\r\n                                            <p>-</p>\r\n                                            <p>{{ getShortTime(getConfig(responseMetadata['locations'][locationKey].id)?.begin_date + ' ' + getConfig(responseMetadata['locations'][locationKey].id)?.end_time || '') }}</p>\r\n                                        </div>\r\n                                    </header>\r\n                                    <div\r\n                                            class=\"dnd-zone\"\r\n                                            cdkDropList\r\n                                            [cdkDropListData]=\"listMatches[locationKey]\"\r\n                                            (cdkDropListDropped)=\"drop($event)\"\r\n                                            [id]=\"locationKey\"\r\n                                            [data_location_id]=\"\r\n                      responseMetadata['locations'][locationKey].id\r\n                    \"\r\n                                    >\r\n                                        <div\r\n                                                *ngFor=\"let item of listMatches[locationKey]\"\r\n                                                class=\"dnd-item location-match-row\"\r\n                                                [ngClass]=\"{\r\n                        'conflict-border': isMatchHasConflict(item.id, 'match'),\r\n                        'warning-border': isWarningMatch(item.id) && !isMatchHasConflict(item.id, 'match'),\r\n                      }\"\r\n                                                cdkDrag\r\n                                                [data_stage_id]=\"item.stage_id\"\r\n                                                [data_time_slot_id]=\"item.time_slot_id\"\r\n                                                [data_type]=\"item.type\"\r\n                                                [data_break_durations]=\"item.break_durations ?? 0\"\r\n                                                [cdkDragDisabled]=\"isLock\"\r\n                                                (click)=\"!isLock && toggleDropdown($event, item)\"\r\n                                        >\r\n                                            <button type=\"button\" rippleEffect class=\"conflict-tooltip btn btn-link\" placement=\"right\" container=\"body\" ngbTooltip=\"This match has conflict\" *ngIf=\"isMatchHasConflict(item.id, 'match')\">\r\n                                                <i class=\"fa-light fa-circle-exclamation\" style=\"font-size: 16px;\"></i>\r\n                                            </button>\r\n                                            <button type=\"button\" rippleEffect class=\"warning-tooltip btn btn-link\" placement=\"right\" container=\"body\" ngbTooltip=\"This match has exceeded the end time configuration of this location\" *ngIf=\"isWarningMatch(item.id ) && !isMatchHasConflict(item.id, 'match')\">\r\n                                                <i class=\"fa-light fa-exclamation-triangle\" style=\"font-size: 16px;\"></i>\r\n                                            </button>\r\n\r\n                                            <ng-container *ngIf=\"item.type === 'match'\">\r\n                                                <ng-container\r\n                                                        *ngTemplateOutlet=\"\r\n                            matchScheduledTemplate;\r\n                            context: { $implicit: item }\r\n                          \"\r\n                                                ></ng-container>\r\n                                            </ng-container>\r\n                                            <ng-container *ngIf=\"item.type === 'break'\">\r\n                                                <div class=\"break-info-header\">\r\n                                                    <p class=\"text-center m-0\">\r\n                                                        {{ item.description || 'Break time' }}\r\n                                                    </p>\r\n                                                </div>\r\n                                                <div class=\"break-row\">\r\n                                                    <i class=\"fa-regular fa-clock\" aria-hidden=\"true\"></i>\r\n                                                    <p class=\"break-time m-0\">\r\n                                                        {{ item.break_durations }} mins\r\n                                                    </p>\r\n                                                </div>\r\n                                            </ng-container>\r\n\r\n                                            <!-- Individual Dropdown for this item -->\r\n                                            <div class=\"item-dropdown\" [class.visible]=\"isDropdownOpen(item)\">\r\n                                                <div class=\"dropdown-content\">\r\n                                                    <!-- Match type dropdown options -->\r\n                                                    <ng-container *ngIf=\"item.type === 'match'\">\r\n                                                        <button class=\"dropdown-item\" (click)=\"onEditMatch(item)\">\r\n                                                            <i class=\"fa-regular fa-whistle mr-2\" style=\"rotate: -45deg;\"></i>\r\n                                                            {{ 'Update Match Referees' | translate }}\r\n                                                        </button>\r\n                                                        <button\r\n                                                                class=\"dropdown-item\"\r\n                                                                (click)=\"\r\n                                onUnscheduleTimeSlot(\r\n                                  item,\r\n                                  getConfig(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )?.id\r\n                                )\r\n                              \"\r\n                                                        >\r\n                                                            <i class=\"fa fa-trash mr-2\"></i>\r\n                                                            {{ 'Unschedule Match' | translate }}\r\n                                                        </button>\r\n                                                    </ng-container>\r\n\r\n                                                    <!-- Break type dropdown options -->\r\n                                                    <ng-container *ngIf=\"item.type === 'break'\">\r\n                                                        <button\r\n                                                                class=\"dropdown-item\"\r\n                                                                (click)=\"\r\n                                onEditEventTime(\r\n                                  item,\r\n                                  getConfig(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )?.id\r\n                                )\r\n                              \"\r\n                                                        >\r\n                                                            <i class=\"fa fa-clock mr-2\"></i>\r\n                                                            {{ 'Edit Event Time' | translate }}\r\n                                                        </button>\r\n                                                        <button\r\n                                                                class=\"dropdown-item\"\r\n                                                                (click)=\"\r\n                                onUnscheduleTimeSlot(\r\n                                  item,\r\n                                  getConfig(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )?.id\r\n                                )\r\n                              \"\r\n                                                        >\r\n                                                            <i class=\"fa fa-trash mr-2\"></i>\r\n                                                            {{ 'Delete Event' | translate }}\r\n                                                        </button>\r\n                                                    </ng-container>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <footer class=\"location-footer\" *ngIf=\"!isLock\">\r\n                                        <button\r\n                                                class=\"btn btn-link w-100\"\r\n                                                (click)=\"\r\n                        openModalAddBreak(\r\n                          locationKey,\r\n                          listMatches[locationKey][\r\n                            listMatches[locationKey].length - 1\r\n                          ]?.time_slot_id,\r\n                          getConfig(\r\n                            responseMetadata['locations'][locationKey].id\r\n                          )?.id\r\n                        )\r\n                      \"\r\n                                        >\r\n                                            <i class=\"fa fa-plus\" aria-hidden=\"true\"></i>\r\n                                            Add event / break\r\n                                        </button>\r\n                                    </footer>\r\n                                </div>\r\n                            </div>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n                <ng-container *ngIf=\"!hasPlan && !isFetching && !isLock\">\r\n                    <ng-container *ngTemplateOutlet=\"noSchedule\"></ng-container>\r\n                </ng-container>\r\n                <ng-container *ngIf=\"isFetching\">\r\n                    <ng-container *ngTemplateOutlet=\"fetchingState\"></ng-container>\r\n                </ng-container>\r\n                <ng-container *ngIf=\"isScheduling && !isFetching\">\r\n                    <ng-container *ngTemplateOutlet=\"schedulingState\"></ng-container>\r\n                </ng-container>\r\n            </div>\r\n            <div id=\"listUnScheduleZone\" class=\"col-xl-3 col-lg-4 col-sm-6 col-12\">\r\n                <div class=\"unschedule-container\">\r\n                    <div class=\"location-column unplanned-matches-container\">\r\n                        <div class=\"location-header\">\r\n                            <p class=\"h4\">Not Planned</p>\r\n                            <p class=\"small\">\r\n                                You can add unscheduled matches to the calendar by drag and drop them.\r\n                            </p>\r\n                        </div>\r\n                        <div class=\"dnd-zone\" cdkDropList [cdkDropListData]=\"listUnScheduledMatches\" (cdkDropListDropped)=\"drop($event)\" id=\"unScheduleZone\">\r\n                            <ng-container *ngIf=\"hasMatches\">\r\n                                <div *ngFor=\"let item of listUnScheduledMatches\" class=\"dnd-item location-match-row\" cdkDrag [data_stage_id]=\"item.stage_id\" [data_time_slot_id]=\"item.time_slot_id\" [data_type]=\"item.type\" [cdkDragDisabled]=\"isLock\">\r\n                                    <ng-container *ngIf=\"item.type === 'match'\">\r\n                                        <ng-container\r\n                                                *ngTemplateOutlet=\"\r\n                        matchNotScheduledTemplate;\r\n                        context: { $implicit: item }\r\n                      \"\r\n                                        ></ng-container>\r\n                                    </ng-container>\r\n                                </div>\r\n                            </ng-container>\r\n                            <ng-container *ngIf=\"!hasMatches && !isScheduling\">\r\n                                <div id=\"notHaveMatches\">\r\n                                    <p class=\"text-center\">\r\n                                        No matches found for this tournament.\r\n                                    </p>\r\n\r\n                                    <button (click)=\"autoGenerate()\" class=\"btn btn-primary\">\r\n                                        <i class=\"fa-solid fa-wand-magic-sparkles\"></i>\r\n                                        Auto Generate\r\n                                    </button>\r\n                                </div>\r\n                            </ng-container>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<ng-template #modalSetupSchedule let-modal>\r\n    <app-modal-setup-schedule [seasonId]=\"seasonId\" [tournamentId]=\"tournamentId\" [tournamentInfo]=\"tournamentInfo\" (onSubmit)=\"onScheduleAction($event)\" />\r\n</ng-template>\r\n<ng-template #modalEditSchedule let-modal>\r\n    <app-modal-update-config [selectedConfig]=\"selectedConfig\" (onSubmit)=\"onScheduleAction($event)\" />\r\n</ng-template>\r\n<ng-template #modalCrudBreak let-modal>\r\n    <app-modal-crud-break [breakModalParams]=\"breakModalParams\" (onSubmit)=\"onScheduleAction($event)\" />\r\n</ng-template>\r\n\r\n<ng-template #modalUpdateMatch let-modal>\r\n    <app-modal-update-match [timeSlotInfo]=\"selectedItem\" [seasonId]=\"seasonId\" (onSubmit)=\"onScheduleAction($event)\" />\r\n</ng-template>\r\n\r\n<ng-template #modalRefereeList let-modal>\r\n    <div class=\"modal-header\">\r\n        <h4 class=\"modal-title\" id=\"modal-title\">{{ 'Match Referees' | translate }}</h4>\r\n        <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"modal.dismiss()\">\r\n            <span aria-hidden=\"true\">&times;</span>\r\n        </button>\r\n    </div>\r\n    <div class=\"modal-body\">\r\n        <div class=\"referee-list\">\r\n            <div class=\"referee-item\" *ngFor=\"let ref of selectedMatchReferees\">\r\n                <div class=\"referee-info\">\r\n          <span\r\n                  *ngIf=\"ref.referee_type == 'user'\"\r\n                  [ngClass]=\"{\r\n              'text-danger': isMatchHasConflict(selectedMatchId, 'referee', ref.id)\r\n            }\"\r\n          >\r\n            {{ ref.user?.first_name }} {{ ref.user?.last_name }}\r\n          </span>\r\n                    <span\r\n                            *ngIf=\"ref.referee_type == 'freetext'\"\r\n                            [ngClass]=\"{\r\n              'text-danger': isMatchHasConflict(selectedMatchId, 'referee', ref.id)\r\n            }\"\r\n                    >\r\n            {{ ref.referee_name }}\r\n          </span>\r\n                </div>\r\n                <button type=\"button\" rippleEffect class=\"conflict-tooltip btn btn-link p-0\" placement=\"right\" container=\"body\" ngbTooltip=\"This referee has a conflict\" *ngIf=\"isMatchHasConflict(selectedMatchId, 'referee', ref.id)\">\r\n                    <i class=\"fa-light fa-circle-exclamation text-danger\" style=\"font-size: 16px;\"></i>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n\r\n<ng-template #noSchedule>\r\n    <div id=\"noSchedule\">\r\n        <div class=\"col d-flex flex-column align-items-center justify-content-center g-2\" style=\"height: 500px;\">\r\n            <p class=\"h5\">No Plan Created</p>\r\n            <p style=\"color: rgba(168, 170, 174, 1);\" class=\"w-75 text-center\">\r\n                Please enter the necessary information to allow the system to generate an accurate schedule based on your requirements.\r\n            </p>\r\n            <button class=\"btn btn-primary\" (click)=\"openModalSetupSchedule()\">\r\n                Setup\r\n            </button>\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n\r\n<ng-template #fetchingState>\r\n    <div id=\"fetchingState\">\r\n        <div class=\"col d-flex flex-column align-items-center justify-content-center g-2\" style=\"height: 500px;\">\r\n            <p class=\"h5\">Fetching</p>\r\n            <p style=\"color: rgba(168, 170, 174, 1);\" class=\"w-75 text-center\">\r\n                Waiting for getting schedule data...\r\n            </p>\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n\r\n<ng-template #schedulingState>\r\n    <div id=\"schedulingState\">\r\n        <div class=\"col d-flex flex-column align-items-center justify-content-center g-2\" style=\"height: 500px;\">\r\n            <p class=\"h5\">Scheduling...</p>\r\n            <p style=\"color: rgba(168, 170, 174, 1);\" class=\"w-75 text-center\">\r\n                Scheduling is being processed in the background due to the large number of matches. Please wait approximately 5 to 15 minutes for completion. You will be notified when the process is finished.\r\n            </p>\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n\r\n<ng-template #matchNotScheduledTemplate let-item>\r\n    <div class=\"match-info-header\">\r\n        <p class=\"text-center m-0\">{{ item.match.round_name }}</p>\r\n    </div>\r\n    <div class=\"team-row\">\r\n        <div class=\"home-team\">\r\n            <img src=\"https://assets.codepen.io/285131/whufc.svg\" alt=\"Home Team Logo\" class=\"team-logo\" />\r\n            <span class=\"h6 team-name\">{{ item.match?.home_team?.name ?? 'TBD' }}</span>\r\n        </div>\r\n        <button class=\"swap-button\" (click)=\"swapTeam(item.match)\">\r\n            <i class=\"fa fa-right-left\" aria-hidden=\"true\"></i>\r\n        </button>\r\n        <div class=\"away-team\">\r\n            <span class=\"h6 team-name\">{{ item.match?.away_team?.name ?? 'TBD' }}</span>\r\n            <img src=\"https://assets.codepen.io/285131/whufc.svg\" alt=\"Away Team Logo\" class=\"team-logo\" />\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n\r\n<ng-template #matchScheduledTemplate let-item>\r\n    <div class=\"match-info-header\">\r\n        <p class=\"text-center m-0\">\r\n            {{ item.match.round_name }}\r\n        </p>\r\n    </div>\r\n    <div class=\"team-row\">\r\n        <div class=\"home-team\">\r\n            <img src=\"https://assets.codepen.io/285131/whufc.svg\" alt=\"Home Team Logo\" class=\"team-logo\" />\r\n            <span\r\n                    class=\"h6 team-name\"\r\n                    [ngClass]=\"{\r\n          'text-danger': isMatchHasConflict(\r\n            item.id,\r\n            'team',\r\n            item.match.home_team_id\r\n          )\r\n        }\"\r\n            >\r\n        {{ item.match.home_team?.name ?? 'TBD' }}\r\n      </span>\r\n        </div>\r\n        <button class=\"swap-button\" (click)=\"swapTeam(item.match)\">\r\n            <i class=\"fa fa-right-left\" aria-hidden=\"true\"></i>\r\n        </button>\r\n        <div class=\"away-team\">\r\n      <span\r\n              class=\"h6 team-name\"\r\n              [ngClass]=\"{\r\n          'text-danger': isMatchHasConflict(\r\n            item.id,\r\n            'team',\r\n            item.match.away_team_id\r\n          )\r\n        }\"\r\n      >\r\n        {{ item.match.away_team?.name ?? 'TBD' }}\r\n      </span>\r\n            <img src=\"https://assets.codepen.io/285131/whufc.svg\" alt=\"Away Team Logo\" class=\"team-logo\" />\r\n        </div>\r\n    </div>\r\n    <div class=\"match-date\">\r\n        <i class=\"fa-regular fa-clock\" aria-hidden=\"true\"></i>\r\n        <p class=\"text-center m-0\">\r\n            {{ getShortTime(item.start_time) }}\r\n        </p>\r\n        <p class=\"text-center m-0\">-</p>\r\n        <p class=\"text-center m-0\">\r\n            {{ getShortTime(item.end_time) }}\r\n        </p>\r\n    </div>\r\n    <div class=\"referees-row\" *ngIf=\"item.referees && item.referees.length > 0\">\r\n        <i class=\"fa-regular fa-whistle\" style=\"rotate: -45deg;\"></i>\r\n\r\n        <!-- Display limited referees (3-4 based on name length) -->\r\n        <div class=\"referee-names\" *ngFor=\"let ref of getDisplayedReferees(item.referees, item.id); let last = last\">\r\n            <div class=\"referee-name\">\r\n        <span\r\n                *ngIf=\"ref.referee_type == 'user'\"\r\n                [ngClass]=\"{\r\n            'text-danger': isMatchHasConflict(item.id, 'referee', ref.id)\r\n          }\"\r\n        >\r\n          {{ ref.user?.first_name }} {{ ref.user?.last_name }}\r\n        </span>\r\n                <span\r\n                        *ngIf=\"ref.referee_type == 'freetext'\"\r\n                        [ngClass]=\"{\r\n            'text-danger': isMatchHasConflict(item.id, 'referee', ref.id)\r\n          }\"\r\n                >\r\n          {{ ref.referee_name }}\r\n        </span>\r\n                <span *ngIf=\"!last\">-</span>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Show remaining count if there are more referees -->\r\n        <div class=\"referee-more\" *ngIf=\"getRemainingRefereesCount(item.referees, item.id) > 0\">\r\n      <span class=\"referee-expand-btn\" (click)=\"toggleRefereeExpansion(item.id, $event)\">\r\n        +{{ getRemainingRefereesCount(item.referees, item.id) }} more\r\n      </span>\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}