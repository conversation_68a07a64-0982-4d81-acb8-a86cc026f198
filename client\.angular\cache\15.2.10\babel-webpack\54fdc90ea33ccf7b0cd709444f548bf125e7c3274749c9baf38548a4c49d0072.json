{"ast": null, "code": "import { FormGroup } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { environment } from '../../../../../environments/environment';\nimport Swal from 'sweetalert2';\nimport { DataTableDirective } from 'angular-datatables';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../services/commons.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"../../../../services/loading.service\";\nimport * as i7 from \"../../../../services/season.service\";\nimport * as i8 from \"../../../../services/season-referee.service\";\nimport * as i9 from \"ngx-toastr\";\nimport * as i10 from \"../../../../services/user.service\";\nfunction ModalManageRefereesComponent_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" * \", i0.ɵɵpipeBind1(2, 1, ctx_r0.newRefereeError), \" \");\n  }\n}\nexport class ModalManageRefereesComponent {\n  constructor(_router, _commonsService, _http, _trans, renderer, _modalService, _loadingService, _seasonService, _seasonRefereeService, _toastService, _translateService, _userService) {\n    this._router = _router;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._seasonService = _seasonService;\n    this._seasonRefereeService = _seasonRefereeService;\n    this._toastService = _toastService;\n    this._translateService = _translateService;\n    this._userService = _userService;\n    this.seasonId = null;\n    this.newRefereeForm = new FormGroup({});\n    this.newRefereeModel = {\n      season_id: this.seasonId,\n      referee_name: '',\n      referee_type: 'freetext',\n      user_id: []\n    };\n    this.newRefereeFields = [{\n      key: 'season_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      type: 'radio',\n      key: 'referee_type',\n      props: {\n        translate: true,\n        label: 'Referee Type',\n        required: true,\n        options: [{\n          label: 'Free Text',\n          value: 'freetext'\n        }, {\n          label: 'User',\n          value: 'user'\n        }]\n      }\n    }, {\n      key: 'referee_name',\n      type: 'input',\n      props: {\n        type: 'text',\n        label: 'Referee Name',\n        placeholder: 'Enter one or more referee names, separated by commas (e.g., John Doe, Jane Smith)'\n      },\n      expressions: {\n        hide: 'model.referee_type === \"user\"',\n        required: 'model.referee_type === \"freetext\"'\n      }\n    }, {\n      key: 'user_id',\n      type: 'ng-select',\n      props: {\n        multiple: true,\n        hideOnMultiple: true,\n        defaultValue: [],\n        label: this._translateService.instant('Select list referees'),\n        placeholder: this._translateService.instant('Select referees'),\n        options: []\n      },\n      expressions: {\n        required: 'model.referee_type === \"user\"',\n        hide: 'model.referee_type === \"freetext\"'\n      }\n    }];\n    this.newRefereeError = null;\n    this.listReferee = [];\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    // table_name = 'manage_referees';\n    this.dtElement = DataTableDirective;\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\n  }\n  ngOnInit() {\n    this.getListReferee();\n    this.newRefereeModel = {\n      season_id: this.seasonId,\n      referee_name: '',\n      referee_type: 'freetext',\n      user_id: []\n    };\n  }\n  ngAfterViewInit() {\n    this.initTable();\n    this.dtTrigger.next(this.dtOptions); // <-- move it here\n  }\n\n  initTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/referees`, dataTablesParameters).subscribe(resp => {\n          this._loadingService.dismiss();\n          const currentSelectedRefId = resp.data.map(e => e.user_id);\n          this.listReferee = this.listReferee.filter(e => {\n            return !currentSelectedRefId.includes(e.id);\n          });\n          this.newRefereeFields.forEach(e => {\n            if (e.key === 'user_id') {\n              e.props.options = this.listReferee.map(ref => ({\n                label: this.name_settings?.is_on ? `${ref.first_name} ${ref.last_name} - ${ref.email}` : `${ref.last_name} ${ref.first_name} - ${ref.email}`,\n                value: ref.id\n              }));\n            }\n          });\n          callback({\n            // this function callback is used to return data to datatable\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      columns: [{\n        title: 'id',\n        data: 'id',\n        visible: false\n      }, {\n        title: this._translateService.instant('Referee Name'),\n        data: 'referee_name'\n      }, {\n        data: 'id',\n        className: 'text-right',\n        render: data => {\n          return `<button class=\"btn btn-danger btn-sm btn-delete-referee\" data-id=\"${data}\">Delete</button>`;\n        }\n      }],\n      rowCallback: (row, data, index) => {\n        const self = this;\n        // Remove any existing click handlers first\n        $('button.btn-delete-referee', row).off('click');\n        // Add click handler\n        $('button.btn-delete-referee', row).on('click', function () {\n          const id = $(this).data('id');\n          self.onDeleteReferee(id);\n        });\n        return row;\n      },\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: []\n      }\n    };\n  }\n  reloadTable() {\n    this.dtElement.dtInstance.then(dtInstance => {\n      dtInstance.ajax.reload();\n    });\n  }\n  onUpdate(newModelValue) {\n    if (this.newRefereeError) {\n      this.newRefereeError = '';\n    }\n  }\n  checkRefereeName(listName) {\n    const nameList = listName.split(',');\n    const nameListTrimmed = nameList.map(name => name.trim());\n    const listNameOutOfMaxLength = nameListTrimmed.filter(name => {\n      return name.length > 50;\n    });\n    return {\n      isValid: listNameOutOfMaxLength.length === 0,\n      message: listNameOutOfMaxLength.length > 0 ? 'Each referee\\'s name must not exceed 50 characters.' : '',\n      listNameOutOfMaxLength\n    };\n  }\n  onCreateNewReferee(model) {\n    const {\n      referee_type,\n      referee_name,\n      user_id\n    } = this.newRefereeModel;\n    console.log(this.newRefereeModel);\n    if (referee_type === 'freetext') {\n      if (!referee_name) {\n        this.newRefereeError = 'Please enter at least one referee name.';\n        return;\n      }\n      const {\n        isValid,\n        message,\n        listNameOutOfMaxLength\n      } = this.checkRefereeName(referee_name);\n      if (!isValid) {\n        this.newRefereeError = message;\n        return;\n      }\n    } else if (referee_type === 'user' && (!user_id || user_id.length === 0)) {\n      this.newRefereeError = 'Please select at least one user.';\n      return;\n    }\n    this._seasonRefereeService.createNewReferee(model).subscribe(res => {\n      this.reloadTable();\n      this.resetForm();\n    }, error => {});\n  }\n  resetForm() {\n    this.newRefereeModel = {\n      ...this.newRefereeModel,\n      referee_name: '',\n      user_id: []\n    };\n  }\n  onDeleteReferee(refereeId) {\n    Swal.fire({\n      title: 'Are you sure?',\n      text: 'This action will automatically remove the referee from all assigned matches in this season, and you won\\'t be able to revert this!',\n      icon: 'warning',\n      reverseButtons: true,\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Delete'),\n      cancelButtonText: this._translateService.instant('No'),\n      buttonsStyling: false,\n      customClass: {\n        confirmButton: 'btn btn-danger ml-1',\n        cancelButton: 'btn btn-outline-primary'\n      }\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._seasonRefereeService.deleteRefereeFromSeason(this.seasonId, refereeId).subscribe(data => {\n          Swal.fire({\n            title: 'Deleted!',\n            text: data['message'],\n            icon: 'success'\n          });\n          this.reloadTable();\n        });\n      }\n    });\n  }\n  closeModal() {\n    this._modalService.dismissAll();\n  }\n  getListReferee() {\n    this._userService.getUserHasRefereeRole().subscribe(response => {\n      this.listReferee = response.data;\n      // this.newRefereeFields.forEach((e) => {\n      //   if (e.key === 'user_id') {\n      //     e.props.options = this.listReferee.map((ref) => ({\n      //       label: `${ref.first_name} ${ref.last_name} - ${ref.email}`,\n      //       value: ref.id\n      //     }));\n      //   }\n      // });\n    });\n  }\n  static #_ = this.ɵfac = function ModalManageRefereesComponent_Factory(t) {\n    return new (t || ModalManageRefereesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.SeasonService), i0.ɵɵdirectiveInject(i8.SeasonRefereeService), i0.ɵɵdirectiveInject(i9.ToastrService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i10.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalManageRefereesComponent,\n    selectors: [[\"app-modal-manage-referees\"]],\n    viewQuery: function ModalManageRefereesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    inputs: {\n      seasonId: \"seasonId\"\n    },\n    decls: 19,\n    vars: 13,\n    consts: [[1, \"modal-header\"], [\"id\", \"modalManageReferee\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\", 2, \"height\", \"80vh\"], [3, \"formGroup\", \"ngSubmit\"], [3, \"form\", \"fields\", \"model\", \"modelChange\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\"], [1, \"card\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [1, \"modal-footer\"], [1, \"text-danger\"]],\n    template: function ModalManageRefereesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalManageRefereesComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 4)(8, \"form\", 5);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalManageRefereesComponent_Template_form_ngSubmit_8_listener() {\n          return ctx.onCreateNewReferee(ctx.newRefereeModel);\n        });\n        i0.ɵɵelementStart(9, \"formly-form\", 6);\n        i0.ɵɵlistener(\"modelChange\", function ModalManageRefereesComponent_Template_formly_form_modelChange_9_listener($event) {\n          return ctx.onUpdate($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(10, ModalManageRefereesComponent_p_10_Template, 3, 3, \"p\", 7);\n        i0.ɵɵelement(11, \"hr\");\n        i0.ɵɵelementStart(12, \"button\", 8);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(15, \"hr\");\n        i0.ɵɵelementStart(16, \"div\", 9);\n        i0.ɵɵelement(17, \"table\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(18, \"div\", 11);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 9, \"Manage Referee\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"formGroup\", ctx.newRefereeForm);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"form\", ctx.newRefereeForm)(\"fields\", ctx.newRefereeFields)(\"model\", ctx.newRefereeModel);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.newRefereeError);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 11, \"Submit\"), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAgB;AAc1C,SAASC,OAAO,QAAQ,MAAM;AAE9B,SAASC,WAAW,QAAQ,yCAAyC;AAGrE,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,kBAAkB,QAAQ,oBAAoB;;;;;;;;;;;;;;ICInDC,EAAA,CAAAC,cAAA,YAA+C;IAC7CD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,QAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,eAAA,OACF;;;ADEJ,OAAM,MAAOC,4BAA4B;EA2EvCC,YACSC,OAAe,EACfC,eAA+B,EAC/BC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,aAAuB,EACvBC,eAA+B,EAC/BC,cAA6B,EAC7BC,qBAA2C,EAC1CC,aAA4B,EAC7BC,iBAAmC,EACnCC,YAAyB;IAXzB,KAAAX,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,qBAAqB,GAArBA,qBAAqB;IACpB,KAAAC,aAAa,GAAbA,aAAa;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,YAAY,GAAZA,YAAY;IArFZ,KAAAC,QAAQ,GAAkB,IAAI;IAEhC,KAAAC,cAAc,GAAG,IAAI7B,SAAS,CAAC,EAAE,CAAC;IAClC,KAAA8B,eAAe,GAAG;MACvBC,SAAS,EAAE,IAAI,CAACH,QAAQ;MACxBI,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,UAAU;MACxBC,OAAO,EAAE;KACV;IACM,KAAAC,gBAAgB,GAAG,CACxB;MACEC,GAAG,EAAE,WAAW;MAChBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;;KAET,EACD;MACEA,IAAI,EAAE,OAAO;MACbD,GAAG,EAAE,cAAc;MACnBE,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,cAAc;QACrBC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,CACP;UAAEF,KAAK,EAAE,WAAW;UAAEG,KAAK,EAAE;QAAU,CAAE,EACzC;UAAEH,KAAK,EAAE,MAAM;UAAEG,KAAK,EAAE;QAAM,CAAE;;KAGrC,EACD;MACEP,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE,MAAM;QACZG,KAAK,EAAE,cAAc;QACrBI,WAAW,EAAE;OACd;MACDC,WAAW,EAAE;QACXC,IAAI,EAAE,+BAA+B;QACrCL,QAAQ,EAAE;;KAEb,EACD;MACEL,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLS,QAAQ,EAAE,IAAI;QACdC,cAAc,EAAE,IAAI;QACpBC,YAAY,EAAE,EAAE;QAChBT,KAAK,EAAE,IAAI,CAACd,iBAAiB,CAACwB,OAAO,CAAC,sBAAsB,CAAC;QAC7DN,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACwB,OAAO,CAAC,iBAAiB,CAAC;QAC9DR,OAAO,EAAE;OACV;MACDG,WAAW,EAAE;QACXJ,QAAQ,EAAE,+BAA+B;QACzCK,IAAI,EAAE;;KAET,CACF;IAEM,KAAAjC,eAAe,GAAkB,IAAI;IAErC,KAAAsC,WAAW,GAAG,EAAE;IAEvB,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAIpD,OAAO,EAAe;IAC5D;IAGA,KAAAqD,SAAS,GAAQlD,kBAAkB;IAiBjC,IAAI,CAACmD,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;EACxE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IAErB,IAAI,CAAC/B,eAAe,GAAG;MACrBC,SAAS,EAAE,IAAI,CAACH,QAAQ;MACxBI,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,UAAU;MACxBC,OAAO,EAAE;KACV;EACH;EAEA4B,eAAeA,CAAA;IACb,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACV,SAAS,CAACW,IAAI,CAAC,IAAI,CAACZ,SAAS,CAAC,CAAC,CAAC;EACvC;;EAEAW,SAASA,CAAA;IACP,IAAI,CAACX,SAAS,GAAG;MACfa,GAAG,EAAE,IAAI,CAAChD,eAAe,CAACiD,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAI,CAACrD,KAAK,CACPsD,GAAG,CACF,GAAGtE,WAAW,CAACuE,MAAM,YAAY,IAAI,CAAC7C,QAAQ,WAAW,EACzD0C,oBAAoB,CACrB,CACAI,SAAS,CAAEC,IAAS,IAAI;UACvB,IAAI,CAACrD,eAAe,CAACsD,OAAO,EAAE;UAE9B,MAAMC,oBAAoB,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC9C,OAAO,CAAC;UAE5D,IAAI,CAACiB,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC8B,MAAM,CAAED,CAAC,IAAI;YAE/C,OAAO,CAACH,oBAAoB,CAACK,QAAQ,CAACF,CAAC,CAACG,EAAE,CAAC;UAC7C,CAAC,CAAC;UAGF,IAAI,CAAChD,gBAAgB,CAACiD,OAAO,CAAEJ,CAAC,IAAI;YAClC,IAAIA,CAAC,CAAC5C,GAAG,KAAK,SAAS,EAAE;cACvB4C,CAAC,CAAC1C,KAAK,CAACI,OAAO,GAAG,IAAI,CAACS,WAAW,CAAC4B,GAAG,CAAEM,GAAG,KAAM;gBAC/C7C,KAAK,EAAE,IAAI,CAACe,aAAa,EAAE+B,KAAK,GAC5B,GAAGD,GAAG,CAACE,UAAU,IAAIF,GAAG,CAACG,SAAS,MAAMH,GAAG,CAACI,KAAK,EAAE,GACnD,GAAGJ,GAAG,CAACG,SAAS,IAAIH,GAAG,CAACE,UAAU,MAAMF,GAAG,CAACI,KAAK,EAAE;gBACvD9C,KAAK,EAAE0C,GAAG,CAACF;eACZ,CAAC,CAAC;;UAEP,CAAC,CAAC;UAEFZ,QAAQ,CAAC;YACP;YACAmB,YAAY,EAAEf,IAAI,CAACe,YAAY;YAC/BC,eAAe,EAAEhB,IAAI,CAACgB,eAAe;YACrCb,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QAEJ,CAAC,CAAC;MAEN,CAAC;MACDc,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAAC7E,eAAe,CAACiD,iBAAiB,CAAC6B,IAAI;MACrDC,OAAO,EAAE,CACP;QACEC,KAAK,EAAE,IAAI;QACXnB,IAAI,EAAE,IAAI;QACVoB,OAAO,EAAE;OACV,EACD;QACED,KAAK,EAAE,IAAI,CAACvE,iBAAiB,CAACwB,OAAO,CAAC,cAAc,CAAC;QACrD4B,IAAI,EAAE;OACP,EACD;QACEA,IAAI,EAAE,IAAI;QACVqB,SAAS,EAAE,YAAY;QACvBC,MAAM,EAAGtB,IAAI,IAAI;UACf,OACE,qEAAqEA,IAAI,mBAAmB;QAEhG;OACD,CACF;MACDuB,WAAW,EAAEA,CAACC,GAAS,EAAExB,IAAoB,EAAEyB,KAAa,KAAI;QAC9D,MAAMC,IAAI,GAAG,IAAI;QACjB;QACAC,CAAC,CAAC,2BAA2B,EAAEH,GAAG,CAAC,CAACI,GAAG,CAAC,OAAO,CAAC;QAEhD;QACAD,CAAC,CAAC,2BAA2B,EAAEH,GAAG,CAAC,CAACK,EAAE,CAAC,OAAO,EAAE;UAC9C,MAAMxB,EAAE,GAAGsB,CAAC,CAAC,IAAI,CAAC,CAAC3B,IAAI,CAAC,IAAI,CAAC;UAC7B0B,IAAI,CAACI,eAAe,CAACzB,EAAE,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAOmB,GAAG;MACZ,CAAC;MACDO,OAAO,EAAE;QACP5C,GAAG,EAAE,IAAI,CAAChD,eAAe,CAACiD,iBAAiB,CAAC2C,OAAO,CAAC5C,GAAG;QACvD4C,OAAO,EAAE;;KAEZ;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACxD,SAAS,CAACyD,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;MAC5DA,UAAU,CAAC1C,IAAI,CAAC4C,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAACC,aAAa;IACpB,IAAI,IAAI,CAACtG,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,GAAG,EAAE;;EAE7B;EAEAuG,gBAAgBA,CAACC,QAAQ;IACvB,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;IACpC,MAAMC,eAAe,GAAGF,QAAQ,CAACvC,GAAG,CAAE0C,IAAI,IAAKA,IAAI,CAACC,IAAI,EAAE,CAAC;IAE3D,MAAMC,sBAAsB,GAAGH,eAAe,CAACvC,MAAM,CAAEwC,IAAI,IAAI;MAC7D,OAAOA,IAAI,CAACG,MAAM,GAAG,EAAE;IACzB,CAAC,CAAC;IAEF,OAAO;MACLC,OAAO,EAAEF,sBAAsB,CAACC,MAAM,KAAK,CAAC;MAC5CE,OAAO,EAAEH,sBAAsB,CAACC,MAAM,GAAG,CAAC,GAAG,qDAAqD,GAAG,EAAE;MACvGD;KACD;EAEH;EAEAI,kBAAkBA,CAACC,KAAK;IACtB,MAAM;MAAE/F,YAAY;MAAED,YAAY;MAAEE;IAAO,CAAE,GAAG,IAAI,CAACJ,eAAe;IAEpEmG,OAAO,CAACC,GAAG,CAAC,IAAI,CAACpG,eAAe,CAAC;IAEjC,IAAIG,YAAY,KAAK,UAAU,EAAE;MAC/B,IAAI,CAACD,YAAY,EAAE;QACjB,IAAI,CAACnB,eAAe,GAAG,yCAAyC;QAChE;;MAGF,MAAM;QAAEgH,OAAO;QAAEC,OAAO;QAAEH;MAAsB,CAAE,GAAG,IAAI,CAACP,gBAAgB,CAACpF,YAAY,CAAC;MACxF,IAAI,CAAC6F,OAAO,EAAE;QACZ,IAAI,CAAChH,eAAe,GAAGiH,OAAO;QAE9B;;KAEH,MAAM,IAAI7F,YAAY,KAAK,MAAM,KAAK,CAACC,OAAO,IAAIA,OAAO,CAAC0F,MAAM,KAAK,CAAC,CAAC,EAAE;MACxE,IAAI,CAAC/G,eAAe,GAAG,kCAAkC;MACzD;;IAGF,IAAI,CAACW,qBAAqB,CAAC2G,gBAAgB,CAACH,KAAK,CAAC,CAACtD,SAAS,CACzD0D,GAAG,IAAI;MAGN,IAAI,CAACtB,WAAW,EAAE;MAClB,IAAI,CAACuB,SAAS,EAAE;IAClB,CAAC,EACAC,KAAK,IAAI,CAEV,CAAC,CACF;EACH;EAEAD,SAASA,CAAA;IACP,IAAI,CAACvG,eAAe,GAAG;MACrB,GAAG,IAAI,CAACA,eAAe;MACvBE,YAAY,EAAE,EAAE;MAChBE,OAAO,EAAE;KACV;EACH;EAEA0E,eAAeA,CAAC2B,SAA0B;IACxCpI,IAAI,CAACqI,IAAI,CAAC;MACRvC,KAAK,EAAE,eAAe;MACtBwC,IAAI,EAAE,oIAAoI;MAC1IC,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAACnH,iBAAiB,CAACwB,OAAO,CAAC,QAAQ,CAAC;MAC3D4F,gBAAgB,EAAE,IAAI,CAACpH,iBAAiB,CAACwB,OAAO,CAAC,IAAI,CAAC;MACtD6F,cAAc,EAAE,KAAK;MACrBC,WAAW,EAAE;QACXC,aAAa,EAAE,qBAAqB;QACpCC,YAAY,EAAE;;KAEjB,CAAC,CAAClC,IAAI,CAAEmC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI,CAAC5H,qBAAqB,CAAC6H,uBAAuB,CAAC,IAAI,CAACzH,QAAQ,EAAE2G,SAAS,CAAC,CAAC7D,SAAS,CAAEI,IAAI,IAAI;UAC9F3E,IAAI,CAACqI,IAAI,CAAC;YACRvC,KAAK,EAAE,UAAU;YACjBwC,IAAI,EAAE3D,IAAI,CAAC,SAAS,CAAC;YACrB4D,IAAI,EAAE;WACP,CAAC;UACF,IAAI,CAAC5B,WAAW,EAAE;QACpB,CAAC,CAAC;;IAEN,CAAC,CAAC;EAEJ;EAGAwC,UAAUA,CAAA;IACR,IAAI,CAACjI,aAAa,CAACkI,UAAU,EAAE;EACjC;EAEA1F,cAAcA,CAAA;IACZ,IAAI,CAAClC,YAAY,CAAC6H,qBAAqB,EAAE,CAAC9E,SAAS,CAAE+E,QAAQ,IAAI;MAC/D,IAAI,CAACtG,WAAW,GAAGsG,QAAQ,CAAC3E,IAAI;MAChC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,CAAC,CAAC;EACJ;EAAC,QAAA4E,CAAA;qBAvTU5I,4BAA4B,EAAAT,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA5J,EAAA,CAAAsJ,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA9J,EAAA,CAAAsJ,iBAAA,CAAAtJ,EAAA,CAAA+J,SAAA,GAAA/J,EAAA,CAAAsJ,iBAAA,CAAAU,EAAA,CAAAC,QAAA,GAAAjK,EAAA,CAAAsJ,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAnK,EAAA,CAAAsJ,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAArK,EAAA,CAAAsJ,iBAAA,CAAAgB,EAAA,CAAAC,oBAAA,GAAAvK,EAAA,CAAAsJ,iBAAA,CAAAkB,EAAA,CAAAC,aAAA,GAAAzK,EAAA,CAAAsJ,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA9J,EAAA,CAAAsJ,iBAAA,CAAAoB,GAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA;UAA5BnK,4BAA4B;IAAAoK,SAAA;IAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAuE5BjL,kBAAkB;;;;;;;;;;;;;;;QCpG/BC,EAAA,CAAAC,cAAA,aAA0B;QAEtBD,EAAA,CAAAE,MAAA,GACF;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,gBAKC;QAFCD,EAAA,CAAAkL,UAAA,mBAAAC,8DAAA;UAAA,OAASF,GAAA,CAAAhC,UAAA,EAAY;QAAA,EAAC;QAGtBjJ,EAAA,CAAAC,cAAA,cAAyB;QAAAD,EAAA,CAAAE,MAAA,aAAO;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAI3CH,EAAA,CAAAC,cAAA,aAAuE;QAGnED,EAAA,CAAAkL,UAAA,sBAAAE,+DAAA;UAAA,OAAYH,GAAA,CAAAvD,kBAAA,CAAAuD,GAAA,CAAAxJ,eAAA,CAAmC;QAAA,EAAC;QAEhDzB,EAAA,CAAAC,cAAA,qBAKC;QADCD,EAAA,CAAAkL,UAAA,yBAAAG,yEAAAC,MAAA;UAAA,OAAeL,GAAA,CAAApE,QAAA,CAAAyE,MAAA,CAAgB;QAAA,EAAC;QACjCtL,EAAA,CAAAG,YAAA,EAAc;QACfH,EAAA,CAAAuL,UAAA,KAAAC,0CAAA,eAEI;QACJxL,EAAA,CAAAyL,SAAA,UAAM;QACNzL,EAAA,CAAAC,cAAA,iBAA2D;QACzDD,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGXH,EAAA,CAAAyL,SAAA,UAAM;QACNzL,EAAA,CAAAC,cAAA,cAAkB;QAChBD,EAAA,CAAAyL,SAAA,iBACQ;QACVzL,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAyL,SAAA,eAsBM;;;QA5DFzL,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,8BACF;QAaEN,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAA0L,UAAA,cAAAT,GAAA,CAAAzJ,cAAA,CAA4B;QAI1BxB,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAA0L,UAAA,SAAAT,GAAA,CAAAzJ,cAAA,CAAuB,WAAAyJ,GAAA,CAAAnJ,gBAAA,WAAAmJ,GAAA,CAAAxJ,eAAA;QAKrBzB,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAA0L,UAAA,SAAAT,GAAA,CAAAzK,eAAA,CAAqB;QAKvBR,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,wBACF;QAKiBN,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAA0L,UAAA,cAAAT,GAAA,CAAAlI,SAAA,CAAuB,cAAAkI,GAAA,CAAAjI,SAAA", "names": ["FormGroup", "Subject", "environment", "<PERSON><PERSON>", "DataTableDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ctx_r0", "newRefereeError", "ModalManageRefereesComponent", "constructor", "_router", "_commonsService", "_http", "_trans", "renderer", "_modalService", "_loadingService", "_seasonService", "_seasonRefereeService", "_toastService", "_translateService", "_userService", "seasonId", "newRefereeForm", "newRefereeModel", "season_id", "referee_name", "referee_type", "user_id", "newRefereeFields", "key", "type", "props", "translate", "label", "required", "options", "value", "placeholder", "expressions", "hide", "multiple", "hideOnMultiple", "defaultValue", "instant", "listReferee", "dtOptions", "dtTrigger", "dtElement", "name_settings", "JSON", "parse", "localStorage", "getItem", "ngOnInit", "getListReferee", "ngAfterViewInit", "initTable", "next", "dom", "dataTableDefaults", "select", "rowId", "ajax", "dataTablesParameters", "callback", "get", "apiUrl", "subscribe", "resp", "dismiss", "currentSelectedRefId", "data", "map", "e", "filter", "includes", "id", "for<PERSON>ach", "ref", "is_on", "first_name", "last_name", "email", "recordsTotal", "recordsFiltered", "responsive", "scrollX", "language", "lang", "columns", "title", "visible", "className", "render", "row<PERSON>allback", "row", "index", "self", "$", "off", "on", "onDeleteReferee", "buttons", "reloadTable", "dtInstance", "then", "reload", "onUpdate", "newModelValue", "checkRefereeName", "listName", "nameList", "split", "nameListTrimmed", "name", "trim", "listNameOutOfMaxLength", "length", "<PERSON><PERSON><PERSON><PERSON>", "message", "onCreateNewReferee", "model", "console", "log", "createNewReferee", "res", "resetForm", "error", "refereeId", "fire", "text", "icon", "reverseButtons", "showCancelButton", "confirmButtonText", "cancelButtonText", "buttonsStyling", "customClass", "confirmButton", "cancelButton", "result", "isConfirmed", "deleteRefereeFromSeason", "closeModal", "dismissAll", "getUserHasRefereeRole", "response", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "CommonsService", "i3", "HttpClient", "i4", "TranslateService", "Renderer2", "i5", "NgbModal", "i6", "LoadingService", "i7", "SeasonService", "i8", "SeasonRefereeService", "i9", "ToastrService", "i10", "UserService", "_2", "selectors", "viewQuery", "ModalManageRefereesComponent_Query", "rf", "ctx", "ɵɵlistener", "ModalManageRefereesComponent_Template_button_click_4_listener", "ModalManageRefereesComponent_Template_form_ngSubmit_8_listener", "ModalManageRefereesComponent_Template_formly_form_modelChange_9_listener", "$event", "ɵɵtemplate", "ModalManageRefereesComponent_p_10_Template", "ɵɵelement", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league\\modal-manage-referees\\modal-manage-referees.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league\\modal-manage-referees\\modal-manage-referees.component.html"], "sourcesContent": ["import { Component, Input, Renderer2, ViewChild } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonsService } from '../../../../services/commons.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { TeamService } from '../../../../services/team.service';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { LoadingService } from '../../../../services/loading.service';\r\nimport { RegistrationService } from '../../../../services/registration.service';\r\nimport { SeasonService } from '../../../../services/season.service';\r\nimport { CoreSidebarService } from '../../../../../@core/components/core-sidebar/core-sidebar.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { SeasonRefereeService } from '../../../../services/season-referee.service';\r\nimport { UserService } from '../../../../services/user.service';\r\nimport { Subject } from 'rxjs';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { environment } from '../../../../../environments/environment';\r\nimport moment from 'moment/moment';\r\nimport { AppConfig } from '../../../../app-config';\r\nimport Swal from 'sweetalert2';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ToastrService } from 'ngx-toastr';\r\n\r\n@Component({\r\n  selector: 'app-modal-manage-referees',\r\n  templateUrl: './modal-manage-referees.component.html',\r\n  styleUrls: ['./modal-manage-referees.component.scss']\r\n})\r\nexport class ModalManageRefereesComponent {\r\n\r\n  @Input() seasonId: number | null = null;\r\n\r\n  public newRefereeForm = new FormGroup({});\r\n  public newRefereeModel = {\r\n    season_id: this.seasonId,\r\n    referee_name: '',\r\n    referee_type: 'freetext',\r\n    user_id: []\r\n  };\r\n  public newRefereeFields = [\r\n    {\r\n      key: 'season_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      }\r\n    },\r\n    {\r\n      type: 'radio',\r\n      key: 'referee_type',\r\n      props: {\r\n        translate: true,\r\n        label: 'Referee Type',\r\n        required: true,\r\n        options: [\r\n          { label: 'Free Text', value: 'freetext' },\r\n          { label: 'User', value: 'user' }\r\n        ]\r\n      }\r\n    },\r\n    {\r\n      key: 'referee_name',\r\n      type: 'input',\r\n      props: {\r\n        type: 'text',\r\n        label: 'Referee Name',\r\n        placeholder: 'Enter one or more referee names, separated by commas (e.g., John Doe, Jane Smith)'\r\n      },\r\n      expressions: {\r\n        hide: 'model.referee_type === \"user\"',\r\n        required: 'model.referee_type === \"freetext\"'\r\n      }\r\n    },\r\n    {\r\n      key: 'user_id',\r\n      type: 'ng-select',\r\n      props: {\r\n        multiple: true,\r\n        hideOnMultiple: true,\r\n        defaultValue: [],\r\n        label: this._translateService.instant('Select list referees'),\r\n        placeholder: this._translateService.instant('Select referees'),\r\n        options: []\r\n      },\r\n      expressions: {\r\n        required: 'model.referee_type === \"user\"',\r\n        hide: 'model.referee_type === \"freetext\"'\r\n      }\r\n    }\r\n  ];\r\n\r\n  public newRefereeError: null | string = null;\r\n\r\n  public listReferee = [];\r\n\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  // table_name = 'manage_referees';\r\n\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  public name_settings : any;\r\n\r\n  constructor(\r\n    public _router: Router,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _modalService: NgbModal,\r\n    public _loadingService: LoadingService,\r\n    public _seasonService: SeasonService,\r\n    public _seasonRefereeService: SeasonRefereeService,\r\n    private _toastService: ToastrService,\r\n    public _translateService: TranslateService,\r\n    public _userService: UserService\r\n  ) {\r\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.getListReferee();\r\n\r\n    this.newRefereeModel = {\r\n      season_id: this.seasonId,\r\n      referee_name: '',\r\n      referee_type: 'freetext',\r\n      user_id: []\r\n    };\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.initTable();\r\n    this.dtTrigger.next(this.dtOptions); // <-- move it here\r\n  }\r\n\r\n  initTable() {\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        this._http\r\n          .get<any>(\r\n            `${environment.apiUrl}/seasons/${this.seasonId}/referees`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            this._loadingService.dismiss();\r\n\r\n            const currentSelectedRefId = resp.data.map((e) => e.user_id);\r\n\r\n            this.listReferee = this.listReferee.filter((e) => {\r\n\r\n              return !currentSelectedRefId.includes(e.id);\r\n            });\r\n\r\n\r\n            this.newRefereeFields.forEach((e) => {\r\n              if (e.key === 'user_id') {\r\n                e.props.options = this.listReferee.map((ref) => ({\r\n                  label: this.name_settings?.is_on\r\n                    ? `${ref.first_name} ${ref.last_name} - ${ref.email}`\r\n                    : `${ref.last_name} ${ref.first_name} - ${ref.email}`,\r\n                  value: ref.id\r\n                }));\r\n              }\r\n            });\r\n\r\n            callback({\r\n              // this function callback is used to return data to datatable\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n\r\n          });\r\n\r\n      },\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      columns: [\r\n        {\r\n          title: 'id',\r\n          data: 'id',\r\n          visible: false\r\n        },\r\n        {\r\n          title: this._translateService.instant('Referee Name'),\r\n          data: 'referee_name'\r\n        },\r\n        {\r\n          data: 'id',\r\n          className: 'text-right',\r\n          render: (data) => {\r\n            return (\r\n              `<button class=\"btn btn-danger btn-sm btn-delete-referee\" data-id=\"${data}\">Delete</button>`\r\n            );\r\n          }\r\n        }\r\n      ],\r\n      rowCallback: (row: Node, data: any[] | Object, index: number) => {\r\n        const self = this;\r\n        // Remove any existing click handlers first\r\n        $('button.btn-delete-referee', row).off('click');\r\n\r\n        // Add click handler\r\n        $('button.btn-delete-referee', row).on('click', function() {\r\n          const id = $(this).data('id');\r\n          self.onDeleteReferee(id);\r\n        });\r\n\r\n        return row;\r\n      },\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: []\r\n      }\r\n    };\r\n  }\r\n\r\n  reloadTable() {\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      dtInstance.ajax.reload();\r\n    });\r\n  }\r\n\r\n  onUpdate(newModelValue) {\r\n    if (this.newRefereeError) {\r\n      this.newRefereeError = '';\r\n    }\r\n  }\r\n\r\n  checkRefereeName(listName) {\r\n    const nameList = listName.split(',');\r\n    const nameListTrimmed = nameList.map((name) => name.trim());\r\n\r\n    const listNameOutOfMaxLength = nameListTrimmed.filter((name) => {\r\n      return name.length > 50;\r\n    });\r\n\r\n    return {\r\n      isValid: listNameOutOfMaxLength.length === 0,\r\n      message: listNameOutOfMaxLength.length > 0 ? 'Each referee\\'s name must not exceed 50 characters.' : '',\r\n      listNameOutOfMaxLength\r\n    };\r\n\r\n  }\r\n\r\n  onCreateNewReferee(model) {\r\n    const { referee_type, referee_name, user_id } = this.newRefereeModel;\r\n\r\n    console.log(this.newRefereeModel);\r\n\r\n    if (referee_type === 'freetext') {\r\n      if (!referee_name) {\r\n        this.newRefereeError = 'Please enter at least one referee name.';\r\n        return;\r\n      }\r\n\r\n      const { isValid, message, listNameOutOfMaxLength } = this.checkRefereeName(referee_name);\r\n      if (!isValid) {\r\n        this.newRefereeError = message;\r\n\r\n        return;\r\n      }\r\n    } else if (referee_type === 'user' && (!user_id || user_id.length === 0)) {\r\n      this.newRefereeError = 'Please select at least one user.';\r\n      return;\r\n    }\r\n\r\n    this._seasonRefereeService.createNewReferee(model).subscribe(\r\n      (res) => {\r\n\r\n\r\n        this.reloadTable();\r\n        this.resetForm();\r\n      },\r\n      (error) => {\r\n\r\n      }\r\n    );\r\n  }\r\n\r\n  resetForm() {\r\n    this.newRefereeModel = {\r\n      ...this.newRefereeModel,\r\n      referee_name: '',\r\n      user_id: []\r\n    };\r\n  }\r\n\r\n  onDeleteReferee(refereeId: string | number) {\r\n    Swal.fire({\r\n      title: 'Are you sure?',\r\n      text: 'This action will automatically remove the referee from all assigned matches in this season, and you won\\'t be able to revert this!',\r\n      icon: 'warning',\r\n      reverseButtons: true,\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Delete'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      buttonsStyling: false,\r\n      customClass: {\r\n        confirmButton: 'btn btn-danger ml-1',\r\n        cancelButton: 'btn btn-outline-primary'\r\n      }\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this._seasonRefereeService.deleteRefereeFromSeason(this.seasonId, refereeId).subscribe((data) => {\r\n          Swal.fire({\r\n            title: 'Deleted!',\r\n            text: data['message'],\r\n            icon: 'success'\r\n          });\r\n          this.reloadTable();\r\n        });\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n\r\n  closeModal() {\r\n    this._modalService.dismissAll();\r\n  }\r\n\r\n  getListReferee() {\r\n    this._userService.getUserHasRefereeRole().subscribe((response) => {\r\n      this.listReferee = response.data;\r\n      // this.newRefereeFields.forEach((e) => {\r\n      //   if (e.key === 'user_id') {\r\n      //     e.props.options = this.listReferee.map((ref) => ({\r\n      //       label: `${ref.first_name} ${ref.last_name} - ${ref.email}`,\r\n      //       value: ref.id\r\n      //     }));\r\n      //   }\r\n      // });\r\n    });\r\n  }\r\n}\r\n", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\" id=\"modalManageReferee\">\r\n    {{ 'Manage Referee' | translate }}\r\n  </h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n\r\n<div class=\"modal-body\" style=\"height: 80vh\" tabindex=\"0\" ngbAutofocus>\r\n  <form\r\n    [formGroup]=\"newRefereeForm\"\r\n    (ngSubmit)=\"onCreateNewReferee(newRefereeModel)\"\r\n  >\r\n    <formly-form\r\n      [form]=\"newRefereeForm\"\r\n      [fields]=\"newRefereeFields\"\r\n      [model]=\"newRefereeModel\"\r\n      (modelChange)=\"onUpdate($event)\"\r\n    ></formly-form>\r\n    <p *ngIf=\"newRefereeError\" class=\"text-danger\">\r\n      * {{ newRefereeError | translate }}\r\n    </p>\r\n    <hr />\r\n    <button type=\"submit\" class=\"btn btn-primary\" rippleEffect>\r\n      {{ 'Submit' | translate }}\r\n    </button>\r\n  </form>\r\n\r\n  <hr />\r\n  <div class=\"card\">\r\n    <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"table row-border hover\">\r\n    </table>\r\n  </div>\r\n</div>\r\n<div class=\"modal-footer\">\r\n  <!--    <table class=\"table border row-border hover\">\r\n        <thead>\r\n          <tr>\r\n            <th class=\"text-left\">{{ 'Referee' | translate }}</th>\r\n            <th class=\"text-right\">{{ 'Action' | translate }}</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let referee of currentSeasonReferees\">\r\n            <td class=\"text-left\">{{ referee.referee_name }}</td>\r\n            <td class=\"text-right\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-sm btn-danger\"\r\n              >\r\n                {{ 'Delete' | translate }}\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>-->\r\n</div>\r\n\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}