import { HttpClient } from '@angular/common/http';
import {
  Component,
  OnInit,
  ViewChild,
  AfterViewInit,
  OnDestroy,
  Input,
  TemplateRef
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DataTableDirective } from 'angular-datatables';
import { CommonsService } from 'app/services/commons.service';
import { ExportService } from 'app/services/export.service';
import { environment } from 'environments/environment';
import { Subject } from 'rxjs';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { EditorSidebarParams } from 'app/interfaces/editor-sidebar';
import { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';
import moment from 'moment';

@Component({
  selector: 'app-league-table-view',
  templateUrl: './league-table-view.component.html',
  styleUrls: ['./league-table-view.component.scss']
})
export class LeagueTableViewComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('rowActionBtn') rowActionBtn: TemplateRef<any>;
  @ViewChild(DataTableDirective, { static: false })
  dtElement: any = DataTableDirective;
  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
  dtOptions: any = {};

  @Input() seasonId: any;
  @Input() clubId: any;
  @Input() tournamentId: any;
  @Input() dateRange: any;
  @Input() matchStatus: string;
  @Input() seasons: any[];
  @Input() clubs: any[];
  @Input() tournaments: any[];

  public isTableLoading: boolean = false;
  public table_name = 'team-table';

  public params: EditorSidebarParams = {
    editor_id: this.table_name,
    title: {
      create: this._translateService.instant('Create New Tournament'),
      edit: 'Edit team',
      remove: 'Delete team'
    },
    url: `${environment.apiUrl}/teams/editor`,
    method: 'POST',
    action: 'create'
  };

  public fields: any[] = [
    {
      key: 'home_score',
      type: 'number',
      props: {
        label: this._translateService.instant('Home team score'),
        placeholder: this._translateService.instant('Enter score of team'),
        required: true,
      }
    },
    {
      key: 'away_score',
      type: 'number',
      props: {
        label: this._translateService.instant('Away team score'),
        placeholder: this._translateService.instant('Enter score of team'),
        required: true,
      }
    }
  ];

  constructor(
    public _http: HttpClient,
    public _commonsService: CommonsService,
    public _translateService: TranslateService,
    public _coreSidebarService: CoreSidebarService,
    private _exportService: ExportService
  ) {}

  ngOnInit(): void {
    this.buildDataTable();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.dtTrigger.next(this.dtOptions);
    }, 500);
  }

  buildDataTable(): void {
    this.dtOptions = {
      dom: this._commonsService.dataTableDefaults.dom,
      select: 'single',
      rowId: 'id',
      processing: true,
      language: {
        ...this._commonsService.dataTableDefaults.lang,
        processing: ``,
      },
      ajax: (dataTablesParameters: any, callback) => {
        // Build query parameters for all filters
        let params = new URLSearchParams();

        if (this.clubId != undefined && this.clubId !== null) {
          params.append('club_id', this.clubId);
        }

        if (this.tournamentId != undefined && this.tournamentId !== null) {
          params.append('tournament_id', this.tournamentId);
        }

        if (this.dateRange?.start_date) {
          params.append('start_date', this.dateRange.start_date);
        }

        if (this.dateRange?.end_date) {
          params.append('end_date', this.dateRange.end_date);
        }

        if (this.matchStatus && this.matchStatus !== 'all') {
          params.append('match_status', this.matchStatus);
        }

        const queryString = params.toString();
        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${
          queryString ? '?' + queryString : ''
        }`;

        this.isTableLoading = true;
        this._http.post<any>(url, dataTablesParameters).subscribe(
          (resp: any) => {
            this.isTableLoading = false;
            callback({
              recordsTotal: resp.recordsTotal,
              recordsFiltered: resp.recordsFiltered,
              data: resp.data,
            });
          },
          (error) => {
            this.isTableLoading = false;
            console.error('Error loading table data:', error);
            callback({
              recordsTotal: 0,
              recordsFiltered: 0,
              data: [],
            });
          }
        );
      },
      responsive: false,
      scrollX: true,
      columnDefs: [{ responsivePriority: 1, targets: -1 }],
      columns: [
        {
          title: this._translateService.instant('No.'),
          data: null,
          className: 'font-weight-bolder',
          type: 'any-number',
          render: function (data, type, row, metadata) {
            return metadata.row + 1
          }
        },
        {
          title: this._translateService.instant('Tournament'),
          data: 'name',
          className: 'font-weight-bolder',
          type: 'any-number',
          render: {
            display: (data, type, row) => {
              return data ?? 'TBD';
            },
            filter: (data, type, row) => {
              return data;
            }
          }
        },
        {
          title: this._translateService.instant('Round'),
          data: 'round_name',
          render: function (data) {
            const displayData = data || 'TBD';
            return `<div class="text-center" style="width:max-content;">${displayData}</div>`;
          },
        },
        {
          title: this._translateService.instant('Date'),
          data: 'start_time',
          className: 'text-center',
          render: function (data, type, row) {
            const displayDate =
              !data || data === 'TBD'
                ? 'TBD'
                : moment(data).format('YYYY-MM-DD');
            return `<div class="text-center" style="min-width: max-content;">${displayDate}</div>`;
          },
        },
        {
          title: this._translateService.instant('Start time'),
          data: 'start_time',
          className: 'text-center',
          render: function(data, type, row) {
            if (!data || data == 'TBD') {
              return 'TBD';
            }
            return moment(data).format('HH:mm');
          }
        },
        {
          title: this._translateService.instant('End time'),
          data: 'end_time',
          className: 'text-center',
          render: function(data, type, row) {
            if (!data || data == 'TBD') {
              return 'TBD';
            }
            return moment(data).format('HH:mm');
          }
        },
        {
          title: this._translateService.instant('Location'),
          data: 'location',
          render: function(data, type, row) {
            if (!data || data == 'TBD') {
              return 'TBD';
            }
            return data;
          }
        },
        {
          title: this._translateService.instant('Home Team'),
          data: 'home_team_name',
          className: 'text-center'
        },
        {
          title: 'VS',
          data: null,
          className: 'text-center',
          render: function(data, type, row) {
            return 'vs';
          }
        },
        {
          title: this._translateService.instant('Away Team'),
          data: 'away_team_name',
          className: 'text-center'
        },
        {
          title: this._translateService.instant('Home score'),
          data: 'home_score',
          className: 'text-center'
        },
        {
          data: null,
          className: 'text-center',
          render: function(data, type, row) {
            return '-';
          }
        },
        {
          title: this._translateService.instant('Away score'),
          data: 'away_score',
          className: 'text-center'
        }
      ],
      buttons: {
        dom: this._commonsService.dataTableDefaults.buttons.dom,
        buttons: [
          {
            text: `<i class="fa-solid fa-file-csv mr-1"></i> ${this._translateService.instant(
              'Export CSV'
            )}`,
            extend: 'csv',
            action: async (e: any, dt: any, button: any, config: any) => {
              const data = dt.buttons.exportData();
              
              // Generate filename with current filter information
              let filename = 'Matches_Report';
              
              if (this.seasonId) {
                const season = this.seasons?.find(s => s.id == this.seasonId);
                if (season) {
                  filename += `_${season.name.replace(/\s+/g, '_')}`;
                }
              }
              
              if (this.tournamentId) {
                const tournament = this.tournaments?.find(t => t.id == this.tournamentId);
                if (tournament) {
                  filename += `_${tournament.name.replace(/\s+/g, '_')}`;
                }
              }
              
              if (this.clubId) {
                const club = this.clubs?.find(c => c.id == this.clubId);
                if (club) {
                  filename += `_${club.code.replace(/\s+/g, '_')}`;
                }
              }
              
              if (this.matchStatus && this.matchStatus !== 'all') {
                filename += `_${this.matchStatus}`;
              }
              
              if (this.dateRange?.start_date && this.dateRange?.end_date) {
                filename += `_${this.dateRange.start_date}_to_${this.dateRange.end_date}`;
              } else if (this.dateRange?.start_date) {
                filename += `_from_${this.dateRange.start_date}`;
              }
              
              filename += '.csv';
              
              await this._exportService.exportCsv(data, filename);
            }
          }
        ]
      }
    };
  }

  refreshDataTable() {
    if (this.dtElement?.dtInstance) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.ajax.reload();
      });
    }
  }

  ngOnDestroy(): void {
    this.dtTrigger.unsubscribe();
  }
}
