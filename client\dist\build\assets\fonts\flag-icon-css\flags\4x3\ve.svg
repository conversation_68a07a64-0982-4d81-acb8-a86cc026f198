<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="640" height="480" viewBox="0 0 128 96">
  <defs>
  <g id="f" transform="translate(0 -36)">
    <g id="e">
    <g id="d">
      <path d="M0-5l-1.545 4.755 2.853.927z" id="c" fill="#fff"/>
      <use xlink:href="#c" transform="scale(-1 1)" width="180" height="120"/>
    </g>
    <use xlink:href="#d" transform="rotate(72)" width="180" height="120"/>
    </g>
    <use xlink:href="#d" transform="rotate(-72)" width="180" height="120"/>
    <use xlink:href="#e" transform="matrix(-.809 .588 -.588 -.809 0 0)" width="180" height="120"/>
  </g>
  </defs>
  <path d="M0 0h128v96h-128z" fill="#cf142b"/>
  <path d="M0 0h128v64h-128z" fill="#00247d"/>
  <path d="M0 0h128v32h-128z" fill="#fc0"/>
  <g transform="matrix(.8 0 0 .8 64 67.2)">
  <g id="b">
    <g id="a">
    <use xlink:href="#f" transform="rotate(10)" width="180" height="120"/>
    <use xlink:href="#f" transform="rotate(30)" width="180" height="120"/>
    </g>
    <use xlink:href="#a" transform="rotate(40)" width="180" height="120"/>
  </g>
  <use xlink:href="#b" transform="rotate(-80)" width="180" height="120"/>
  </g>
</svg>
