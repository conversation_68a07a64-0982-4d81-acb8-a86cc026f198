{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { AppConfig } from 'app/app-config';\n// sweet alert\nimport Swal from 'sweetalert2';\nimport { ModalAddGroupTeamComponent } from './modal-add-group-team/modal-add-group-team.component';\nimport { FormGroup } from '@angular/forms';\nimport { CorePipesModule } from '@core/pipes/pipes.module';\nimport { SortByNamePipe } from '@core/pipes/sort-by-name.pipe';\nimport { ModalAddNumberTeamComponent } from './modal-add-number-team/modal-add-number-team.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"app/services/tournament.service\";\nimport * as i4 from \"app/services/team.service\";\nimport * as i5 from \"app/services/stage.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"ngx-toastr\";\nimport * as i8 from \"app/services/commons.service\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"@angular/common/http\";\nimport * as i11 from \"@core/pipes/sort-by-name.pipe\";\nfunction StageTeamsComponent_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", \"Added \" + ((tmp_0_0 = ctx_r6.group_stages.length) !== null && tmp_0_0 !== undefined ? tmp_0_0 : 0) + \"/\" + ctx_r6.tournament.no_of_groups + \" groups\", \" \");\n  }\n}\nfunction StageTeamsComponent_div_6_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 17);\n    i0.ɵɵelement(2, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngbTooltip\", \"Please remove match before add team\");\n  }\n}\nfunction StageTeamsComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, StageTeamsComponent_div_6_div_2_Template, 2, 1, \"div\", 12);\n    i0.ɵɵelementStart(3, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_6_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.addGroupTeam());\n    });\n    i0.ɵɵelement(4, \"i\", 14);\n    i0.ɵɵtext(5);\n    i0.ɵɵtemplate(6, StageTeamsComponent_div_6_ng_container_6_Template, 3, 1, \"ng-container\", 15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.stage_type == ctx_r0.AppConfig.TOURNAMENT_TYPES.groups);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.stage_type == ctx_r0.AppConfig.TOURNAMENT_TYPES.league ? ctx_r0._translateService.instant(\"Add Team\") : ctx_r0.stage_type == ctx_r0.AppConfig.TOURNAMENT_TYPES.groups ? ctx_r0._translateService.instant(\"Add Team & Group\") : ctx_r0._translateService.instant(\"Add Teams\"), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.allowEditTeam == false);\n  }\n}\nfunction StageTeamsComponent_div_7_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 17);\n    i0.ɵɵelement(2, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngbTooltip\", \"Please remove match before add team\");\n  }\n}\nfunction StageTeamsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 11)(2, \"div\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.openModalAddNumbTeam());\n    });\n    i0.ɵɵelement(5, \"i\", 21);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_7_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.addGroupTeam());\n    });\n    i0.ɵɵelement(9, \"i\", 14);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵtemplate(12, StageTeamsComponent_div_7_ng_container_12_Template, 3, 1, \"ng-container\", 15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵproperty(\"disabled\", ctx_r1.allowEditTeam == false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", \"Added \" + ((tmp_1_0 = ctx_r1.group_stages[0] == null ? null : ctx_r1.group_stages[0].length) !== null && tmp_1_0 !== undefined ? tmp_1_0 : 0) + \"/\" + ctx_r1.stage.team_number + \" teams\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 6, \"Set number of teams\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.stage.team_number == 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 8, \"Add Team\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowEditTeam == false);\n  }\n}\nfunction StageTeamsComponent_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Please specify the number of teams before adding teams.\"), \" \");\n  }\n}\nfunction StageTeamsComponent_div_11_span_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 31);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_11_span_1_i_3_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const group_stage_r14 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.removeGroup(group_stage_r14));\n    });\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", i0.ɵɵpipeBind1(1, 1, \"Remove Group\"));\n  }\n}\nfunction StageTeamsComponent_div_11_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtemplate(3, StageTeamsComponent_div_11_span_1_i_3_Template, 2, 3, \"i\", 29);\n    i0.ɵɵelementStart(4, \"i\", 30);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_11_span_1_Template_i_click_4_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const group_stage_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r23 = i0.ɵɵnextContext();\n      const _r4 = i0.ɵɵreference(13);\n      return i0.ɵɵresetView(ctx_r23.openModalEdit(_r4, group_stage_r14[0]));\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_stage_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(2, 4, \"group\"), \" \", group_stage_r14[0].group, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.allowEditTeam == true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", i0.ɵɵpipeBind1(5, 6, \"Edit group\"));\n  }\n}\nfunction StageTeamsComponent_div_11_span_2_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 33);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_11_span_2_i_1_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const group_stage_r14 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.removeAllTeam(group_stage_r14));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r27.allowEditTeam == false);\n  }\n}\nfunction StageTeamsComponent_div_11_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtemplate(1, StageTeamsComponent_div_11_span_2_i_1_Template, 1, 1, \"i\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.allowEditTeam == true);\n  }\n}\nfunction StageTeamsComponent_div_11_div_4_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 46);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_11_div_4_a_19_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const stage_r31 = i0.ɵɵnextContext().$implicit;\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.removeTeam(stage_r31, \"remove\"));\n    });\n    i0.ɵɵelementStart(1, \"i\", 49);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_11_div_4_a_19_Template_i_click_1_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const stage_r31 = i0.ɵɵnextContext().$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.removeTeam(stage_r31, \"remove\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleMap(ctx_r33.btnStyle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 3, \"Remove Team\"));\n  }\n}\nfunction StageTeamsComponent_div_11_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36)(3, \"div\", 37);\n    i0.ɵɵelement(4, \"img\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 39);\n    i0.ɵɵelement(6, \"h4\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 41)(8, \"div\", 42)(9, \"button\", 43);\n    i0.ɵɵelement(10, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 45);\n    i0.ɵɵelementContainerStart(12);\n    i0.ɵɵelementStart(13, \"a\", 46);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_11_div_4_Template_a_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const stage_r31 = restoredCtx.$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.changeTeam(stage_r31, \"edit\"));\n    });\n    i0.ɵɵelement(14, \"i\", 47);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18);\n    i0.ɵɵtemplate(19, StageTeamsComponent_div_11_div_4_a_19_Template, 5, 5, \"a\", 48);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const stage_r31 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"src\", stage_r31.team.club.logo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", stage_r31.team.name, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(ctx_r18.btnStyle);\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleMap(ctx_r18.btnStyle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 8, \"Change Team\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.allowEditTeam == true);\n  }\n}\nfunction StageTeamsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, StageTeamsComponent_div_11_span_1_Template, 6, 8, \"span\", 25);\n    i0.ɵɵtemplate(2, StageTeamsComponent_div_11_span_2_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementStart(3, \"div\", 26);\n    i0.ɵɵtemplate(4, StageTeamsComponent_div_11_div_4_Template, 20, 10, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_stage_r14 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.stage_type == ctx_r3.AppConfig.TOURNAMENT_TYPES.groups);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.stage_type != ctx_r3.AppConfig.TOURNAMENT_TYPES.groups);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.sortBy(group_stage_r14));\n  }\n}\nfunction StageTeamsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 50);\n    i0.ɵɵlistener(\"ngSubmit\", function StageTeamsComponent_ng_template_12_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.onEdit(ctx_r42.model));\n    });\n    i0.ɵɵelementStart(1, \"div\", 51)(2, \"h4\", 52);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_ng_template_12_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r43);\n      const modal_r41 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(modal_r41.dismiss(\"Cross click\"));\n    });\n    i0.ɵɵelementStart(6, \"span\", 54);\n    i0.ɵɵtext(7, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"div\", 56);\n    i0.ɵɵelement(10, \"img\", 57);\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"formly-form\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 59)(16, \"button\", 60);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r5.form);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 7, \"Edit group\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Enter group name\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"form\", ctx_r5.form)(\"fields\", ctx_r5.fields)(\"model\", ctx_r5.model);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 11, \"Save\"));\n  }\n}\nexport class StageTeamsComponent {\n  constructor(_trans, _route, _router, _tournamentService, _teamService, _stageService, _loadingService, _toastrService, _translateService, _commonsService, _modalService, _http, _sortByNamePipe) {\n    this._trans = _trans;\n    this._route = _route;\n    this._router = _router;\n    this._tournamentService = _tournamentService;\n    this._teamService = _teamService;\n    this._stageService = _stageService;\n    this._loadingService = _loadingService;\n    this._toastrService = _toastrService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._modalService = _modalService;\n    this._http = _http;\n    this._sortByNamePipe = _sortByNamePipe;\n    this.group_stages = [];\n    this.allowEditTeam = true;\n    this.onDataChange = new EventEmitter();\n    this.stage_type = null;\n    this.team_by_group = [];\n    this.team_id = null;\n    this.groups = [];\n    this.AppConfig = AppConfig;\n    this.form = new FormGroup({});\n    this.model = {};\n    this.fields = [{\n      key: 'group_name',\n      type: 'input',\n      props: {\n        label: '',\n        placeholder: this._trans.instant('Group Name'),\n        required: true,\n        maxLength: 20\n      },\n      // validate message\n      validation: {\n        messages: {\n          required: this._trans.instant('Group Name is required')\n        }\n      }\n    }];\n    this.rowActions = [{\n      type: 'collection',\n      buttons: [{\n        label: 'Change team',\n        icon: 'fa-regular fa-pen-to-square'\n      }, {\n        label: 'Delete',\n        icon: 'fa-regular fa-trash'\n      }]\n    }];\n  }\n  ngOnInit() {\n    this.stage_type = this.stage.type;\n    this.getTeamsByGroup();\n    this.getTeamsInStage();\n    console.log('group_stages', this.group_stages);\n  }\n  getTeamByGroup(group_id) {\n    this._loadingService.show();\n    return this._teamService.getTeamNotInStage(this.stage.id, group_id).toPromise();\n  }\n  removeTeamFromStage(stage_team, action) {\n    let params = new FormData();\n    params.append('action', action);\n    params.append('data[' + stage_team.id + '][stage_id]', stage_team.stage_id);\n    params.append('data[' + stage_team.id + '][team_id]', stage_team.team_id);\n    params.append('data[' + stage_team.id + '][group]', stage_team.group);\n    return this._stageService.removeTeamFromStage(params).toPromise();\n  }\n  removeTeam(stage_team, action) {\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      html: `\n        <div class=\"text-center\">\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n\n        <p class=\"text-center\"> ` + this._translateService.instant('You will not be able to recover this team') + `      \n        </p>\n      </div>\n        `,\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('No'),\n      reverseButtons: true,\n      showLoaderOnConfirm: true,\n      preConfirm: () => {\n        return new Promise(resolve => {\n          this.removeTeamFromStage(stage_team, action).then(res => {\n            if (res) {\n              this.getTeamsInStage();\n              Swal.fire({\n                title: this._translateService.instant('Success'),\n                text: this._translateService.instant('Team removed successfully'),\n                icon: 'success',\n                confirmButtonText: this._translateService.instant('OK'),\n                confirmButtonColor: '#3085d6'\n              }).then(result => {\n                if (result.isConfirmed) {}\n              });\n            } else {\n              Swal.fire({\n                title: this._translateService.instant('Error'),\n                text: this._translateService.instant('Team not removed'),\n                icon: 'error',\n                confirmButtonText: this._translateService.instant('OK'),\n                confirmButtonColor: '#3085d6'\n              });\n            }\n          });\n        });\n      }\n    });\n  }\n  changeTeam(stage_team, action) {\n    this.getTeamByGroup(this.tournament.group_id).then(res => {\n      res = res.data;\n      if (res) {\n        // sort by name\n        res.sort((a, b) => {\n          if (a.name < b.name) {\n            return -1;\n          }\n          if (a.name > b.name) {\n            return 1;\n          }\n          return 0;\n        });\n        let options = '';\n        res.forEach(team => {\n          options += `<option value=\"${team.id}\">${team.name}</option>`;\n        });\n        // pop up to select team\n        Swal.fire({\n          title: this._translateService.instant('Select team to change'),\n          html: `<select class=\"form-control\" id=\"team_id\">\n        ${options}\n        </select>`,\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Change'),\n          cancelButtonText: this._translateService.instant('Cancel'),\n          reverseButtons: true,\n          showLoaderOnConfirm: true,\n          preConfirm: () => {\n            return new Promise(resolve => {\n              let team_id = document.getElementById('team_id').value;\n              let params = new FormData();\n              params.append('action', action);\n              params.append('data[' + stage_team.id + '][stage_id]', stage_team.stage_id);\n              params.append('data[' + stage_team.id + '][team_id]', team_id);\n              if (stage_team.group) {\n                params.append('data[' + stage_team.id + '][group]', stage_team.group);\n              }\n              this._stageService.changeTeamInStage(params).subscribe(res => {\n                this.getTeamsByGroup();\n                if (res) {\n                  Swal.fire({\n                    title: this._translateService.instant('Success'),\n                    html: `\n                      <div class=\"text-center\">\n                      <img src=\"assets/images/ai/done.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n              \n                      <p class=\"text-center\"> ` + this._translateService.instant('Team changed successfully') + `      \n                      </p>\n                    </div>\n                      `,\n                    confirmButtonText: this._translateService.instant('OK'),\n                    confirmButtonColor: '#3085d6'\n                  }).then(result => {\n                    this.getTeamsInStage();\n                  });\n                } else {\n                  Swal.fire({\n                    title: 'Error',\n                    text: 'Team not changed',\n                    icon: 'error',\n                    confirmButtonText: this._translateService.instant('OK'),\n                    confirmButtonColor: '#3085d6'\n                  });\n                }\n              }, error => {\n                console.log('error', error);\n                Swal.fire({\n                  title: 'Error',\n                  text: 'No team available for change',\n                  icon: 'error',\n                  confirmButtonText: this._translateService.instant('OK'),\n                  confirmButtonColor: '#3085d6'\n                });\n              });\n            });\n          }\n        });\n      }\n    });\n  }\n  addGroupTeam() {\n    if (this.allowEditTeam == false) {\n      Swal.fire({\n        title: this._translateService.instant('Error'),\n        text: this._translateService.instant('You must remove all schedule before add team'),\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK'),\n        confirmButtonColor: '#3085d6'\n      });\n      return;\n    }\n    this.openModalAddTeam();\n  }\n  getTeamsByGroup() {\n    if (!this.tournament.group_id) return;\n    this.getTeamByGroup(this.tournament.group_id).then(res => {\n      res = res.data;\n      if (res) {\n        this.team_by_group = res;\n      }\n    });\n  }\n  openModalAddTeam() {\n    if (this.team_by_group.length == 0) {\n      Swal.fire({\n        title: this._translateService.instant('No team to add'),\n        icon: 'info',\n        confirmButtonText: this._translateService.instant('OK'),\n        confirmButtonColor: '#3085d6'\n      });\n      return;\n    }\n    const modalRef = this._modalService.open(ModalAddGroupTeamComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false,\n      centered: true,\n      windowClass: 'modal-add-group-team'\n    });\n    if (this.team_by_group) {\n      const sortTeam = this._sortByNamePipe.transform(this.team_by_group, 'name');\n      modalRef.componentInstance.teams = sortTeam;\n      modalRef.componentInstance.stage_type = this.stage_type;\n      // get current group\n      let current_group = [];\n      this.group_stages.forEach(group => {\n        current_group.push(group[0].group);\n      });\n      modalRef.componentInstance.current_group = current_group;\n      modalRef.result.then(result => {\n        if (result) {\n          let group_name = this.stage_type == 'Groups' ? result.group_name : null;\n          let selected_team = result.selected_team;\n          selected_team = selected_team.join(',');\n          let params = new FormData();\n          params.append('stage_id', this.stage.id);\n          if (this.stage_type == 'Groups') {\n            params.append('group', group_name);\n          }\n          params.append('teams', selected_team);\n          this._stageService.createTeamMultiple(params).subscribe(res => {\n            if (res) {\n              this.getTeamsByGroup();\n              Swal.fire({\n                title: this._translateService.instant('Success'),\n                html: `\n                  <div class=\"text-center\">\n                    <img src=\"assets/images/ai/done.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n                    <p class=\"text-center\"> ` + this._translateService.instant('Teams added successfully') + `      \n                    </p>\n                  </div>`,\n                confirmButtonText: this._translateService.instant('OK'),\n                confirmButtonColor: '#3085d6'\n              }).then(result => {\n                this.getTeamsInStage();\n              });\n            }\n          }, error => {\n            Swal.fire({\n              title: this._translateService.instant('Failed to Add Team  '),\n              text: this._translateService.instant(error.message),\n              icon: 'error',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            });\n          });\n        }\n      });\n    }\n  }\n  openModalAddNumbTeam() {\n    const modalRef = this._modalService.open(ModalAddNumberTeamComponent, {\n      size: 'md',\n      backdrop: 'static',\n      keyboard: false,\n      centered: true,\n      windowClass: 'modal-add-group-team'\n    });\n    if (this.stage) {\n      modalRef.componentInstance.team_number = this.stage.team_number;\n    }\n    modalRef.result.then(result => {\n      if (result) {\n        this._stageService.updateTeamNumber(this.stage.id, result).subscribe(res => {\n          if (res) {\n            this.stage.team_number = result;\n            Swal.fire({\n              title: this._translateService.instant('Teams Set Successfully!!'),\n              text: this._translateService.instant(`You’ve set ${result} teams for the tournament. Now, let’s add teams!`),\n              icon: 'success',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            });\n          }\n        }), error => {\n          Swal.fire({\n            title: this._translateService.instant('Error'),\n            text: this._translateService.instant(error.message),\n            icon: 'error',\n            confirmButtonText: this._translateService.instant('OK'),\n            confirmButtonColor: '#3085d6'\n          });\n        };\n      }\n    });\n  }\n  removeGroup(group_stage) {\n    console.log('removeGroup');\n    let teams = [];\n    group_stage.forEach(group => {\n      teams.push(group.team_id);\n    });\n    // implode teams\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      html: ` \n      <div class=\"text-center\">\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n        <p class=\"text-center\"> ` + this._translateService.instant('You will not be able to recover this group') + `\n        </p>\n      </div>\n      \n      `,\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('No'),\n      reverseButtons: true\n    }).then(result => {\n      let stage_id = group_stage[0].stage_id;\n      let group = group_stage[0].group;\n      teams = teams.join(',');\n      let params = new FormData();\n      // stage_id: 139\n      // group: A\n      // teams: 7, 8, 9\n      params.append('stage_id', stage_id);\n      params.append('group', group);\n      params.append('teams', teams);\n      if (result.value) {\n        this._stageService.removeTeamMultiple(params).subscribe(res => {\n          if (res) {\n            this._loadingService.show();\n            this.getTeamsInStage();\n            this.getTeamsByGroup();\n            Swal.fire({\n              title: this._translateService.instant('Success'),\n              text: this._translateService.instant('Group removed successfully'),\n              icon: 'success',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            });\n          } else {\n            Swal.fire({\n              title: 'Error',\n              text: this._translateService.instant('Group not removed'),\n              icon: 'error',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            });\n          }\n        });\n      }\n    });\n  }\n  removeAllTeam(group_stage) {\n    let teams = [];\n    group_stage.forEach(group => {\n      teams.push(group.team_id);\n    });\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      html: `\n      <div class=\"text-center\">\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n\n        <p class=\"text-center\"> ` + this._translateService.instant('You are about to remove all teams in this group') + `      \n        </p>\n      </div>`,\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('No'),\n      reverseButtons: true\n    }).then(result => {\n      let stage_id = group_stage[0].stage_id;\n      teams = teams.join(',');\n      let params = new FormData();\n      // stage_id: 139\n      // teams: 7, 8, 9\n      params.append('stage_id', stage_id);\n      params.append('teams', teams);\n      if (result.value) {\n        this._stageService.removeTeamMultiple(params).subscribe(res => {\n          if (res) {\n            Swal.fire({\n              title: this._translateService.instant('Success'),\n              text: this._translateService.instant('Team removed successfully'),\n              icon: 'success',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            }).then(result => {\n              // if (result.isConfirmed) {\n              this._loadingService.show();\n              setTimeout(() => {\n                this.getTeamsByGroup();\n                this.getTeamsInStage();\n              }, 400);\n              // }\n            });\n          } else {\n            Swal.fire({\n              title: 'Error',\n              text: 'Teams not removed',\n              icon: 'error',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            });\n          }\n        });\n      }\n    });\n  }\n  getTeamsInStage() {\n    this._stageService.getTeamsInStage(this.stage.id).subscribe(res => {\n      this.group_stages = res.data;\n      this.onDataChange.emit(this.group_stages);\n    });\n    this._loadingService.dismiss();\n  }\n  checkTeamAlreadyInStage(teams) {\n    return new Promise((resolve, reject) => {\n      // check if teams are already in stage\n      this._teamService.getTeamListInStage(this.stage.id).subscribe(res => {\n        let teams_in_stage = res.data;\n        teams_in_stage.sort((a, b) => a.id - b.id);\n        teams_in_stage = teams_in_stage.map(team => team.id);\n        let list_teams_in_stage = teams_in_stage.join(',');\n        let list_teams = teams.join(',');\n        if (list_teams == list_teams_in_stage) {\n          console.log('teams already imported');\n          resolve(false);\n        } else {\n          // find the teams that are not in stage\n          let teams_not_in_stage = teams.filter(team => !teams_in_stage.includes(team));\n          resolve(teams_not_in_stage);\n        }\n      });\n    });\n  }\n  sortBy(stage_teams) {\n    stage_teams.sort((a, b) => a.team.name.localeCompare(b.team.name));\n    return stage_teams;\n  }\n  importTeams() {\n    this._stageService.getDataByTournament(this.tournament.id).subscribe(res => {\n      let stages = res.data;\n      stages = stages.filter(stage => stage.type == 'Groups');\n      Swal.fire({\n        title: this._translateService.instant('Do you want to import teams from') + ' ' + this.tournament.name + ' ' + stages[0].name + '? ',\n        showCancelButton: true,\n        cancelButtonText: this._translateService.instant('No'),\n        confirmButtonText: this._translateService.instant('Yes')\n      }).then(result => {\n        if (result.isConfirmed) {\n          let stage_id = stages[0].id;\n          // get teams from stages\n          this._teamService.getTeamListInStage(stage_id).subscribe(res => {\n            let teams = res.data;\n            teams.sort((a, b) => a.id - b.id);\n            teams = teams.map(team => team.id);\n            // check if teams are already in stage\n            this.checkTeamAlreadyInStage(teams).then(res => {\n              if (res) {\n                teams = res;\n                // sort teams by name\n                let params = new FormData();\n                teams = teams.join(',');\n                params.append('stage_id', this.stage.id);\n                params.append('teams', teams);\n                this._stageService.createTeamMultiple(params).subscribe(res => {\n                  if (res) {\n                    Swal.fire({\n                      title: 'Success',\n                      text: this._translateService.instant('Teams imported successfully'),\n                      icon: 'success',\n                      confirmButtonText: this._translateService.instant('OK'),\n                      confirmButtonColor: '#3085d6'\n                    }).then(result => {\n                      this.getTeamsByGroup();\n                      this._loadingService.show();\n                      setTimeout(() => {\n                        this._stageService.getTeamsInStage(this.stage.id).subscribe(res => {\n                          this.group_stages = res.data;\n                          this._loadingService.dismiss();\n                        });\n                      }, 400);\n                    });\n                  } else {\n                    Swal.fire({\n                      title: 'Error',\n                      text: this._translateService.instant('Teams not imported'),\n                      icon: 'error',\n                      confirmButtonText: this._translateService.instant('OK'),\n                      confirmButtonColor: '#3085d6'\n                    });\n                  }\n                });\n              } else {\n                Swal.fire({\n                  title: 'Error',\n                  text: this._translateService.instant('No teams available to add'),\n                  icon: 'error',\n                  confirmButtonText: this._translateService.instant('OK'),\n                  confirmButtonColor: '#3085d6'\n                });\n              }\n            });\n          });\n        }\n      });\n    });\n  }\n  openModalEdit(content, groupStage) {\n    this.groupStage = groupStage;\n    if (this.groupStage.group) {\n      this.model = {\n        group_name: this.groupStage.group\n      };\n    }\n    this._modalService.open(content, {\n      ariaLabelledBy: 'modal-basic-title'\n    }).result.then(result => {\n      this.groupStage = null;\n    }, reason => {\n      this.groupStage = null;\n    });\n  }\n  onEdit(model) {\n    this.groupStage;\n    if (this.form.invalid || !this.groupStage) return;\n    let params = new FormData();\n    params.append('stage_id', this.groupStage.stage_id);\n    params.append('old_group', this.groupStage.group.toString());\n    params.append('group', model.group_name);\n    params.append('all_teams_in_groups', 'true');\n    this._loadingService.show();\n    this.editGroup(params).then(res => {\n      this.getTeamsInStage();\n      this._modalService.dismissAll();\n    }, error => {\n      if (error.errors) {\n        this.form.controls[\"group_name\"].setErrors({\n          serverError: error.message\n        });\n      }\n    });\n  }\n  editGroup(params) {\n    return this._stageService.editGroup(params).toPromise().then(res => {\n      if (res) {\n        return res;\n      }\n    });\n  }\n  static #_ = this.ɵfac = function StageTeamsComponent_Factory(t) {\n    return new (t || StageTeamsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TournamentService), i0.ɵɵdirectiveInject(i4.TeamService), i0.ɵɵdirectiveInject(i5.StageService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.ToastrService), i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i8.CommonsService), i0.ɵɵdirectiveInject(i9.NgbModal), i0.ɵɵdirectiveInject(i10.HttpClient), i0.ɵɵdirectiveInject(i11.SortByNamePipe));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StageTeamsComponent,\n    selectors: [[\"stage-teams\"]],\n    inputs: {\n      stage: \"stage\",\n      tournament: \"tournament\",\n      group_stages: \"group_stages\",\n      allowEditTeam: \"allowEditTeam\"\n    },\n    outputs: {\n      onDataChange: \"onDataChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([CorePipesModule, SortByNamePipe])],\n    decls: 14,\n    vars: 7,\n    consts: [[1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [\"class\", \"float-right\", 4, \"ngIf\"], [\"class\", \"float-right\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"pl-2 mb-0\", \"style\", \" font-size: 1rem; color: #6c757d; font-weight: 500; opacity: 0.7;\", 4, \"ngIf\"], [1, \"card-body\"], [1, \"row\"], [\"style\", \"width: 100%; padding: 10px\", \"class\", \"col-12 bg-light card p-1\", 4, \"ngFor\", \"ngForOf\"], [\"content\", \"\"], [1, \"float-right\"], [1, \"d-flex\", \"align-items-center\"], [\"class\", \"mr-2\", \"style\", \"font-size: 1rem; color: #6c757d; font-weight: 500; opacity: 0.7;\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa\", \"fa-plus\"], [4, \"ngIf\"], [1, \"mr-2\", 2, \"font-size\", \"1rem\", \"color\", \"#6c757d\", \"font-weight\", \"500\", \"opacity\", \"0.7\"], [1, \"ml-1\", 3, \"ngbTooltip\"], [1, \"fa\", \"fa-info-circle\"], [1, \"float-right\", 3, \"disabled\"], [1, \"btn\", \"btn-primary\", \"mr-1\", 3, \"click\"], [1, \"bi\", \"bi-people\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"pl-2\", \"mb-0\", 2, \"font-size\", \"1rem\", \"color\", \"#6c757d\", \"font-weight\", \"500\", \"opacity\", \"0.7\"], [1, \"col-12\", \"bg-light\", \"card\", \"p-1\", 2, \"width\", \"100%\", \"padding\", \"10px\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [1, \"row\", \"d-flex\", \"justify-content-left\"], [\"class\", \"col-lg-3 col-md-6 col-sm-12 mt-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-primary\"], [\"style\", \"z-index: 1; position: relative\", \"class\", \"fa fa-trash float-right text-danger p-1\", \"placement\", \"left\", 3, \"ngbTooltip\", \"click\", 4, \"ngIf\"], [\"placement\", \"left\", 1, \"fa\", \"fa-edit\", \"float-right\", \"text-warning\", \"p-1\", 2, \"z-index\", \"1\", \"position\", \"relative\", 3, \"ngbTooltip\", \"click\"], [\"placement\", \"left\", 1, \"fa\", \"fa-trash\", \"float-right\", \"text-danger\", \"p-1\", 2, \"z-index\", \"1\", \"position\", \"relative\", 3, \"ngbTooltip\", \"click\"], [\"style\", \"z-index: 1; position: relative\", \"class\", \"fa fa-trash fa-lg float-right text-danger\", \"placement\", \"left\", \"ngbTooltip\", \"Remove all teams\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"placement\", \"left\", \"ngbTooltip\", \"Remove all teams\", 1, \"fa\", \"fa-trash\", \"fa-lg\", \"float-right\", \"text-danger\", 2, \"z-index\", \"1\", \"position\", \"relative\", 3, \"disabled\", \"click\"], [1, \"col-lg-3\", \"col-md-6\", \"col-sm-12\", \"mt-1\"], [1, \"rounded\", \"bg-white\"], [1, \"row\", \"p-1\", \"align-items-center\"], [1, \"col-auto\"], [\"alt\", \"logo\", \"width\", \"60px\", \"height\", \"60px\", 1, \"avatar\", \"avatar-sm\", \"bg-white\", \"rounded-0\", 3, \"src\"], [1, \"col\"], [1, \"text-nowrap\", 3, \"innerHTML\"], [1, \"col\", \"text-right\"], [\"ngbDropdown\", \"\", \"container\", \"body\", 1, \"col-auto\", \"p-0\", \"m-0\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", \"data-toggle\", \"dropdown\", 1, \"btn\", \"hide-arrow\", \"p-0\", \"text-secondary\"], [1, \"fa-regular\", \"fa-ellipsis-vertical\"], [\"ngbDropdownMenu\", \"\"], [\"ngbDropdownItem\", \"\", 3, \"click\"], [\"placement\", \"left\", \"ngbTooltip\", \"Change another team\", 1, \"fa-light\", \"fa-arrows-repeat\", \"fa-lg\", \"mr-1\"], [\"ngbDropdownItem\", \"\", 3, \"style\", \"click\", 4, \"ngIf\"], [\"id\", \"trash\", \"placement\", \"left\", \"ngbTooltip\", \"Remove this team\", 1, \"fal\", \"fa-trash\", \"fa-lg\", \"mr-1\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"modal-header\"], [\"id\", \"modal-basic-title\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"text-center\"], [\"src\", \"assets/images/ai/Frame.svg\", \"alt\", \"Frame\", \"width\", \"200px\", \"height\", \"149px\"], [3, \"form\", \"fields\", \"model\"], [1, \"modal-footer\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"]],\n    template: function StageTeamsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(5, \"div\");\n        i0.ɵɵtemplate(6, StageTeamsComponent_div_6_Template, 7, 3, \"div\", 3);\n        i0.ɵɵtemplate(7, StageTeamsComponent_div_7_Template, 13, 10, \"div\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, StageTeamsComponent_p_8_Template, 3, 3, \"p\", 5);\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7);\n        i0.ɵɵtemplate(11, StageTeamsComponent_div_11_Template, 5, 3, \"div\", 8);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(12, StageTeamsComponent_ng_template_12_Template, 19, 13, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 5, \"Stage Teams\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage_type == ctx.AppConfig.TOURNAMENT_TYPES.league || ctx.stage_type == ctx.AppConfig.TOURNAMENT_TYPES.groups);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage_type == \"Knockout\" && ctx.tournament.type_knockout == ctx.AppConfig.KNOCKOUT_TYPES.type4);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage_type == \"Knockout\" && ctx.tournament.type_knockout == ctx.AppConfig.KNOCKOUT_TYPES.type4);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.group_stages);\n      }\n    },\n    styles: [\"i[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\ni[_ngcontent-%COMP%]:hover {\\n  color: #6f6b7d;\\n}\\ni[_ngcontent-%COMP%]:active {\\n  transform: scale(1.2);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvc3RhZ2VzL3N0YWdlLXRlYW1zL3N0YWdlLXRlYW1zLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsZUFBQTtBQUNGO0FBQ0U7RUFDRSxjQUFBO0FBQ0o7QUFDRTtFQUVFLHFCQUFBO0FBQUoiLCJzb3VyY2VzQ29udGVudCI6WyJpIHtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgY29sb3I6ICM2ZjZiN2Q7XHJcbiAgfVxyXG4gICY6YWN0aXZlIHtcclxuICAgIC8vIHNjYWxlIDEuMlxyXG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjIpO1xyXG5cclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAAoBA,YAAY,QAA+B,eAAe;AAc9E,SAASC,SAAS,QAAQ,gBAAgB;AAE1C;AACA,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,0BAA0B,QAAQ,uDAAuD;AAClG,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,2BAA2B,QAAQ,yDAAyD;;;;;;;;;;;;;;;IChB7FC,EAAA,CAAAC,cAAA,cAAmJ;IAC7ID,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,mBAAAC,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,MAAA,cAAAH,OAAA,KAAAI,SAAA,GAAAJ,OAAA,cAAAC,MAAA,CAAAI,UAAA,CAAAC,YAAA,kBACJ;;;;;IAWEZ,EAAA,CAAAa,uBAAA,GAA6C;IAC3Cb,EAAA,CAAAC,cAAA,eAAwE;IACtED,EAAA,CAAAc,SAAA,YAAiC;IACnCd,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAe,qBAAA,EAAe;;;IAHMf,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAgB,UAAA,qDAAoD;;;;;;IAlBjFhB,EAAA,CAAAC,cAAA,cAE2D;IAEvDD,EAAA,CAAAiB,UAAA,IAAAC,wCAAA,kBAEQ;IACNlB,EAAA,CAAAC,cAAA,iBAA4D;IAA1BD,EAAA,CAAAmB,UAAA,mBAAAC,2DAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACxD1B,EAAA,CAAAc,SAAA,YAA0B;IAE1Bd,EAAA,CAAAE,MAAA,GAOA;IAAAF,EAAA,CAAAiB,UAAA,IAAAU,iDAAA,2BAIe;IACjB3B,EAAA,CAAAG,YAAA,EAAS;;;;IAlBQH,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAgB,UAAA,SAAAY,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAnC,SAAA,CAAAqC,gBAAA,CAAAC,MAAA,CAAqD;IAMpE/B,EAAA,CAAAI,SAAA,GAOA;IAPAJ,EAAA,CAAAK,kBAAA,MAAAuB,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAnC,SAAA,CAAAqC,gBAAA,CAAAE,MAAA,GAAAJ,MAAA,CAAAK,iBAAA,CAAAC,OAAA,eAAAN,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAnC,SAAA,CAAAqC,gBAAA,CAAAC,MAAA,GAAAH,MAAA,CAAAK,iBAAA,CAAAC,OAAA,uBAAAN,MAAA,CAAAK,iBAAA,CAAAC,OAAA,mBAOA;IAAelC,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAgB,UAAA,SAAAY,MAAA,CAAAO,aAAA,UAA4B;;;;;IAsB/CnC,EAAA,CAAAa,uBAAA,GAA6C;IAC3Cb,EAAA,CAAAC,cAAA,eAAwE;IACtED,EAAA,CAAAc,SAAA,YAAiC;IACnCd,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAe,qBAAA,EAAe;;;IAHMf,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAgB,UAAA,qDAAoD;;;;;;IAb7EhB,EAAA,CAAAC,cAAA,cAA4J;IAGxJD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAAsE;IAAjCD,EAAA,CAAAmB,UAAA,mBAAAiB,2DAAA;MAAApC,EAAA,CAAAqB,aAAA,CAAAgB,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAa,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IACnEvC,EAAA,CAAAc,SAAA,YAA4B;IAC5Bd,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA6F;IAA7DD,EAAA,CAAAmB,UAAA,mBAAAqB,2DAAA;MAAAxC,EAAA,CAAAqB,aAAA,CAAAgB,IAAA;MAAA,MAAAI,OAAA,GAAAzC,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAgB,OAAA,CAAAf,YAAA,EAAc;IAAA,EAAC;IACtD1B,EAAA,CAAAc,SAAA,YAA0B;IAC1Bd,EAAA,CAAAE,MAAA,IACA;;IAAAF,EAAA,CAAAiB,UAAA,KAAAyB,kDAAA,2BAIe;IACjB1C,EAAA,CAAAG,YAAA,EAAS;;;;;IAjB6GH,EAAA,CAAAgB,UAAA,aAAA2B,MAAA,CAAAR,aAAA,UAAmC;IAGvJnC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,mBAAAuC,OAAA,GAAAD,MAAA,CAAAnC,YAAA,qBAAAmC,MAAA,CAAAnC,YAAA,IAAAC,MAAA,cAAAmC,OAAA,KAAAlC,SAAA,GAAAkC,OAAA,cAAAD,MAAA,CAAAE,KAAA,CAAAC,WAAA,iBACF;IAGE9C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAA+C,WAAA,mCACF;IACyD/C,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAgB,UAAA,aAAA2B,MAAA,CAAAE,KAAA,CAAAC,WAAA,MAAmC;IAE1F9C,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAA+C,WAAA,yBACA;IAAe/C,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAgB,UAAA,SAAA2B,MAAA,CAAAR,aAAA,UAA4B;;;;;IAsBjDnC,EAAA,CAAAC,cAAA,YAA8L;IAC5LD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAA+C,WAAA,uEACF;;;;;;IASQ/C,EAAA,CAAAC,cAAA,YAE4C;IADMD,EAAA,CAAAmB,UAAA,mBAAA6B,kEAAA;MAAAhD,EAAA,CAAAqB,aAAA,CAAA4B,IAAA;MAAA,MAAAC,eAAA,GAAAlD,EAAA,CAAAwB,aAAA,IAAA2B,SAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAA2B,OAAA,CAAAC,WAAA,CAAAH,eAAA,CAAwB;IAAA,EAAC;;IACxClD,EAAA,CAAAG,YAAA,EAAI;;;IAA9CH,EAAA,CAAAsD,qBAAA,eAAAtD,EAAA,CAAA+C,WAAA,uBAAyC;;;;;;IAJ7C/C,EAAA,CAAAC,cAAA,eAAmF;IAAAD,EAAA,CAAAE,MAAA,GAEjF;;IAAAF,EAAA,CAAAiB,UAAA,IAAAsC,8CAAA,gBAEgD;IAChDvD,EAAA,CAAAC,cAAA,YAC0F;IAAhDD,EAAA,CAAAmB,UAAA,mBAAAqC,8DAAA;MAAAxD,EAAA,CAAAqB,aAAA,CAAAoC,IAAA;MAAA,MAAAP,eAAA,GAAAlD,EAAA,CAAAwB,aAAA,GAAA2B,SAAA;MAAA,MAAAO,OAAA,GAAA1D,EAAA,CAAAwB,aAAA;MAAA,MAAAmC,GAAA,GAAA3D,EAAA,CAAA4D,WAAA;MAAA,OAAS5D,EAAA,CAAAyB,WAAA,CAAAiC,OAAA,CAAAG,aAAA,CAAAF,GAAA,EAAAT,eAAA,CAAkC,CAAC,EAAE;IAAA,EAAC;;IAAClD,EAAA,CAAAG,YAAA,EAAI;;;;;IANbH,EAAA,CAAAI,SAAA,GAEjF;IAFiFJ,EAAA,CAAA8D,kBAAA,KAAA9D,EAAA,CAAA+C,WAAA,sBAAAG,eAAA,IAAAa,KAAA,MAEjF;IAAI/D,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAgB,UAAA,SAAAgD,OAAA,CAAA7B,aAAA,SAA2B;IAI7BnC,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAsD,qBAAA,eAAAtD,EAAA,CAAA+C,WAAA,qBAAuC;;;;;;IAKzC/C,EAAA,CAAAC,cAAA,YAEoE;IADhBD,EAAA,CAAAmB,UAAA,mBAAA8C,kEAAA;MAAAjE,EAAA,CAAAqB,aAAA,CAAA6C,IAAA;MAAA,MAAAhB,eAAA,GAAAlD,EAAA,CAAAwB,aAAA,IAAA2B,SAAA;MAAA,MAAAgB,OAAA,GAAAnE,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAA0C,OAAA,CAAAC,aAAA,CAAAlB,eAAA,CAA0B;IAAA,EAAC;IACpBlD,EAAA,CAAAG,YAAA,EAAI;;;;IAAtEH,EAAA,CAAAgB,UAAA,aAAAqD,OAAA,CAAAlC,aAAA,UAAmC;;;;;IAHvCnC,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAiB,UAAA,IAAAqD,8CAAA,gBAEwE;IAC1EtE,EAAA,CAAAG,YAAA,EAAO;;;;IAHDH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAgB,UAAA,SAAAuD,OAAA,CAAApC,aAAA,SAA2B;;;;;;IAkCjBnC,EAAA,CAAAC,cAAA,YACqB;IAD4BD,EAAA,CAAAmB,UAAA,mBAAAqD,kEAAA;MAAAxE,EAAA,CAAAqB,aAAA,CAAAoD,IAAA;MAAA,MAAAC,SAAA,GAAA1E,EAAA,CAAAwB,aAAA,GAAA2B,SAAA;MAAA,MAAAwB,OAAA,GAAA3E,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAkD,OAAA,CAAAC,UAAA,CAAAF,SAAA,EAAkB,QAAQ,CAAC;IAAA,EAAC;IAEpF1E,EAAA,CAAAC,cAAA,YACiD;IADHD,EAAA,CAAAmB,UAAA,mBAAA0D,kEAAA;MAAA7E,EAAA,CAAAqB,aAAA,CAAAoD,IAAA;MAAA,MAAAC,SAAA,GAAA1E,EAAA,CAAAwB,aAAA,GAAA2B,SAAA;MAAA,MAAA2B,OAAA,GAAA9E,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAqD,OAAA,CAAAF,UAAA,CAAAF,SAAA,EAAkB,QAAQ,CAAC;IAAA,EAAC;IAClC1E,EAAA,CAAAG,YAAA,EAAI;IACrDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA+B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAH5CH,EAAA,CAAA+E,UAAA,CAAAC,OAAA,CAAAC,QAAA,CAAkB;IAGZjF,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAA+C,WAAA,sBAA+B;;;;;;IAhCrD/C,EAAA,CAAAC,cAAA,cAAuG;IAI/FD,EAAA,CAAAc,SAAA,cACkB;IACpBd,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAiB;IACfD,EAAA,CAAAc,SAAA,aAA2D;IAC7Dd,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAA4B;IAItBD,EAAA,CAAAc,SAAA,aAA+C;IACjDd,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAAqB;IACnBD,EAAA,CAAAa,uBAAA,IAAc;IACZb,EAAA,CAAAC,cAAA,aAA0E;IAAvDD,EAAA,CAAAmB,UAAA,mBAAAgE,8DAAA;MAAA,MAAAC,WAAA,GAAApF,EAAA,CAAAqB,aAAA,CAAAgE,IAAA;MAAA,MAAAX,SAAA,GAAAU,WAAA,CAAAjC,SAAA;MAAA,MAAAmC,OAAA,GAAAtF,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAA6D,OAAA,CAAAC,UAAA,CAAAb,SAAA,EAAkB,MAAM,CAAC;IAAA,EAAC;IACpD1E,EAAA,CAAAc,SAAA,aACuC;IACvCd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA+B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEhDH,EAAA,CAAAe,qBAAA,EAAe;IAEff,EAAA,CAAAa,uBAAA,IAAc;IACZb,EAAA,CAAAiB,UAAA,KAAAuE,8CAAA,gBAKI;IACNxF,EAAA,CAAAe,qBAAA,EAAe;IACjBf,EAAA,CAAAG,YAAA,EAAM;;;;;IA/ByCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAsD,qBAAA,QAAAoB,SAAA,CAAAe,IAAA,CAAAC,IAAA,CAAAC,IAAA,EAAA3F,EAAA,CAAA4F,aAAA,CAAgC;IAK7E5F,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAgB,UAAA,cAAA0D,SAAA,CAAAe,IAAA,CAAAI,IAAA,EAAA7F,EAAA,CAAA8F,cAAA,CAA6B;IAKiC9F,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA+E,UAAA,CAAAgB,OAAA,CAAAd,QAAA,CAAkB;IAMvBjF,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA+E,UAAA,CAAAgB,OAAA,CAAAd,QAAA,CAAkB;IAGjEjF,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAA+C,WAAA,uBAA+B;IAKnC/C,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAgB,UAAA,SAAA+E,OAAA,CAAA5D,aAAA,SAA2B;;;;;IA/CjDnC,EAAA,CAAAC,cAAA,cAC0D;IACxDD,EAAA,CAAAiB,UAAA,IAAA+E,0CAAA,mBAOO;IAGPhG,EAAA,CAAAiB,UAAA,IAAAgF,0CAAA,mBAIO;IAEPjG,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAiB,UAAA,IAAAiF,yCAAA,oBAwCM;IACRlG,EAAA,CAAAG,YAAA,EAAM;;;;;IA1DCH,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAgB,UAAA,SAAAmF,MAAA,CAAAtE,UAAA,IAAAsE,MAAA,CAAA1G,SAAA,CAAAqC,gBAAA,CAAAC,MAAA,CAAqD;IAUrD/B,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAgB,UAAA,SAAAmF,MAAA,CAAAtE,UAAA,IAAAsE,MAAA,CAAA1G,SAAA,CAAAqC,gBAAA,CAAAC,MAAA,CAAqD;IAOM/B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAgB,UAAA,YAAAmF,MAAA,CAAAC,MAAA,CAAAlD,eAAA,EAAwB;;;;;;IAmDhGlD,EAAA,CAAAC,cAAA,eAAoD;IAA3BD,EAAA,CAAAmB,UAAA,sBAAAkF,qEAAA;MAAArG,EAAA,CAAAqB,aAAA,CAAAiF,IAAA;MAAA,MAAAC,OAAA,GAAAvG,EAAA,CAAAwB,aAAA;MAAA,OAAYxB,EAAA,CAAAyB,WAAA,CAAA8E,OAAA,CAAAC,MAAA,CAAAD,OAAA,CAAAE,KAAA,CAAa;IAAA,EAAC;IACjDzG,EAAA,CAAAC,cAAA,cAA0B;IACuBD,EAAA,CAAAE,MAAA,GAA4B;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChFH,EAAA,CAAAC,cAAA,iBAA8F;IAA1DD,EAAA,CAAAmB,UAAA,mBAAAuF,oEAAA;MAAA,MAAAtB,WAAA,GAAApF,EAAA,CAAAqB,aAAA,CAAAiF,IAAA;MAAA,MAAAK,SAAA,GAAAvB,WAAA,CAAAjC,SAAA;MAAA,OAASnD,EAAA,CAAAyB,WAAA,CAAAkF,SAAA,CAAAC,OAAA,CAAc,aAAa,CAAC;IAAA,EAAC;IACxE5G,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,aAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG3CH,EAAA,CAAAC,cAAA,cAAwB;IAEpBD,EAAA,CAAAc,SAAA,eAA+E;IAC/Ed,EAAA,CAAAC,cAAA,SAAG;IACDD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAc,SAAA,uBAA2E;IAC7Ed,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACsBD,EAAA,CAAAE,MAAA,IAAsB;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAjB3EH,EAAA,CAAAgB,UAAA,cAAA6F,MAAA,CAAAC,IAAA,CAAkB;IAE2B9G,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAA+C,WAAA,qBAA4B;IASvE/C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAA+C,WAAA,iCACF;IAEW/C,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAgB,UAAA,SAAA6F,MAAA,CAAAC,IAAA,CAAa,WAAAD,MAAA,CAAAE,MAAA,WAAAF,MAAA,CAAAJ,KAAA;IAGoBzG,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAA+C,WAAA,iBAAsB;;;AD/H1E,OAAM,MAAOiE,mBAAmB;EAiC9BC,YACSC,MAAwB,EACxBC,MAAsB,EACtBC,OAAe,EACfC,kBAAqC,EACrCC,YAAyB,EACzBC,aAA2B,EAC3BC,eAA+B,EAC/BC,cAA6B,EAC7BxF,iBAAmC,EACnCyF,eAA+B,EAC/BC,aAAuB,EACvBC,KAAiB,EAChBC,eAA+B;IAZhC,KAAAX,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAxF,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAyF,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,eAAe,GAAfA,eAAe;IA3ChB,KAAArH,YAAY,GAAQ,EAAE;IACtB,KAAA2B,aAAa,GAAG,IAAI;IACnB,KAAA2F,YAAY,GAAsB,IAAItI,YAAY,EAAO;IACnE,KAAAqC,UAAU,GAAQ,IAAI;IACtB,KAAAkG,aAAa,GAAQ,EAAE;IACvB,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAjG,MAAM,GAAQ,EAAE;IAChB,KAAAtC,SAAS,GAAGA,SAAS;IACrB,KAAAqH,IAAI,GAAG,IAAIlH,SAAS,CAAC,EAAE,CAAC;IACxB,KAAA6G,KAAK,GAAG,EAAE;IACV,KAAAM,MAAM,GAAwB,CAC5B;MACEkB,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,IAAI,CAACnB,MAAM,CAAChF,OAAO,CAAC,YAAY,CAAC;QAC9CoG,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;OACZ;MACD;MACAC,UAAU,EAAE;QACVC,QAAQ,EAAE;UACRH,QAAQ,EAAE,IAAI,CAACpB,MAAM,CAAChF,OAAO,CAAC,wBAAwB;;;KAI3D,CACF;IAoeM,KAAAwG,UAAU,GAAmB,CAClC;MACER,IAAI,EAAE,YAAY;MAClBS,OAAO,EAAE,CACP;QACEP,KAAK,EAAE,aAAa;QAEpBQ,IAAI,EAAE;OACP,EACD;QACER,KAAK,EAAE,QAAQ;QAEfQ,IAAI,EAAE;OACP;KAEJ,CACF;EApeE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAAChH,UAAU,GAAG,IAAI,CAACgB,KAAK,CAACqF,IAAI;IACjC,IAAI,CAACY,eAAe,EAAE;IACtB,IAAI,CAACC,eAAe,EAAE;IACtBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACzI,YAAY,CAAC;EAChD;EAEA0I,cAAcA,CAACC,QAAQ;IACrB,IAAI,CAAC3B,eAAe,CAAC4B,IAAI,EAAE;IAC3B,OAAO,IAAI,CAAC9B,YAAY,CACrB+B,iBAAiB,CAAC,IAAI,CAACxG,KAAK,CAACyG,EAAE,EAAEH,QAAQ,CAAC,CAC1CI,SAAS,EAAE;EAChB;EAEAC,mBAAmBA,CAACC,UAAU,EAAEC,MAAM;IACpC,IAAIC,MAAM,GAAG,IAAIC,QAAQ,EAAE;IAC3BD,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEH,MAAM,CAAC;IAC/BC,MAAM,CAACE,MAAM,CAAC,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,aAAa,EAAEG,UAAU,CAACK,QAAQ,CAAC;IAC3EH,MAAM,CAACE,MAAM,CAAC,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,YAAY,EAAEG,UAAU,CAACzB,OAAO,CAAC;IACzE2B,MAAM,CAACE,MAAM,CAAC,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,UAAU,EAAEG,UAAU,CAAC1F,KAAK,CAAC;IACrE,OAAO,IAAI,CAACwD,aAAa,CAACiC,mBAAmB,CAACG,MAAM,CAAC,CAACJ,SAAS,EAAE;EACnE;EAEA3E,UAAUA,CAAC6E,UAAU,EAAEC,MAAM;IAC3BhK,IAAI,CAACqK,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,eAAe,CAAC;MACtD+H,IAAI,EACF;;;;iCAIyB,GACzB,IAAI,CAAChI,iBAAiB,CAACC,OAAO,CAC5B,2CAA2C,CAC5C,GACD;;;SAGC;MACHgI,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,KAAK,CAAC;MACxDkI,gBAAgB,EAAE,IAAI,CAACnI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;MACtDmI,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,IAAI;MACzBC,UAAU,EAAEA,CAAA,KAAK;QACf,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;UAC7B,IAAI,CAACjB,mBAAmB,CAACC,UAAU,EAAEC,MAAM,CAAC,CAACgB,IAAI,CAAEC,GAAQ,IAAI;YAC7D,IAAIA,GAAG,EAAE;cACP,IAAI,CAAC5B,eAAe,EAAE;cACtBrJ,IAAI,CAACqK,IAAI,CAAC;gBACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,SAAS,CAAC;gBAChD0I,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAClC,2BAA2B,CAC5B;gBACD0G,IAAI,EAAE,SAAS;gBACfuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;gBACvD2I,kBAAkB,EAAE;eACrB,CAAC,CAACH,IAAI,CAAEI,MAAM,IAAI;gBACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;cAE1B,CAAC,CAAC;aACH,MAAM;cACLrL,IAAI,CAACqK,IAAI,CAAC;gBACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,OAAO,CAAC;gBAC9C0I,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAAC,kBAAkB,CAAC;gBACxD0G,IAAI,EAAE,OAAO;gBACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;gBACvD2I,kBAAkB,EAAE;eACrB,CAAC;;UAEN,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;KACD,CAAC;EACJ;EAEAtF,UAAUA,CAACkE,UAAU,EAAEC,MAAM;IAC3B,IAAI,CAACR,cAAc,CAAC,IAAI,CAACvI,UAAU,CAACwI,QAAQ,CAAC,CAACuB,IAAI,CAAEC,GAAQ,IAAI;MAC9DA,GAAG,GAAGA,GAAG,CAACK,IAAI;MACd,IAAIL,GAAG,EAAE;QACP;QACAA,GAAG,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAChB,IAAID,CAAC,CAACrF,IAAI,GAAGsF,CAAC,CAACtF,IAAI,EAAE;YACnB,OAAO,CAAC,CAAC;;UAEX,IAAIqF,CAAC,CAACrF,IAAI,GAAGsF,CAAC,CAACtF,IAAI,EAAE;YACnB,OAAO,CAAC;;UAEV,OAAO,CAAC;QACV,CAAC,CAAC;QAEF,IAAIuF,OAAO,GAAG,EAAE;QAChBT,GAAG,CAACU,OAAO,CAAE5F,IAAI,IAAI;UACnB2F,OAAO,IAAI,kBAAkB3F,IAAI,CAAC6D,EAAE,KAAK7D,IAAI,CAACI,IAAI,WAAW;QAC/D,CAAC,CAAC;QACF;QACAnG,IAAI,CAACqK,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,uBAAuB,CAAC;UAC9D+H,IAAI,EAAE;UACNmB,OAAO;kBACC;UACRlB,gBAAgB,EAAE,IAAI;UACtBC,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,QAAQ,CAAC;UAC3DkI,gBAAgB,EAAE,IAAI,CAACnI,iBAAiB,CAACC,OAAO,CAAC,QAAQ,CAAC;UAC1DmI,cAAc,EAAE,IAAI;UACpBC,mBAAmB,EAAE,IAAI;UACzBC,UAAU,EAAEA,CAAA,KAAK;YACf,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;cAC7B,IAAIzC,OAAO,GACTsD,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,CACjCC,KAAK;cACR,IAAI7B,MAAM,GAAG,IAAIC,QAAQ,EAAE;cAC3BD,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEH,MAAM,CAAC;cAC/BC,MAAM,CAACE,MAAM,CACX,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,aAAa,EACvCG,UAAU,CAACK,QAAQ,CACpB;cACDH,MAAM,CAACE,MAAM,CAAC,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,YAAY,EAAEtB,OAAO,CAAC;cAE9D,IAAIyB,UAAU,CAAC1F,KAAK,EAAE;gBACpB4F,MAAM,CAACE,MAAM,CACX,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,UAAU,EACpCG,UAAU,CAAC1F,KAAK,CACjB;;cAGH,IAAI,CAACwD,aAAa,CAACkE,iBAAiB,CAAC9B,MAAM,CAAC,CAAC+B,SAAS,CACnDf,GAAQ,IAAI;gBACX,IAAI,CAAC7B,eAAe,EAAE;gBACtB,IAAI6B,GAAG,EAAE;kBACPjL,IAAI,CAACqK,IAAI,CAAC;oBACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,SAAS,CAAC;oBAChD+H,IAAI,EACF;;;;+CAIuB,GACvB,IAAI,CAAChI,iBAAiB,CAACC,OAAO,CAC5B,2BAA2B,CAC5B,GACD;;;uBAGD;oBACDiI,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;oBACvD2I,kBAAkB,EAAE;mBACrB,CAAC,CAACH,IAAI,CAAEI,MAAM,IAAI;oBACjB,IAAI,CAAC/B,eAAe,EAAE;kBACxB,CAAC,CAAC;iBACH,MAAM;kBACLrJ,IAAI,CAACqK,IAAI,CAAC;oBACRC,KAAK,EAAE,OAAO;oBACdY,IAAI,EAAE,kBAAkB;oBACxBhC,IAAI,EAAE,OAAO;oBACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;oBACvD2I,kBAAkB,EAAE;mBACrB,CAAC;;cAEN,CAAC,EACAc,KAAK,IAAI;gBACR3C,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE0C,KAAK,CAAC;gBAC3BjM,IAAI,CAACqK,IAAI,CAAC;kBACRC,KAAK,EAAE,OAAO;kBACdY,IAAI,EAAE,8BAA8B;kBACpChC,IAAI,EAAE,OAAO;kBACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;kBACvD2I,kBAAkB,EAAE;iBACrB,CAAC;cACJ,CAAC,CACF;YACH,CAAC,CAAC;UACJ;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAnJ,YAAYA,CAAA;IACV,IAAI,IAAI,CAACS,aAAa,IAAI,KAAK,EAAE;MAC/BzC,IAAI,CAACqK,IAAI,CAAC;QACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,OAAO,CAAC;QAC9C0I,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAClC,8CAA8C,CAC/C;QACD0G,IAAI,EAAE,OAAO;QACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;QACvD2I,kBAAkB,EAAE;OACrB,CAAC;MAEF;;IAEF,IAAI,CAACe,gBAAgB,EAAE;EACzB;EAEA9C,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACnI,UAAU,CAACwI,QAAQ,EAAE;IAC/B,IAAI,CAACD,cAAc,CAAC,IAAI,CAACvI,UAAU,CAACwI,QAAQ,CAAC,CAACuB,IAAI,CAAEC,GAAQ,IAAI;MAC9DA,GAAG,GAAGA,GAAG,CAACK,IAAI;MACd,IAAIL,GAAG,EAAE;QACP,IAAI,CAAC5C,aAAa,GAAG4C,GAAG;;IAE5B,CAAC,CAAC;EACJ;EAEAiB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC7D,aAAa,CAACtH,MAAM,IAAI,CAAC,EAAE;MAClCf,IAAI,CAACqK,IAAI,CAAC;QACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,gBAAgB,CAAC;QACvD0G,IAAI,EAAE,MAAM;QACZuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;QACvD2I,kBAAkB,EAAE;OACrB,CAAC;MACF;;IAGF,MAAMgB,QAAQ,GAAG,IAAI,CAAClE,aAAa,CAACmE,IAAI,CAACnM,0BAA0B,EAAE;MACnEoM,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;KACd,CAAC;IACF,IAAI,IAAI,CAACpE,aAAa,EAAE;MACtB,MAAMqE,QAAQ,GAAG,IAAI,CAACvE,eAAe,CAACwE,SAAS,CAAC,IAAI,CAACtE,aAAa,EAAE,MAAM,CAAC;MAC3E8D,QAAQ,CAACS,iBAAiB,CAACC,KAAK,GAAGH,QAAQ;MAC3CP,QAAQ,CAACS,iBAAiB,CAACzK,UAAU,GAAG,IAAI,CAACA,UAAU;MAEvD;MACA,IAAI2K,aAAa,GAAG,EAAE;MACtB,IAAI,CAAChM,YAAY,CAAC6K,OAAO,CAAEtH,KAAK,IAAI;QAClCyI,aAAa,CAACC,IAAI,CAAC1I,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC;MACpC,CAAC,CAAC;MAEF8H,QAAQ,CAACS,iBAAiB,CAACE,aAAa,GAAGA,aAAa;MACxDX,QAAQ,CAACf,MAAM,CAACJ,IAAI,CAAEI,MAAM,IAAI;QAC9B,IAAIA,MAAM,EAAE;UACV,IAAI4B,UAAU,GACZ,IAAI,CAAC7K,UAAU,IAAI,QAAQ,GAAGiJ,MAAM,CAAC4B,UAAU,GAAG,IAAI;UACxD,IAAIC,aAAa,GAAQ7B,MAAM,CAAC6B,aAAa;UAC7CA,aAAa,GAAGA,aAAa,CAACC,IAAI,CAAC,GAAG,CAAC;UACvC,IAAIjD,MAAM,GAAG,IAAIC,QAAQ,EAAE;UAC3BD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAAChH,KAAK,CAACyG,EAAE,CAAC;UACxC,IAAI,IAAI,CAACzH,UAAU,IAAI,QAAQ,EAAE;YAC/B8H,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE6C,UAAU,CAAC;;UAEpC/C,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE8C,aAAa,CAAC;UAErC,IAAI,CAACpF,aAAa,CACfsF,kBAAkB,CAAClD,MAAM,CAAC,CAC1B+B,SAAS,CAAEf,GAAQ,IAAI;YACtB,IAAIA,GAAG,EAAE;cACP,IAAI,CAAC7B,eAAe,EAAE;cACtBpJ,IAAI,CAACqK,IAAI,CAAC;gBACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,SAAS,CAAC;gBAChD+H,IAAI,EACF;;;6CAGyB,GACzB,IAAI,CAAChI,iBAAiB,CAACC,OAAO,CAAC,0BAA0B,CAAC,GAC1D;;yBAEK;gBACPiI,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;gBACvD2I,kBAAkB,EAAE;eACrB,CAAC,CAACH,IAAI,CAAEI,MAAM,IAAI;gBACjB,IAAI,CAAC/B,eAAe,EAAE;cACxB,CAAC,CAAC;;UAEN,CAAC,EAAG4C,KAAK,IAAI;YACTjM,IAAI,CAACqK,IAAI,CAAC;cACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,sBAAsB,CAAC;cAC7D0I,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAACyJ,KAAK,CAACmB,OAAO,CAAC;cACnDlE,IAAI,EAAE,OAAO;cACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;cACvD2I,kBAAkB,EAAE;aACrB,CAAC;UACN,CAAC,CACA;;MAEP,CAAC,CAAC;;EAEN;EAEAtI,oBAAoBA,CAAA;IAClB,MAAMsJ,QAAQ,GAAG,IAAI,CAAClE,aAAa,CAACmE,IAAI,CAAC/L,2BAA2B,EAAE;MACpEgM,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;KACd,CAAC;IACF,IAAI,IAAI,CAACtJ,KAAK,EAAE;MACdgJ,QAAQ,CAACS,iBAAiB,CAACxJ,WAAW,GAAG,IAAI,CAACD,KAAK,CAACC,WAAW;;IAEjE+I,QAAQ,CAACf,MAAM,CAACJ,IAAI,CAAEI,MAAM,IAAI;MAC9B,IAAIA,MAAM,EAAE;QACV,IAAI,CAACvD,aAAa,CAACwF,gBAAgB,CAAC,IAAI,CAAClK,KAAK,CAACyG,EAAE,EAAEwB,MAAM,CAAC,CAACY,SAAS,CAAEf,GAAQ,IAAI;UAChF,IAAIA,GAAG,EAAE;YACP,IAAI,CAAC9H,KAAK,CAACC,WAAW,GAAGgI,MAAM;YAC/BpL,IAAI,CAACqK,IAAI,CAAC;cACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,0BAA0B,CAAC;cACjE0I,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAAC,cAAc4I,MAAM,kDAAkD,CAAC;cAC5GlC,IAAI,EAAE,SAAS;cACfuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;cACvD2I,kBAAkB,EAAE;aACrB,CAAC;;QAEJ,CAAC,CACF,EAAGc,KAAK,IAAI;UACXjM,IAAI,CAACqK,IAAI,CAAC;YACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,OAAO,CAAC;YAC9C0I,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAACyJ,KAAK,CAACmB,OAAO,CAAC;YACnDlE,IAAI,EAAE,OAAO;YACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;YACvD2I,kBAAkB,EAAE;WACrB,CAAC;QACJ,CAAC;;IAEL,CAAC,CAAC;EACJ;EAEAxH,WAAWA,CAAC2J,WAAW;IACrBhE,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,IAAIsD,KAAK,GAAQ,EAAE;IACnBS,WAAW,CAAC3B,OAAO,CAAEtH,KAAK,IAAI;MAC5BwI,KAAK,CAACE,IAAI,CAAC1I,KAAK,CAACiE,OAAO,CAAC;IAC3B,CAAC,CAAC;IAEF;IAEAtI,IAAI,CAACqK,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,eAAe,CAAC;MACtD+H,IAAI,EACF;;;iCAGyB,GACzB,IAAI,CAAChI,iBAAiB,CAACC,OAAO,CAC5B,4CAA4C,CAC7C,GACD;;;;OAID;MACDgI,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,KAAK,CAAC;MACxDkI,gBAAgB,EAAE,IAAI,CAACnI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;MACtDmI,cAAc,EAAE;KACjB,CAAC,CAACK,IAAI,CAAEI,MAAM,IAAI;MACjB,IAAIhB,QAAQ,GAAGkD,WAAW,CAAC,CAAC,CAAC,CAAClD,QAAQ;MACtC,IAAI/F,KAAK,GAAGiJ,WAAW,CAAC,CAAC,CAAC,CAACjJ,KAAK;MAChCwI,KAAK,GAAGA,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;MAEvB,IAAIjD,MAAM,GAAG,IAAIC,QAAQ,EAAE;MAC3B;MACA;MACA;MACAD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEC,QAAQ,CAAC;MACnCH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE9F,KAAK,CAAC;MAC7B4F,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE0C,KAAK,CAAC;MAC7B,IAAIzB,MAAM,CAACU,KAAK,EAAE;QAChB,IAAI,CAACjE,aAAa,CAAC0F,kBAAkB,CAACtD,MAAM,CAAC,CAAC+B,SAAS,CAAEf,GAAQ,IAAI;UACnE,IAAIA,GAAG,EAAE;YACP,IAAI,CAACnD,eAAe,CAAC4B,IAAI,EAAE;YAC3B,IAAI,CAACL,eAAe,EAAE;YACtB,IAAI,CAACD,eAAe,EAAE;YAEtBpJ,IAAI,CAACqK,IAAI,CAAC;cACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,SAAS,CAAC;cAChD0I,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAClC,4BAA4B,CAC7B;cACD0G,IAAI,EAAE,SAAS;cACfuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;cACvD2I,kBAAkB,EAAE;aACrB,CAAC;WACH,MAAM;YACLnL,IAAI,CAACqK,IAAI,CAAC;cACRC,KAAK,EAAE,OAAO;cACdY,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAAC,mBAAmB,CAAC;cACzD0G,IAAI,EAAE,OAAO;cACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;cACvD2I,kBAAkB,EAAE;aACrB,CAAC;;QAEN,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAzG,aAAaA,CAAC4I,WAAW;IACvB,IAAIT,KAAK,GAAQ,EAAE;IACnBS,WAAW,CAAC3B,OAAO,CAAEtH,KAAK,IAAI;MAC5BwI,KAAK,CAACE,IAAI,CAAC1I,KAAK,CAACiE,OAAO,CAAC;IAC3B,CAAC,CAAC;IAEFtI,IAAI,CAACqK,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,eAAe,CAAC;MACtD+H,IAAI,EACF;;;;iCAIyB,GACzB,IAAI,CAAChI,iBAAiB,CAACC,OAAO,CAC5B,iDAAiD,CAClD,GACD;;aAEK;MACPgI,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,KAAK,CAAC;MACxDkI,gBAAgB,EAAE,IAAI,CAACnI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;MACtDmI,cAAc,EAAE;KACjB,CAAC,CAACK,IAAI,CAAEI,MAAM,IAAI;MACjB,IAAIhB,QAAQ,GAAGkD,WAAW,CAAC,CAAC,CAAC,CAAClD,QAAQ;MACtCyC,KAAK,GAAGA,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;MAEvB,IAAIjD,MAAM,GAAG,IAAIC,QAAQ,EAAE;MAC3B;MACA;MACAD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEC,QAAQ,CAAC;MACnCH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE0C,KAAK,CAAC;MAE7B,IAAIzB,MAAM,CAACU,KAAK,EAAE;QAChB,IAAI,CAACjE,aAAa,CAAC0F,kBAAkB,CAACtD,MAAM,CAAC,CAAC+B,SAAS,CAAEf,GAAQ,IAAI;UACnE,IAAIA,GAAG,EAAE;YACPjL,IAAI,CAACqK,IAAI,CAAC;cACRC,KAAK,EAAE,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,SAAS,CAAC;cAChD0I,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAAC,2BAA2B,CAAC;cACjE0G,IAAI,EAAE,SAAS;cACfuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;cACvD2I,kBAAkB,EAAE;aACrB,CAAC,CAACH,IAAI,CAAEI,MAAM,IAAI;cACjB;cACA,IAAI,CAACtD,eAAe,CAAC4B,IAAI,EAAE;cAC3B8D,UAAU,CAAC,MAAK;gBACd,IAAI,CAACpE,eAAe,EAAE;gBACtB,IAAI,CAACC,eAAe,EAAE;cACxB,CAAC,EAAE,GAAG,CAAC;cACP;YACF,CAAC,CAAC;WACH,MAAM;YACLrJ,IAAI,CAACqK,IAAI,CAAC;cACRC,KAAK,EAAE,OAAO;cACdY,IAAI,EAAE,mBAAmB;cACzBhC,IAAI,EAAE,OAAO;cACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;cACvD2I,kBAAkB,EAAE;aACrB,CAAC;;QAEN,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EACO9B,eAAeA,CAAA;IACpB,IAAI,CAACxB,aAAa,CAACwB,eAAe,CAAC,IAAI,CAAClG,KAAK,CAACyG,EAAE,CAAC,CAACoC,SAAS,CAAEf,GAAQ,IAAI;MACvE,IAAI,CAACnK,YAAY,GAAGmK,GAAG,CAACK,IAAI;MAC5B,IAAI,CAAClD,YAAY,CAACqF,IAAI,CAAC,IAAI,CAAC3M,YAAY,CAAC;IAC3C,CAAC,CAAC;IACF,IAAI,CAACgH,eAAe,CAACZ,OAAO,EAAE;EAChC;EAoBAwG,uBAAuBA,CAACb,KAAK;IAC3B,OAAO,IAAI/B,OAAO,CAAC,CAACC,OAAO,EAAE4C,MAAM,KAAI;MACrC;MACA,IAAI,CAAC/F,YAAY,CACdgG,kBAAkB,CAAC,IAAI,CAACzK,KAAK,CAACyG,EAAE,CAAC,CACjCoC,SAAS,CAAEf,GAAQ,IAAI;QACtB,IAAI4C,cAAc,GAAQ5C,GAAG,CAACK,IAAI;QAClCuC,cAAc,CAACtC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5B,EAAE,GAAG6B,CAAC,CAAC7B,EAAE,CAAC;QAC1CiE,cAAc,GAAGA,cAAc,CAACC,GAAG,CAAE/H,IAAI,IAAKA,IAAI,CAAC6D,EAAE,CAAC;QACtD,IAAImE,mBAAmB,GAAGF,cAAc,CAACX,IAAI,CAAC,GAAG,CAAC;QAClD,IAAIc,UAAU,GAAGnB,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;QAChC,IAAIc,UAAU,IAAID,mBAAmB,EAAE;UACrCzE,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACrCwB,OAAO,CAAC,KAAK,CAAC;SACf,MAAM;UACL;UACA,IAAIkD,kBAAkB,GAAGpB,KAAK,CAACqB,MAAM,CAClCnI,IAAI,IAAK,CAAC8H,cAAc,CAACM,QAAQ,CAACpI,IAAI,CAAC,CACzC;UACDgF,OAAO,CAACkD,kBAAkB,CAAC;;MAE/B,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAvH,MAAMA,CAAC0H,WAAW;IAChBA,WAAW,CAAC7C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzF,IAAI,CAACI,IAAI,CAACkI,aAAa,CAAC5C,CAAC,CAAC1F,IAAI,CAACI,IAAI,CAAC,CAAC;IAClE,OAAOiI,WAAW;EACpB;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACzG,aAAa,CACf0G,mBAAmB,CAAC,IAAI,CAACtN,UAAU,CAAC2I,EAAE,CAAC,CACvCoC,SAAS,CAAEf,GAAQ,IAAI;MACtB,IAAIuD,MAAM,GAAGvD,GAAG,CAACK,IAAI;MACrBkD,MAAM,GAAGA,MAAM,CAACN,MAAM,CAAE/K,KAAK,IAAKA,KAAK,CAACqF,IAAI,IAAI,QAAQ,CAAC;MAEzDxI,IAAI,CAACqK,IAAI,CAAC;QACRC,KAAK,EACH,IAAI,CAAC/H,iBAAiB,CAACC,OAAO,CAAC,kCAAkC,CAAC,GAClE,GAAG,GACH,IAAI,CAACvB,UAAU,CAACkF,IAAI,GACpB,GAAG,GACHqI,MAAM,CAAC,CAAC,CAAC,CAACrI,IAAI,GACd,IAAI;QACNqE,gBAAgB,EAAE,IAAI;QACtBE,gBAAgB,EAAE,IAAI,CAACnI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;QACtDiI,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,KAAK;OACxD,CAAC,CAACwI,IAAI,CAAEI,MAAM,IAAI;QACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;UACtB,IAAIjB,QAAQ,GAAGoE,MAAM,CAAC,CAAC,CAAC,CAAC5E,EAAE;UAC3B;UACA,IAAI,CAAChC,YAAY,CACdgG,kBAAkB,CAACxD,QAAQ,CAAC,CAC5B4B,SAAS,CAAEf,GAAQ,IAAI;YACtB,IAAI4B,KAAK,GAAQ5B,GAAG,CAACK,IAAI;YACzBuB,KAAK,CAACtB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5B,EAAE,GAAG6B,CAAC,CAAC7B,EAAE,CAAC;YACjCiD,KAAK,GAAGA,KAAK,CAACiB,GAAG,CAAE/H,IAAI,IAAKA,IAAI,CAAC6D,EAAE,CAAC;YAEpC;YACA,IAAI,CAAC8D,uBAAuB,CAACb,KAAK,CAAC,CAAC7B,IAAI,CAAEC,GAAG,IAAI;cAC/C,IAAIA,GAAG,EAAE;gBACP4B,KAAK,GAAG5B,GAAG;gBACX;gBACA,IAAIhB,MAAM,GAAG,IAAIC,QAAQ,EAAE;gBAC3B2C,KAAK,GAAGA,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;gBACvBjD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAAChH,KAAK,CAACyG,EAAE,CAAC;gBACxCK,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE0C,KAAK,CAAC;gBAC7B,IAAI,CAAChF,aAAa,CACfsF,kBAAkB,CAAClD,MAAM,CAAC,CAC1B+B,SAAS,CAAEf,GAAQ,IAAI;kBACtB,IAAIA,GAAG,EAAE;oBACPjL,IAAI,CAACqK,IAAI,CAAC;sBACRC,KAAK,EAAE,SAAS;sBAChBY,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAAC,6BAA6B,CAAC;sBACnE0G,IAAI,EAAE,SAAS;sBACfuB,iBAAiB,EACf,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;sBACtC2I,kBAAkB,EAAE;qBACrB,CAAC,CAACH,IAAI,CAAEI,MAAM,IAAI;sBACjB,IAAI,CAAChC,eAAe,EAAE;sBAEtB,IAAI,CAACtB,eAAe,CAAC4B,IAAI,EAAE;sBAC3B8D,UAAU,CAAC,MAAK;wBACd,IAAI,CAAC3F,aAAa,CACfwB,eAAe,CAAC,IAAI,CAAClG,KAAK,CAACyG,EAAE,CAAC,CAC9BoC,SAAS,CAAEf,GAAQ,IAAI;0BACtB,IAAI,CAACnK,YAAY,GAAGmK,GAAG,CAACK,IAAI;0BAE5B,IAAI,CAACxD,eAAe,CAACZ,OAAO,EAAE;wBAChC,CAAC,CAAC;sBACN,CAAC,EAAE,GAAG,CAAC;oBACT,CAAC,CAAC;mBACH,MAAM;oBACLlH,IAAI,CAACqK,IAAI,CAAC;sBACRC,KAAK,EAAE,OAAO;sBACdY,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAAC,oBAAoB,CAAC;sBAC1D0G,IAAI,EAAE,OAAO;sBACbuB,iBAAiB,EACf,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;sBACtC2I,kBAAkB,EAAE;qBACrB,CAAC;;gBAEN,CAAC,CAAC;eACL,MAAM;gBACLnL,IAAI,CAACqK,IAAI,CAAC;kBACRC,KAAK,EAAE,OAAO;kBACdY,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAACC,OAAO,CAAC,2BAA2B,CAAC;kBACjE0G,IAAI,EAAE,OAAO;kBACbuB,iBAAiB,EAAE,IAAI,CAAClI,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAAC;kBACvD2I,kBAAkB,EAAE;iBACrB,CAAC;;YAEN,CAAC,CAAC;UACJ,CAAC,CAAC;;MAER,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAGAhH,aAAaA,CAACsK,OAAO,EAAEC,UAAU;IAC/B,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,IAAI,CAACA,UAAU,CAACrK,KAAK,EAAE;MACzB,IAAI,CAAC0C,KAAK,GAAG;QACXiG,UAAU,EAAE,IAAI,CAAC0B,UAAU,CAACrK;OAC7B;;IAEH,IAAI,CAAC4D,aAAa,CACfmE,IAAI,CAACqC,OAAO,EAAE;MACbE,cAAc,EAAE;KACjB,CAAC,CACDvD,MAAM,CAACJ,IAAI,CACTI,MAAM,IAAI;MACT,IAAI,CAACsD,UAAU,GAAG,IAAI;IACxB,CAAC,EACAE,MAAM,IAAI;MACT,IAAI,CAACF,UAAU,GAAG,IAAI;IACxB,CAAC,CACF;EACL;EAEA5H,MAAMA,CAACC,KAAK;IACV,IAAI,CAAC2H,UAAU;IAEf,IAAI,IAAI,CAACtH,IAAI,CAACyH,OAAO,IAAI,CAAC,IAAI,CAACH,UAAU,EAAE;IAC3C,IAAIzE,MAAM,GAAG,IAAIC,QAAQ,EAAE;IAC3BD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAACuE,UAAU,CAACtE,QAAQ,CAAC;IACnDH,MAAM,CAACE,MAAM,CAAC,WAAW,EAAE,IAAI,CAACuE,UAAU,CAACrK,KAAK,CAACyK,QAAQ,EAAE,CAAC;IAC5D7E,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEpD,KAAK,CAACiG,UAAU,CAAC;IACxC/C,MAAM,CAACE,MAAM,CAAC,qBAAqB,EAAE,MAAM,CAAC;IAE5C,IAAI,CAACrC,eAAe,CAAC4B,IAAI,EAAE;IAC3B,IAAI,CAACqF,SAAS,CAAC9E,MAAM,CAAC,CAACe,IAAI,CAAEC,GAAG,IAAI;MAClC,IAAI,CAAC5B,eAAe,EAAE;MACtB,IAAI,CAACpB,aAAa,CAAC+G,UAAU,EAAE;IAC/B,CAAC,EACA/C,KAAK,IAAI;MACR,IAAIA,KAAK,CAACgD,MAAM,EAAC;QACf,IAAI,CAAC7H,IAAI,CAAC8H,QAAQ,CAAC,YAAY,CAAC,CAACC,SAAS,CAAC;UACzCC,WAAW,EAAEnD,KAAK,CAACmB;SACpB,CAAC;;IAEN,CAAC,CACF;EACH;EAEQ2B,SAASA,CAAC9E,MAAM;IAErB,OAAO,IAAI,CAACpC,aAAa,CAACkH,SAAS,CAAC9E,MAAM,CAAC,CAACJ,SAAS,EAAE,CAACmB,IAAI,CAAEC,GAAQ,IAAI;MACzE,IAAIA,GAAG,EAAE;QACP,OAAOA,GAAG;;IAEd,CAAC,CACF;EACD;EAAC,QAAAoE,CAAA;qBApsBU/H,mBAAmB,EAAAhH,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAlP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAArP,EAAA,CAAAgP,iBAAA,CAAAM,EAAA,CAAAC,iBAAA,GAAAvP,EAAA,CAAAgP,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAzP,EAAA,CAAAgP,iBAAA,CAAAU,EAAA,CAAAC,YAAA,GAAA3P,EAAA,CAAAgP,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA7P,EAAA,CAAAgP,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAA/P,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAlP,EAAA,CAAAgP,iBAAA,CAAAgB,EAAA,CAAAC,cAAA,GAAAjQ,EAAA,CAAAgP,iBAAA,CAAAkB,EAAA,CAAAC,QAAA,GAAAnQ,EAAA,CAAAgP,iBAAA,CAAAoB,GAAA,CAAAC,UAAA,GAAArQ,EAAA,CAAAgP,iBAAA,CAAAsB,GAAA,CAAAxQ,cAAA;EAAA;EAAA,QAAAyQ,EAAA;UAAnBvJ,mBAAmB;IAAAwJ,SAAA;IAAAC,MAAA;MAAA5N,KAAA;MAAAlC,UAAA;MAAAH,YAAA;MAAA2B,aAAA;IAAA;IAAAuO,OAAA;MAAA5I,YAAA;IAAA;IAAA6I,QAAA,GAAA3Q,EAAA,CAAA4Q,kBAAA,CAFnB,CAAC/Q,eAAe,EAAEC,cAAc,CAAC;IAAA+Q,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC9B9ClR,EAAA,CAAAC,cAAA,aAAkB;QAESD,EAAA,CAAAE,MAAA,GAA+B;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAc,SAAA,UAAW;QACXd,EAAA,CAAAiB,UAAA,IAAAmQ,kCAAA,iBAwBM;QAGNpR,EAAA,CAAAiB,UAAA,IAAAoQ,kCAAA,mBA8BM;QAGRrR,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAiB,UAAA,IAAAqQ,gCAAA,eAEI;QACJtR,EAAA,CAAAC,cAAA,aAAuB;QAInBD,EAAA,CAAAiB,UAAA,KAAAsQ,mCAAA,iBA+DM;QAERvR,EAAA,CAAAG,YAAA,EAAM;QAIVH,EAAA,CAAAiB,UAAA,KAAAuQ,2CAAA,kCAAAxR,EAAA,CAAAyR,sBAAA,CAqBc;;;QAhKazR,EAAA,CAAAI,SAAA,GAA+B;QAA/BJ,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAA+C,WAAA,sBAA+B;QAE5B/C,EAAA,CAAAI,SAAA,GAGpB;QAHoBJ,EAAA,CAAAgB,UAAA,SAAAmQ,GAAA,CAAAtP,UAAA,IAAAsP,GAAA,CAAA1R,SAAA,CAAAqC,gBAAA,CAAAE,MAAA,IAAAmP,GAAA,CAAAtP,UAAA,IAAAsP,GAAA,CAAA1R,SAAA,CAAAqC,gBAAA,CAAAC,MAAA,CAGpB;QAwBoB/B,EAAA,CAAAI,SAAA,GAA4F;QAA5FJ,EAAA,CAAAgB,UAAA,SAAAmQ,GAAA,CAAAtP,UAAA,kBAAAsP,GAAA,CAAAxQ,UAAA,CAAA+Q,aAAA,IAAAP,GAAA,CAAA1R,SAAA,CAAAkS,cAAA,CAAAC,KAAA,CAA4F;QAkClG5R,EAAA,CAAAI,SAAA,GAA4F;QAA5FJ,EAAA,CAAAgB,UAAA,SAAAmQ,GAAA,CAAAtP,UAAA,kBAAAsP,GAAA,CAAAxQ,UAAA,CAAA+Q,aAAA,IAAAP,GAAA,CAAA1R,SAAA,CAAAkS,cAAA,CAAAC,KAAA,CAA4F;QAQpF5R,EAAA,CAAAI,SAAA,GAAiB;QAAjBJ,EAAA,CAAAgB,UAAA,YAAAmQ,GAAA,CAAA3Q,YAAA,CAAiB", "names": ["EventEmitter", "AppConfig", "<PERSON><PERSON>", "ModalAddGroupTeamComponent", "FormGroup", "CorePipesModule", "SortByNamePipe", "ModalAddNumberTeamComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "tmp_0_0", "ctx_r6", "group_stages", "length", "undefined", "tournament", "no_of_groups", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementContainerEnd", "ɵɵproperty", "ɵɵtemplate", "StageTeamsComponent_div_6_div_2_Template", "ɵɵlistener", "StageTeamsComponent_div_6_Template_button_click_3_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "addGroupTeam", "StageTeamsComponent_div_6_ng_container_6_Template", "ctx_r0", "stage_type", "TOURNAMENT_TYPES", "groups", "league", "_translateService", "instant", "allowEditTeam", "StageTeamsComponent_div_7_Template_button_click_4_listener", "_r12", "ctx_r11", "openModalAddNumbTeam", "StageTeamsComponent_div_7_Template_button_click_8_listener", "ctx_r13", "StageTeamsComponent_div_7_ng_container_12_Template", "ctx_r1", "tmp_1_0", "stage", "team_number", "ɵɵpipeBind1", "StageTeamsComponent_div_11_span_1_i_3_Template_i_click_0_listener", "_r22", "group_stage_r14", "$implicit", "ctx_r20", "removeGroup", "ɵɵpropertyInterpolate", "StageTeamsComponent_div_11_span_1_i_3_Template", "StageTeamsComponent_div_11_span_1_Template_i_click_4_listener", "_r24", "ctx_r23", "_r4", "ɵɵreference", "openModalEdit", "ɵɵtextInterpolate2", "group", "ctx_r16", "StageTeamsComponent_div_11_span_2_i_1_Template_i_click_0_listener", "_r30", "ctx_r28", "removeAllTeam", "ctx_r27", "StageTeamsComponent_div_11_span_2_i_1_Template", "ctx_r17", "StageTeamsComponent_div_11_div_4_a_19_Template_a_click_0_listener", "_r36", "stage_r31", "ctx_r34", "removeTeam", "StageTeamsComponent_div_11_div_4_a_19_Template_i_click_1_listener", "ctx_r37", "ɵɵstyleMap", "ctx_r33", "btnStyle", "ɵɵtextInterpolate", "StageTeamsComponent_div_11_div_4_Template_a_click_13_listener", "restoredCtx", "_r40", "ctx_r39", "changeTeam", "StageTeamsComponent_div_11_div_4_a_19_Template", "team", "club", "logo", "ɵɵsanitizeUrl", "name", "ɵɵsanitizeHtml", "ctx_r18", "StageTeamsComponent_div_11_span_1_Template", "StageTeamsComponent_div_11_span_2_Template", "StageTeamsComponent_div_11_div_4_Template", "ctx_r3", "sortBy", "StageTeamsComponent_ng_template_12_Template_form_ngSubmit_0_listener", "_r43", "ctx_r42", "onEdit", "model", "StageTeamsComponent_ng_template_12_Template_button_click_5_listener", "modal_r41", "dismiss", "ctx_r5", "form", "fields", "StageTeamsComponent", "constructor", "_trans", "_route", "_router", "_tournamentService", "_teamService", "_stageService", "_loadingService", "_toastrService", "_commonsService", "_modalService", "_http", "_sortByNamePipe", "onDataChange", "team_by_group", "team_id", "key", "type", "props", "label", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "validation", "messages", "rowActions", "buttons", "icon", "ngOnInit", "getTeamsByGroup", "getTeamsInStage", "console", "log", "getTeamByGroup", "group_id", "show", "getTeamNotInStage", "id", "to<PERSON>romise", "removeTeamFromStage", "stage_team", "action", "params", "FormData", "append", "stage_id", "fire", "title", "html", "showCancelButton", "confirmButtonText", "cancelButtonText", "reverseButtons", "showLoaderOnConfirm", "preConfirm", "Promise", "resolve", "then", "res", "text", "confirmButtonColor", "result", "isConfirmed", "data", "sort", "a", "b", "options", "for<PERSON>ach", "document", "getElementById", "value", "changeTeamInStage", "subscribe", "error", "openModalAddTeam", "modalRef", "open", "size", "backdrop", "keyboard", "centered", "windowClass", "sortTeam", "transform", "componentInstance", "teams", "current_group", "push", "group_name", "selected_team", "join", "createTeamMultiple", "message", "updateTeamNumber", "group_stage", "removeTeamMultiple", "setTimeout", "emit", "checkTeamAlreadyInStage", "reject", "getTeamListInStage", "teams_in_stage", "map", "list_teams_in_stage", "list_teams", "teams_not_in_stage", "filter", "includes", "stage_teams", "localeCompare", "importTeams", "getDataByTournament", "stages", "content", "groupStage", "ariaLabelledBy", "reason", "invalid", "toString", "editGroup", "dismissAll", "errors", "controls", "setErrors", "serverError", "_", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "ActivatedRoute", "Router", "i3", "TournamentService", "i4", "TeamService", "i5", "StageService", "i6", "LoadingService", "i7", "ToastrService", "i8", "CommonsService", "i9", "NgbModal", "i10", "HttpClient", "i11", "_2", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "StageTeamsComponent_Template", "rf", "ctx", "StageTeamsComponent_div_6_Template", "StageTeamsComponent_div_7_Template", "StageTeamsComponent_p_8_Template", "StageTeamsComponent_div_11_Template", "StageTeamsComponent_ng_template_12_Template", "ɵɵtemplateRefExtractor", "type_knockout", "KNOCKOUT_TYPES", "type4"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-teams\\stage-teams.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-teams\\stage-teams.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { environment } from 'environments/environment';\r\nimport { Subject } from 'rxjs';\r\nimport { EZBtnActions } from 'app/components/btn-dropdown-action/btn-dropdown-action.component';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { ToastrService } from 'ngx-toastr';\r\n// sweet alert\r\nimport Swal from 'sweetalert2';\r\nimport { ModalAddGroupTeamComponent } from './modal-add-group-team/modal-add-group-team.component';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { CorePipesModule } from '@core/pipes/pipes.module';\r\nimport { SortByNamePipe } from '@core/pipes/sort-by-name.pipe';\r\nimport { ModalAddNumberTeamComponent } from './modal-add-number-team/modal-add-number-team.component';\r\n\r\n@Component({\r\n  selector: 'stage-teams',\r\n  templateUrl: './stage-teams.component.html',\r\n  styleUrls: ['./stage-teams.component.scss'],\r\n  providers: [CorePipesModule, SortByNamePipe]\r\n})\r\nexport class StageTeamsComponent implements OnInit {\r\n  @Input() stage: any;\r\n  @Input() tournament: any;\r\n  @Input() group_stages: any = [];\r\n  @Input() allowEditTeam = true;\r\n  @Output() onDataChange: EventEmitter<any> = new EventEmitter<any>();\r\n  stage_type: any = null;\r\n  team_by_group: any = [];\r\n  team_id = null;\r\n  groups: any = [];\r\n  AppConfig = AppConfig;\r\n  form = new FormGroup({});\r\n  model = {};\r\n  fields: FormlyFieldConfig[] = [\r\n    {\r\n      key: 'group_name',\r\n      type: 'input',\r\n      props: {\r\n        label: '',\r\n        placeholder: this._trans.instant('Group Name'),\r\n        required: true,\r\n        maxLength: 20,\r\n      },\r\n      // validate message\r\n      validation: {\r\n        messages: {\r\n          required: this._trans.instant('Group Name is required'),\r\n        },\r\n      },\r\n\r\n    },\r\n  ];\r\n\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _tournamentService: TournamentService,\r\n    public _teamService: TeamService,\r\n    public _stageService: StageService,\r\n    public _loadingService: LoadingService,\r\n    public _toastrService: ToastrService,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public _modalService: NgbModal,\r\n    public _http: HttpClient,\r\n    private _sortByNamePipe: SortByNamePipe\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.stage_type = this.stage.type;\r\n    this.getTeamsByGroup();\r\n    this.getTeamsInStage();\r\n    console.log('group_stages', this.group_stages);\r\n  }\r\n\r\n  getTeamByGroup(group_id) {\r\n    this._loadingService.show();\r\n    return this._teamService\r\n      .getTeamNotInStage(this.stage.id, group_id)\r\n      .toPromise();\r\n  }\r\n\r\n  removeTeamFromStage(stage_team, action) {\r\n    let params = new FormData();\r\n    params.append('action', action);\r\n    params.append('data[' + stage_team.id + '][stage_id]', stage_team.stage_id);\r\n    params.append('data[' + stage_team.id + '][team_id]', stage_team.team_id);\r\n    params.append('data[' + stage_team.id + '][group]', stage_team.group);\r\n    return this._stageService.removeTeamFromStage(params).toPromise();\r\n  }\r\n\r\n  removeTeam(stage_team, action) {\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure?'),\r\n      html:\r\n        `\r\n        <div class=\"text-center\">\r\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n\r\n        <p class=\"text-center\"> ` +\r\n        this._translateService.instant(\r\n          'You will not be able to recover this team'\r\n        ) +\r\n        `      \r\n        </p>\r\n      </div>\r\n        `,\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      reverseButtons: true,\r\n      showLoaderOnConfirm: true,\r\n      preConfirm: () => {\r\n        return new Promise((resolve) => {\r\n          this.removeTeamFromStage(stage_team, action).then((res: any) => {\r\n            if (res) {\r\n              this.getTeamsInStage();\r\n              Swal.fire({\r\n                title: this._translateService.instant('Success'),\r\n                text: this._translateService.instant(\r\n                  'Team removed successfully'\r\n                ),\r\n                icon: 'success',\r\n                confirmButtonText: this._translateService.instant('OK'),\r\n                confirmButtonColor: '#3085d6',\r\n              }).then((result) => {\r\n                if (result.isConfirmed) {\r\n                }\r\n              });\r\n            } else {\r\n              Swal.fire({\r\n                title: this._translateService.instant('Error'),\r\n                text: this._translateService.instant('Team not removed'),\r\n                icon: 'error',\r\n                confirmButtonText: this._translateService.instant('OK'),\r\n                confirmButtonColor: '#3085d6',\r\n              });\r\n            }\r\n          });\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  changeTeam(stage_team, action) {\r\n    this.getTeamByGroup(this.tournament.group_id).then((res: any) => {\r\n      res = res.data;\r\n      if (res) {\r\n        // sort by name\r\n        res.sort((a, b) => {\r\n          if (a.name < b.name) {\r\n            return -1;\r\n          }\r\n          if (a.name > b.name) {\r\n            return 1;\r\n          }\r\n          return 0;\r\n        });\r\n\r\n        let options = '';\r\n        res.forEach((team) => {\r\n          options += `<option value=\"${team.id}\">${team.name}</option>`;\r\n        });\r\n        // pop up to select team\r\n        Swal.fire({\r\n          title: this._translateService.instant('Select team to change'),\r\n          html: `<select class=\"form-control\" id=\"team_id\">\r\n        ${options}\r\n        </select>`,\r\n          showCancelButton: true,\r\n          confirmButtonText: this._translateService.instant('Change'),\r\n          cancelButtonText: this._translateService.instant('Cancel'),\r\n          reverseButtons: true,\r\n          showLoaderOnConfirm: true,\r\n          preConfirm: () => {\r\n            return new Promise((resolve) => {\r\n              let team_id = (<HTMLInputElement>(\r\n                document.getElementById('team_id')\r\n              )).value;\r\n              let params = new FormData();\r\n              params.append('action', action);\r\n              params.append(\r\n                'data[' + stage_team.id + '][stage_id]',\r\n                stage_team.stage_id\r\n              );\r\n              params.append('data[' + stage_team.id + '][team_id]', team_id);\r\n\r\n              if (stage_team.group) {\r\n                params.append(\r\n                  'data[' + stage_team.id + '][group]',\r\n                  stage_team.group\r\n                );\r\n              }\r\n\r\n              this._stageService.changeTeamInStage(params).subscribe(\r\n                (res: any) => {\r\n                  this.getTeamsByGroup();\r\n                  if (res) {\r\n                    Swal.fire({\r\n                      title: this._translateService.instant('Success'),\r\n                      html:\r\n                        `\r\n                      <div class=\"text-center\">\r\n                      <img src=\"assets/images/ai/done.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n              \r\n                      <p class=\"text-center\"> ` +\r\n                        this._translateService.instant(\r\n                          'Team changed successfully'\r\n                        ) +\r\n                        `      \r\n                      </p>\r\n                    </div>\r\n                      `,\r\n                      confirmButtonText: this._translateService.instant('OK'),\r\n                      confirmButtonColor: '#3085d6',\r\n                    }).then((result) => {\r\n                      this.getTeamsInStage();\r\n                    });\r\n                  } else {\r\n                    Swal.fire({\r\n                      title: 'Error',\r\n                      text: 'Team not changed',\r\n                      icon: 'error',\r\n                      confirmButtonText: this._translateService.instant('OK'),\r\n                      confirmButtonColor: '#3085d6',\r\n                    });\r\n                  }\r\n                },\r\n                (error) => {\r\n                  console.log('error', error);\r\n                  Swal.fire({\r\n                    title: 'Error',\r\n                    text: 'No team available for change',\r\n                    icon: 'error',\r\n                    confirmButtonText: this._translateService.instant('OK'),\r\n                    confirmButtonColor: '#3085d6',\r\n                  });\r\n                }\r\n              );\r\n            });\r\n          },\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  addGroupTeam() {\r\n    if (this.allowEditTeam == false) {\r\n      Swal.fire({\r\n        title: this._translateService.instant('Error'),\r\n        text: this._translateService.instant(\r\n          'You must remove all schedule before add team'\r\n        ),\r\n        icon: 'error',\r\n        confirmButtonText: this._translateService.instant('OK'),\r\n        confirmButtonColor: '#3085d6',\r\n      });\r\n\r\n      return;\r\n    }\r\n    this.openModalAddTeam();\r\n  }\r\n\r\n  getTeamsByGroup() {\r\n    if (!this.tournament.group_id) return;\r\n    this.getTeamByGroup(this.tournament.group_id).then((res: any) => {\r\n      res = res.data;\r\n      if (res) {\r\n        this.team_by_group = res;\r\n      }\r\n    });\r\n  }\r\n\r\n  openModalAddTeam() {\r\n    if (this.team_by_group.length == 0) {\r\n      Swal.fire({\r\n        title: this._translateService.instant('No team to add'),\r\n        icon: 'info',\r\n        confirmButtonText: this._translateService.instant('OK'),\r\n        confirmButtonColor: '#3085d6',\r\n      });\r\n      return;\r\n    }\r\n\r\n    const modalRef = this._modalService.open(ModalAddGroupTeamComponent, {\r\n      size: 'lg',\r\n      backdrop: 'static',\r\n      keyboard: false,\r\n      centered: true,\r\n      windowClass: 'modal-add-group-team',\r\n    });\r\n    if (this.team_by_group) {\r\n      const sortTeam = this._sortByNamePipe.transform(this.team_by_group, 'name');\r\n      modalRef.componentInstance.teams = sortTeam;\r\n      modalRef.componentInstance.stage_type = this.stage_type;\r\n\r\n      // get current group\r\n      let current_group = [];\r\n      this.group_stages.forEach((group) => {\r\n        current_group.push(group[0].group);\r\n      });\r\n\r\n      modalRef.componentInstance.current_group = current_group;\r\n      modalRef.result.then((result) => {\r\n        if (result) {\r\n          let group_name =\r\n            this.stage_type == 'Groups' ? result.group_name : null;\r\n          let selected_team: any = result.selected_team;\r\n          selected_team = selected_team.join(',');\r\n          let params = new FormData();\r\n          params.append('stage_id', this.stage.id);\r\n          if (this.stage_type == 'Groups') {\r\n            params.append('group', group_name);\r\n          }\r\n          params.append('teams', selected_team);\r\n\r\n          this._stageService\r\n            .createTeamMultiple(params)\r\n            .subscribe((res: any) => {\r\n              if (res) {\r\n                this.getTeamsByGroup();\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Success'),\r\n                  html:\r\n                    `\r\n                  <div class=\"text-center\">\r\n                    <img src=\"assets/images/ai/done.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n                    <p class=\"text-center\"> ` +\r\n                    this._translateService.instant('Teams added successfully') +\r\n                    `      \r\n                    </p>\r\n                  </div>`,\r\n                  confirmButtonText: this._translateService.instant('OK'),\r\n                  confirmButtonColor: '#3085d6',\r\n                }).then((result) => {\r\n                  this.getTeamsInStage();\r\n                });\r\n              }\r\n            }, (error) => {\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Failed to Add Team  '),\r\n                  text: this._translateService.instant(error.message),\r\n                  icon: 'error',\r\n                  confirmButtonText: this._translateService.instant('OK'),\r\n                  confirmButtonColor: '#3085d6',\r\n                });\r\n            }\r\n            );\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  openModalAddNumbTeam() {\r\n    const modalRef = this._modalService.open(ModalAddNumberTeamComponent, {\r\n      size: 'md',\r\n      backdrop: 'static',\r\n      keyboard: false,\r\n      centered: true,\r\n      windowClass: 'modal-add-group-team',\r\n    });\r\n    if (this.stage) {\r\n      modalRef.componentInstance.team_number = this.stage.team_number;\r\n    }\r\n    modalRef.result.then((result) => {\r\n      if (result) {\r\n        this._stageService.updateTeamNumber(this.stage.id, result).subscribe((res: any) => {\r\n          if (res) {\r\n            this.stage.team_number = result;\r\n            Swal.fire({\r\n              title: this._translateService.instant('Teams Set Successfully!!'),\r\n              text: this._translateService.instant(`You’ve set ${result} teams for the tournament. Now, let’s add teams!`),\r\n              icon: 'success',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            });\r\n          }\r\n          }\r\n        ), (error) => {\r\n          Swal.fire({\r\n            title: this._translateService.instant('Error'),\r\n            text: this._translateService.instant(error.message),\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n            confirmButtonColor: '#3085d6',\r\n          });\r\n        };\r\n      }\r\n    })\r\n  }\r\n\r\n  removeGroup(group_stage) {\r\n    console.log('removeGroup');\r\n    let teams: any = [];\r\n    group_stage.forEach((group) => {\r\n      teams.push(group.team_id);\r\n    });\r\n\r\n    // implode teams\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure?'),\r\n      html:\r\n        ` \r\n      <div class=\"text-center\">\r\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n        <p class=\"text-center\"> ` +\r\n        this._translateService.instant(\r\n          'You will not be able to recover this group'\r\n        ) +\r\n        `\r\n        </p>\r\n      </div>\r\n      \r\n      `,\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      reverseButtons: true,\r\n    }).then((result) => {\r\n      let stage_id = group_stage[0].stage_id;\r\n      let group = group_stage[0].group;\r\n      teams = teams.join(',');\r\n\r\n      let params = new FormData();\r\n      // stage_id: 139\r\n      // group: A\r\n      // teams: 7, 8, 9\r\n      params.append('stage_id', stage_id);\r\n      params.append('group', group);\r\n      params.append('teams', teams);\r\n      if (result.value) {\r\n        this._stageService.removeTeamMultiple(params).subscribe((res: any) => {\r\n          if (res) {\r\n            this._loadingService.show();\r\n            this.getTeamsInStage();\r\n            this.getTeamsByGroup();\r\n\r\n            Swal.fire({\r\n              title: this._translateService.instant('Success'),\r\n              text: this._translateService.instant(\r\n                'Group removed successfully'\r\n              ),\r\n              icon: 'success',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            });\r\n          } else {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: this._translateService.instant('Group not removed'),\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  removeAllTeam(group_stage) {\r\n    let teams: any = [];\r\n    group_stage.forEach((group) => {\r\n      teams.push(group.team_id);\r\n    });\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure?'),\r\n      html:\r\n        `\r\n      <div class=\"text-center\">\r\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n\r\n        <p class=\"text-center\"> ` +\r\n        this._translateService.instant(\r\n          'You are about to remove all teams in this group'\r\n        ) +\r\n        `      \r\n        </p>\r\n      </div>`,\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      reverseButtons: true,\r\n    }).then((result) => {\r\n      let stage_id = group_stage[0].stage_id;\r\n      teams = teams.join(',');\r\n\r\n      let params = new FormData();\r\n      // stage_id: 139\r\n      // teams: 7, 8, 9\r\n      params.append('stage_id', stage_id);\r\n      params.append('teams', teams);\r\n\r\n      if (result.value) {\r\n        this._stageService.removeTeamMultiple(params).subscribe((res: any) => {\r\n          if (res) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Success'),\r\n              text: this._translateService.instant('Team removed successfully'),\r\n              icon: 'success',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            }).then((result) => {\r\n              // if (result.isConfirmed) {\r\n              this._loadingService.show();\r\n              setTimeout(() => {\r\n                this.getTeamsByGroup();\r\n                this.getTeamsInStage();\r\n              }, 400);\r\n              // }\r\n            });\r\n          } else {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: 'Teams not removed',\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n  public getTeamsInStage() {\r\n    this._stageService.getTeamsInStage(this.stage.id).subscribe((res: any) => {\r\n      this.group_stages = res.data;\r\n      this.onDataChange.emit(this.group_stages);\r\n    });\r\n    this._loadingService.dismiss();\r\n  }\r\n\r\n  public rowActions: EZBtnActions[] = [\r\n    {\r\n      type: 'collection',\r\n      buttons: [\r\n        {\r\n          label: 'Change team',\r\n\r\n          icon: 'fa-regular fa-pen-to-square',\r\n        },\r\n        {\r\n          label: 'Delete',\r\n\r\n          icon: 'fa-regular fa-trash',\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  checkTeamAlreadyInStage(teams) {\r\n    return new Promise((resolve, reject) => {\r\n      // check if teams are already in stage\r\n      this._teamService\r\n        .getTeamListInStage(this.stage.id)\r\n        .subscribe((res: any) => {\r\n          let teams_in_stage: any = res.data;\r\n          teams_in_stage.sort((a, b) => a.id - b.id);\r\n          teams_in_stage = teams_in_stage.map((team) => team.id);\r\n          let list_teams_in_stage = teams_in_stage.join(',');\r\n          let list_teams = teams.join(',');\r\n          if (list_teams == list_teams_in_stage) {\r\n            console.log('teams already imported');\r\n            resolve(false);\r\n          } else {\r\n            // find the teams that are not in stage\r\n            let teams_not_in_stage = teams.filter(\r\n              (team) => !teams_in_stage.includes(team)\r\n            );\r\n            resolve(teams_not_in_stage);\r\n          }\r\n        });\r\n    });\r\n  }\r\n\r\n  sortBy(stage_teams) {\r\n    stage_teams.sort((a, b) => a.team.name.localeCompare(b.team.name));\r\n    return stage_teams;\r\n  }\r\n\r\n  importTeams() {\r\n    this._stageService\r\n      .getDataByTournament(this.tournament.id)\r\n      .subscribe((res: any) => {\r\n        let stages = res.data;\r\n        stages = stages.filter((stage) => stage.type == 'Groups');\r\n\r\n        Swal.fire({\r\n          title:\r\n            this._translateService.instant('Do you want to import teams from') +\r\n            ' ' +\r\n            this.tournament.name +\r\n            ' ' +\r\n            stages[0].name +\r\n            '? ',\r\n          showCancelButton: true,\r\n          cancelButtonText: this._translateService.instant('No'),\r\n          confirmButtonText: this._translateService.instant('Yes'),\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            let stage_id = stages[0].id;\r\n            // get teams from stages\r\n            this._teamService\r\n              .getTeamListInStage(stage_id)\r\n              .subscribe((res: any) => {\r\n                let teams: any = res.data;\r\n                teams.sort((a, b) => a.id - b.id);\r\n                teams = teams.map((team) => team.id);\r\n\r\n                // check if teams are already in stage\r\n                this.checkTeamAlreadyInStage(teams).then((res) => {\r\n                  if (res) {\r\n                    teams = res;\r\n                    // sort teams by name\r\n                    let params = new FormData();\r\n                    teams = teams.join(',');\r\n                    params.append('stage_id', this.stage.id);\r\n                    params.append('teams', teams);\r\n                    this._stageService\r\n                      .createTeamMultiple(params)\r\n                      .subscribe((res: any) => {\r\n                        if (res) {\r\n                          Swal.fire({\r\n                            title: 'Success',\r\n                            text: this._translateService.instant('Teams imported successfully'),\r\n                            icon: 'success',\r\n                            confirmButtonText:\r\n                              this._translateService.instant('OK'),\r\n                            confirmButtonColor: '#3085d6',\r\n                          }).then((result) => {\r\n                            this.getTeamsByGroup();\r\n\r\n                            this._loadingService.show();\r\n                            setTimeout(() => {\r\n                              this._stageService\r\n                                .getTeamsInStage(this.stage.id)\r\n                                .subscribe((res: any) => {\r\n                                  this.group_stages = res.data;\r\n\r\n                                  this._loadingService.dismiss();\r\n                                });\r\n                            }, 400);\r\n                          });\r\n                        } else {\r\n                          Swal.fire({\r\n                            title: 'Error',\r\n                            text: this._translateService.instant('Teams not imported'),\r\n                            icon: 'error',\r\n                            confirmButtonText:\r\n                              this._translateService.instant('OK'),\r\n                            confirmButtonColor: '#3085d6',\r\n                          });\r\n                        }\r\n                      });\r\n                  } else {\r\n                    Swal.fire({\r\n                      title: 'Error',\r\n                      text: this._translateService.instant('No teams available to add'),\r\n                      icon: 'error',\r\n                      confirmButtonText: this._translateService.instant('OK'),\r\n                      confirmButtonColor: '#3085d6',\r\n                    });\r\n                  }\r\n                });\r\n              });\r\n          }\r\n        });\r\n      });\r\n  }\r\n\r\n  groupStage;\r\n  openModalEdit(content, groupStage) {\r\n    this.groupStage = groupStage;\r\n    if (this.groupStage.group) {\r\n      this.model = {\r\n        group_name: this.groupStage.group,\r\n      };\r\n    }\r\n    this._modalService\r\n      .open(content, {\r\n        ariaLabelledBy: 'modal-basic-title',\r\n      })\r\n      .result.then(\r\n        (result) => {\r\n          this.groupStage = null;\r\n        },\r\n        (reason) => {\r\n          this.groupStage = null;\r\n        }\r\n      );\r\n  }\r\n\r\n  onEdit(model) {\r\n    this.groupStage;\r\n    \r\n    if (this.form.invalid || !this.groupStage) return;\r\n    let params = new FormData();\r\n    params.append('stage_id', this.groupStage.stage_id);\r\n    params.append('old_group', this.groupStage.group.toString());\r\n    params.append('group', model.group_name);\r\n    params.append('all_teams_in_groups', 'true');\r\n\r\n    this._loadingService.show();\r\n    this.editGroup(params).then((res) => {\r\n      this.getTeamsInStage();\r\n      this._modalService.dismissAll();\r\n      },\r\n      (error) => {  \r\n        if (error.errors){\r\n          this.form.controls[\"group_name\"].setErrors({\r\n            serverError: error.message,\r\n          });\r\n        }\r\n      }\r\n    )\r\n  }\r\n\r\n  private editGroup(params) {\r\n\r\n     return this._stageService.editGroup(params).toPromise().then((res: any) => {\r\n      if (res) {\r\n        return res;\r\n      }\r\n    }\r\n  )\r\n  }\r\n}\r\n", "<div class=\"card\">\r\n  <div class=\"card-header\">\r\n    <h3 class=\"card-title\">{{ 'Stage Teams' | translate }}</h3>\r\n    <div></div>\r\n    <div class=\"float-right\" *ngIf=\"\r\n              stage_type == AppConfig.TOURNAMENT_TYPES.league ||\r\n              stage_type == AppConfig.TOURNAMENT_TYPES.groups\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <div class=\"mr-2\" *ngIf=\"stage_type == AppConfig.TOURNAMENT_TYPES.groups\" style=\"font-size: 1rem; color: #6c757d; font-weight: 500; opacity: 0.7;\">\r\n              {{ 'Added ' + (group_stages.length ?? 0) + '/' + tournament.no_of_groups + ' groups' }}      \r\n          </div>\r\n          <button class=\"btn btn-primary \"  (click)=\"addGroupTeam()\" > \r\n            <i class=\"fa fa-plus\"></i>\r\n            <!-- space -->\r\n            {{\r\n            stage_type == AppConfig.TOURNAMENT_TYPES.league\r\n            ? _translateService.instant('Add Team')\r\n            : stage_type == AppConfig.TOURNAMENT_TYPES.groups\r\n            ? _translateService.instant('Add Team & Group')\r\n            : _translateService.instant('Add Teams')\r\n            }}\r\n            <ng-container *ngIf=\"allowEditTeam == false\">\r\n              <span class=\"ml-1\" [ngbTooltip]=\"'Please remove match before add team'\">\r\n                <i class=\"fa fa-info-circle\"></i>\r\n              </span>\r\n            </ng-container>\r\n          </button>\r\n      </div>\r\n    </div>\r\n    \r\n    \r\n    <div class=\"float-right\" *ngIf=\"stage_type == 'Knockout' && tournament.type_knockout == AppConfig.KNOCKOUT_TYPES.type4\" [disabled]=\"allowEditTeam == false\">\r\n      <div class=\"d-flex align-items-center\">\r\n      <div class=\"mr-2\" style=\"font-size: 1rem; color: #6c757d; font-weight: 500; opacity: 0.7;\">\r\n        {{ 'Added ' + (group_stages[0]?.length ?? 0) + '/' + stage.team_number + ' teams' }}      \r\n      </div>\r\n      <button class=\"btn btn-primary mr-1\" (click)=\"openModalAddNumbTeam()\">\r\n        <i class=\"bi bi-people\"></i>\r\n        {{ 'Set number of teams' | translate }}\r\n      </button>\r\n      <button class=\"btn btn-primary\" (click)=\"addGroupTeam()\" [disabled]=\"stage.team_number == 0\">\r\n        <i class=\"fa fa-plus\"></i>\r\n        {{ 'Add Team' | translate }}\r\n        <ng-container *ngIf=\"allowEditTeam == false\">\r\n          <span class=\"ml-1\" [ngbTooltip]=\"'Please remove match before add team'\">\r\n            <i class=\"fa fa-info-circle\"></i>\r\n          </span>\r\n        </ng-container>\r\n      </button>\r\n    </div>\r\n\r\n      <!-- button import group -->\r\n      <!-- <button class=\"btn btn-warning\" (click)=\"importTeams()\" [disabled]=\"allowEditTeam == false\">\r\n        <i class=\"fa-solid fa-arrow-up-from-line\"></i>\r\n        {{ 'Import From Groups' | translate }}\r\n        <ng-container *ngIf=\"allowEditTeam == false\">\r\n          <span class=\"ml-1\" [ngbTooltip]=\"'Please remove match before add team'\">\r\n            <i class=\"fa fa-info-circle\"></i>\r\n          </span>\r\n        </ng-container>\r\n      </button> -->\r\n    </div>\r\n    <!-- <span *ngIf=\"allowEditTeam==false\" ngbTooltip=\"Please delete the schedule before adding or removing teams\" placement=\"left\"\r\n    [ngbTooltip]=\"TooltipTemplate\" container=\"body\"></span> -->\r\n  </div>\r\n  <p class=\"pl-2 mb-0\" *ngIf=\"stage_type == 'Knockout' && tournament.type_knockout == AppConfig.KNOCKOUT_TYPES.type4\" style=\" font-size: 1rem; color: #6c757d; font-weight: 500; opacity: 0.7;\">\r\n    {{ 'Please specify the number of teams before adding teams.' | translate }}\r\n  </p>\r\n  <div class=\"card-body\">\r\n    <!-- center the container -->\r\n    <div class=\"row\">\r\n      <!-- group stages -->\r\n      <div style=\"width: 100%; padding: 10px\" class=\"col-12 bg-light card p-1\"\r\n        *ngFor=\"let group_stage of group_stages; let i = index\">\r\n        <span *ngIf=\"stage_type == AppConfig.TOURNAMENT_TYPES.groups\" class=\"text-primary\">{{ 'group' | translate }} {{\r\n          group_stage[0].group }}\r\n          <i *ngIf=\"allowEditTeam == true\" style=\"z-index: 1; position: relative\"\r\n            class=\"fa fa-trash float-right text-danger p-1\" (click)=\"removeGroup(group_stage)\" placement=\"left\"\r\n            ngbTooltip=\"{{'Remove Group'|translate}}\"></i>\r\n          <i style=\"z-index: 1; position: relative\" class=\"fa fa-edit float-right text-warning p-1\" placement=\"left\"\r\n            ngbTooltip=\"{{'Edit group'|translate}}\" (click)=\"openModalEdit(content,group_stage[0])\"></i>\r\n        </span>\r\n\r\n        <!-- if stagetype !=Group -->\r\n        <span *ngIf=\"stage_type != AppConfig.TOURNAMENT_TYPES.groups\" class=\"text-primary\">\r\n          <i *ngIf=\"allowEditTeam == true\" style=\"z-index: 1; position: relative\"\r\n            class=\"fa fa-trash fa-lg float-right text-danger\" (click)=\"removeAllTeam(group_stage)\" placement=\"left\"\r\n            [disabled]=\"allowEditTeam == false\" ngbTooltip=\"Remove all teams\"></i>\r\n        </span>\r\n\r\n        <div class=\"row d-flex justify-content-left\">\r\n          <div class=\"col-lg-3 col-md-6 col-sm-12 mt-1\" *ngFor=\"let stage of sortBy(group_stage); let j = index\">\r\n            <div class=\"rounded bg-white\">\r\n              <div class=\"row p-1 align-items-center\">\r\n                <div class=\"col-auto\">\r\n                  <img class=\"avatar avatar-sm bg-white rounded-0\" src=\"{{ stage.team.club.logo }}\" alt=\"logo\" width=\"60px\"\r\n                    height=\"60px\" />\r\n                </div>\r\n\r\n                <div class=\"col\">\r\n                  <h4 [innerHTML]=\"stage.team.name\" class=\"text-nowrap\"></h4>\r\n                </div>\r\n\r\n                <div class=\"col text-right\">\r\n                  <div class=\"col-auto p-0 m-0\" ngbDropdown container=\"body\">\r\n                    <button type=\"button\" class=\"btn hide-arrow p-0 text-secondary\" [style]=\"btnStyle\" ngbDropdownToggle\r\n                      data-toggle=\"dropdown\">\r\n                      <i class=\"fa-regular fa-ellipsis-vertical\"></i>\r\n                    </button>\r\n                    <div ngbDropdownMenu>\r\n                      <ng-container>\r\n                        <a ngbDropdownItem (click)=\"changeTeam(stage, 'edit')\" [style]=\"btnStyle\">\r\n                          <i class=\"fa-light fa-arrows-repeat fa-lg mr-1\" placement=\"left\"\r\n                            ngbTooltip=\"Change another team\"></i>\r\n                          <span>{{ 'Change Team' | translate }}</span>\r\n                        </a>\r\n                      </ng-container>\r\n\r\n                      <ng-container>\r\n                        <a *ngIf=\"allowEditTeam == true\" ngbDropdownItem (click)=\"removeTeam(stage, 'remove')\"\r\n                          [style]=\"btnStyle\">\r\n                          <i id=\"trash\" class=\"fal fa-trash fa-lg mr-1\" (click)=\"removeTeam(stage, 'remove')\"\r\n                            placement=\"left\" ngbTooltip=\"Remove this team\"></i>\r\n                          <span>{{ 'Remove Team' | translate }}</span>\r\n                        </a>\r\n                      </ng-container>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- remove group -->\r\n      </div>\r\n      <!-- repeat the above div two more times for each row -->\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<ng-template #content let-modal>\r\n  <form [formGroup]=\"form\" (ngSubmit)=\"onEdit(model)\">\r\n    <div class=\"modal-header\">\r\n      <h4 class=\"modal-title\" id=\"modal-basic-title\">{{'Edit group' |translate }}</h4>\r\n      <button type=\"button\" class=\"close\" (click)=\"modal.dismiss('Cross click')\" aria-label=\"Close\">\r\n        <span aria-hidden=\"true\">&times;</span>\r\n      </button>\r\n    </div>\r\n    <div class=\"modal-body\">\r\n      <div class=\"text-center\">\r\n        <img src=\"assets/images/ai/Frame.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n        <p>\r\n          {{'Enter group name' | translate}}\r\n        </p>\r\n      </div>\r\n      <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n    </div>\r\n    <div class=\"modal-footer\">\r\n      <button type=\"submit\" class=\"btn btn-success\">{{'Save' | translate}}</button>\r\n    </div>\r\n  </form>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}