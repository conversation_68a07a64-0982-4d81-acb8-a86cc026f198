{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class ScheduleViewComponent {\n  constructor(_translateService) {\n    this._translateService = _translateService;\n  }\n  ngOnInit() {\n    // Schedule view implementation will go here\n  }\n  static #_ = this.ɵfac = function ScheduleViewComponent_Factory(t) {\n    return new (t || ScheduleViewComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ScheduleViewComponent,\n    selectors: [[\"app-schedule-view\"]],\n    inputs: {\n      seasonId: \"seasonId\",\n      clubId: \"clubId\",\n      tournamentId: \"tournamentId\",\n      dateRange: \"dateRange\",\n      matchStatus: \"matchStatus\",\n      seasons: \"seasons\",\n      clubs: \"clubs\",\n      tournaments: \"tournaments\"\n    },\n    decls: 8,\n    vars: 6,\n    consts: [[1, \"p-2\"], [1, \"text-center\"]],\n    template: function ScheduleViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\");\n        i0.ɵɵtext(3);\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"Schedule Matches Content\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, \"Coming soon...\"));\n      }\n    },\n    dependencies: [i1.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";;AAQA,OAAM,MAAOA,qBAAqB;EAUhCC,YACSC,iBAAmC;IAAnC,KAAAA,iBAAiB,GAAjBA,iBAAiB;EACvB;EAEHC,QAAQA,CAAA;IACN;EAAA;EACD,QAAAC,CAAA;qBAhBUJ,qBAAqB,EAAAK,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;UAArBT,qBAAqB;IAAAU,SAAA;IAAAC,MAAA;MAAAC,QAAA;MAAAC,MAAA;MAAAC,YAAA;MAAAC,SAAA;MAAAC,WAAA;MAAAC,OAAA;MAAAC,KAAA;MAAAC,WAAA;IAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRlCpB,EAAA,CAAAsB,cAAA,aAAiB;QAETtB,EAAA,CAAAuB,MAAA,GAAwC;;QAAAvB,EAAA,CAAAwB,YAAA,EAAK;QACjDxB,EAAA,CAAAsB,cAAA,QAAG;QAAAtB,EAAA,CAAAuB,MAAA,GAA8B;;QAAAvB,EAAA,CAAAwB,YAAA,EAAI;;;QADjCxB,EAAA,CAAAyB,SAAA,GAAwC;QAAxCzB,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAA2B,WAAA,mCAAwC;QACzC3B,EAAA,CAAAyB,SAAA,GAA8B;QAA9BzB,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAA2B,WAAA,yBAA8B", "names": ["ScheduleViewComponent", "constructor", "_translateService", "ngOnInit", "_", "i0", "ɵɵdirectiveInject", "i1", "TranslateService", "_2", "selectors", "inputs", "seasonId", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "matchStatus", "seasons", "clubs", "tournaments", "decls", "vars", "consts", "template", "ScheduleViewComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\schedule-view\\schedule-view.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\schedule-view\\schedule-view.component.html"], "sourcesContent": ["import { Component, OnInit, Input } from '@angular/core';\nimport { TranslateService } from '@ngx-translate/core';\n\n@Component({\n  selector: 'app-schedule-view',\n  templateUrl: './schedule-view.component.html',\n  styleUrls: ['./schedule-view.component.scss']\n})\nexport class ScheduleViewComponent implements OnInit {\n  @Input() seasonId: any;\n  @Input() clubId: any;\n  @Input() tournamentId: any;\n  @Input() dateRange: any;\n  @Input() matchStatus: string;\n  @Input() seasons: any[];\n  @Input() clubs: any[];\n  @Input() tournaments: any[];\n\n  constructor(\n    public _translateService: TranslateService\n  ) {}\n\n  ngOnInit(): void {\n    // Schedule view implementation will go here\n  }\n}\n", "<div class=\"p-2\">\n  <div class=\"text-center\">\n    <h4>{{'Schedule Matches Content'|translate}}</h4>\n    <p>{{'Coming soon...'|translate}}</p>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}