{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport { AppConfig } from 'app/app-config';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"app/services/tournament.service\";\nexport class ModalUpdateScoreComponent {\n  constructor(_translateService, _modalService, _tournamentService) {\n    this._translateService = _translateService;\n    this._modalService = _modalService;\n    this._tournamentService = _tournamentService;\n    this.onUpdated = new EventEmitter();\n    this.fields = [];\n    this.form = new FormGroup({});\n  }\n  ngOnInit() {\n    this.loadInitSettings();\n    this.model = {\n      id: this.match.id,\n      home_score: this.match.home_score,\n      away_score: this.match.away_score,\n      home_penalty: this.match.home_penalty,\n      away_penalty: this.match.away_penalty\n    };\n    this.fields = [{\n      key: 'type',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      },\n      defaultValue: this.stage_type\n    }, {\n      key: 'home_score',\n      type: 'core-touchspin',\n      props: {\n        label: this.match.home_team_name,\n        type: 'number',\n        min: 0,\n        max: 999,\n        step: 1\n      },\n      defaultValue: 0\n    }, {\n      key: 'away_score',\n      type: 'core-touchspin',\n      props: {\n        label: this.match.away_team_name,\n        type: 'number',\n        min: 0,\n        max: 999,\n        step: 1\n      },\n      defaultValue: 0\n    }, {\n      key: 'home_penalty',\n      type: 'core-touchspin',\n      props: {\n        label: `${this.match.home_team_name} ${this._translateService.instant('penalty score')}`,\n        type: 'number',\n        min: 0,\n        max: 100,\n        step: 1\n      },\n      expressions: {\n        hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\n      }\n    }, {\n      key: 'away_penalty',\n      type: 'core-touchspin',\n      props: {\n        label: `${this.match.away_team_name} ${this._translateService.instant('penalty score')}`,\n        type: 'number',\n        min: 0,\n        max: 100,\n        step: 1\n      },\n      expressions: {\n        hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\n      }\n    }];\n  }\n  loadInitSettings() {\n    const settings = localStorage.getItem('initSettings');\n    if (settings) {\n      this.initSettings = JSON.parse(settings);\n      console.log('initSettings:', this.initSettings);\n    } else {\n      console.log('No initSettings found in localStorage');\n    }\n  }\n  onSubmit(model) {\n    if (this.form.invalid) {\n      return;\n    }\n    this._tournamentService.updateMatch(model, 'updateScore').subscribe(res => {\n      console.log(res);\n      this.onUpdated.emit(res);\n      this.close();\n    }, ({\n      error\n    }) => {\n      Swal.fire({\n        title: 'Error!',\n        text: error,\n        icon: 'error',\n        confirmButtonText: 'Ok'\n      });\n      this.close();\n    });\n  }\n  close() {\n    this._modalService.dismissAll();\n  }\n  static #_ = this.ɵfac = function ModalUpdateScoreComponent_Factory(t) {\n    return new (t || ModalUpdateScoreComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.TournamentService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalUpdateScoreComponent,\n    selectors: [[\"app-modal-update-score\"]],\n    inputs: {\n      stage_type: \"stage_type\",\n      match: \"match\"\n    },\n    outputs: {\n      onUpdated: \"onUpdated\"\n    },\n    decls: 19,\n    vars: 13,\n    consts: [[1, \"modal-header\", \"bg-white\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"text-center\"], [\"src\", \"assets/images/alerts/update_score.png\", \"alt\", \"\", 2, \"width\", \"80%\", \"max-width\", \"250px\"], [\"id\", \"label_updatescore\", 1, \"modal-title\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\"]],\n    template: function ModalUpdateScoreComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n        i0.ɵɵlistener(\"click\", function ModalUpdateScoreComponent_Template_button_click_1_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵelementStart(2, \"span\", 2);\n        i0.ɵɵtext(3, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(4, \"form\", 3);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalUpdateScoreComponent_Template_form_ngSubmit_4_listener() {\n          return ctx.onSubmit(ctx.model);\n        });\n        i0.ɵɵelementStart(5, \"div\", 4);\n        i0.ɵɵelement(6, \"img\", 5);\n        i0.ɵɵelementStart(7, \"h4\", 6);\n        i0.ɵɵtext(8);\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"h3\");\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(14, \"formly-form\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"div\", 9)(16, \"button\", 10);\n        i0.ɵɵtext(17);\n        i0.ɵɵpipe(18, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 7, \"Update Score\"), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 9, \"Score\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"model\", ctx.model);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 11, \"Save\"), \" \");\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,SAAS,QAAQ,gBAAgB;AAG1C,SAASC,SAAS,QAAQ,gBAAgB;AAG1C,OAAOC,IAAI,MAAM,aAAa;;;;;AAO9B,OAAM,MAAOC,yBAAyB;EAOpCC,YACSC,iBAAmC,EACnCC,aAAuB,EACvBC,kBAAqC;IAFrC,KAAAF,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAPjB,KAAAC,SAAS,GAAG,IAAIT,YAAY,EAAE;IACxC,KAAAU,MAAM,GAAG,EAAE;IAUX,KAAAC,IAAI,GAAG,IAAIV,SAAS,CAAC,EAAE,CAAC;EAFxB;EAKAW,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,KAAK,GAAG;MACXC,EAAE,EAAE,IAAI,CAACC,KAAK,CAACD,EAAE;MACjBE,UAAU,EAAE,IAAI,CAACD,KAAK,CAACC,UAAU;MACjCC,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;MACjCC,YAAY,EAAE,IAAI,CAACH,KAAK,CAACG,YAAY;MACrCC,YAAY,EAAE,IAAI,CAACJ,KAAK,CAACI;KAC1B;IACD,IAAI,CAACV,MAAM,GAAG,CACZ;MACEW,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;OACP;MACDE,YAAY,EAAE,IAAI,CAACC;KACpB,EACD;MACEJ,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLG,KAAK,EAAE,IAAI,CAACV,KAAK,CAACW,cAAc;QAChCL,IAAI,EAAE,QAAQ;QACdM,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE;OACP;MACDN,YAAY,EAAE;KACf,EACD;MACEH,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLG,KAAK,EAAE,IAAI,CAACV,KAAK,CAACe,cAAc;QAChCT,IAAI,EAAE,QAAQ;QACdM,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE;OACP;MACDN,YAAY,EAAE;KACf,EACD;MACEH,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLG,KAAK,EAAE,GAAG,IAAI,CAACV,KAAK,CAACW,cAAc,IAAI,IAAI,CAACrB,iBAAiB,CAAC0B,OAAO,CACnE,eAAe,CAChB,EAAE;QACHV,IAAI,EAAE,QAAQ;QACdM,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE;OACP;MACDG,WAAW,EAAE;QACXC,IAAI,EAAE,kBAAkBhC,SAAS,CAACiC,gBAAgB,CAACC,QAAQ,iDAAiD,IAAI,CAACC,YAAY,CAACC,UAAU;;KAE3I,EACD;MACEjB,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLG,KAAK,EAAE,GAAG,IAAI,CAACV,KAAK,CAACe,cAAc,IAAI,IAAI,CAACzB,iBAAiB,CAAC0B,OAAO,CACnE,eAAe,CAChB,EAAE;QACHV,IAAI,EAAE,QAAQ;QACdM,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE;OACP;MACDG,WAAW,EAAE;QACXC,IAAI,EAAE,kBAAkBhC,SAAS,CAACiC,gBAAgB,CAACC,QAAQ,iDAAiD,IAAI,CAACC,YAAY,CAACC,UAAU;;KAE3I,CACF;EACH;EAEAzB,gBAAgBA,CAAA;IACd,MAAM0B,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACrD,IAAIF,QAAQ,EAAE;MACZ,IAAI,CAACF,YAAY,GAAGK,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;MACxCK,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACR,YAAY,CAAC;KAChD,MAAM;MACLO,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;EAEAC,QAAQA,CAAChC,KAAK;IACZ,IAAI,IAAI,CAACH,IAAI,CAACoC,OAAO,EAAE;MACrB;;IAEF,IAAI,CAACvC,kBAAkB,CAACwC,WAAW,CAAClC,KAAK,EAAE,aAAa,CAAC,CAACmC,SAAS,CAAEC,GAAG,IAAI;MAC1EN,OAAO,CAACC,GAAG,CAACK,GAAG,CAAC;MAChB,IAAI,CAACzC,SAAS,CAAC0C,IAAI,CAACD,GAAG,CAAC;MACxB,IAAI,CAACE,KAAK,EAAE;IACd,CAAC,EAAE,CAAC;MAAEC;IAAK,CAAE,KAAI;MACflD,IAAI,CAACmD,IAAI,CAAC;QACRC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAEH,KAAK;QACXI,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;MACF,IAAI,CAACN,KAAK,EAAE;IACd,CAAC,CAAC;EACJ;EAEAA,KAAKA,CAAA;IACH,IAAI,CAAC7C,aAAa,CAACoD,UAAU,EAAE;EACjC;EAAC,QAAAC,CAAA;qBA7HUxD,yBAAyB,EAAAyD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,QAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAAzBjE,yBAAyB;IAAAkE,SAAA;IAAAC,MAAA;MAAA9C,UAAA;MAAAT,KAAA;IAAA;IAAAwD,OAAA;MAAA/D,SAAA;IAAA;IAAAgE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdtCjB,EAAA,CAAAmB,cAAA,aAAmC;QACsBnB,EAAA,CAAAoB,UAAA,mBAAAC,2DAAA;UAAA,OAASH,GAAA,CAAA3B,KAAA,EAAO;QAAA,EAAC;QACtES,EAAA,CAAAmB,cAAA,cAAyB;QAAAnB,EAAA,CAAAsB,MAAA,aAAO;QAAAtB,EAAA,CAAAuB,YAAA,EAAO;QAG3CvB,EAAA,CAAAmB,cAAA,cAAsD;QAA7BnB,EAAA,CAAAoB,UAAA,sBAAAI,4DAAA;UAAA,OAAYN,GAAA,CAAAjC,QAAA,CAAAiC,GAAA,CAAAjE,KAAA,CAAe;QAAA,EAAC;QACnD+C,EAAA,CAAAmB,cAAA,aAAyB;QACvBnB,EAAA,CAAAyB,SAAA,aAAgG;QAChGzB,EAAA,CAAAmB,cAAA,YAA+C;QAC7CnB,EAAA,CAAAsB,MAAA,GACF;;QAAAtB,EAAA,CAAAuB,YAAA,EAAK;QAEPvB,EAAA,CAAAmB,cAAA,cAAkD;QAC5CnB,EAAA,CAAAsB,MAAA,IAAyB;;QAAAtB,EAAA,CAAAuB,YAAA,EAAK;QAClCvB,EAAA,CAAAyB,SAAA,sBAA2E;QAC7EzB,EAAA,CAAAuB,YAAA,EAAM;QACNvB,EAAA,CAAAmB,cAAA,cAA0B;QAEtBnB,EAAA,CAAAsB,MAAA,IACF;;QAAAtB,EAAA,CAAAuB,YAAA,EAAS;;;QAdPvB,EAAA,CAAA0B,SAAA,GAAkB;QAAlB1B,EAAA,CAAA2B,UAAA,cAAAT,GAAA,CAAApE,IAAA,CAAkB;QAIlBkD,EAAA,CAAA0B,SAAA,GACF;QADE1B,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,4BACF;QAGI7B,EAAA,CAAA0B,SAAA,GAAyB;QAAzB1B,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAA6B,WAAA,iBAAyB;QAChB7B,EAAA,CAAA0B,SAAA,GAAa;QAAb1B,EAAA,CAAA2B,UAAA,SAAAT,GAAA,CAAApE,IAAA,CAAa,WAAAoE,GAAA,CAAArE,MAAA,WAAAqE,GAAA,CAAAjE,KAAA;QAIxB+C,EAAA,CAAA0B,SAAA,GACF;QADE1B,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,sBACF", "names": ["EventEmitter", "FormGroup", "AppConfig", "<PERSON><PERSON>", "ModalUpdateScoreComponent", "constructor", "_translateService", "_modalService", "_tournamentService", "onUpdated", "fields", "form", "ngOnInit", "loadInitSettings", "model", "id", "match", "home_score", "away_score", "home_penalty", "away_penalty", "key", "type", "props", "defaultValue", "stage_type", "label", "home_team_name", "min", "max", "step", "away_team_name", "instant", "expressions", "hide", "TOURNAMENT_TYPES", "knockout", "initSettings", "sport_type", "settings", "localStorage", "getItem", "JSON", "parse", "console", "log", "onSubmit", "invalid", "updateMatch", "subscribe", "res", "emit", "close", "error", "fire", "title", "text", "icon", "confirmButtonText", "dismissAll", "_", "i0", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "NgbModal", "i3", "TournamentService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ModalUpdateScoreComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "ModalUpdateScoreComponent_Template_button_click_1_listener", "ɵɵtext", "ɵɵelementEnd", "ModalUpdateScoreComponent_Template_form_ngSubmit_4_listener", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\modal-update-score\\modal-update-score.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\modal-update-score\\modal-update-score.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { log } from 'console';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-modal-update-score',\r\n  templateUrl: './modal-update-score.component.html',\r\n  styleUrls: ['./modal-update-score.component.scss']\r\n})\r\nexport class ModalUpdateScoreComponent implements OnInit {\r\n  @Input() stage_type: any;\r\n  @Input() match: any;\r\n  @Output() onUpdated = new EventEmitter();\r\n  fields = [];\r\n  initSettings: any;\r\n\r\n  constructor(\r\n    public _translateService: TranslateService,\r\n    public _modalService: NgbModal,\r\n    public _tournamentService: TournamentService\r\n  ) {\r\n  }\r\n\r\n  form = new FormGroup({});\r\n  model: any;\r\n\r\n  ngOnInit(): void {\r\n    this.loadInitSettings();\r\n    this.model = {\r\n      id: this.match.id,\r\n      home_score: this.match.home_score,\r\n      away_score: this.match.away_score,\r\n      home_penalty: this.match.home_penalty,\r\n      away_penalty: this.match.away_penalty\r\n    };\r\n    this.fields = [\r\n      {\r\n        key: 'type',\r\n        type: 'input',\r\n        props: {\r\n          type: 'hidden'\r\n        },\r\n        defaultValue: this.stage_type\r\n      },\r\n      {\r\n        key: 'home_score',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this.match.home_team_name,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 999,\r\n          step: 1\r\n        },\r\n        defaultValue: 0\r\n      },\r\n      {\r\n        key: 'away_score',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this.match.away_team_name,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 999,\r\n          step: 1\r\n        },\r\n        defaultValue: 0\r\n      },\r\n      {\r\n        key: 'home_penalty',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: `${this.match.home_team_name} ${this._translateService.instant(\r\n            'penalty score'\r\n          )}`,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 100,\r\n          step: 1\r\n        },\r\n        expressions: {\r\n          hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\r\n        }\r\n      },\r\n      {\r\n        key: 'away_penalty',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: `${this.match.away_team_name} ${this._translateService.instant(\r\n            'penalty score'\r\n          )}`,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 100,\r\n          step: 1\r\n        },\r\n        expressions: {\r\n          hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\r\n        }\r\n      }\r\n    ];\r\n  }\r\n\r\n  loadInitSettings() {\r\n    const settings = localStorage.getItem('initSettings');\r\n    if (settings) {\r\n      this.initSettings = JSON.parse(settings);\r\n      console.log('initSettings:', this.initSettings);\r\n    } else {\r\n      console.log('No initSettings found in localStorage');\r\n    }\r\n  }\r\n\r\n  onSubmit(model) {\r\n    if (this.form.invalid) {\r\n      return;\r\n    }\r\n    this._tournamentService.updateMatch(model, 'updateScore').subscribe((res) => {\r\n      console.log(res);\r\n      this.onUpdated.emit(res);\r\n      this.close();\r\n    }, ({ error }) => {\r\n      Swal.fire({\r\n        title: 'Error!',\r\n        text: error,\r\n        icon: 'error',\r\n        confirmButtonText: 'Ok'\r\n      });\r\n      this.close();\r\n    });\r\n  }\r\n\r\n  close() {\r\n    this._modalService.dismissAll();\r\n  }\r\n}\r\n", "<div class=\"modal-header bg-white\">\r\n  <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"close()\">\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form [formGroup]=\"form\" (ngSubmit)=\"onSubmit(model)\">\r\n  <div class=\"text-center\">\r\n    <img src=\"assets/images/alerts/update_score.png\" style=\"width: 80%; max-width: 250px;\" alt=\"\" />\r\n    <h4 class=\"modal-title\" id=\"label_updatescore\">\r\n      {{ 'Update Score' | translate }}\r\n    </h4>\r\n  </div>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <h3>{{ 'Score' | translate }}</h3>\r\n    <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"btn btn-primary\" rippleEffect>\r\n      {{ 'Save' | translate }}\r\n    </button>\r\n  </div>\r\n</form>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}