{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild, HostListener } from '@angular/core';\nimport { environment } from 'environments/environment';\nimport moment from 'moment';\nimport { NgbDate } from '@ng-bootstrap/ng-bootstrap';\nimport Swal from 'sweetalert2';\nlet LeagueReportsComponent = class LeagueReportsComponent {\n  constructor(route, _router, _commonsService, _http, _trans, renderer, _teamService, _modalService, _loadingService, _toastService, _registrationService, _clubService, _translateService, _coreSidebarService, _titleService, _exportService, _tournamentService, calendar, formatter) {\n    this.route = route;\n    this._router = _router;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._teamService = _teamService;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._toastService = _toastService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this._titleService = _titleService;\n    this._exportService = _exportService;\n    this._tournamentService = _tournamentService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false; // Track if we're in the middle of selecting range\n    this.matchStatus = 'all';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n      this.dtTrigger.next(this.dtOptions);\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n      // this.clubId = this.clubs[0];\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n    this.refreshDataTable();\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    var _this = this;\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      processing: true,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        // processing: `<div class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\n        //               <div class=\"spinner-border text-primary\" role=\"status\">\n        //                 <span class=\"sr-only\">Loading...</span>\n        //               </div>\n        //             </div>`,\n        processing: ``\n      },\n      ajax: (dataTablesParameters, callback) => {\n        // Build query parameters for all filters\n        let params = new URLSearchParams();\n        if (this.clubId != undefined && this.clubId !== null) {\n          params.append('club_id', this.clubId);\n        }\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\n          params.append('tournament_id', this.tournamentId);\n        }\n        if (this.dateRange.start_date) {\n          params.append('start_date', this.dateRange.start_date);\n        }\n        if (this.dateRange.end_date) {\n          params.append('end_date', this.dateRange.end_date);\n        }\n        if (this.matchStatus && this.matchStatus !== 'all') {\n          params.append('match_status', this.matchStatus);\n        }\n        const queryString = params.toString();\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${queryString ? '?' + queryString : ''}`;\n        this.isTableLoading = true;\n        this._http.post(url, dataTablesParameters).subscribe(resp => {\n          this.isTableLoading = false;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        }, error => {\n          this.isTableLoading = false;\n          console.error('Error loading table data:', error);\n          callback({\n            recordsTotal: 0,\n            recordsFiltered: 0,\n            data: []\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }],\n      columns: [{\n        title: this._translateService.instant('No.'),\n        data: null,\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: function (data, type, row, metadata) {\n          return metadata.row + 1;\n        }\n      }, {\n        title: this._translateService.instant('Tournament'),\n        data: 'name',\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: {\n          display: (data, type, row) => {\n            return data ?? 'TBD';\n          },\n          filter: (data, type, row) => {\n            return data;\n          }\n        }\n      }, {\n        title: this._translateService.instant('Round'),\n        data: 'round_name',\n        render: function (data) {\n          const displayData = data || 'TBD';\n          return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          const displayDate = !data || data === 'TBD' ? 'TBD' : moment(data).format('YYYY-MM-DD');\n          return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Home Team'),\n        data: 'home_team_name',\n        className: 'text-center'\n      }, {\n        title: 'VS',\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return 'vs';\n        }\n      }, {\n        title: this._translateService.instant('Away Team'),\n        data: 'away_team_name',\n        className: 'text-center'\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center'\n      }, {\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return '-';\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant('Export CSV')}`,\n          extend: 'csv',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              // Generate filename with current filter information\n              let filename = 'Matches_Report';\n              if (_this.seasonId) {\n                const season = _this.seasons?.find(s => s.id == _this.seasonId);\n                if (season) {\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.tournamentId) {\n                const tournament = _this.tournaments?.find(t => t.id == _this.tournamentId);\n                if (tournament) {\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.clubId) {\n                const club = _this.clubs?.find(c => c.id == _this.clubId);\n                if (club) {\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.matchStatus && _this.matchStatus !== 'all') {\n                filename += `_${_this.matchStatus}`;\n              }\n              if (_this.dateRange.start_date && _this.dateRange.end_date) {\n                filename += `_${_this.dateRange.start_date}_to_${_this.dateRange.end_date}`;\n              } else if (_this.dateRange.start_date) {\n                filename += `_from_${_this.dateRange.start_date}`;\n              }\n              filename += '.csv';\n              yield _this._exportService.exportCsv(data, filename);\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  setClubs(data) {\n    this.clubs = data;\n    // get field has key club_id\n    const clubField = this.fields.find(field => field.key === 'club_id');\n    // set options for club field\n    let current_clubs = [];\n    data.forEach(club => {\n      let club_name = this._translateService.instant(club.name);\n      current_clubs.push({\n        label: club_name,\n        value: club.id\n      });\n    });\n    clubField.props.options = current_clubs;\n  }\n  onCaptureEvent(event) {\n    // console.log(event);\n  }\n  editor(action, row) {\n    this.fields[0].defaultValue = this.clubId;\n    switch (action) {\n      case 'create':\n        this.fields[2].props.disabled = false;\n        break;\n      case 'edit':\n        this.fields[2].props.disabled = true;\n        break;\n      case 'remove':\n        break;\n      default:\n        break;\n    }\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('tournament_id')) {\n        let tournament_id = event.target.getAttribute('tournament_id');\n        let stage_id = event.target.getAttribute('stage_id');\n        //  navigate to path ':tournament_id/stages/:stage_id'\n        this._router.navigate([tournament_id, 'stages', stage_id], {\n          relativeTo: this.route\n        });\n      }\n    });\n  }\n  onSelectViewType(event) {\n    this.viewType = event;\n    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  refreshDataTable() {\n    if (this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    this.unlistener();\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n};\n__decorate([ViewChild('dateRangePicker', {\n  static: false\n})], LeagueReportsComponent.prototype, \"dateRangePicker\", void 0);\n__decorate([HostListener('document:click', ['$event'])], LeagueReportsComponent.prototype, \"onDocumentClick\", null);\nLeagueReportsComponent = __decorate([Component({\n  selector: 'app-league-reports',\n  templateUrl: './league-reports.component.html',\n  styleUrls: ['./league-reports.component.scss']\n})], LeagueReportsComponent);\nexport { LeagueReportsComponent };", "map": {"version": 3, "mappings": ";;AACA,SACEA,SAAS,EAGTC,SAAS,EACTC,YAAY,QAEP,eAAe;AAItB,SAASC,WAAW,QAAQ,0BAA0B;AAGtD,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAAsBC,OAAO,QAAgC,4BAA4B;AACzF,OAAOC,IAAI,MAAM,aAAa;AAOvB,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EA4BjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,eAA+B,EAC/BC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,YAAyB,EACzBC,aAAuB,EACvBC,eAA+B,EAC/BC,aAA4B,EAC5BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,mBAAuC,EACvCC,aAAoB,EACnBC,cAA6B,EAC9BC,kBAAqC,EACpCC,QAAqB,EACtBC,SAAiC;IAlBhC,KAAAlB,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IA1CX,KAAAC,MAAM,GAAQ,IAAI;IAClB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,SAAS,GAAQ;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAC,WAAW,GAAmB,IAAI;IAClC,KAAAC,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAC,WAAW,GAAY,KAAK,CAAC,CAAC;IAEvB,KAAAC,WAAW,GAAW,KAAK;IAMlC,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAK,CAAE,EACrC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,CAC3C;IAuBC,IAAI,CAAClB,aAAa,CAACmB,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC1B,eAAe,CAAC2B,IAAI,EAAE;IAC3B,IAAI,CAACzB,oBAAoB,CAAC0B,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACC,OAAO,GAAGD,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;MAClC,IAAI,CAACC,eAAe,EAAE,CAAC,CAAC;MACxB,IAAI,IAAI,CAACC,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;MAEJ,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC;IACrC,CAAC,EACAC,KAAK,IAAI;MACRtD,IAAI,CAACuD,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAC7C,iBAAiB,CAAC8C,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAAClD,eAAe,CAACmD,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAACjD,YAAY,CAACkD,WAAW,EAAE,CAACxB,SAAS,CACtCyB,GAAG,IAAI;MACN,IAAI,CAACC,KAAK,GAAGD,GAAG,CAACxB,IAAI;MACrB;MACA,IAAI,IAAI,CAACK,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;IAEN,CAAC,EACAI,KAAK,IAAI;MACRtD,IAAI,CAACuD,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAC7C,iBAAiB,CAAC8C,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAM,cAAcA,CAACC,MAAM;IACnB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAAC5B,QAAQ,GAAGyB,MAAM;MACtB,IAAI,CAACvB,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAACtB,YAAY,GAAG,IAAI;MACxB,IAAI,CAACiD,gBAAgB,EAAE;MACvBF,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACL,MAAM;IACjBM,OAAO,CAACC,GAAG,CAAC,iBAAiBP,MAAM,EAAE,CAAC;IACtC,IAAI,CAAC9C,MAAM,GAAG8C,MAAM;IACpB,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAI,kBAAkBA,CAACR,MAAM;IACvBM,OAAO,CAACC,GAAG,CAAC,uBAAuBP,MAAM,EAAE,CAAC;IAC5C,IAAI,CAAC7C,YAAY,GAAG6C,MAAM;IAC1B,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAK,mBAAmBA,CAACT,MAAM;IACxBM,OAAO,CAACC,GAAG,CAAC,wBAAwBP,MAAM,EAAE,CAAC;IAC7C,IAAI,CAACpC,WAAW,GAAGoC,MAAM;IACzB,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAM,eAAeA,CAACC,IAAa;IAE3B,IAAI,CAAC,IAAI,CAAClD,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClC4C,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAAC9C,QAAQ,GAAGkD,IAAI;MACpB,IAAI,CAAChD,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZiD,IAAI,IACJA,IAAI,CAACC,KAAK,CAAC,IAAI,CAACnD,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGiD,IAAI;MAClB,IAAI,CAAChD,WAAW,GAAG,KAAK;MACxB,IAAI,CAACkD,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAACC,KAAK,EAAE;UACtD,IAAI,CAACD,eAAe,CAACC,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACLV,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAAC7C,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAGkD,IAAI;MACpB,IAAI,CAAChD,WAAW,GAAG,IAAI;;EAE3B;EAEAsD,cAAcA,CAAA;IACZ,IAAI,CAACtD,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACoD,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACG,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGP,UAAU,CAAC,MAAK;MACzC,IAAI,CAACS,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACL,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACS,MAAM,IAC5B,CAAC,IAAI,CAACT,eAAe,CAACS,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAAC7D,WAAW,EAAE;MACpB;;IAGF,MAAM8D,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACV,eAAe,CAACC,KAAK,EAAE;EAC9B;EAEAkB,cAAcA,CAAA;IACZ,IAAI,CAACzE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACkD,eAAe,EAAE;EACxB;EAEAsB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAACrF,SAAS,CAACsF,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAACtF,QAAQ,CAACwF,OAAO,CAAC7G,OAAO,CAAC8G,IAAI,CAACH,MAAM,CAAC,CAAC,GACxD3G,OAAO,CAAC8G,IAAI,CAACH,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEAM,SAASA,CAAC/B,IAAa;IACrB,OACE,IAAI,CAAClD,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAACF,WAAW,IAChBmD,IAAI,CAACC,KAAK,CAAC,IAAI,CAACnD,QAAQ,CAAC,IACzBkD,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACnF,WAAW,CAAC;EAEjC;EAEAoF,QAAQA,CAACjC,IAAa;IACpB,OAAO,IAAI,CAACjD,MAAM,IAAIiD,IAAI,CAACC,KAAK,CAAC,IAAI,CAACnD,QAAQ,CAAC,IAAIkD,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACjF,MAAM,CAAC;EAC7E;EAEAmF,OAAOA,CAAClC,IAAa;IACnB,OACEA,IAAI,CAACmC,MAAM,CAAC,IAAI,CAACrF,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAIiD,IAAI,CAACmC,MAAM,CAAC,IAAI,CAACpF,MAAM,CAAE,IACzC,IAAI,CAACkF,QAAQ,CAACjC,IAAI,CAAC,IACnB,IAAI,CAAC+B,SAAS,CAAC/B,IAAI,CAAC;EAExB;EAEAoC,iBAAiBA,CAAA;IACfzC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACnD,SAAS,CAAC;IACjD,IAAI,CAACgD,gBAAgB,EAAE;EACzB;EAEAS,eAAeA,CAAA;IACb,IAAI,IAAI,CAACpD,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAMsF,UAAU,GAAGtH,MAAM,CACvB,GAAG,IAAI,CAAC+B,QAAQ,CAACwF,IAAI,IAAI,IAAI,CAACxF,QAAQ,CAACyF,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC3F,QAAQ,CAAC4F,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAME,QAAQ,GAAG5H,MAAM,CACrB,GAAG,IAAI,CAACgC,MAAM,CAACuF,IAAI,IAAI,IAAI,CAACvF,MAAM,CAACwF,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC1F,MAAM,CAAC2F,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAAChG,SAAS,CAACC,UAAU,GAAG2F,UAAU,CAACO,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACnG,SAAS,CAACE,QAAQ,GAAGgG,QAAQ,CAACC,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAAChG,cAAc,GAAG,GAAGyF,UAAU,CAACO,MAAM,CACxC,cAAc,CACf,MAAMD,QAAQ,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACR,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAACtF,QAAQ,EAAE;MACxB,MAAMuF,UAAU,GAAGtH,MAAM,CACvB,GAAG,IAAI,CAAC+B,QAAQ,CAACwF,IAAI,IAAI,IAAI,CAACxF,QAAQ,CAACyF,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC3F,QAAQ,CAAC4F,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAAChG,SAAS,CAACC,UAAU,GAAG2F,UAAU,CAACO,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACnG,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAGyF,UAAU,CAACO,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAACnG,SAAS,CAACC,UAAU,GAAG,IAAI;MAChC,IAAI,CAACD,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACwF,iBAAiB,EAAE;;EAE5B;EAEAtE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAACrC,KAAK,CACPsH,GAAG,CACF,GAAG/H,WAAW,CAACgI,MAAM,YAAY,IAAI,CAAClF,QAAQ,+BAA+B,CAC9E,CACAH,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAACqF,WAAW,GAAGrF,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAa,KAAK,IAAI;QACRtD,IAAI,CAACuD,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAAC7C,iBAAiB,CAAC8C,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAkE,QAAQA,CAAA;IAAA,IAAAC,KAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAAC3H,MAAM,CAACsD,OAAO,CAAC,gBAAgB,CAAC;MAClDsE,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAAChI,MAAM,CAACsD,OAAO,CAAC,MAAM,CAAC;UACjC2E,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAAChI,MAAM,CAACsD,OAAO,CAAC,aAAa,CAAC;UACxC2E,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAAChI,MAAM,CAACsD,OAAO,CAAC,gBAAgB,CAAC;UAC3C2E,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACnG,iBAAiB,EAAE;IACxB,IAAI,CAAC0B,SAAS,EAAE;IAChB,IAAI,CAACV,SAAS,GAAG;MACfoF,GAAG,EAAE,IAAI,CAACpI,eAAe,CAACqI,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;QACR,GAAG,IAAI,CAACzI,eAAe,CAACqI,iBAAiB,CAACK,IAAI;QAC9C;QACA;QACA;QACA;QAEA;QACAF,UAAU,EAAE;OACb;MACD5F,IAAI,EAAEA,CAAC+F,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAIC,MAAM,GAAG,IAAIC,eAAe,EAAE;QAElC,IAAI,IAAI,CAAC7H,MAAM,IAAI8H,SAAS,IAAI,IAAI,CAAC9H,MAAM,KAAK,IAAI,EAAE;UACpD4H,MAAM,CAACG,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC/H,MAAM,CAAC;;QAGvC,IAAI,IAAI,CAACC,YAAY,IAAI6H,SAAS,IAAI,IAAI,CAAC7H,YAAY,KAAK,IAAI,EAAE;UAChE2H,MAAM,CAACG,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC9H,YAAY,CAAC;;QAGnD,IAAI,IAAI,CAACC,SAAS,CAACC,UAAU,EAAE;UAC7ByH,MAAM,CAACG,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC7H,SAAS,CAACC,UAAU,CAAC;;QAGxD,IAAI,IAAI,CAACD,SAAS,CAACE,QAAQ,EAAE;UAC3BwH,MAAM,CAACG,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC7H,SAAS,CAACE,QAAQ,CAAC;;QAGpD,IAAI,IAAI,CAACM,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,KAAK,EAAE;UAClDkH,MAAM,CAACG,MAAM,CAAC,cAAc,EAAE,IAAI,CAACrH,WAAW,CAAC;;QAGjD,MAAMsH,WAAW,GAAGJ,MAAM,CAAC3B,QAAQ,EAAE;QACrC,MAAMgC,GAAG,GAAG,GAAG1J,WAAW,CAACgI,MAAM,YAAY,IAAI,CAAClF,QAAQ,WACxD2G,WAAW,GAAG,GAAG,GAAGA,WAAW,GAAG,EACpC,EAAE;QAEF,IAAI,CAACE,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAClJ,KAAK,CAACmJ,IAAI,CAAMF,GAAG,EAAEP,oBAAoB,CAAC,CAACxG,SAAS,CACtDkH,IAAS,IAAI;UACZ,IAAI,CAACF,cAAc,GAAG,KAAK;UAC3BP,QAAQ,CAAC;YACPU,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCnH,IAAI,EAAEiH,IAAI,CAACjH;WACZ,CAAC;QACJ,CAAC,EACAa,KAAK,IAAI;UACR,IAAI,CAACkG,cAAc,GAAG,KAAK;UAC3B9E,OAAO,CAACpB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD2F,QAAQ,CAAC;YACPU,YAAY,EAAE,CAAC;YACfC,eAAe,EAAE,CAAC;YAClBnH,IAAI,EAAE;WACP,CAAC;QACJ,CAAC,CACF;MACH,CAAC;MACDoH,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CAAC;MACpDC,OAAO,EAAE,CACP;QACE1G,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,KAAK,CAAC;QAC5CpB,IAAI,EAAE,IAAI;QACV0H,SAAS,EAAE,oBAAoB;QAC/B9B,IAAI,EAAE,YAAY;QAClB+B,MAAM,EAAE,SAAAA,CAAU3H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG,EAAEC,QAAQ;UAEzC,OAAOA,QAAQ,CAACD,GAAG,GAAG,CAAC;QACzB;OACD,EACD;QACE7G,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,YAAY,CAAC;QACnDpB,IAAI,EAAE,MAAM;QACZ0H,SAAS,EAAE,oBAAoB;QAC/B9B,IAAI,EAAE,YAAY;QAClB+B,MAAM,EAAE;UACNG,OAAO,EAAEA,CAAC9H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG,KAAI;YAC3B,OAAO5H,IAAI,IAAI,KAAK;UACtB,CAAC;UACD+H,MAAM,EAAEA,CAAC/H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG,KAAI;YAC1B,OAAO5H,IAAI;UACb;;OAEH,EACD;QACEe,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,OAAO,CAAC;QAC9CpB,IAAI,EAAE,YAAY;QAClB2H,MAAM,EAAE,SAAAA,CAAU3H,IAAI;UACpB,MAAMgI,WAAW,GAAGhI,IAAI,IAAI,KAAK;UACjC,OAAO,uDAAuDgI,WAAW,QAAQ;QACnF;OACD,EACD;QACEjH,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,MAAM,CAAC;QAC7CpB,IAAI,EAAE,YAAY;QAClB0H,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAU3H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG;UAC/B,MAAMK,WAAW,GACf,CAACjI,IAAI,IAAIA,IAAI,KAAK,KAAK,GACnB,KAAK,GACL3C,MAAM,CAAC2C,IAAI,CAAC,CAACkF,MAAM,CAAC,YAAY,CAAC;UACvC,OAAO,4DAA4D+C,WAAW,QAAQ;QACxF;OACD,EACD;QACElH,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,YAAY,CAAC;QACnDpB,IAAI,EAAE,YAAY;QAClB0H,SAAS,EAAE,aAAa;QAExBC,MAAM,EAAE,SAAAA,CAAS3H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG;UAC9B,IAAI,CAAC5H,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAO3C,MAAM,CAAC2C,IAAI,CAAC,CAACkF,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACEnE,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,UAAU,CAAC;QACjDpB,IAAI,EAAE,UAAU;QAChB0H,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS3H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG;UAC9B,IAAI,CAAC5H,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAO3C,MAAM,CAAC2C,IAAI,CAAC,CAACkF,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACEnE,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,UAAU,CAAC;QACjDpB,IAAI,EAAE,UAAU;QAChB2H,MAAM,EAAE,SAAAA,CAAS3H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG;UAC9B,IAAI,CAAC5H,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOA,IAAI;QACb;OACD,EACD;QACEe,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,WAAW,CAAC;QAClDpB,IAAI,EAAE,gBAAgB;QACtB0H,SAAS,EAAE;OACZ,EACD;QACE3G,KAAK,EAAE,IAAI;QACXf,IAAI,EAAE,IAAI;QACV0H,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS3H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG;UAC9B,OAAO,IAAI;QACb;OACD,EACD;QACE7G,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,WAAW,CAAC;QAClDpB,IAAI,EAAE,gBAAgB;QACtB0H,SAAS,EAAE;OACZ,EACD;QACE3G,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,YAAY,CAAC;QACnDpB,IAAI,EAAE,YAAY;QAClB0H,SAAS,EAAE;OACZ,EACD;QACE1H,IAAI,EAAE,IAAI;QACV0H,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS3H,IAAI,EAAE4F,IAAI,EAAEgC,GAAG;UAC9B,OAAO,GAAG;QACZ;OACD,EACD;QACE7G,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC8C,OAAO,CAAC,YAAY,CAAC;QACnDpB,IAAI,EAAE,YAAY;QAClB0H,SAAS,EAAE;OACZ,CACF;MACDQ,OAAO,EAAE;QACPlC,GAAG,EAAE,IAAI,CAACpI,eAAe,CAACqI,iBAAiB,CAACiC,OAAO,CAAClC,GAAG;QACvDkC,OAAO,EAAE,CACP;UACElH,IAAI,EAAE,6CAA6C,IAAI,CAAC1C,iBAAiB,CAAC8C,OAAO,CAC/E,YAAY,CACb,EAAE;UACH+G,MAAM,EAAE,KAAK;UACbC,MAAM;YAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAM1I,IAAI,GAAGwI,EAAE,CAACN,OAAO,CAACS,UAAU,EAAE;cAEpC;cACA,IAAIC,QAAQ,GAAG,gBAAgB;cAE/B,IAAIrD,KAAI,CAACrF,QAAQ,EAAE;gBACjB,MAAM2I,MAAM,GAAGtD,KAAI,CAACtF,OAAO,EAAE6I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5I,EAAE,IAAIoF,KAAI,CAACrF,QAAQ,CAAC;gBAC7D,IAAI2I,MAAM,EAAE;kBACVD,QAAQ,IAAI,IAAIC,MAAM,CAAC/C,IAAI,CAACkD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAItD,IAAIzD,KAAI,CAACzG,YAAY,EAAE;gBACrB,MAAMmK,UAAU,GAAG1D,KAAI,CAACF,WAAW,EAAEyD,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAC/I,EAAE,IAAIoF,KAAI,CAACzG,YAAY,CAAC;gBACzE,IAAImK,UAAU,EAAE;kBACdL,QAAQ,IAAI,IAAIK,UAAU,CAACnD,IAAI,CAACkD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAI1D,IAAIzD,KAAI,CAAC1G,MAAM,EAAE;gBACf,MAAMsK,IAAI,GAAG5D,KAAI,CAAC9D,KAAK,EAAEqH,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACjJ,EAAE,IAAIoF,KAAI,CAAC1G,MAAM,CAAC;gBACvD,IAAIsK,IAAI,EAAE;kBACRP,QAAQ,IAAI,IAAIO,IAAI,CAACE,IAAI,CAACL,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAIpD,IAAIzD,KAAI,CAAChG,WAAW,IAAIgG,KAAI,CAAChG,WAAW,KAAK,KAAK,EAAE;gBAClDqJ,QAAQ,IAAI,IAAIrD,KAAI,CAAChG,WAAW,EAAE;;cAGpC,IAAIgG,KAAI,CAACxG,SAAS,CAACC,UAAU,IAAIuG,KAAI,CAACxG,SAAS,CAACE,QAAQ,EAAE;gBACxD2J,QAAQ,IAAI,IAAIrD,KAAI,CAACxG,SAAS,CAACC,UAAU,OAAOuG,KAAI,CAACxG,SAAS,CAACE,QAAQ,EAAE;eAC1E,MAAM,IAAIsG,KAAI,CAACxG,SAAS,CAACC,UAAU,EAAE;gBACpC4J,QAAQ,IAAI,SAASrD,KAAI,CAACxG,SAAS,CAACC,UAAU,EAAE;;cAGlD4J,QAAQ,IAAI,MAAM;cAElB,MAAMrD,KAAI,CAAC9G,cAAc,CAAC6K,SAAS,CAACtJ,IAAI,EAAE4I,QAAQ,CAAC;YACrD,CAAC;YAAA,gBAAAR,OAAAmB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAC,QAAQA,CAAC7J,IAAI;IACX,IAAI,CAACyB,KAAK,GAAGzB,IAAI;IACjB;IACA,MAAM8J,SAAS,GAAG,IAAI,CAACC,MAAM,CAACjB,IAAI,CAAEkB,KAAK,IAAKA,KAAK,CAACC,GAAG,KAAK,SAAS,CAAC;IACtE;IACA,IAAIC,aAAa,GAAG,EAAE;IACtBlK,IAAI,CAACmK,OAAO,CAAEhB,IAAI,IAAI;MACpB,IAAIiB,SAAS,GAAG,IAAI,CAAC9L,iBAAiB,CAAC8C,OAAO,CAAC+H,IAAI,CAACrD,IAAI,CAAC;MACzDoE,aAAa,CAACG,IAAI,CAAC;QACjB5K,KAAK,EAAE2K,SAAS;QAChB1K,KAAK,EAAEyJ,IAAI,CAAChJ;OACb,CAAC;IACJ,CAAC,CAAC;IACF2J,SAAS,CAACQ,KAAK,CAACC,OAAO,GAAGL,aAAa;EACzC;EAEAM,cAAcA,CAACzH,KAAU;IACvB;EAAA;EAGF0H,MAAMA,CAACrC,MAAM,EAAER,GAAI;IACjB,IAAI,CAACmC,MAAM,CAAC,CAAC,CAAC,CAACW,YAAY,GAAG,IAAI,CAAC7L,MAAM;IACzC,QAAQuJ,MAAM;MACZ,KAAK,QAAQ;QACX,IAAI,CAAC2B,MAAM,CAAC,CAAC,CAAC,CAACO,KAAK,CAACK,QAAQ,GAAG,KAAK;QACrC;MACF,KAAK,MAAM;QACT,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,CAACO,KAAK,CAACK,QAAQ,GAAG,IAAI;QACpC;MACF,KAAK,QAAQ;QACX;MACF;QACE;;IAEJ,IAAI,CAAClE,MAAM,CAAC2B,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAAC3B,MAAM,CAACmB,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAACrJ,mBAAmB,CAACqM,kBAAkB,CAAC,IAAI,CAACC,UAAU,CAAC,CAACC,UAAU,EAAE;EAC3E;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAACjN,QAAQ,CAACkN,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGlI,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACK,MAAM,CAAC8H,YAAY,CAAC,eAAe,CAAC,EAAE;QAC9C,IAAIC,aAAa,GAAGpI,KAAK,CAACK,MAAM,CAACK,YAAY,CAAC,eAAe,CAAC;QAC9D,IAAI2H,QAAQ,GAAGrI,KAAK,CAACK,MAAM,CAACK,YAAY,CAAC,UAAU,CAAC;QACpD;QACA,IAAI,CAAC9F,OAAO,CAAC0N,QAAQ,CAAC,CAACF,aAAa,EAAE,QAAQ,EAAEC,QAAQ,CAAC,EAAE;UACzDE,UAAU,EAAE,IAAI,CAAC5N;SAClB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA6N,gBAAgBA,CAACxI,KAAU;IACzB,IAAI,CAACyI,QAAQ,GAAGzI,KAAK;IACrB,IAAI,IAAI,CAACyI,QAAQ,KAAK,cAAc,IAAI,IAAI,CAACnL,SAAS,CAACC,UAAU,EAAE;MACjE,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEQsB,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAAC1B,SAAS,CAACC,UAAU,EAAE;MAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEAgL,WAAWA,CAAA;IACT,IAAI,CAAC/K,SAAS,CAACgL,WAAW,EAAE;IAC5B,IAAI,CAACV,UAAU,EAAE;IACjB,IAAI,IAAI,CAAChI,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;CACD;AAlpBkD2I,UAAA,EAAhDzO,SAAS,CAAC,iBAAiB,EAAE;EAAE0O,MAAM,EAAE;AAAK,CAAE,CAAC,+DAAsB;AAqKtED,UAAA,EADCxO,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,6DAS1C;AA/KUK,sBAAsB,GAAAmO,UAAA,EALlC1O,SAAS,CAAC;EACT4O,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,GACWvO,sBAAsB,CAopBlC;SAppBYA,sBAAsB", "names": ["Component", "ViewChild", "HostListener", "environment", "moment", "NgbDate", "<PERSON><PERSON>", "LeagueReportsComponent", "constructor", "route", "_router", "_commonsService", "_http", "_trans", "renderer", "_teamService", "_modalService", "_loadingService", "_toastService", "_registrationService", "_clubService", "_translateService", "_coreSidebarService", "_titleService", "_exportService", "_tournamentService", "calendar", "formatter", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "start_date", "end_date", "dateRangeValue", "hoveredDate", "fromDate", "toDate", "isSelecting", "matchStatus", "matchStatusOptions", "label", "value", "setTitle", "_getCurrentSeason", "show", "getAllSeasonActive", "subscribe", "data", "seasons", "seasonId", "id", "_getTournaments", "dtElement", "dtInstance", "then", "ajax", "reload", "dtTrigger", "next", "dtOptions", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "instant", "dismiss", "_getClubs", "getAllClubs", "res", "clubs", "onSelectSeason", "$event", "Promise", "resolve", "reject", "refreshDataTable", "onSelectClub", "console", "log", "onSelectTournament", "onSelectMatchStatus", "onDateSelection", "date", "after", "updateDateRange", "setTimeout", "dateRangePicker", "close", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "clearDateRange", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "from", "isHovered", "before", "isInside", "isRange", "equals", "onDateRangeChange", "fromMoment", "year", "month", "toString", "padStart", "day", "toMoment", "format", "get", "apiUrl", "tournaments", "ngOnInit", "_this", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "name", "isLink", "dom", "dataTableDefaults", "select", "rowId", "processing", "language", "lang", "dataTablesParameters", "callback", "params", "URLSearchParams", "undefined", "append", "queryString", "url", "isTableLoading", "post", "resp", "recordsTotal", "recordsFiltered", "responsive", "scrollX", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "row", "metadata", "display", "filter", "displayData", "displayDate", "buttons", "extend", "action", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "filename", "season", "find", "s", "replace", "tournament", "t", "club", "c", "code", "exportCsv", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "setClubs", "clubField", "fields", "field", "key", "current_clubs", "for<PERSON>ach", "club_name", "push", "props", "options", "onCaptureEvent", "editor", "defaultValue", "disabled", "getSidebarRegistry", "table_name", "toggle<PERSON><PERSON>", "ngAfterViewInit", "unlistener", "listen", "hasAttribute", "tournament_id", "stage_id", "navigate", "relativeTo", "onSelectViewType", "viewType", "ngOnDestroy", "unsubscribe", "__decorate", "static", "selector", "templateUrl", "styleUrls"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  HostListener,\r\n  OnDestroy\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit, OnDestroy\r\n{\r\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\r\n\r\n  public seasonId: any;\r\n  public clubId: any = null;\r\n  public tournamentId: any = null;\r\n  public dateRange: any = { start_date: null, end_date: null };\r\n  public dateRangeValue: string = '';\r\n  hoveredDate: NgbDate | null = null;\r\n  fromDate: NgbDate | null = null;\r\n  toDate: NgbDate | null = null;\r\n  isSelecting: boolean = false; // Track if we're in the middle of selecting range\r\n  private clickOutsideTimeout: any;\r\n  public matchStatus: string = 'all';\r\n  public contentHeader: object;\r\n  public seasons;\r\n  public clubs;\r\n  public tournaments;\r\n\r\n  matchStatusOptions = [\r\n    { label: 'All Status', value: 'all' },\r\n    { label: 'Upcoming', value: 'upcoming' },\r\n    { label: 'Active', value: 'active' },\r\n    { label: 'Completed', value: 'completed' },\r\n    { label: 'Cancelled', value: 'cancelled' },\r\n  ];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _teamService: TeamService,\r\n    public _modalService: NgbModal,\r\n    public _loadingService: LoadingService,\r\n    public _toastService: ToastrService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _titleService: Title,\r\n    private _exportService: ExportService,\r\n    public _tournamentService: TournamentService,\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n        this.dtTrigger.next(this.dtOptions);\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n        // this.clubId = this.clubs[0];\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectTournament($event) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      rowId: 'id',\r\n      processing: true,\r\n      language: {\r\n        ...this._commonsService.dataTableDefaults.lang,\r\n        // processing: `<div class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\r\n        //               <div class=\"spinner-border text-primary\" role=\"status\">\r\n        //                 <span class=\"sr-only\">Loading...</span>\r\n        //               </div>\r\n                      \r\n        //             </div>`,\r\n        processing: ``,\r\n      },\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        // Build query parameters for all filters\r\n        let params = new URLSearchParams();\r\n\r\n        if (this.clubId != undefined && this.clubId !== null) {\r\n          params.append('club_id', this.clubId);\r\n        }\r\n\r\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\r\n          params.append('tournament_id', this.tournamentId);\r\n        }\r\n\r\n        if (this.dateRange.start_date) {\r\n          params.append('start_date', this.dateRange.start_date);\r\n        }\r\n\r\n        if (this.dateRange.end_date) {\r\n          params.append('end_date', this.dateRange.end_date);\r\n        }\r\n\r\n        if (this.matchStatus && this.matchStatus !== 'all') {\r\n          params.append('match_status', this.matchStatus);\r\n        }\r\n\r\n        const queryString = params.toString();\r\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${\r\n          queryString ? '?' + queryString : ''\r\n        }`;\r\n\r\n        this.isTableLoading = true;\r\n        this._http.post<any>(url, dataTablesParameters).subscribe(\r\n          (resp: any) => {\r\n            this.isTableLoading = false;\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          },\r\n          (error) => {\r\n            this.isTableLoading = false;\r\n            console.error('Error loading table data:', error);\r\n            callback({\r\n              recordsTotal: 0,\r\n              recordsFiltered: 0,\r\n              data: [],\r\n            });\r\n          }\r\n        );\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      columnDefs: [{ responsivePriority: 1, targets: -1 }],\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('No.'),\r\n          data: null,\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: function (data, type, row, metadata) {\r\n\r\n            return metadata.row + 1\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Tournament'),\r\n          data: 'name',\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: {\r\n            display: (data, type, row) => {\r\n              return data ?? 'TBD';\r\n            },\r\n            filter: (data, type, row) => {\r\n              return data;\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          render: function (data) {\r\n            const displayData = data || 'TBD';\r\n            return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Date'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n          render: function (data, type, row) {\r\n            const displayDate =\r\n              !data || data === 'TBD'\r\n                ? 'TBD'\r\n                : moment(data).format('YYYY-MM-DD');\r\n            return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Start time'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('End time'),\r\n          data: 'end_time',\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Location'),\r\n          data: 'location',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home Team'),\r\n          data: 'home_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: 'VS',\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return 'vs';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away Team'),\r\n          data: 'away_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home score'),\r\n          data: 'home_score',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return '-';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away score'),\r\n          data: 'away_score',\r\n          className: 'text-center'\r\n        }\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant(\r\n              'Export CSV'\r\n            )}`,\r\n            extend: 'csv',\r\n            action: async (e: any, dt: any, button: any, config: any) => {\r\n              const data = dt.buttons.exportData();\r\n              \r\n              // Generate filename with current filter information\r\n              let filename = 'Matches_Report';\r\n              \r\n              if (this.seasonId) {\r\n                const season = this.seasons?.find(s => s.id == this.seasonId);\r\n                if (season) {\r\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.tournamentId) {\r\n                const tournament = this.tournaments?.find(t => t.id == this.tournamentId);\r\n                if (tournament) {\r\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.clubId) {\r\n                const club = this.clubs?.find(c => c.id == this.clubId);\r\n                if (club) {\r\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.matchStatus && this.matchStatus !== 'all') {\r\n                filename += `_${this.matchStatus}`;\r\n              }\r\n              \r\n              if (this.dateRange.start_date && this.dateRange.end_date) {\r\n                filename += `_${this.dateRange.start_date}_to_${this.dateRange.end_date}`;\r\n              } else if (this.dateRange.start_date) {\r\n                filename += `_from_${this.dateRange.start_date}`;\r\n              }\r\n              \r\n              filename += '.csv';\r\n              \r\n              await this._exportService.exportCsv(data, filename);\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  setClubs(data) {\r\n    this.clubs = data;\r\n    // get field has key club_id\r\n    const clubField = this.fields.find((field) => field.key === 'club_id');\r\n    // set options for club field\r\n    let current_clubs = [];\r\n    data.forEach((club) => {\r\n      let club_name = this._translateService.instant(club.name);\r\n      current_clubs.push({\r\n        label: club_name,\r\n        value: club.id\r\n      });\r\n    });\r\n    clubField.props.options = current_clubs;\r\n  }\r\n\r\n  onCaptureEvent(event: any) {\r\n    // console.log(event);\r\n  }\r\n\r\n  editor(action, row?) {\r\n    this.fields[0].defaultValue = this.clubId;\r\n    switch (action) {\r\n      case 'create':\r\n        this.fields[2].props.disabled = false;\r\n        break;\r\n      case 'edit':\r\n        this.fields[2].props.disabled = true;\r\n        break;\r\n      case 'remove':\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('tournament_id')) {\r\n        let tournament_id = event.target.getAttribute('tournament_id');\r\n        let stage_id = event.target.getAttribute('stage_id');\r\n        //  navigate to path ':tournament_id/stages/:stage_id'\r\n        this._router.navigate([tournament_id, 'stages', stage_id], {\r\n          relativeTo: this.route\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  onSelectViewType(event: any) {\r\n    this.viewType = event;\r\n    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  private refreshDataTable() {\r\n    if (this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n    this.unlistener();\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}