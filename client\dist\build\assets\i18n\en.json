{"'Select' or 'Type new' the Group Name": "'Select' or 'Type new' the Group Name", "(filtered from _MAX_ total entries)": "(filtered from _MAX_ total entries)", "Abandoned": "Abandoned", "About Season": "About Season", "Accept all": "Accept all", "Accept": "Accept", "Access": "Access", "Account": "Account", "Action": "Action", "Active": "Active", "Add Event": "Add Event", "Add New Team": "Add New Team", "Add Team & Group": "Add Team & Group", "Add Team": "Add Team", "Added {{count}} teams": "Added {{count}} teams", "Add Teams For": "Add Teams For", "Add event": "Add event", "Add match": "Add match", "Add new coach": "Add new coach", "Add new player": "Add new player", "Add new role": "Add new role", "Add replace match": "Add replace match", "Add team": "Add team", "Add": "Add", "Address": "Address", "Registrations": "Registrations", "Tournaments": "Tournaments", "Administrator Access": "Full Access", "Age Group": "Age Group", "All Notifications": "All Notifications", "All Matches": "All Matches", "All": "All", "Already have an account?": "Already have an account?", "Android Version": "Android Version", "App Start Notification": "App Start Notification", "App": "App", "App_Date": "App.Date", "Application": "Application", "Approval Status": "Approval Status", "Approve full": "Approve full", "Approved": "Approved", "Archived": "Archived", "Are you sure to delete?": "Are you sure to delete?", "Are you sure to send request": "Are you sure to send request", "Are you sure you want to cancel this match(s)?": "Are you sure you want to cancel this match(s)?", "Are you sure you want to disable 2FA?": "Are you sure you want to disable 2FA?", "Are you sure you want to reset score this match(s)?": "Are you sure you want to reset score this match(s)?", "Are you sure you want to swap teams in this match?": "Are you sure you want to swap teams in this match?", "Are you sure?": "Are you sure?", "Assign All": "Assign All", "Assign Club Manager": "Assign Club Manager", "Assign Coach": "Assign Coach", "Assign Coaches": "Assign Coaches", "Assign Manager": "Assign Manager", "Assign New Group": "Assign New Group", "Assign Player": "Assign Player", "Assign Players": "Assign Players", "Assign coach to team": "Assign coach to team", "Assign player to team": "Assign player to team", "Assign": "Assign", "Assigned": "Assigned", "Attachments": "Attachments", "Auto Generate": "Auto Generate", "Awaiting Update": "Awaiting Update", "Away Score": "Away Score", "Away Team": "Away Team", "Away penalty score": "Away penalty score", "Away score": "Away score", "Away team": "Away team", "Away": "Away", "Back to Login": "Back to Login", "Back": "Back", "Boys": "Boys", "Cancel Match": "Cancel Match", "Cancel registration": "Cancel registration", "Cancel type": "Cancel type", "Cancel": "Cancel", "Cancelled": "Cancelled", "Change Team": "Change Team", "Change": "Change", "Check Update": "Check Update", "Check for update": "Check for update", "Choose a season to assign player": "Choose a season to assign player", "Choose a season to view the registrations": "Choose a season to view the registrations", "Choose a season to view the teamsheet": "Choose a season to view the teamsheet", "Choose file": "Choose file", "Choose language": "Choose language", "Choose teams for group": "Choose teams for group", "Click a row to select": "Click a row to select", "Club Manager": "Club Manager", "Club": "Club", "Clubs & Groups": "Clubs & Groups", "Clubs": "Clubs", "Coach Name": "Coach Name", "Coach removed successfully": "Coach removed successfully", "Coaches assigned successfully": "Coaches assigned successfully", "Code is required": "Code is required", "Code": "Code", "Column": "Column", "Confirm password": "Confirm password", "Content": "Content", "Continue as Guest": "Continue as Guest", "Create Account": "Sign Up!", "Create New Tournament": "Create New Tournament", "Create new Group": "Create new Group", "Create new club": "Create new club", "Create new event": "Create new event", "Create new location": "Create new location", "Create new team": "Create new team", "Create user": "Create user", "Create your parent account!": "Create your parent account!", "Create": "Create", "Created at": "Created at", "Crop Image": "Crop Image", "Current Version": "Current Version", "Current password": "Current password", "Date of birth": "Date of birth", "Date": "Date", "Delete Role": "Delete Role", "Delete event": "Delete event", "Delete user": "Delete user", "Delete": "Delete", "Deleted successfully": "Deleted successfully", "Description": "Description", "Details": "Details", "Disable": "Disable", "Display TBD": "Display TBD", "Display Results": "Display Results", "Display Table": "Display Table", "Do you want to import teams from": "Do you want to import teams from", "Document expiry date": "Document expiry date", "Document photo": "Document photo", "Document type": "Document type", "Don't show again": "Don't show again", "Done": "Done", "Download failed": "Download failed", "Downloading": "Downloading", "Drag and drop image": "Drag and drop image", "Edit Role": "Edit Role", "Edit club": "Edit club", "Edit event": "Edit event", "Edit group": "Edit group", "Edit location": "Edit location", "Edit match": "Edit match", "Edit registration": "Edit registration", "Edit team": "Edit team", "Edit user": "Edit user", "Edit": "Edit", "Email & Push Notification": "Email & Push Notification", "Email (Parent)": "Email (Parent)", "Email address": "Email address", "Email is invalid": "<PERSON><PERSON> is invalid", "Email is required": "Email is required", "Email": "Email", "Enable": "Enable", "End date": "End date", "End time": "End time", "Enter Your SMTP Account": "Enter Your SMTP Account", "Enter a role name": "Enter a role name", "Enter address of location": "Enter address of location", "Enter authentication code": "Enter authentication code", "Enter code of club": "Enter code of club", "Enter date of match": "Enter date of match", "Enter description": "Enter description", "Enter email of manager exist in system": "Enter email of manager exist in system", "Enter end date of event": "Enter end date of event", "Enter fee of event": "Enter fee of event", "Enter group name": "Enter group name", "Enter latitude of location": "Enter latitude of location", "Enter longtitude of location": "Enter longtitude of location", "Enter name of club": "Enter name of club", "Enter name of event": "Enter name of event", "Enter name of group": "Enter name of group", "Enter name of location": "Enter name of location", "Enter name of team": "Enter name of team", "Enter parking of location": "Enter parking of location", "Enter reason": "Enter reason", "Enter stage name": "Enter stage name", "Enter start date of event": "Enter start date of event", "Enter start register date of event": "Enter start register date of event", "Enter start time of match": "Enter start time of match", "Enter surface of location": "Enter surface of location", "Enter type of group": "Enter type of group", "Enter years of group": "Enter years of group", "Error": "Error", "Event fee": "Event fee", "Event name": "Event name", "Event type": "Event type", "Events": "Events", "Export Excel": "Export Excel", "Export to Excel": "Export to Excel", "Export": "Export", "Favourite Clubs": "Favourite Clubs", "Favourite Teams": "Favourite Teams", "Fee": "Fee", "Female": "Female", "Field": "Field", "Filter Groups": "Filter Groups", "Finished matches": "Finished matches", "Finished": "Finished", "First": "First", "Fit to screen": "Fit to screen", "Fit": "Fit", "Competitions": "Competitions", "Fixtures": "Fixtures", "Follows": "Follows", "Forgot Password": "Forgot Password", "From Email": "From Email", "From Name": "From Name", "From": "From", "Gender": "Gender", "Girls": "Girls", "Goal difference": "Goal difference", "Goal": "Goal", "Goals scored": "Goals scored", "Group Name is required": "Group Name is required", "Group Name": "Group Name", "Group name": "Group name", "Group not removed": "Group not removed", "Group removed successfully": "Group removed successfully", "Group type": "Group type", "Group years": "Group years", "Group": "Group", "Groups + Knockout": "Groups + Knockout", "Groups": "Groups", "HKID/Passport type": "HKID/Passport type", "Head to Head": "Head to Head", "Home Score": "Home Score", "Home Team": "Home Team", "Home penalty score": "Home penalty score", "Home score": "Home score", "Home team": "Home team", "Home": "Home", "Host": "Host", "I agree to the": "I agree to the", "ID document showing player photo and DOB": "ID document showing player photo and DOB", "Import From Groups": "Import From Groups", "Knockout": "Knockout", "Last login": "Last login", "Last updated at": "Last updated at", "Last": "Last", "Latitude": "Latitude", "League Administrator": "League Administrator", "League Reports": "League Reports", "League group": "League group", "League": "League", "Leagues": "Leagues", "List Teamsheet": "List Teamsheet", "Live matches": "Live matches", "Loading": "Loading", "Location": "Location", "Locations": "Locations", "Login": "<PERSON><PERSON>", "Logo": "Logo", "Logout": "Logout", "Longitude": "Longitude", "Male": "Male", "Manage Clubs": "Manage Clubs", "Manage Events": "Manage Events", "Manage Groups": "Manage Groups", "Manage Leagues": "Manage Leagues", "Manage Location": "Manage Location", "Manage Payments": "Manage Payments", "Manage Registrations": "Manage Registrations", "All Registrations": "All Registrations", "Manage Team": "Manage Team", "Manage Teamsheets": "Manage Teamsheets", "All Tournaments": "All Tournaments", "Manage Users": "Manage Users", "Mark all as read": "Mark all as read", "Matches Report": "Matches Report", "Matches": "Matches", "Menu": "<PERSON><PERSON>", "Messages": "Messages", "Mixed": "Mixed", "Name": "Name", "New Tournament": "New Tournament", "New on our platform?": "New on our platform?", "New version is available. Please press keystroke <code>Ctrl+Shift+R</code> or <code><i class=\"fa-solid fa-command\"></i> Cmd+Shift+R</code> to update to version <span class=\"badge badge-success\">new version</span>": "New version is available. Please press keystroke <code>Ctrl+Shift+R</code> or <code><i class=\"fa-solid fa-command\"></i> Cmd+Shift+R</code> to update to version <span class=\"badge badge-success\">{{name}}</span>", "New version is available. Please update to version <span class=\"badge badge-success\">new version</span>": "New version is available. Please update to version <span class=\"badge badge-success\">{{name}}</span>", "No data available in table": "No data available in table", "No data found, please try again later or contact administrator!": "No data found, please try again later or contact administrator!", "No finished matches": "No finished matches", "No invoice": "No invoice", "No live matches": "No live matches", "No matches found": "No matches found", "No matching records found": "No matching records found", "No player": "No player", "No result": "No result", "No results found": "No results found", "No upcoming fixture": "No upcoming fixture", "No data": "No data", "No": "No", "Note": "Note", "Note: Image ratio must be 1:1": "Note: Image ratio must be 1:1", "Note: Please use your registered email address and password to login.": "Note: Please use your registered email address and password to login.", "Notification": "Notification", "Favourite": "Favourite", "Number of encounters": "Number of encounters", "OK": "OK", "Only \"Updated\" and \"Pending\" status can be validate": "Only \"Updated\" and \"Pending\" status can be validate", "Only admin can lock or unlock team sheet": "Only admin can lock or unlock team sheet", "Only player with status \"Awaiting Update\" can be update": "Only player with status \"Awaiting Update\" can be update", "Open": "Open", "Other names (Parent)": "Other names (Parent)", "Other names is required": "Other names is required", "Other names is too long": "Other names is too long", "Other names is too short": "Other names is too short", "Other names": "Other names", "PDF Link": "PDF Link", "Parent email": "Parent email", "Parent name": "Parent name", "Parent": "Parent", "Parking": "Parking", "Passport expiry date": "Passport expiry date", "Password Requirements": "Password Requirements", "Password is required": "Password is required", "Password must be at least :min characters long": "Password must be at least {{min}} characters long", "Password": "Password", "Pay now": "Pay now", "Payment Status": "Payment Status", "Payment status": "Payment status", "Payments": "Payments", "Pending": "Pending", "Permissions": "Permissions", "Phone": "Phone", "Photo Library": "Photo Library", "Photo gallery": "Photo gallery", "Photo": "Photo", "Player List": "Player List", "Player assigned to this team successfully": "Player assigned to this team successfully", "Player name": "Player name", "Player photo": "Player photo", "Player registration status": "Player registration status", "Player removed successfully": "Player removed successfully", "Player": "Player", "Please click \"Register your child\" to register": "Please click \"Register your child\" to register", "Please describe the reason for the update": "Please describe the reason for the update", "Please enter your email address and we'll send you an email to reset your password": "Please enter your email address and we'll send you an email to reset your password", "Please remove unnecessary whitespaces": "Please remove unnecessary whitespaces", "Please select a row to edit": "Please select a row to edit", "Please select a row to remove": "Please select a row to remove", "Please select only one match": "Please select only one match", "Please update this field": "Please update this field", "Please update to the latest version": "Please update to the latest version", "Points for draw": "Points for draw", "Points for loss": "Points for loss", "Points for win": "Points for win", "Points": "Points", "Port": "Port", "Postponed": "Postponed", "Print": "Print", "Processing...": "Processing...", "Profile": "Profile", "Push Notification": "Push Notification", "REGISTER NOW": "REGISTER NOW", "Read": "Read", "Reason for rejection": "Reason for rejection", "Reason": "Reason", "Receiver": "Receiver", "Recover Your Password": "Recover Your Password", "Red card": "Red card", "Reg_Date": "Reg.Date", "Register new player": "Register new player", "Register your child": "Register your child", "Register": "Register", "Registered": "Registered", "Registration End": "Registration End", "Registration Information": "Registration Information", "Registration List": "Registration List", "Registration Start": "Registration Start", "Registration": "Registration", "Reject": "Reject", "Rejected": "Rejected", "Release": "Release", "Remember Me": "Remember Me", "Remove Team": "Remove Team", "Remove Group": "Remove Group", "Remove": "Remove", "Reports": "Reports", "Request to update": "Request to update", "Request": "Request", "Requests Update": "Requests Update", "Rescheduled": "Rescheduled", "Reset Password": "Reset Password", "Reset data": "Reset data", "Results": "Results", "Role List": "Role List", "Role Name": "Role Name", "Role Permissions": "Role Permissions", "Role": "Role", "Round name": "Round name", "Round/Group": "Round/Group name", "Enter Round/Group name": "Enter Round/Group name", "Save": "Save", "Score": "Score", "Search": "Search", "Season Fees": "Season Fees", "Season": "Season", "Secret key has been sent to your email": "Secret key has been sent to your email", "Secret key": "Secret key", "Security": "Security", "Select All": "Select All", "Select Club": "Select Club", "Select Group": "Select Group", "Select League": "Select League", "Select Player": "Select Player", "Select Season": "Select Season", "Select Team": "Select Team", "Select all": "Select all", "Select away team": "Select away team", "Select home team": "Select home team", "Select status of event": "Select status of event", "Select team to change": "Select team to change", "Select": "Select", "Selected %d rows": "Selected %d rows", "There are %d players assigned to this team. Please remove them to delete.": "There are {{team_players_count}} players assigned to this team. Please remove them to delete.", "Selected 1 row": "Selected 1 row", "Selected Team is required": "Selected Team is required", "Send Message": "Send Message", "Send Reminder": "Send Reminder", "Send by": "Send by", "Send to clubs": "Send to clubs", "Send to groups": "Send to groups", "Send to players": "Send to players", "Send to": "Send to", "Quick lookup": "Quick Lookup", "Sent": "<PERSON><PERSON>", "Setting will show when app start": "Setting will show when app start", "System": "System", "Setup SMTP Account": "Setup SMTP Account", "Settings": "Settings", "Show _MENU_ entries": "Show _MENU_ entries", "Show password": "Show password", "Showing 0 to 0 of 0 entries": "Showing 0 to 0 of 0 entries", "Showing _START_ to _END_ of _TOTAL_ entries": "Showing _START_ to _END_ of _TOTAL_ entries", "Sign Up": "Sign Up", "Sign in to your account": "Sign in to your account", "Stage Details": "Stage Details", "Stage Teams": "Stage Teams", "Stage name": "Stage name", "Stage": "Stage", "Start Register Date": "Start Register Date", "Start date": "Start date", "Start register date": "Start register date", "Start time": "Start time", "Status": "Status", "Submit Teamsheet": "Submit Teamsheet", "Submit": "Submit", "Submitted History": "Submitted History", "Submitted": "Submitted", "Success": "Success", "Suggestions": "Suggestions", "Surface": "Surface", "Surname (Parent)": "Surname (Parent)", "Surname is required": "Surname is required", "Surname is too long": "Surname is too long", "Surname is too short": "Surname is too short", "Surname": "Surname", "Sync Payment Status": "Sync Payment Status", "System Versions": "System Versions", "Swap teams": "Swap teams", "Tables": "Tables", "Take Photo": "Take Photo", "Team Assignment": "Team Assignment", "Team Coach": "Team Coach", "Team List": "Team List", "Team Management": "Team Management", "Team changed successfully": "Team changed successfully", "Team not removed": "Team not removed", "Team removed successfully": "Team removed successfully", "Team sheet submitted successfully": "Team sheet submitted successfully", "Team": "Team", "Teams added successfully": "Teams added successfully", "Teams": "Teams", "Teamsheet": "Teamsheet", "Teamsheets": "Teamsheets", "Terms of Service and Privacy Policy": "Terms of Service and Privacy Policy", "Third place": "Third place", "This action cannot be undone": "This action cannot be undone", "This feature is developing": "This feature is developing", "This field is invalid": "{{field}} is invalid", "This field is required": "{{field}} is required", "This field must be at least": "{{field}} must be at least", "This field must be less than": "{{field}} must be less than", "Please upload a file smaller than {{size}}MB": "Please upload a file smaller than {{size}}MB", "Please upload a file with type {{types}}": "Please upload a file with type {{types}}", "This match has not yet been set location": "This match has not yet been set location", "This will reset all your data and you will be logged out": "This will reset all your data and you will be logged out", "Tie break order": "Tie break order", "Time": "Time", "Title": "Title", "To be able to authorize transactions you need to scan this QR Code with your Google Authentication App or enter the secret key below": "To be able to authorize transactions you need to scan this QR Code with your Google Authentication App or enter the secret key below", "To": "To", "Total Users": "Total Users", "Total": "Total", "Tournament": "Tournament", "Two Factor Authentication": "Two Factor Authentication", "Two-factor authentication adds a layer of security to your account by requiring more than just a password to log in": "Two-factor authentication adds a layer of security to your account by requiring more than just a password to log in", "Two-step verification": "Two-step verification", "Type the content here": "Type the content here", "Type": "Type", "Unread": "Unread", "Upcoming matches": "Upcoming matches", "Update App": "Update App", "Update Score": "Update Score", "Reset Score": "Reset Score", "Update group successfully": "Update group successfully", "Update player": "Update player", "Update successfully": "Update successfully", "Update": "Update", "Updated at": "Updated at", "Updated": "Updated", "Upload image": "Upload image", "Upload photo": "Upload photo", "User": "User", "Users": "Users", "Validate player": "Validate player", "Validate": "Validate", "Validated": "Validated", "Validation Status": "Validation Status", "Validation and send email success": "Validation and send email success", "Value must be greater than": "Value must be greater than", "Value must be less than": "Value must be less than", "Version": "Version", "View Archived": "View Archived", "View detail": "View detail", "View details": "View details", "View file": "View file", "Warning": "Warning", "We will send secret key to your email. Please check and verify again": "We will send secret key to your email. Please check and verify again", "Welcome to": "Welcome to", "Year": "Year", "Yellow card": "Yellow card", "Yes": "Yes", "You are about to remove all teams in this group": "You are about to remove all teams in this group", "You are editing multiple rows! Please be careful, data will be updated for all selected rows.": "You are editing multiple rows! Please be careful, data will be updated for all selected rows.", "You are using the latest version": "You are using the latest version", "You can not add match in this stage": "You can not add match in this stage", "You can not add replace match for this match": "You can not add replace match for this match", "You can not cancel match that has been cancelled": "You can not cancel match that has been cancelled", "You can not swap teams for this match": "You can not swap teams for this match", "You can't get the code?": "You can't get the code?", "You must accept the terms and conditions": "You must accept the terms and conditions", "You must remove all schedule before add team": "You must remove all schedule before add team", "You want to assign all players to this team?": "You want to assign all players to this team?", "You want to assign this player to this team?": "You want to assign this player to this team?", "You will not be able to recover this group": "You will not be able to recover this group", "You will not be able to recover this team": "You will not be able to recover this team", "You will not be able to recover this!": "You will not be able to recover this!", "You won't be able to revert this!": "You won't be able to revert this!", "Your children": "Your children", "Your players can only register in the following groups": "Your players can only register in the following groups", "Your version is outdated": "Your version is outdated", "a_version": "Android version", "address": "address", "age": "age", "amount": "amount", "approved": "approved", "area": "area", "ask_want_to_remove_coach": "Are you sure you want to remove this coach?", "ask_want_to_remove_player": "Are you sure you want to remove this player?", "ask_want_to_submit_teamsheet": "You want to submit this team sheet?", "assign player": "Assign player", "available": "available", "away_penalty": "Away Penalty", "away_score": "Away Score", "away_team_id": "away team", "birthday": "birthday", "body": "body", "characters": "characters", "city": "city", "club_id": "club", "coach(es)": "coach(es)", "code": "code", "content": "content", "country": "country", "created_at": "created at", "creator": "creator", "current_password": "current password", "date": "date", "date_of_birth": "date of birth", "day": "day", "deleted_at": "deleted at", "description": "description", "disabled": "disabled", "district": "district", "dob": "dob", "document_expiry_date": "document expiry date", "document_photo": "document photo", "document_type": "document type", "duration": "duration", "email": "email", "enabled": "enabled", "end registration": "end registration", "end_date": "end date", "end_register_date": "end register date", "end_time": "end time", "excerpt": "excerpt", "failed": "failed", "Failed": "Failed", "fee": "fee", "filter": "filter", "first_name": "surname", "from_email": "from email", "from_name": "from name", "gender": "gender", "group": "Group", "group_id": "group", "home_penalty": "Home Penalty", "home_score": "Home Score", "home_team_id": "home team", "hour": "hour", "iOS & Android version settings": "iOS & Android version settings", "iOS Version": "iOS Version", "i_version": "iOS version", "image": "image", "is not valid or contains space between years": "is not valid or contains space between years", "last_name": "other names", "lesson": "lesson", "line_address_1": "line address 1", "line_address_2": "line address 2", "location_id": "location", "logo": "logo", "marked as paid": "marked as paid", "message": "message", "middle_name": "middle name", "minute": "minute", "mobile": "mobile", "month": "month", "name": "name", "national_code": "national code", "no_encounters": "No encounters", "note": "note", "number": "number", "other_names": "other names", "paid": "paid", "password": "password", "password_confirmation": "password confirmation", "phone": "phone", "photo": "photo", "player registration": "player registration", "player(s)": "player(s)", "player_id": "player", "player_name": "Player Name", "player_photo": "player photo", "points_draw": "Points for draw", "points_loss": "Points for loss", "points_win": "Points for win", "postal_code": "postal code", "price": "price", "province": "province", "ranking_criteria": "Tie break order", "recaptcha_response_field": "recaptcha response field", "refunded": "refunded", "registered": "registered", "remember": "remember", "restored_at": "restored at", "result_text_under_image": "result text under image", "role": "role", "role_id": "role", "second": "second", "send_to": "send to", "sent": "sent", "sex": "sex", "short_text": "short text", "size": "size", "smtp_account": "smtp account", "smtp_host": "smtp host", "smtp_password": "smtp password", "smtp_port": "smtp port", "stage": "Stage", "stage_id": "Stage", "stage_name": "Stage Name", "stage_type": "Stage Type", "start registration": "start registration", "start_date": "start date", "start_register_date": "start register date", "start_time": "start time", "start_time_short": "start time", "state": "state", "status": "status", "street": "street", "student": "student", "subject": "subject", "surname": "surname", "teacher": "teacher", "team_id": "Team", "team_player_id": "Player", "teamsheet": "Teamsheet", "terms": "terms", "test_description": "test description", "test_locale": "test locale", "test_name": "test name", "text": "text", "time": "time", "title": "title", "two-factor authentication": "two-factor authentication", "type": "type", "updated_at": "updated at", "upload": "upload", "username": "username", "year": "year", "years": "years", "Follow club/teams to receive the latest announcements related to clubs/teams": "Follow club/teams to receive the latest announcements related to clubs/teams", "File too large! Maximum {{maxUploadSize}}MB allowed": "File too large! Maximum {{maxUploadSize}}MB allowed", "{{numsOfInvoices}} invoices synced": "{{numsOfInvoices}} invoices synced", "Follow": "Follow", "Unfollow": "Unfollow"}