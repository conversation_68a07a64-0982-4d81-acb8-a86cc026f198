{"ast": null, "code": "import { DataTableDirective } from 'angular-datatables';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"app/services/commons.service\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"app/services/team.service\";\nimport * as i6 from \"ngx-toastr\";\nimport * as i7 from \"app/services/registration.service\";\nimport * as i8 from \"app/services/tournament.service\";\nimport * as i9 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i10 from \"app/services/club.service\";\nimport * as i11 from \"@angular/platform-browser\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"app/layout/components/content-header/content-header.component\";\nimport * as i15 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i16 from \"angular-datatables\";\nimport * as i17 from \"@ng-select/ng-select\";\nimport * as i18 from \"../../../components/btn-dropdown-action/btn-dropdown-action.component\";\nimport * as i19 from \"../../../components/editor-sidebar/editor-sidebar.component\";\nfunction LeagueReportsComponent_ng_template_14_ng_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r16.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, season_r16.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_14_ng_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tournament_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", tournament_r17.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, tournament_r17.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_14_ng_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const club_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", club_r18.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, club_r18.code), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_14_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 39);\n    i0.ɵɵlistener(\"mouseenter\", function LeagueReportsComponent_ng_template_14_ng_template_35_Template_span_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r22);\n      const date_r19 = restoredCtx.date;\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.hoveredDate = date_r19);\n    })(\"mouseleave\", function LeagueReportsComponent_ng_template_14_ng_template_35_Template_span_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.hoveredDate = null);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r19 = ctx.date;\n    const focused_r20 = ctx.focused;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"focused\", focused_r20)(\"range\", ctx_r12.isRange(date_r19))(\"faded\", ctx_r12.isHovered(date_r19) || ctx_r12.isInside(date_r19));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", date_r19.day, \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_14_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelement(0, \"hr\", 40);\n    i0.ɵɵelementStart(1, \"div\", 41)(2, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_ng_template_14_ng_template_37_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r25);\n      i0.ɵɵnextContext();\n      const _r10 = i0.ɵɵreference(30);\n      const ctx_r24 = i0.ɵɵnextContext();\n      ctx_r24.clearDateRange();\n      return i0.ɵɵresetView(_r10.close());\n    });\n    i0.ɵɵtext(3, \" Clear \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_ng_template_14_ng_template_37_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r25);\n      i0.ɵɵnextContext();\n      const _r10 = i0.ɵɵreference(30);\n      return i0.ɵɵresetView(_r10.close());\n    });\n    i0.ɵɵtext(5, \" Close \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LeagueReportsComponent_ng_template_14_ng_option_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r27.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, status_r27.label), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"div\", 4)(3, \"label\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ng-select\", 23);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_ng_template_14_Template_ng_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.seasonId = $event);\n    })(\"change\", function LeagueReportsComponent_ng_template_14_Template_ng_select_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.onSelectSeason($event));\n    });\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵtemplate(8, LeagueReportsComponent_ng_template_14_ng_option_8_Template, 3, 4, \"ng-option\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 21)(10, \"div\", 25)(11, \"label\", 26);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ng-select\", 23);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_ng_template_14_Template_ng_select_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.tournamentId = $event);\n    })(\"change\", function LeagueReportsComponent_ng_template_14_Template_ng_select_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onSelectTournament($event));\n    });\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵtemplate(16, LeagueReportsComponent_ng_template_14_ng_option_16_Template, 3, 4, \"ng-option\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"label\", 27);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"ng-select\", 23);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_ng_template_14_Template_ng_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.clubId = $event);\n    })(\"change\", function LeagueReportsComponent_ng_template_14_Template_ng_select_change_21_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onSelectClub($event));\n    });\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵtemplate(23, LeagueReportsComponent_ng_template_14_ng_option_23_Template, 3, 4, \"ng-option\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"label\", 28);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 29)(29, \"input\", 30, 31);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_ng_template_14_Template_input_click_29_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.openDatePicker());\n    })(\"dateSelect\", function LeagueReportsComponent_ng_template_14_Template_input_dateSelect_29_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onDateSelection($event));\n    });\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 32)(33, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_ng_template_14_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.openDatePicker());\n    });\n    i0.ɵɵelement(34, \"i\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(35, LeagueReportsComponent_ng_template_14_ng_template_35_Template, 2, 7, \"ng-template\", null, 34, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(37, LeagueReportsComponent_ng_template_14_ng_template_37_Template, 6, 0, \"ng-template\", null, 35, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 25)(40, \"label\", 36);\n    i0.ɵɵtext(41);\n    i0.ɵɵpipe(42, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"ng-select\", 23);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_ng_template_14_Template_ng_select_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.matchStatus = $event);\n    })(\"change\", function LeagueReportsComponent_ng_template_14_Template_ng_select_change_43_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.onSelectMatchStatus($event));\n    });\n    i0.ɵɵpipe(44, \"translate\");\n    i0.ɵɵtemplate(45, LeagueReportsComponent_ng_template_14_ng_option_45_Template, 3, 4, \"ng-option\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 21)(47, \"div\", 4);\n    i0.ɵɵelement(48, \"table\", 37);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r11 = i0.ɵɵreference(36);\n    const _r13 = i0.ɵɵreference(38);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 34, \"Season\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(7, 36, \"Select Season\"));\n    i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx_r1.seasonId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.seasons);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 38, \"Tournament\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(15, 40, \"Select Tournament\"));\n    i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx_r1.tournamentId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.tournaments);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 42, \"Club\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(22, 44, \"Select Club\"));\n    i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx_r1.clubId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.clubs);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 46, \"Date Range\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(31, 48, \"Select Date Range\"));\n    i0.ɵɵproperty(\"value\", ctx_r1.formatter.format(ctx_r1.fromDate) + (ctx_r1.toDate ? \" - \" + ctx_r1.formatter.format(ctx_r1.toDate) : \"\"))(\"dayTemplate\", _r11)(\"footerTemplate\", _r13)(\"firstDayOfWeek\", 1)(\"displayMonths\", 2)(\"autoClose\", false);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(42, 50, \"Match Status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(44, 52, \"Select Status\"));\n    i0.ɵɵproperty(\"searchable\", false)(\"clearable\", false)(\"ngModel\", ctx_r1.matchStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.matchStatusOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dtOptions\", ctx_r1.dtOptions)(\"dtTrigger\", ctx_r1.dtTrigger);\n  }\n}\nfunction LeagueReportsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"Schedule Matches Content\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, \"Coming soon...\"));\n  }\n}\nfunction LeagueReportsComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"Bracket Content\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, \"Coming soon...\"));\n  }\n}\nfunction LeagueReportsComponent_core_sidebar_28_app_editor_sidebar_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-editor-sidebar\", 48);\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"table\", ctx_r40.dtElement)(\"fields\", ctx_r40.fields)(\"params\", ctx_r40.params);\n  }\n}\nfunction LeagueReportsComponent_core_sidebar_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"core-sidebar\", 46);\n    i0.ɵɵtemplate(1, LeagueReportsComponent_core_sidebar_28_app_editor_sidebar_1_Template, 1, 3, \"app-editor-sidebar\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"name\", ctx_r4.table_name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.dtElement);\n  }\n}\nfunction LeagueReportsComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-btn-dropdown-action\", 49);\n    i0.ɵɵlistener(\"emitter\", function LeagueReportsComponent_ng_template_29_Template_app_btn_dropdown_action_emitter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const emitter_r42 = restoredCtx.captureEvents;\n      return i0.ɵɵresetView(emitter_r42($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r41 = ctx.adtData;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"actions\", ctx_r6.rowActions)(\"data\", data_r41);\n  }\n}\nexport class LeagueReportsComponent {\n  constructor(calendar, formatter, _translateService, _commonsService, _loadingService, _teamService, toastr, _registrationService, _tournamentService, _coreSidebarService, _clubService, titleService, cdr) {\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._loadingService = _loadingService;\n    this._teamService = _teamService;\n    this.toastr = toastr;\n    this._registrationService = _registrationService;\n    this._tournamentService = _tournamentService;\n    this._coreSidebarService = _coreSidebarService;\n    this._clubService = _clubService;\n    this.titleService = titleService;\n    this.cdr = cdr;\n    this.activeTab = 'league_table'; // NgBootstrap nav active tab\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    // Data arrays\n    this.seasons = [];\n    this.tournaments = [];\n    this.clubs = [];\n    this.matchStatusOptions = [{\n      value: 'all',\n      label: 'All Status'\n    }, {\n      value: 'upcoming',\n      label: 'Upcoming'\n    }, {\n      value: 'active',\n      label: 'Active'\n    }, {\n      value: 'completed',\n      label: 'Completed'\n    }, {\n      value: 'cancelled',\n      label: 'Cancelled'\n    }];\n    this.matchStatus = 'all';\n    // Date picker variables\n    this.hoveredDate = null;\n    // Table configuration\n    this.table_name = 'league-reports';\n    this.previousActiveTab = '';\n    this.fromDate = calendar.getToday();\n    this.toDate = calendar.getNext(calendar.getToday(), 'd', 10);\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: 'League Reports',\n      actionText: '',\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'League Tournament',\n          isLink: true,\n          link: '/league-tournament'\n        }, {\n          name: 'Reports',\n          isLink: false\n        }]\n      }\n    };\n    this.initializeDataTable();\n    this.loadInitialData();\n  }\n  ngAfterViewInit() {\n    // DataTable will be initialized when the league_table tab becomes active\n    setTimeout(() => {\n      if (this.activeTab === 'league_table') {\n        this.dtTrigger.next(null);\n      }\n    }, 100);\n  }\n  ngOnDestroy() {\n    if (this.dtTrigger) {\n      this.dtTrigger.unsubscribe();\n    }\n  }\n  /**\r\n   * Lifecycle hook to check for tab changes reactively\r\n   * This follows Angular best practices and avoids ExpressionChangedAfterItHasBeenCheckedError\r\n   */\n  ngAfterViewChecked() {\n    // Only process if tab actually changed (avoid unnecessary processing)\n    if (this.activeTab !== this.previousActiveTab) {\n      this.previousActiveTab = this.activeTab;\n      // Use setTimeout to defer DataTable initialization to next tick\n      // This prevents ExpressionChangedAfterItHasBeenCheckedError\n      if (this.activeTab === 'league_table') {\n        setTimeout(() => {\n          this.initializeDataTableIfNeeded();\n        }, 0);\n      }\n    }\n  }\n  /**\r\n   * Initialize DataTable only if not already initialized\r\n   */\n  initializeDataTableIfNeeded() {\n    if (this.activeTab === 'league_table' && this.dtElement) {\n      // Check if DataTable is already initialized\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          // Reload existing table\n          dtInstance.ajax.reload();\n        }).catch(() => {\n          // If reload fails, trigger new table\n          this.dtTrigger.next(null);\n        });\n      } else {\n        // Initialize new table\n        this.dtTrigger.next(null);\n      }\n    }\n  }\n  initializeDataTable() {\n    this.dtOptions = {\n      serverSide: true,\n      processing: true,\n      ajax: (dataTablesParameters, callback) => {\n        const requestParams = {\n          ...dataTablesParameters,\n          season_id: this.seasonId,\n          tournament_id: this.tournamentId,\n          club_id: this.clubId,\n          match_status: this.matchStatus,\n          from_date: this.fromDate ? this.formatter.format(this.fromDate) : null,\n          to_date: this.toDate ? this.formatter.format(this.toDate) : null\n        };\n        // Mock data for now - replace with actual service call\n        const mockData = {\n          recordsTotal: 10,\n          recordsFiltered: 10,\n          data: [{\n            position: 1,\n            team_name: 'Team A',\n            played: 10,\n            won: 8,\n            drawn: 1,\n            lost: 1,\n            goals_for: 25,\n            goals_against: 8,\n            goal_difference: 17,\n            points: 25\n          }, {\n            position: 2,\n            team_name: 'Team B',\n            played: 10,\n            won: 6,\n            drawn: 2,\n            lost: 2,\n            goals_for: 18,\n            goals_against: 12,\n            goal_difference: 6,\n            points: 20\n          }, {\n            position: 3,\n            team_name: 'Team C',\n            played: 10,\n            won: 5,\n            drawn: 3,\n            lost: 2,\n            goals_for: 15,\n            goals_against: 10,\n            goal_difference: 5,\n            points: 18\n          }]\n        };\n        setTimeout(() => {\n          callback({\n            recordsTotal: mockData.recordsTotal,\n            recordsFiltered: mockData.recordsFiltered,\n            data: mockData.data\n          });\n        }, 500);\n      },\n      columns: [{\n        title: 'Position',\n        data: 'position'\n      }, {\n        title: 'Team',\n        data: 'team_name'\n      }, {\n        title: 'Played',\n        data: 'played'\n      }, {\n        title: 'Won',\n        data: 'won'\n      }, {\n        title: 'Drawn',\n        data: 'drawn'\n      }, {\n        title: 'Lost',\n        data: 'lost'\n      }, {\n        title: 'Goals For',\n        data: 'goals_for'\n      }, {\n        title: 'Goals Against',\n        data: 'goals_against'\n      }, {\n        title: 'Goal Difference',\n        data: 'goal_difference'\n      }, {\n        title: 'Points',\n        data: 'points'\n      }]\n    };\n  }\n  loadInitialData() {\n    // Mock data for dropdowns - replace with actual service calls\n    this.seasons = [{\n      id: 1,\n      name: '2024 Season'\n    }, {\n      id: 2,\n      name: '2023 Season'\n    }];\n    this.tournaments = [{\n      id: 1,\n      name: 'Premier League'\n    }, {\n      id: 2,\n      name: 'Championship'\n    }];\n    this.clubs = [{\n      id: 1,\n      code: 'MUN',\n      name: 'Manchester United'\n    }, {\n      id: 2,\n      code: 'LIV',\n      name: 'Liverpool'\n    }, {\n      id: 3,\n      code: 'CHE',\n      name: 'Chelsea'\n    }];\n  }\n  // Event handlers for filters\n  onSelectSeason(seasonId) {\n    this.seasonId = seasonId;\n    this.refreshDataTable();\n  }\n  onSelectTournament(tournamentId) {\n    this.tournamentId = tournamentId;\n    this.refreshDataTable();\n  }\n  onSelectClub(clubId) {\n    this.clubId = clubId;\n    this.refreshDataTable();\n  }\n  onSelectMatchStatus(status) {\n    this.matchStatus = status;\n    this.refreshDataTable();\n  }\n  refreshDataTable() {\n    if (this.dtElement && this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  // Date picker methods\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      this.fromDate = date;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n    } else {\n      this.toDate = null;\n      this.fromDate = date;\n    }\n    this.refreshDataTable();\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  openDatePicker() {\n    // Date picker will open automatically when input is clicked\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.refreshDataTable();\n  }\n  get shouldShowSidebar() {\n    return false; // Simplified for now\n  }\n  static #_ = this.ɵfac = function LeagueReportsComponent_Factory(t) {\n    return new (t || LeagueReportsComponent)(i0.ɵɵdirectiveInject(i1.NgbCalendar), i0.ɵɵdirectiveInject(i1.NgbDateParserFormatter), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.CommonsService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.TeamService), i0.ɵɵdirectiveInject(i6.ToastrService), i0.ɵɵdirectiveInject(i7.RegistrationService), i0.ɵɵdirectiveInject(i8.TournamentService), i0.ɵɵdirectiveInject(i9.CoreSidebarService), i0.ɵɵdirectiveInject(i10.ClubService), i0.ɵɵdirectiveInject(i11.Title), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueReportsComponent,\n    selectors: [[\"app-league-reports\"]],\n    viewQuery: function LeagueReportsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 31,\n    vars: 13,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-body\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", 3, \"activeId\", \"activeIdChange\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"league_table\"], [\"ngbNavLink\", \"\"], [1, \"feather\", \"icon-grid\"], [\"ngbNavContent\", \"\"], [\"ngbNavItem\", \"schedule_matches\"], [1, \"feather\", \"icon-calendar\"], [\"ngbNavItem\", \"bracket\"], [1, \"feather\", \"icon-layers\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [\"class\", \"modal modal-slide-in sidebar-todo-modal fade\", \"overlayClass\", \"modal-backdrop\", 3, \"name\", 4, \"ngIf\"], [\"rowActionBtn\", \"\"], [1, \"p-1\"], [1, \"row\", \"mb-1\"], [\"for\", \"season\", 1, \"form-label\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\"], [\"for\", \"tournament\", 1, \"form-label\"], [\"for\", \"club\", 1, \"form-label\"], [\"for\", \"dateRange\", 1, \"form-label\"], [1, \"input-group\"], [\"name\", \"daterange\", \"ngbDatepicker\", \"\", \"readonly\", \"\", \"outsideDays\", \"hidden\", 1, \"form-control\", 3, \"placeholder\", \"value\", \"dayTemplate\", \"footerTemplate\", \"firstDayOfWeek\", \"displayMonths\", \"autoClose\", \"click\", \"dateSelect\"], [\"dateRangePicker\", \"ngbDatepicker\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [\"dayTemplate\", \"\"], [\"footerTemplate\", \"\"], [\"for\", \"matchStatus\", 1, \"form-label\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [3, \"value\"], [1, \"custom-day\", 3, \"mouseenter\", \"mouseleave\"], [1, \"my-0\"], [1, \"d-flex\", \"justify-content-between\", \"p-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"p-2\"], [1, \"text-center\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\", 4, \"ngIf\"], [3, \"table\", \"fields\", \"params\"], [\"btnStyle\", \"font-size:15px;color:black!important\", 3, \"actions\", \"data\", \"emitter\"]],\n    template: function LeagueReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"ul\", 7, 8);\n        i0.ɵɵlistener(\"activeIdChange\", function LeagueReportsComponent_Template_ul_activeIdChange_7_listener($event) {\n          return ctx.activeTab = $event;\n        });\n        i0.ɵɵelementStart(9, \"li\", 9)(10, \"a\", 10);\n        i0.ɵɵelement(11, \"i\", 11);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(14, LeagueReportsComponent_ng_template_14_Template, 49, 54, \"ng-template\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"li\", 13)(16, \"a\", 10);\n        i0.ɵɵelement(17, \"i\", 14);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(20, LeagueReportsComponent_ng_template_20_Template, 8, 6, \"ng-template\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"li\", 15)(22, \"a\", 10);\n        i0.ɵɵelement(23, \"i\", 16);\n        i0.ɵɵtext(24);\n        i0.ɵɵpipe(25, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(26, LeagueReportsComponent_ng_template_26_Template, 8, 6, \"ng-template\", 12);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(27, \"div\", 17);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵtemplate(28, LeagueReportsComponent_core_sidebar_28_Template, 2, 2, \"core-sidebar\", 18);\n        i0.ɵɵtemplate(29, LeagueReportsComponent_ng_template_29_Template, 1, 2, \"ng-template\", null, 19, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"activeId\", ctx.activeTab);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 7, \"Table View\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 9, \"Schedule View\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 11, \"Bracket View\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowSidebar);\n      }\n    },\n    dependencies: [i12.NgForOf, i12.NgIf, i13.NgControlStatus, i13.NgModel, i1.NgbInputDatepicker, i1.NgbNavContent, i1.NgbNav, i1.NgbNavItem, i1.NgbNavLink, i1.NgbNavOutlet, i14.ContentHeaderComponent, i15.CoreSidebarComponent, i16.DataTableDirective, i17.NgSelectComponent, i17.ɵr, i18.BtnDropdownActionComponent, i19.EditorSidebarComponent, i2.TranslatePipe],\n    styles: [\".min-w-max[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n\\n.custom-day[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 0.185rem 0.25rem;\\n  border-radius: 0.25rem;\\n  display: inline-block;\\n  width: 2rem;\\n  cursor: pointer;\\n}\\n\\n.custom-day[_ngcontent-%COMP%]:hover, .custom-day.focused[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n\\n.custom-day.range[_ngcontent-%COMP%], .custom-day[_ngcontent-%COMP%]:hover {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.custom-day.faded[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 123, 255, 0.5);\\n  color: white;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlLXJlcG9ydHMvbGVhZ3VlLXJlcG9ydHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUdBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFGOztBQUdBOztFQUVFLHlCQUFBO0FBQUY7O0FBR0E7O0VBRUUseUJBQUE7RUFDQSxZQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtFQUNBLFlBQUE7QUFBRjs7QUFHQTtFQUNFLHVCQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIubWluLXctbWF4IHtcclxuICBtaW4td2lkdGg6IG1heC1jb250ZW50O1xyXG59XHJcblxyXG4vLyBEYXRlIHJhbmdlIHBpY2tlciBzdHlsZXNcclxuLmN1c3RvbS1kYXkge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBwYWRkaW5nOiAwLjE4NXJlbSAwLjI1cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHdpZHRoOiAycmVtO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmN1c3RvbS1kYXk6aG92ZXIsXHJcbi5jdXN0b20tZGF5LmZvY3VzZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LnJhbmdlLFxyXG4uY3VzdG9tLWRheTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LmZhZGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDEyMywgMjU1LCAwLjUpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLm5hdi1saW5rIHtcclxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,OAAO,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;IC0BjBC,EAAA,CAAAC,cAAA,oBAA8D;IAC7DD,EAAA,CAAAE,MAAA,GACD;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,EAAA,CAAmB;IAC5DN,EAAA,CAAAO,SAAA,GACD;IADCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAJ,UAAA,CAAAK,IAAA,OACD;;;;;IAaAV,EAAA,CAAAC,cAAA,oBAA0E;IACzED,EAAA,CAAAE,MAAA,GACD;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAO,cAAA,CAAAL,EAAA,CAAuB;IACxEN,EAAA,CAAAO,SAAA,GACD;IADCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAE,cAAA,CAAAD,IAAA,OACD;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAwD;IACvDD,EAAA,CAAAE,MAAA,GACD;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAQ,QAAA,CAAAN,EAAA,CAAiB;IACtDN,EAAA,CAAAO,SAAA,GACD;IADCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAG,QAAA,CAAAC,IAAA,OACD;;;;;;IAkCAb,EAAA,CAAAC,cAAA,eAOC;IAFAD,EAAA,CAAAc,UAAA,wBAAAC,yFAAA;MAAA,MAAAC,WAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAF,OAAA,CAAAG,WAAA,GAAAL,QAAA;IAAA,EAAiC,wBAAAM,yFAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA1B,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAG,OAAA,CAAAF,WAAA,GACL,IAAI;IAAA,EADC;IAGjCxB,EAAA,CAAAE,MAAA,GACD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAPNH,EAAA,CAAA2B,WAAA,YAAAC,WAAA,CAAyB,UAAAC,OAAA,CAAAC,OAAA,CAAAX,QAAA,YAAAU,OAAA,CAAAE,SAAA,CAAAZ,QAAA,KAAAU,OAAA,CAAAG,QAAA,CAAAb,QAAA;IAMzBnB,EAAA,CAAAO,SAAA,GACD;IADCP,EAAA,CAAAQ,kBAAA,MAAAW,QAAA,CAAAc,GAAA,MACD;;;;;;IAIAjC,EAAA,CAAAkC,SAAA,aAAiB;IACjBlC,EAAA,CAAAC,cAAA,cAAgD;IAI9CD,EAAA,CAAAc,UAAA,mBAAAqB,sFAAA;MAAAnC,EAAA,CAAAiB,aAAA,CAAAmB,IAAA;MAAApC,EAAA,CAAAsB,aAAA;MAAA,MAAAe,IAAA,GAAArC,EAAA,CAAAsC,WAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAsB,aAAA;MAASiB,OAAA,CAAAC,cAAA,EAAgB;MAAA,OAAExC,EAAA,CAAAuB,WAAA,CAAAc,IAAA,CAAAI,KAAA,EAAuB;IAAA,EAAC;IAEnDzC,EAAA,CAAAE,MAAA,cACD;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAIC;IADAD,EAAA,CAAAc,UAAA,mBAAA4B,sFAAA;MAAA1C,EAAA,CAAAiB,aAAA,CAAAmB,IAAA;MAAApC,EAAA,CAAAsB,aAAA;MAAA,MAAAe,IAAA,GAAArC,EAAA,CAAAsC,WAAA;MAAA,OAAStC,EAAA,CAAAuB,WAAA,CAAAc,IAAA,CAAAI,KAAA,EAAuB;IAAA,EAAC;IAEjCzC,EAAA,CAAAE,MAAA,cACD;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAYVH,EAAA,CAAAC,cAAA,oBAA4E;IAC3ED,EAAA,CAAAE,MAAA,GACD;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAuC,UAAA,CAAAC,KAAA,CAAsB;IAC1E5C,EAAA,CAAAO,SAAA,GACD;IADCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAkC,UAAA,CAAAE,KAAA,OACD;;;;;;IApHJ7C,EAAA,CAAAC,cAAA,cAAiB;IAGyBD,EAAA,CAAAE,MAAA,GAAsB;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrEH,EAAA,CAAAC,cAAA,oBAKmC;IADlCD,EAAA,CAAAc,UAAA,2BAAAgC,kFAAAC,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAA0B,OAAA,CAAAC,QAAA,GAAAH,MAAA;IAAA,EAAsB,oBAAAI,2EAAAJ,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAI,OAAA,GAAApD,EAAA,CAAAsB,aAAA;MAAA,OACZtB,EAAA,CAAAuB,WAAA,CAAA6B,OAAA,CAAAC,cAAA,CAAAN,MAAA,CAAsB;IAAA,EADV;;IAEtB/C,EAAA,CAAAsD,UAAA,IAAAC,0DAAA,wBAEY;IACbvD,EAAA,CAAAG,YAAA,EAAY;IAGdH,EAAA,CAAAC,cAAA,cAAsB;IAEuBD,EAAA,CAAAE,MAAA,IAA0B;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7EH,EAAA,CAAAC,cAAA,qBAKuC;IADtCD,EAAA,CAAAc,UAAA,2BAAA0C,mFAAAT,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAS,OAAA,GAAAzD,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAkC,OAAA,CAAAC,YAAA,GAAAX,MAAA;IAAA,EAA0B,oBAAAY,4EAAAZ,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAY,OAAA,GAAA5D,EAAA,CAAAsB,aAAA;MAAA,OAChBtB,EAAA,CAAAuB,WAAA,CAAAqC,OAAA,CAAAC,kBAAA,CAAAd,MAAA,CAA0B;IAAA,EADV;;IAE1B/C,EAAA,CAAAsD,UAAA,KAAAQ,2DAAA,wBAEY;IACb9D,EAAA,CAAAG,YAAA,EAAY;IAEbH,EAAA,CAAAC,cAAA,eAAsB;IACgBD,EAAA,CAAAE,MAAA,IAAoB;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAC,cAAA,qBAKiC;IADhCD,EAAA,CAAAc,UAAA,2BAAAiD,mFAAAhB,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAgB,OAAA,GAAAhE,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAyC,OAAA,CAAAC,MAAA,GAAAlB,MAAA;IAAA,EAAoB,oBAAAmB,4EAAAnB,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAmB,OAAA,GAAAnE,EAAA,CAAAsB,aAAA;MAAA,OACVtB,EAAA,CAAAuB,WAAA,CAAA4C,OAAA,CAAAC,YAAA,CAAArB,MAAA,CAAoB;IAAA,EADV;;IAEpB/C,EAAA,CAAAsD,UAAA,KAAAe,2DAAA,wBAEY;IACbrE,EAAA,CAAAG,YAAA,EAAY;IAEbH,EAAA,CAAAC,cAAA,eAAsB;IACqBD,EAAA,CAAAE,MAAA,IAA0B;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5EH,EAAA,CAAAC,cAAA,eAAyB;IASvBD,EAAA,CAAAc,UAAA,mBAAAwD,uEAAA;MAAAtE,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAuB,OAAA,GAAAvE,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAAgD,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC,wBAAAC,4EAAA1B,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAA0B,OAAA,GAAA1E,EAAA,CAAAsB,aAAA;MAAA,OAGZtB,EAAA,CAAAuB,WAAA,CAAAmD,OAAA,CAAAC,eAAA,CAAA5B,MAAA,CAAuB;IAAA,EAHX;;IAR3B/C,EAAA,CAAAG,YAAA,EAgBE;IACFH,EAAA,CAAAC,cAAA,eAAgC;IAG9BD,EAAA,CAAAc,UAAA,mBAAA8D,wEAAA;MAAA5E,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAA6B,OAAA,GAAA7E,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAAsD,OAAA,CAAAL,cAAA,EAAgB;IAAA,EAAC;IAE1BxE,EAAA,CAAAkC,SAAA,aAAqC;IACtClC,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAsD,UAAA,KAAAwB,6DAAA,iCAAA9E,EAAA,CAAA+E,sBAAA,CAWc;IAEd/E,EAAA,CAAAsD,UAAA,KAAA0B,6DAAA,iCAAAhF,EAAA,CAAA+E,sBAAA,CAkBc;IACf/E,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAsB;IACuBD,EAAA,CAAAE,MAAA,IAA4B;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChFH,EAAA,CAAAC,cAAA,qBAKwC;IADvCD,EAAA,CAAAc,UAAA,2BAAAmE,mFAAAlC,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAkC,OAAA,GAAAlF,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAA2D,OAAA,CAAAC,WAAA,GAAApC,MAAA;IAAA,EAAyB,oBAAAqC,4EAAArC,MAAA;MAAA/C,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAqC,OAAA,GAAArF,EAAA,CAAAsB,aAAA;MAAA,OACftB,EAAA,CAAAuB,WAAA,CAAA8D,OAAA,CAAAC,mBAAA,CAAAvC,MAAA,CAA2B;IAAA,EADZ;;IAEzB/C,EAAA,CAAAsD,UAAA,KAAAiC,2DAAA,wBAEY;IACbvF,EAAA,CAAAG,YAAA,EAAY;IAGdH,EAAA,CAAAC,cAAA,eAAsB;IAEpBD,EAAA,CAAAkC,SAAA,iBACQ;IACTlC,EAAA,CAAAG,YAAA,EAAM;;;;;;IAzHkCH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,kBAAsB;IAI5DT,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAyF,qBAAA,gBAAAzF,EAAA,CAAAS,WAAA,yBAA2C;IAF3CT,EAAA,CAAAI,UAAA,oBAAmB,gCAAAsF,MAAA,CAAAxC,QAAA;IAKWlD,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAI,UAAA,YAAAsF,MAAA,CAAAC,OAAA,CAAU;IAQE3F,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,uBAA0B;IAIpET,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAyF,qBAAA,gBAAAzF,EAAA,CAAAS,WAAA,8BAA+C;IAF/CT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAsF,MAAA,CAAAhC,YAAA;IAKe1D,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,YAAAsF,MAAA,CAAAE,WAAA,CAAc;IAMZ5F,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,iBAAoB;IAIxDT,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAyF,qBAAA,gBAAAzF,EAAA,CAAAS,WAAA,wBAAyC;IAFzCT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAsF,MAAA,CAAAzB,MAAA;IAKSjE,EAAA,CAAAO,SAAA,GAAQ;IAARP,EAAA,CAAAI,UAAA,YAAAsF,MAAA,CAAAG,KAAA,CAAQ;IAMK7F,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,uBAA0B;IAKlET,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAyF,qBAAA,gBAAAzF,EAAA,CAAAS,WAAA,8BAA+C;IAI/CT,EAAA,CAAAI,UAAA,UAAAsF,MAAA,CAAAI,SAAA,CAAAC,MAAA,CAAAL,MAAA,CAAAM,QAAA,KAAAN,MAAA,CAAAO,MAAA,WAAAP,MAAA,CAAAI,SAAA,CAAAC,MAAA,CAAAL,MAAA,CAAAO,MAAA,QAAuF,gBAAAC,IAAA,oBAAAC,IAAA;IAsD7CnG,EAAA,CAAAO,SAAA,IAA4B;IAA5BP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,yBAA4B;IAIvET,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAyF,qBAAA,gBAAAzF,EAAA,CAAAS,WAAA,0BAA2C;IAF3CT,EAAA,CAAAI,UAAA,qBAAoB,gCAAAsF,MAAA,CAAAP,WAAA;IAKUnF,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAI,UAAA,YAAAsF,MAAA,CAAAU,kBAAA,CAAqB;IAQnCpG,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,cAAAsF,MAAA,CAAAW,SAAA,CAAuB,cAAAX,MAAA,CAAAY,SAAA;;;;;IAa3CtG,EAAA,CAAAC,cAAA,cAAiB;IAEXD,EAAA,CAAAE,MAAA,GAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;IADjCH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,mCAAwC;IACzCT,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,yBAA8B;;;;;IAWnCT,EAAA,CAAAC,cAAA,cAAiB;IAEXD,EAAA,CAAAE,MAAA,GAA+B;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;IADjCH,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,0BAA+B;IAChCT,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAS,WAAA,yBAA8B;;;;;IAe3CT,EAAA,CAAAkC,SAAA,6BACqB;;;;IADiBlC,EAAA,CAAAI,UAAA,UAAAmG,OAAA,CAAAC,SAAA,CAAmB,WAAAD,OAAA,CAAAE,MAAA,YAAAF,OAAA,CAAAG,MAAA;;;;;IAD1D1G,EAAA,CAAAC,cAAA,uBAA+I;IAC9ID,EAAA,CAAAsD,UAAA,IAAAqD,oEAAA,iCACqB;IACtB3G,EAAA,CAAAG,YAAA,EAAe;;;;IAH8EH,EAAA,CAAAI,UAAA,SAAAwG,MAAA,CAAAC,UAAA,CAAmB;IAC1F7G,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAwG,MAAA,CAAAJ,SAAA,CAAe;;;;;;IAKpCxG,EAAA,CAAAC,cAAA,kCACiD;IADaD,EAAA,CAAAc,UAAA,qBAAAgG,0FAAA/D,MAAA;MAAA,MAAA/B,WAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAA8F,IAAA;MAAA,MAAAC,WAAA,GAAAhG,WAAA,CAAAiG,aAAA;MAAA,OAAWjH,EAAA,CAAAuB,WAAA,CAAAyF,WAAA,CAAAjE,MAAA,CAAe;IAAA,EAAC;IACxC/C,EAAA,CAAAG,YAAA,EAA0B;;;;;IADlDH,EAAA,CAAAI,UAAA,YAAA8G,MAAA,CAAAC,UAAA,CAAsB,SAAAC,QAAA;;;ADzKhD,OAAM,MAAOC,sBAAsB;EAqCjCC,YACUC,QAAqB,EACtBzB,SAAiC,EAChC0B,iBAAmC,EACnCC,eAA+B,EAC/BC,eAA+B,EAC/BC,YAAyB,EACzBC,MAAqB,EACrBC,oBAAyC,EACzCC,kBAAqC,EACrCC,mBAAuC,EACvCC,YAAyB,EACzBC,YAAmB,EACnBC,GAAsB;IAZtB,KAAAX,QAAQ,GAARA,QAAQ;IACT,KAAAzB,SAAS,GAATA,SAAS;IACR,KAAA0B,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IA9CN,KAAAC,SAAS,GAAG,cAAc,CAAC,CAAC;IAInC,KAAA9B,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAiB,IAAIvG,OAAO,EAAE;IAEvC;IACO,KAAA4F,OAAO,GAAU,EAAE;IACnB,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,KAAK,GAAU,EAAE;IACjB,KAAAO,kBAAkB,GAAG,CAC1B;MAAExD,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAY,CAAE,EACrC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,CAC3C;IAMM,KAAAsC,WAAW,GAAG,KAAK;IAE1B;IACA,KAAA3D,WAAW,GAAmB,IAAI;IAIlC;IACO,KAAAqF,UAAU,GAAG,gBAAgB;IA4D5B,KAAAuB,iBAAiB,GAAW,EAAE;IA3CpC,IAAI,CAACpC,QAAQ,GAAGuB,QAAQ,CAACc,QAAQ,EAAE;IACnC,IAAI,CAACpC,MAAM,GAAGsB,QAAQ,CAACe,OAAO,CAACf,QAAQ,CAACc,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;EAC9D;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,gBAAgB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEnI,IAAI,EAAE,mBAAmB;UACzBoI,MAAM,EAAE,IAAI;UACZC,IAAI,EAAE;SACP,EACD;UACErI,IAAI,EAAE,SAAS;UACfoI,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACE,mBAAmB,EAAE;IAC1B,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAChB,SAAS,KAAK,cAAc,EAAE;QACrC,IAAI,CAAC7B,SAAS,CAAC8C,IAAI,CAAC,IAAI,CAAC;;IAE7B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC/C,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACgD,WAAW,EAAE;;EAEhC;EAIA;;;;EAIAC,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACpB,SAAS,KAAK,IAAI,CAACC,iBAAiB,EAAE;MAC7C,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,SAAS;MAEvC;MACA;MACA,IAAI,IAAI,CAACA,SAAS,KAAK,cAAc,EAAE;QACrCgB,UAAU,CAAC,MAAK;UACd,IAAI,CAACK,2BAA2B,EAAE;QACpC,CAAC,EAAE,CAAC,CAAC;;;EAGX;EAEA;;;EAGQA,2BAA2BA,CAAA;IACjC,IAAI,IAAI,CAACrB,SAAS,KAAK,cAAc,IAAI,IAAI,CAAC3B,SAAS,EAAE;MACvD;MACA,IAAI,IAAI,CAACA,SAAS,CAACiD,UAAU,EAAE;QAC7B,IAAI,CAACjD,SAAS,CAACiD,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5D;UACAA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC,CAACC,KAAK,CAAC,MAAK;UACZ;UACA,IAAI,CAACvD,SAAS,CAAC8C,IAAI,CAAC,IAAI,CAAC;QAC3B,CAAC,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAAC9C,SAAS,CAAC8C,IAAI,CAAC,IAAI,CAAC;;;EAG/B;EAEQJ,mBAAmBA,CAAA;IACzB,IAAI,CAAC3C,SAAS,GAAG;MACfyD,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAEA,CAACK,oBAAyB,EAAEC,QAAa,KAAI;QACjD,MAAMC,aAAa,GAAG;UACpB,GAAGF,oBAAoB;UACvBG,SAAS,EAAE,IAAI,CAACjH,QAAQ;UACxBkH,aAAa,EAAE,IAAI,CAAC1G,YAAY;UAChC2G,OAAO,EAAE,IAAI,CAACpG,MAAM;UACpBqG,YAAY,EAAE,IAAI,CAACnF,WAAW;UAC9BoF,SAAS,EAAE,IAAI,CAACvE,QAAQ,GAAG,IAAI,CAACF,SAAS,CAACC,MAAM,CAAC,IAAI,CAACC,QAAQ,CAAC,GAAG,IAAI;UACtEwE,OAAO,EAAE,IAAI,CAACvE,MAAM,GAAG,IAAI,CAACH,SAAS,CAACC,MAAM,CAAC,IAAI,CAACE,MAAM,CAAC,GAAG;SAC7D;QAED;QACA,MAAMwE,QAAQ,GAAG;UACfC,YAAY,EAAE,EAAE;UAChBC,eAAe,EAAE,EAAE;UACnBC,IAAI,EAAE,CACJ;YAAEC,QAAQ,EAAE,CAAC;YAAEC,SAAS,EAAE,QAAQ;YAAEC,MAAM,EAAE,EAAE;YAAEC,GAAG,EAAE,CAAC;YAAEC,KAAK,EAAE,CAAC;YAAEC,IAAI,EAAE,CAAC;YAAEC,SAAS,EAAE,EAAE;YAAEC,aAAa,EAAE,CAAC;YAAEC,eAAe,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAE,CAAE,EAC7I;YAAET,QAAQ,EAAE,CAAC;YAAEC,SAAS,EAAE,QAAQ;YAAEC,MAAM,EAAE,EAAE;YAAEC,GAAG,EAAE,CAAC;YAAEC,KAAK,EAAE,CAAC;YAAEC,IAAI,EAAE,CAAC;YAAEC,SAAS,EAAE,EAAE;YAAEC,aAAa,EAAE,EAAE;YAAEC,eAAe,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE,EAC7I;YAAET,QAAQ,EAAE,CAAC;YAAEC,SAAS,EAAE,QAAQ;YAAEC,MAAM,EAAE,EAAE;YAAEC,GAAG,EAAE,CAAC;YAAEC,KAAK,EAAE,CAAC;YAAEC,IAAI,EAAE,CAAC;YAAEC,SAAS,EAAE,EAAE;YAAEC,aAAa,EAAE,EAAE;YAAEC,eAAe,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;SAEhJ;QAEDnC,UAAU,CAAC,MAAK;UACdc,QAAQ,CAAC;YACPS,YAAY,EAAED,QAAQ,CAACC,YAAY;YACnCC,eAAe,EAAEF,QAAQ,CAACE,eAAe;YACzCC,IAAI,EAAEH,QAAQ,CAACG;WAChB,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDW,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,UAAU;QAAEZ,IAAI,EAAE;MAAU,CAAE,EACvC;QAAEY,KAAK,EAAE,MAAM;QAAEZ,IAAI,EAAE;MAAW,CAAE,EACpC;QAAEY,KAAK,EAAE,QAAQ;QAAEZ,IAAI,EAAE;MAAQ,CAAE,EACnC;QAAEY,KAAK,EAAE,KAAK;QAAEZ,IAAI,EAAE;MAAK,CAAE,EAC7B;QAAEY,KAAK,EAAE,OAAO;QAAEZ,IAAI,EAAE;MAAO,CAAE,EACjC;QAAEY,KAAK,EAAE,MAAM;QAAEZ,IAAI,EAAE;MAAM,CAAE,EAC/B;QAAEY,KAAK,EAAE,WAAW;QAAEZ,IAAI,EAAE;MAAW,CAAE,EACzC;QAAEY,KAAK,EAAE,eAAe;QAAEZ,IAAI,EAAE;MAAe,CAAE,EACjD;QAAEY,KAAK,EAAE,iBAAiB;QAAEZ,IAAI,EAAE;MAAiB,CAAE,EACrD;QAAEY,KAAK,EAAE,QAAQ;QAAEZ,IAAI,EAAE;MAAQ,CAAE;KAEtC;EACH;EAEQ3B,eAAeA,CAAA;IACrB;IACA,IAAI,CAACtD,OAAO,GAAG,CACb;MAAErF,EAAE,EAAE,CAAC;MAAEI,IAAI,EAAE;IAAa,CAAE,EAC9B;MAAEJ,EAAE,EAAE,CAAC;MAAEI,IAAI,EAAE;IAAa,CAAE,CAC/B;IAED,IAAI,CAACkF,WAAW,GAAG,CACjB;MAAEtF,EAAE,EAAE,CAAC;MAAEI,IAAI,EAAE;IAAgB,CAAE,EACjC;MAAEJ,EAAE,EAAE,CAAC;MAAEI,IAAI,EAAE;IAAc,CAAE,CAChC;IAED,IAAI,CAACmF,KAAK,GAAG,CACX;MAAEvF,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,KAAK;MAAEH,IAAI,EAAE;IAAmB,CAAE,EACjD;MAAEJ,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,KAAK;MAAEH,IAAI,EAAE;IAAW,CAAE,EACzC;MAAEJ,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,KAAK;MAAEH,IAAI,EAAE;IAAS,CAAE,CACxC;EACH;EAEA;EACA2C,cAAcA,CAACH,QAAa;IAC1B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACuI,gBAAgB,EAAE;EACzB;EAEA5H,kBAAkBA,CAACH,YAAiB;IAClC,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC+H,gBAAgB,EAAE;EACzB;EAEArH,YAAYA,CAACH,MAAW;IACtB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACwH,gBAAgB,EAAE;EACzB;EAEAnG,mBAAmBA,CAACoG,MAAc;IAChC,IAAI,CAACvG,WAAW,GAAGuG,MAAM;IACzB,IAAI,CAACD,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACjF,SAAS,IAAI,IAAI,CAACA,SAAS,CAACiD,UAAU,EAAE;MAC/C,IAAI,CAACjD,SAAS,CAACiD,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEA;EACAjF,eAAeA,CAACvD,IAAa;IAC3B,IAAI,CAAC,IAAI,CAAC4E,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClC,IAAI,CAACD,QAAQ,GAAG5E,IAAI;KACrB,MAAM,IAAI,IAAI,CAAC4E,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI7E,IAAI,IAAIA,IAAI,CAACuK,KAAK,CAAC,IAAI,CAAC3F,QAAQ,CAAC,EAAE;MAC7E,IAAI,CAACC,MAAM,GAAG7E,IAAI;KACnB,MAAM;MACL,IAAI,CAAC6E,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAG5E,IAAI;;IAEtB,IAAI,CAACqK,gBAAgB,EAAE;EACzB;EAEA1J,SAASA,CAACX,IAAa;IACrB,OAAO,IAAI,CAAC4E,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,IAAI,CAACzE,WAAW,IAAIJ,IAAI,CAACuK,KAAK,CAAC,IAAI,CAAC3F,QAAQ,CAAC,IAAI5E,IAAI,CAACwK,MAAM,CAAC,IAAI,CAACpK,WAAW,CAAC;EACxH;EAEAQ,QAAQA,CAACZ,IAAa;IACpB,OAAO,IAAI,CAAC6E,MAAM,IAAI7E,IAAI,CAACuK,KAAK,CAAC,IAAI,CAAC3F,QAAQ,CAAC,IAAI5E,IAAI,CAACwK,MAAM,CAAC,IAAI,CAAC3F,MAAM,CAAC;EAC7E;EAEAnE,OAAOA,CAACV,IAAa;IACnB,OAAOA,IAAI,CAACyK,MAAM,CAAC,IAAI,CAAC7F,QAAQ,CAAC,IAAK,IAAI,CAACC,MAAM,IAAI7E,IAAI,CAACyK,MAAM,CAAC,IAAI,CAAC5F,MAAM,CAAE,IAAI,IAAI,CAACjE,QAAQ,CAACZ,IAAI,CAAC,IAAI,IAAI,CAACW,SAAS,CAACX,IAAI,CAAC;EAC/H;EAEAoD,cAAcA,CAAA;IACZ;EAAA;EAGFhC,cAAcA,CAAA;IACZ,IAAI,CAACwD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACwF,gBAAgB,EAAE;EACzB;EAEA,IAAIK,iBAAiBA,CAAA;IACnB,OAAO,KAAK,CAAC,CAAC;EAChB;EAAC,QAAAC,CAAA;qBA/QU1E,sBAAsB,EAAArH,EAAA,CAAAgM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlM,EAAA,CAAAgM,iBAAA,CAAAC,EAAA,CAAAE,sBAAA,GAAAnM,EAAA,CAAAgM,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAArM,EAAA,CAAAgM,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAvM,EAAA,CAAAgM,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAzM,EAAA,CAAAgM,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAA3M,EAAA,CAAAgM,iBAAA,CAAAY,EAAA,CAAAC,aAAA,GAAA7M,EAAA,CAAAgM,iBAAA,CAAAc,EAAA,CAAAC,mBAAA,GAAA/M,EAAA,CAAAgM,iBAAA,CAAAgB,EAAA,CAAAC,iBAAA,GAAAjN,EAAA,CAAAgM,iBAAA,CAAAkB,EAAA,CAAAC,kBAAA,GAAAnN,EAAA,CAAAgM,iBAAA,CAAAoB,GAAA,CAAAC,WAAA,GAAArN,EAAA,CAAAgM,iBAAA,CAAAsB,GAAA,CAAAC,KAAA,GAAAvN,EAAA,CAAAgM,iBAAA,CAAAhM,EAAA,CAAAwN,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAAtBpG,sBAAsB;IAAAqG,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAOtB/N,kBAAkB;;;;;;;;;;;;QC5B/BE,EAAA,CAAAC,cAAA,aAA+C;QAG7CD,EAAA,CAAAkC,SAAA,4BAAyE;QAIzElC,EAAA,CAAAC,cAAA,aAAiB;QAI6BD,EAAA,CAAAc,UAAA,4BAAAiN,6DAAAhL,MAAA;UAAA,OAAA+K,GAAA,CAAA3F,SAAA,GAAApF,MAAA;QAAA,EAAwB;QACjE/C,EAAA,CAAAC,cAAA,YAA8B;QAE5BD,EAAA,CAAAkC,SAAA,aAAiC;QACjClC,EAAA,CAAAE,MAAA,IACD;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAsD,UAAA,KAAA0K,8CAAA,4BAgIc;QACfhO,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAkC;QAEhCD,EAAA,CAAAkC,SAAA,aAAqC;QACrClC,EAAA,CAAAE,MAAA,IACD;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAsD,UAAA,KAAA2K,8CAAA,0BAOc;QACfjO,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAyB;QAEvBD,EAAA,CAAAkC,SAAA,aAAmC;QACnClC,EAAA,CAAAE,MAAA,IACD;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAsD,UAAA,KAAA4K,8CAAA,0BAOc;QACflO,EAAA,CAAAG,YAAA,EAAK;QAENH,EAAA,CAAAkC,SAAA,eAA6C;QAC9ClC,EAAA,CAAAG,YAAA,EAAM;QAOXH,EAAA,CAAAsD,UAAA,KAAA6K,+CAAA,2BAGe;QAEfnO,EAAA,CAAAsD,UAAA,KAAA8K,8CAAA,iCAAApO,EAAA,CAAA+E,sBAAA,CAGc;;;;QA7LQ/E,EAAA,CAAAO,SAAA,GAA+B;QAA/BP,EAAA,CAAAI,UAAA,kBAAA0N,GAAA,CAAAtF,aAAA,CAA+B;QAQLxI,EAAA,CAAAO,SAAA,GAAwB;QAAxBP,EAAA,CAAAI,UAAA,aAAA0N,GAAA,CAAA3F,SAAA,CAAwB;QAI/DnI,EAAA,CAAAO,SAAA,GACD;QADCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,2BACD;QAsICT,EAAA,CAAAO,SAAA,GACD;QADCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,8BACD;QAaCT,EAAA,CAAAO,SAAA,GACD;QADCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,8BACD;QAWGT,EAAA,CAAAO,SAAA,GAAoB;QAApBP,EAAA,CAAAI,UAAA,iBAAAiO,GAAA,CAAoB;QAQhBrO,EAAA,CAAAO,SAAA,GAAuB;QAAvBP,EAAA,CAAAI,UAAA,SAAA0N,GAAA,CAAAhC,iBAAA,CAAuB", "names": ["DataTableDirective", "Subject", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "season_r16", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "name", "tournament_r17", "club_r18", "code", "ɵɵlistener", "LeagueReportsComponent_ng_template_14_ng_template_35_Template_span_mouseenter_0_listener", "restoredCtx", "ɵɵrestoreView", "_r22", "date_r19", "date", "ctx_r21", "ɵɵnextContext", "ɵɵresetView", "hoveredDate", "LeagueReportsComponent_ng_template_14_ng_template_35_Template_span_mouseleave_0_listener", "ctx_r23", "ɵɵclassProp", "focused_r20", "ctx_r12", "isRange", "isHovered", "isInside", "day", "ɵɵelement", "LeagueReportsComponent_ng_template_14_ng_template_37_Template_button_click_2_listener", "_r25", "_r10", "ɵɵreference", "ctx_r24", "clearDateRange", "close", "LeagueReportsComponent_ng_template_14_ng_template_37_Template_button_click_4_listener", "status_r27", "value", "label", "LeagueReportsComponent_ng_template_14_Template_ng_select_ngModelChange_6_listener", "$event", "_r29", "ctx_r28", "seasonId", "LeagueReportsComponent_ng_template_14_Template_ng_select_change_6_listener", "ctx_r30", "onSelectSeason", "ɵɵtemplate", "LeagueReportsComponent_ng_template_14_ng_option_8_Template", "LeagueReportsComponent_ng_template_14_Template_ng_select_ngModelChange_14_listener", "ctx_r31", "tournamentId", "LeagueReportsComponent_ng_template_14_Template_ng_select_change_14_listener", "ctx_r32", "onSelectTournament", "LeagueReportsComponent_ng_template_14_ng_option_16_Template", "LeagueReportsComponent_ng_template_14_Template_ng_select_ngModelChange_21_listener", "ctx_r33", "clubId", "LeagueReportsComponent_ng_template_14_Template_ng_select_change_21_listener", "ctx_r34", "onSelectClub", "LeagueReportsComponent_ng_template_14_ng_option_23_Template", "LeagueReportsComponent_ng_template_14_Template_input_click_29_listener", "ctx_r35", "openDatePicker", "LeagueReportsComponent_ng_template_14_Template_input_dateSelect_29_listener", "ctx_r36", "onDateSelection", "LeagueReportsComponent_ng_template_14_Template_button_click_33_listener", "ctx_r37", "LeagueReportsComponent_ng_template_14_ng_template_35_Template", "ɵɵtemplateRefExtractor", "LeagueReportsComponent_ng_template_14_ng_template_37_Template", "LeagueReportsComponent_ng_template_14_Template_ng_select_ngModelChange_43_listener", "ctx_r38", "matchStatus", "LeagueReportsComponent_ng_template_14_Template_ng_select_change_43_listener", "ctx_r39", "onSelectMatchStatus", "LeagueReportsComponent_ng_template_14_ng_option_45_Template", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate", "ctx_r1", "seasons", "tournaments", "clubs", "formatter", "format", "fromDate", "toDate", "_r11", "_r13", "matchStatusOptions", "dtOptions", "dtTrigger", "ctx_r40", "dtElement", "fields", "params", "LeagueReportsComponent_core_sidebar_28_app_editor_sidebar_1_Template", "ctx_r4", "table_name", "LeagueReportsComponent_ng_template_29_Template_app_btn_dropdown_action_emitter_0_listener", "_r44", "emitter_r42", "captureEvents", "ctx_r6", "rowActions", "data_r41", "LeagueReportsComponent", "constructor", "calendar", "_translateService", "_commonsService", "_loadingService", "_teamService", "toastr", "_registrationService", "_tournamentService", "_coreSidebarService", "_clubService", "titleService", "cdr", "activeTab", "previousActiveTab", "get<PERSON><PERSON>y", "getNext", "ngOnInit", "contentHeader", "headerTitle", "actionText", "breadcrumb", "type", "links", "isLink", "link", "initializeDataTable", "loadInitialData", "ngAfterViewInit", "setTimeout", "next", "ngOnDestroy", "unsubscribe", "ngAfterViewChecked", "initializeDataTableIfNeeded", "dtInstance", "then", "ajax", "reload", "catch", "serverSide", "processing", "dataTablesParameters", "callback", "requestParams", "season_id", "tournament_id", "club_id", "match_status", "from_date", "to_date", "mockData", "recordsTotal", "recordsFiltered", "data", "position", "team_name", "played", "won", "drawn", "lost", "goals_for", "goals_against", "goal_difference", "points", "columns", "title", "refreshDataTable", "status", "after", "before", "equals", "shouldShowSidebar", "_", "ɵɵdirectiveInject", "i1", "NgbCalendar", "NgbDateParserFormatter", "i2", "TranslateService", "i3", "CommonsService", "i4", "LoadingService", "i5", "TeamService", "i6", "ToastrService", "i7", "RegistrationService", "i8", "TournamentService", "i9", "CoreSidebarService", "i10", "ClubService", "i11", "Title", "ChangeDetectorRef", "_2", "selectors", "viewQuery", "LeagueReportsComponent_Query", "rf", "ctx", "LeagueReportsComponent_Template_ul_activeIdChange_7_listener", "LeagueReportsComponent_ng_template_14_Template", "LeagueReportsComponent_ng_template_20_Template", "LeagueReportsComponent_ng_template_26_Template", "LeagueReportsComponent_core_sidebar_28_Template", "LeagueReportsComponent_ng_template_29_Template", "_r0"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, ChangeDetectorRef } from '@angular/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { Subject } from 'rxjs';\r\nimport { Ngb<PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>ate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  \r\n  // Public variables\r\n  public contentHeader: object;\r\n  public activeTab = 'league_table'; // NgBootstrap nav active tab\r\n  \r\n  // DataTable variables\r\n  @ViewChild(DataTableDirective, { static: false }) dtElement: DataTableDirective;\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<any> = new Subject();\r\n\r\n  // Data arrays\r\n  public seasons: any[] = [];\r\n  public tournaments: any[] = [];\r\n  public clubs: any[] = [];\r\n  public matchStatusOptions = [\r\n    { value: 'all', label: 'All Status' },\r\n    { value: 'upcoming', label: 'Upcoming' },\r\n    { value: 'active', label: 'Active' },\r\n    { value: 'completed', label: 'Completed' },\r\n    { value: 'cancelled', label: 'Cancelled' }\r\n  ];\r\n\r\n  // Selected values\r\n  public seasonId: any;\r\n  public tournamentId: any;\r\n  public clubId: any;\r\n  public matchStatus = 'all';\r\n\r\n  // Date picker variables\r\n  hoveredDate: NgbDate | null = null;\r\n  fromDate: NgbDate | null;\r\n  toDate: NgbDate | null;\r\n\r\n  // Table configuration\r\n  public table_name = 'league-reports';\r\n\r\n  constructor(\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter,\r\n    private _translateService: TranslateService,\r\n    private _commonsService: CommonsService,\r\n    private _loadingService: LoadingService,\r\n    private _teamService: TeamService,\r\n    private toastr: ToastrService,\r\n    private _registrationService: RegistrationService,\r\n    private _tournamentService: TournamentService,\r\n    private _coreSidebarService: CoreSidebarService,\r\n    private _clubService: ClubService,\r\n    private titleService: Title,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    this.fromDate = calendar.getToday();\r\n    this.toDate = calendar.getNext(calendar.getToday(), 'd', 10);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: 'League Reports',\r\n      actionText: '',\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'League Tournament',\r\n            isLink: true,\r\n            link: '/league-tournament'\r\n          },\r\n          {\r\n            name: 'Reports',\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this.initializeDataTable();\r\n    this.loadInitialData();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // DataTable will be initialized when the league_table tab becomes active\r\n    setTimeout(() => {\r\n      if (this.activeTab === 'league_table') {\r\n        this.dtTrigger.next(null);\r\n      }\r\n    }, 100);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.dtTrigger) {\r\n      this.dtTrigger.unsubscribe();\r\n    }\r\n  }\r\n\r\n  private previousActiveTab: string = '';\r\n\r\n  /**\r\n   * Lifecycle hook to check for tab changes reactively\r\n   * This follows Angular best practices and avoids ExpressionChangedAfterItHasBeenCheckedError\r\n   */\r\n  ngAfterViewChecked(): void {\r\n    // Only process if tab actually changed (avoid unnecessary processing)\r\n    if (this.activeTab !== this.previousActiveTab) {\r\n      this.previousActiveTab = this.activeTab;\r\n      \r\n      // Use setTimeout to defer DataTable initialization to next tick\r\n      // This prevents ExpressionChangedAfterItHasBeenCheckedError\r\n      if (this.activeTab === 'league_table') {\r\n        setTimeout(() => {\r\n          this.initializeDataTableIfNeeded();\r\n        }, 0);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize DataTable only if not already initialized\r\n   */\r\n  private initializeDataTableIfNeeded(): void {\r\n    if (this.activeTab === 'league_table' && this.dtElement) {\r\n      // Check if DataTable is already initialized\r\n      if (this.dtElement.dtInstance) {\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          // Reload existing table\r\n          dtInstance.ajax.reload();\r\n        }).catch(() => {\r\n          // If reload fails, trigger new table\r\n          this.dtTrigger.next(null);\r\n        });\r\n      } else {\r\n        // Initialize new table\r\n        this.dtTrigger.next(null);\r\n      }\r\n    }\r\n  }\r\n\r\n  private initializeDataTable(): void {\r\n    this.dtOptions = {\r\n      serverSide: true,\r\n      processing: true,\r\n      ajax: (dataTablesParameters: any, callback: any) => {\r\n        const requestParams = {\r\n          ...dataTablesParameters,\r\n          season_id: this.seasonId,\r\n          tournament_id: this.tournamentId,\r\n          club_id: this.clubId,\r\n          match_status: this.matchStatus,\r\n          from_date: this.fromDate ? this.formatter.format(this.fromDate) : null,\r\n          to_date: this.toDate ? this.formatter.format(this.toDate) : null\r\n        };\r\n\r\n        // Mock data for now - replace with actual service call\r\n        const mockData = {\r\n          recordsTotal: 10,\r\n          recordsFiltered: 10,\r\n          data: [\r\n            { position: 1, team_name: 'Team A', played: 10, won: 8, drawn: 1, lost: 1, goals_for: 25, goals_against: 8, goal_difference: 17, points: 25 },\r\n            { position: 2, team_name: 'Team B', played: 10, won: 6, drawn: 2, lost: 2, goals_for: 18, goals_against: 12, goal_difference: 6, points: 20 },\r\n            { position: 3, team_name: 'Team C', played: 10, won: 5, drawn: 3, lost: 2, goals_for: 15, goals_against: 10, goal_difference: 5, points: 18 }\r\n          ]\r\n        };\r\n        \r\n        setTimeout(() => {\r\n          callback({\r\n            recordsTotal: mockData.recordsTotal,\r\n            recordsFiltered: mockData.recordsFiltered,\r\n            data: mockData.data\r\n          });\r\n        }, 500);\r\n      },\r\n      columns: [\r\n        { title: 'Position', data: 'position' },\r\n        { title: 'Team', data: 'team_name' },\r\n        { title: 'Played', data: 'played' },\r\n        { title: 'Won', data: 'won' },\r\n        { title: 'Drawn', data: 'drawn' },\r\n        { title: 'Lost', data: 'lost' },\r\n        { title: 'Goals For', data: 'goals_for' },\r\n        { title: 'Goals Against', data: 'goals_against' },\r\n        { title: 'Goal Difference', data: 'goal_difference' },\r\n        { title: 'Points', data: 'points' }\r\n      ]\r\n    };\r\n  }\r\n\r\n  private loadInitialData(): void {\r\n    // Mock data for dropdowns - replace with actual service calls\r\n    this.seasons = [\r\n      { id: 1, name: '2024 Season' },\r\n      { id: 2, name: '2023 Season' }\r\n    ];\r\n\r\n    this.tournaments = [\r\n      { id: 1, name: 'Premier League' },\r\n      { id: 2, name: 'Championship' }\r\n    ];\r\n\r\n    this.clubs = [\r\n      { id: 1, code: 'MUN', name: 'Manchester United' },\r\n      { id: 2, code: 'LIV', name: 'Liverpool' },\r\n      { id: 3, code: 'CHE', name: 'Chelsea' }\r\n    ];\r\n  }\r\n\r\n  // Event handlers for filters\r\n  onSelectSeason(seasonId: any): void {\r\n    this.seasonId = seasonId;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectTournament(tournamentId: any): void {\r\n    this.tournamentId = tournamentId;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectClub(clubId: any): void {\r\n    this.clubId = clubId;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus(status: string): void {\r\n    this.matchStatus = status;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  private refreshDataTable(): void {\r\n    if (this.dtElement && this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  // Date picker methods\r\n  onDateSelection(date: NgbDate): void {\r\n    if (!this.fromDate && !this.toDate) {\r\n      this.fromDate = date;\r\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\r\n      this.toDate = date;\r\n    } else {\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n    }\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  isHovered(date: NgbDate): boolean {\r\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\r\n  }\r\n\r\n  isInside(date: NgbDate): boolean {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate): boolean {\r\n    return date.equals(this.fromDate) || (this.toDate && date.equals(this.toDate)) || this.isInside(date) || this.isHovered(date);\r\n  }\r\n\r\n  openDatePicker(): void {\r\n    // Date picker will open automatically when input is clicked\r\n  }\r\n\r\n  clearDateRange(): void {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  get shouldShowSidebar(): boolean {\r\n    return false; // Simplified for now\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n\t<div class=\"content-body\">\r\n\t\t<!-- content-header component -->\r\n\t\t<app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n\t\t\r\n\r\n\t\t<div class=\"row\">\r\n\t\t\t<div class=\"col-12\">\r\n\t\t\t\t<div class=\"card\">\r\n\t\t\t\t\t<div class=\"card-body\">\r\n\t\t\t\t\t\t<ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs\" [(activeId)]=\"activeTab\">\r\n\t\t\t\t\t\t\t<li ngbNavItem=\"league_table\">\r\n\t\t\t\t\t\t\t\t<a ngbNavLink>\r\n\t\t\t\t\t\t\t\t\t<i class=\"feather icon-grid\"></i>\r\n\t\t\t\t\t\t\t\t\t{{ 'Table View' | translate }}\r\n\t\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t\t<ng-template ngbNavContent>\r\n\t\t\t\t\t\t\t\t\t<div class=\"p-1\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-12\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"season\" class=\"form-label\">{{'Season'|translate}}</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ng-select \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[searchable]=\"true\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[clearable]=\"false\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Season'|translate}}\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[(ngModel)]=\"seasonId\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t(change)=\"onSelectSeason($event)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<ng-option *ngFor=\"let season of seasons\" [value]=\"season.id\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ season.name | translate }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</ng-option>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</ng-select>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"tournament\" class=\"form-label\">{{'Tournament'|translate}}</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ng-select \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[searchable]=\"true\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[clearable]=\"true\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Tournament'|translate}}\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[(ngModel)]=\"tournamentId\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t(change)=\"onSelectTournament($event)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<ng-option *ngFor=\"let tournament of tournaments\" [value]=\"tournament.id\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ tournament.name | translate }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</ng-option>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</ng-select>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"club\" class=\"form-label\">{{'Club'|translate}}</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ng-select \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[searchable]=\"true\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[clearable]=\"true\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Club'|translate}}\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[(ngModel)]=\"clubId\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t(change)=\"onSelectClub($event)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<ng-option *ngFor=\"let club of clubs\" [value]=\"club.id\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ club.code | translate }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</ng-option>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</ng-select>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"dateRange\" class=\"form-label\">{{'Date Range'|translate}}</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"input-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"daterange\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"form-control\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Date Range'|translate}}\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tngbDatepicker\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\treadonly\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t#dateRangePicker=\"ngbDatepicker\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[value]=\"formatter.format(fromDate) + (toDate ? ' - ' + formatter.format(toDate) : '')\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(click)=\"openDatePicker()\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[dayTemplate]=\"dayTemplate\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[footerTemplate]=\"footerTemplate\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(dateSelect)=\"onDateSelection($event)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[firstDayOfWeek]=\"1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[displayMonths]=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\toutsideDays=\"hidden\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[autoClose]=\"false\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"input-group-append\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"btn btn-outline-secondary\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(click)=\"openDatePicker()\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"feather icon-calendar\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ng-template #dayTemplate let-date=\"date\" let-focused=\"focused\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"custom-day\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[class.focused]=\"focused\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[class.range]=\"isRange(date)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t[class.faded]=\"isHovered(date) || isInside(date)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(mouseenter)=\"hoveredDate = date\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(mouseleave)=\"hoveredDate = null\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ date.day }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</ng-template>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ng-template #footerTemplate>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<hr class=\"my-0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"d-flex justify-content-between p-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"btn btn-outline-secondary btn-sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(click)=\"clearDateRange(); dateRangePicker.close()\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tClear\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"btn btn-primary btn-sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(click)=\"dateRangePicker.close()\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tClose\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</ng-template>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"matchStatus\" class=\"form-label\">{{'Match Status'|translate}}</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ng-select \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[searchable]=\"false\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[clearable]=\"false\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Status'|translate}}\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t[(ngModel)]=\"matchStatus\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t(change)=\"onSelectMatchStatus($event)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<ng-option *ngFor=\"let status of matchStatusOptions\" [value]=\"status.value\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ status.label | translate }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</ng-option>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</ng-select>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-12\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"table border row-border hover\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t</table>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</ng-template>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t<li ngbNavItem=\"schedule_matches\">\r\n\t\t\t\t\t\t\t\t<a ngbNavLink>\r\n\t\t\t\t\t\t\t\t\t<i class=\"feather icon-calendar\"></i>\r\n\t\t\t\t\t\t\t\t\t{{ 'Schedule View' | translate }}\r\n\t\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t\t<ng-template ngbNavContent>\r\n\t\t\t\t\t\t\t\t\t<div class=\"p-2\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<h4>{{'Schedule Matches Content'|translate}}</h4>\r\n\t\t\t\t\t\t\t\t\t\t\t<p>{{'Coming soon...'|translate}}</p>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</ng-template>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t<li ngbNavItem=\"bracket\">\r\n\t\t\t\t\t\t\t\t<a ngbNavLink>\r\n\t\t\t\t\t\t\t\t\t<i class=\"feather icon-layers\"></i>\r\n\t\t\t\t\t\t\t\t\t{{ 'Bracket View' | translate }}\r\n\t\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t\t<ng-template ngbNavContent>\r\n\t\t\t\t\t\t\t\t\t<div class=\"p-2\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<h4>{{'Bracket Content'|translate}}</h4>\r\n\t\t\t\t\t\t\t\t\t\t\t<p>{{'Coming soon...'|translate}}</p>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</ng-template>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t<div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</div>\r\n\r\n<core-sidebar *ngIf=\"shouldShowSidebar\" class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n\t<app-editor-sidebar *ngIf=\"dtElement\" [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\r\n\t</app-editor-sidebar>\r\n</core-sidebar>\r\n\r\n<ng-template #rowActionBtn let-data=\"adtData\" let-emitter=\"captureEvents\">\r\n\t<app-btn-dropdown-action [actions]=\"rowActions\" [data]=\"data\" (emitter)=\"emitter($event)\"\r\n\t\tbtnStyle=\"font-size:15px;color:black!important\"></app-btn-dropdown-action>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}