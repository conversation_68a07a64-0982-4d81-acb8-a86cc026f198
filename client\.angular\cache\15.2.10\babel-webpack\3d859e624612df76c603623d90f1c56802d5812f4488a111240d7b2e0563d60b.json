{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { environment } from 'environments/environment';\nimport moment from 'moment';\nimport { NgbDate } from '@ng-bootstrap/ng-bootstrap';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"app/services/registration.service\";\nimport * as i6 from \"app/services/club.service\";\nimport * as i7 from \"@angular/platform-browser\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"app/layout/components/content-header/content-header.component\";\nimport * as i12 from \"@ng-select/ng-select\";\nconst _c0 = [\"dateRangePicker\"];\nfunction LeagueReportsComponent_ng_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, season_r12.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tournament_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", tournament_r13.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, tournament_r13.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const club_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", club_r14.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, club_r14.code), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵlistener(\"mouseenter\", function LeagueReportsComponent_ng_template_42_Template_span_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const date_r15 = restoredCtx.date;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.hoveredDate = date_r15);\n    })(\"mouseleave\", function LeagueReportsComponent_ng_template_42_Template_span_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.hoveredDate = null);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r15 = ctx.date;\n    const focused_r16 = ctx.focused;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"focused\", focused_r16)(\"range\", ctx_r5.isRange(date_r15))(\"faded\", ctx_r5.isHovered(date_r15) || ctx_r5.isInside(date_r15));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", date_r15.day, \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelement(0, \"hr\", 37);\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_ng_template_44_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      const _r3 = i0.ɵɵreference(37);\n      ctx_r20.clearDateRange();\n      return i0.ɵɵresetView(_r3.close());\n    });\n    i0.ɵɵtext(3, \" Clear \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_ng_template_44_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r21);\n      i0.ɵɵnextContext();\n      const _r3 = i0.ɵɵreference(37);\n      return i0.ɵɵresetView(_r3.close());\n    });\n    i0.ɵɵtext(5, \" Close \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LeagueReportsComponent_ng_option_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, status_r23.label), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-league-table-view\", 41);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"seasonId\", ctx_r10.seasonId)(\"clubId\", ctx_r10.clubId)(\"tournamentId\", ctx_r10.tournamentId)(\"dateRange\", ctx_r10.dateRange)(\"matchStatus\", ctx_r10.matchStatus)(\"seasons\", ctx_r10.seasons)(\"clubs\", ctx_r10.clubs)(\"tournaments\", ctx_r10.tournaments);\n  }\n}\nfunction LeagueReportsComponent_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-schedule-view\", 41);\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"seasonId\", ctx_r11.seasonId)(\"clubId\", ctx_r11.clubId)(\"tournamentId\", ctx_r11.tournamentId)(\"dateRange\", ctx_r11.dateRange)(\"matchStatus\", ctx_r11.matchStatus)(\"seasons\", ctx_r11.seasons)(\"clubs\", ctx_r11.clubs)(\"tournaments\", ctx_r11.tournaments);\n  }\n}\nexport class LeagueReportsComponent {\n  constructor(route, _router, _http, _trans, renderer, _loadingService, _registrationService, _clubService, _translateService, _titleService, calendar, formatter) {\n    this.route = route;\n    this._router = _router;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._loadingService = _loadingService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._titleService = _titleService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false; // Track if we're in the middle of selecting range\n    this.matchStatus = 'all';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n      this.dtTrigger.next(this.dtOptions);\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n      // this.clubId = this.clubs[0];\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n    this.refreshDataTable();\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    var _this = this;\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      processing: true,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        // processing: `<div class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\n        //               <div class=\"spinner-border text-primary\" role=\"status\">\n        //                 <span class=\"sr-only\">Loading...</span>\n        //               </div>\n        //             </div>`,\n        processing: ``\n      },\n      ajax: (dataTablesParameters, callback) => {\n        // Build query parameters for all filters\n        let params = new URLSearchParams();\n        if (this.clubId != undefined && this.clubId !== null) {\n          params.append('club_id', this.clubId);\n        }\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\n          params.append('tournament_id', this.tournamentId);\n        }\n        if (this.dateRange.start_date) {\n          params.append('start_date', this.dateRange.start_date);\n        }\n        if (this.dateRange.end_date) {\n          params.append('end_date', this.dateRange.end_date);\n        }\n        if (this.matchStatus && this.matchStatus !== 'all') {\n          params.append('match_status', this.matchStatus);\n        }\n        const queryString = params.toString();\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${queryString ? '?' + queryString : ''}`;\n        this.isTableLoading = true;\n        this._http.post(url, dataTablesParameters).subscribe(resp => {\n          this.isTableLoading = false;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        }, error => {\n          this.isTableLoading = false;\n          console.error('Error loading table data:', error);\n          callback({\n            recordsTotal: 0,\n            recordsFiltered: 0,\n            data: []\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }],\n      columns: [{\n        title: this._translateService.instant('No.'),\n        data: null,\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: function (data, type, row, metadata) {\n          return metadata.row + 1;\n        }\n      }, {\n        title: this._translateService.instant('Tournament'),\n        data: 'name',\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: {\n          display: (data, type, row) => {\n            return data ?? 'TBD';\n          },\n          filter: (data, type, row) => {\n            return data;\n          }\n        }\n      }, {\n        title: this._translateService.instant('Round'),\n        data: 'round_name',\n        render: function (data) {\n          const displayData = data || 'TBD';\n          return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          const displayDate = !data || data === 'TBD' ? 'TBD' : moment(data).format('YYYY-MM-DD');\n          return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Home Team'),\n        data: 'home_team_name',\n        className: 'text-center'\n      }, {\n        title: 'VS',\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return 'vs';\n        }\n      }, {\n        title: this._translateService.instant('Away Team'),\n        data: 'away_team_name',\n        className: 'text-center'\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center'\n      }, {\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return '-';\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant('Export CSV')}`,\n          extend: 'csv',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              // Generate filename with current filter information\n              let filename = 'Matches_Report';\n              if (_this.seasonId) {\n                const season = _this.seasons?.find(s => s.id == _this.seasonId);\n                if (season) {\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.tournamentId) {\n                const tournament = _this.tournaments?.find(t => t.id == _this.tournamentId);\n                if (tournament) {\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.clubId) {\n                const club = _this.clubs?.find(c => c.id == _this.clubId);\n                if (club) {\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.matchStatus && _this.matchStatus !== 'all') {\n                filename += `_${_this.matchStatus}`;\n              }\n              if (_this.dateRange.start_date && _this.dateRange.end_date) {\n                filename += `_${_this.dateRange.start_date}_to_${_this.dateRange.end_date}`;\n              } else if (_this.dateRange.start_date) {\n                filename += `_from_${_this.dateRange.start_date}`;\n              }\n              filename += '.csv';\n              yield _this._exportService.exportCsv(data, filename);\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  setClubs(data) {\n    this.clubs = data;\n    // get field has key club_id\n    const clubField = this.fields.find(field => field.key === 'club_id');\n    // set options for club field\n    let current_clubs = [];\n    data.forEach(club => {\n      let club_name = this._translateService.instant(club.name);\n      current_clubs.push({\n        label: club_name,\n        value: club.id\n      });\n    });\n    clubField.props.options = current_clubs;\n  }\n  onCaptureEvent(event) {\n    // console.log(event);\n  }\n  editor(action, row) {\n    this.fields[0].defaultValue = this.clubId;\n    switch (action) {\n      case 'create':\n        this.fields[2].props.disabled = false;\n        break;\n      case 'edit':\n        this.fields[2].props.disabled = true;\n        break;\n      case 'remove':\n        break;\n      default:\n        break;\n    }\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('tournament_id')) {\n        let tournament_id = event.target.getAttribute('tournament_id');\n        let stage_id = event.target.getAttribute('stage_id');\n        //  navigate to path ':tournament_id/stages/:stage_id'\n        this._router.navigate([tournament_id, 'stages', stage_id], {\n          relativeTo: this.route\n        });\n      }\n    });\n  }\n  onSelectViewType(event) {\n    this.viewType = event;\n    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  refreshDataTable() {\n    if (this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    this.unlistener();\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function LeagueReportsComponent_Factory(t) {\n    return new (t || LeagueReportsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.RegistrationService), i0.ɵɵdirectiveInject(i6.ClubService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i7.Title), i0.ɵɵdirectiveInject(i8.NgbCalendar), i0.ɵɵdirectiveInject(i8.NgbDateParserFormatter));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueReportsComponent,\n    selectors: [[\"app-league-reports\"]],\n    viewQuery: function LeagueReportsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dateRangePicker = _t.first);\n      }\n    },\n    hostBindings: function LeagueReportsComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function LeagueReportsComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 69,\n    vars: 60,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"league-reports-page\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\", \"mb-2\"], [1, \"card-body\"], [1, \"row\", \"mb-1\"], [\"for\", \"season\", 1, \"form-label\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\"], [\"for\", \"tournament\", 1, \"form-label\"], [\"for\", \"club\", 1, \"form-label\"], [\"for\", \"dateRange\", 1, \"form-label\"], [1, \"input-group\"], [\"name\", \"daterange\", \"ngbDatepicker\", \"\", \"readonly\", \"\", \"outsideDays\", \"hidden\", 1, \"form-control\", 3, \"placeholder\", \"value\", \"dayTemplate\", \"footerTemplate\", \"firstDayOfWeek\", \"displayMonths\", \"autoClose\", \"click\", \"dateSelect\"], [\"dateRangePicker\", \"ngbDatepicker\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"feather\", \"icon-calendar\"], [\"dayTemplate\", \"\"], [\"footerTemplate\", \"\"], [\"for\", \"matchStatus\", 1, \"form-label\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"league_table\"], [\"ngbNavLink\", \"\"], [1, \"fa-light\", \"fa-table-list\", \"mr-1\"], [\"ngbNavContent\", \"\"], [\"ngbNavItem\", \"schedule_matches\"], [1, \"fa-light\", \"fa-calendar\", \"mr-1\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [3, \"value\"], [1, \"custom-day\", 3, \"mouseenter\", \"mouseleave\"], [1, \"my-0\"], [1, \"d-flex\", \"justify-content-between\", \"p-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [3, \"seasonId\", \"clubId\", \"tournamentId\", \"dateRange\", \"matchStatus\", \"seasons\", \"clubs\", \"tournaments\"]],\n    template: function LeagueReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 5)(10, \"label\", 9);\n        i0.ɵɵtext(11);\n        i0.ɵɵpipe(12, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"ng-select\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_13_listener($event) {\n          return ctx.seasonId = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_13_listener($event) {\n          return ctx.onSelectSeason($event);\n        });\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵtemplate(15, LeagueReportsComponent_ng_option_15_Template, 3, 4, \"ng-option\", 11);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(16, \"div\", 8)(17, \"div\", 12)(18, \"label\", 13);\n        i0.ɵɵtext(19);\n        i0.ɵɵpipe(20, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"ng-select\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_21_listener($event) {\n          return ctx.tournamentId = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_21_listener($event) {\n          return ctx.onSelectTournament($event);\n        });\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵtemplate(23, LeagueReportsComponent_ng_option_23_Template, 3, 4, \"ng-option\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"div\", 12)(25, \"label\", 14);\n        i0.ɵɵtext(26);\n        i0.ɵɵpipe(27, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"ng-select\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_28_listener($event) {\n          return ctx.clubId = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_28_listener($event) {\n          return ctx.onSelectClub($event);\n        });\n        i0.ɵɵpipe(29, \"translate\");\n        i0.ɵɵtemplate(30, LeagueReportsComponent_ng_option_30_Template, 3, 4, \"ng-option\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"div\", 12)(32, \"label\", 15);\n        i0.ɵɵtext(33);\n        i0.ɵɵpipe(34, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"div\", 16)(36, \"input\", 17, 18);\n        i0.ɵɵlistener(\"click\", function LeagueReportsComponent_Template_input_click_36_listener() {\n          return ctx.openDatePicker();\n        })(\"dateSelect\", function LeagueReportsComponent_Template_input_dateSelect_36_listener($event) {\n          return ctx.onDateSelection($event);\n        });\n        i0.ɵɵpipe(38, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"div\", 19)(40, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function LeagueReportsComponent_Template_button_click_40_listener() {\n          return ctx.openDatePicker();\n        });\n        i0.ɵɵelement(41, \"i\", 21);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(42, LeagueReportsComponent_ng_template_42_Template, 2, 7, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(44, LeagueReportsComponent_ng_template_44_Template, 6, 0, \"ng-template\", null, 23, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"div\", 12)(47, \"label\", 24);\n        i0.ɵɵtext(48);\n        i0.ɵɵpipe(49, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"ng-select\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_50_listener($event) {\n          return ctx.matchStatus = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_50_listener($event) {\n          return ctx.onSelectMatchStatus($event);\n        });\n        i0.ɵɵpipe(51, \"translate\");\n        i0.ɵɵtemplate(52, LeagueReportsComponent_ng_option_52_Template, 3, 4, \"ng-option\", 11);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(53, \"div\", 25)(54, \"ul\", 26, 27)(56, \"li\", 28)(57, \"a\", 29);\n        i0.ɵɵelement(58, \"i\", 30);\n        i0.ɵɵtext(59);\n        i0.ɵɵpipe(60, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(61, LeagueReportsComponent_ng_template_61_Template, 1, 8, \"ng-template\", 31);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"li\", 32)(63, \"a\", 29);\n        i0.ɵɵelement(64, \"i\", 33);\n        i0.ɵɵtext(65);\n        i0.ɵɵpipe(66, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(67, LeagueReportsComponent_ng_template_67_Template, 1, 8, \"ng-template\", 31);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(68, \"div\", 34);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r4 = i0.ɵɵreference(43);\n        const _r6 = i0.ɵɵreference(45);\n        const _r9 = i0.ɵɵreference(55);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 36, \"Season\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(14, 38, \"Select Season\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.seasonId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 40, \"Tournament\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(22, 42, \"Select Tournament\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx.tournamentId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.tournaments);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 44, \"Club\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(29, 46, \"Select Club\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx.clubId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.clubs);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(34, 48, \"Date Range\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(38, 50, \"Select Date Range\"));\n        i0.ɵɵproperty(\"value\", ctx.formatter.format(ctx.fromDate) + (ctx.toDate ? \" - \" + ctx.formatter.format(ctx.toDate) : \"\"))(\"dayTemplate\", _r4)(\"footerTemplate\", _r6)(\"firstDayOfWeek\", 1)(\"displayMonths\", 2)(\"autoClose\", false);\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(49, 52, \"Match Status\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(51, 54, \"Select Status\"));\n        i0.ɵɵproperty(\"searchable\", false)(\"clearable\", false)(\"ngModel\", ctx.matchStatus);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.matchStatusOptions);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(60, 56, \"Table View\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(66, 58, \"Schedule View\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r9);\n      }\n    },\n    dependencies: [i9.NgForOf, i10.NgControlStatus, i10.NgModel, i8.NgbInputDatepicker, i8.NgbNavContent, i8.NgbNav, i8.NgbNavItem, i8.NgbNavLink, i8.NgbNavOutlet, i11.ContentHeaderComponent, i12.NgSelectComponent, i12.ɵr, i3.TranslatePipe],\n    styles: [\".min-w-max[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n\\n.custom-day[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 0.185rem 0.25rem;\\n  border-radius: 0.25rem;\\n  display: inline-block;\\n  width: 2rem;\\n  cursor: pointer;\\n}\\n\\n.custom-day[_ngcontent-%COMP%]:hover, .custom-day.focused[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n\\n.custom-day.range[_ngcontent-%COMP%], .custom-day[_ngcontent-%COMP%]:hover {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.custom-day.faded[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 123, 255, 0.5);\\n  color: white;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlLXJlcG9ydHMvbGVhZ3VlLXJlcG9ydHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUdBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFGOztBQUdBOztFQUVFLHlCQUFBO0FBQUY7O0FBR0E7O0VBRUUseUJBQUE7RUFDQSxZQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtFQUNBLFlBQUE7QUFBRjs7QUFHQTtFQUNFLHVCQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIubWluLXctbWF4IHtcclxuICBtaW4td2lkdGg6IG1heC1jb250ZW50O1xyXG59XHJcblxyXG4vLyBEYXRlIHJhbmdlIHBpY2tlciBzdHlsZXNcclxuLmN1c3RvbS1kYXkge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBwYWRkaW5nOiAwLjE4NXJlbSAwLjI1cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHdpZHRoOiAycmVtO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmN1c3RvbS1kYXk6aG92ZXIsXHJcbi5jdXN0b20tZGF5LmZvY3VzZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LnJhbmdlLFxyXG4uY3VzdG9tLWRheTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LmZhZGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDEyMywgMjU1LCAwLjUpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLm5hdi1saW5rIHtcclxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAYA,SAASA,WAAW,QAAQ,0BAA0B;AAGtD,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAAsBC,OAAO,QAAgC,4BAA4B;AACzF,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;;ICEVC,EAAA,CAAAC,cAAA,oBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,EAAA,CAAmB;IAC3DN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAJ,UAAA,CAAAK,IAAA,OACF;;;;;IAaAV,EAAA,CAAAC,cAAA,oBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAO,cAAA,CAAAL,EAAA,CAAuB;IACvEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAE,cAAA,CAAAD,IAAA,OACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAQ,QAAA,CAAAN,EAAA,CAAiB;IACrDN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAG,QAAA,CAAAC,IAAA,OACF;;;;;;IAkCAb,EAAA,CAAAC,cAAA,eAOC;IAFCD,EAAA,CAAAc,UAAA,wBAAAC,0EAAA;MAAA,MAAAC,WAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAF,OAAA,CAAAG,WAAA,GAAAL,QAAA;IAAA,EAAiC,wBAAAM,0EAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA1B,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAG,OAAA,CAAAF,WAAA,GACL,IAAI;IAAA,EADC;IAGjCxB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAPLH,EAAA,CAAA2B,WAAA,YAAAC,WAAA,CAAyB,UAAAC,MAAA,CAAAC,OAAA,CAAAX,QAAA,YAAAU,MAAA,CAAAE,SAAA,CAAAZ,QAAA,KAAAU,MAAA,CAAAG,QAAA,CAAAb,QAAA;IAMzBnB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAW,QAAA,CAAAc,GAAA,MACF;;;;;;IAIAjC,EAAA,CAAAkC,SAAA,aAAiB;IACjBlC,EAAA,CAAAC,cAAA,cAAgD;IAI5CD,EAAA,CAAAc,UAAA,mBAAAqB,uEAAA;MAAAnC,EAAA,CAAAiB,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAsB,aAAA;MAAA,MAAAgB,GAAA,GAAAtC,EAAA,CAAAuC,WAAA;MAASF,OAAA,CAAAG,cAAA,EAAgB;MAAA,OAAExC,EAAA,CAAAuB,WAAA,CAAAe,GAAA,CAAAG,KAAA,EAAuB;IAAA,EAAC;IAEnDzC,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAc,UAAA,mBAAA4B,uEAAA;MAAA1C,EAAA,CAAAiB,aAAA,CAAAmB,IAAA;MAAApC,EAAA,CAAAsB,aAAA;MAAA,MAAAgB,GAAA,GAAAtC,EAAA,CAAAuC,WAAA;MAAA,OAASvC,EAAA,CAAAuB,WAAA,CAAAe,GAAA,CAAAG,KAAA,EAAuB;IAAA,EAAC;IAEjCzC,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAYXH,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAuC,UAAA,CAAAC,KAAA,CAAsB;IACzE5C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAkC,UAAA,CAAAE,KAAA,OACF;;;;;IAgBF7C,EAAA,CAAAkC,SAAA,gCASyB;;;;IARvBlC,EAAA,CAAAI,UAAA,aAAA0C,OAAA,CAAAC,QAAA,CAAqB,WAAAD,OAAA,CAAAE,MAAA,kBAAAF,OAAA,CAAAG,YAAA,eAAAH,OAAA,CAAAI,SAAA,iBAAAJ,OAAA,CAAAK,WAAA,aAAAL,OAAA,CAAAM,OAAA,WAAAN,OAAA,CAAAO,KAAA,iBAAAP,OAAA,CAAAQ,WAAA;;;;;IAiBvBtD,EAAA,CAAAkC,SAAA,4BASqB;;;;IARnBlC,EAAA,CAAAI,UAAA,aAAAmD,OAAA,CAAAR,QAAA,CAAqB,WAAAQ,OAAA,CAAAP,MAAA,kBAAAO,OAAA,CAAAN,YAAA,eAAAM,OAAA,CAAAL,SAAA,iBAAAK,OAAA,CAAAJ,WAAA,aAAAI,OAAA,CAAAH,OAAA,WAAAG,OAAA,CAAAF,KAAA,iBAAAE,OAAA,CAAAD,WAAA;;;ADxIzC,OAAM,MAAOE,sBAAsB;EA4BjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,eAA+B,EAC/BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,aAAoB,EACnBC,QAAqB,EACtBC,SAAiC;IAXhC,KAAAX,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IAnCX,KAAArB,MAAM,GAAQ,IAAI;IAClB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,SAAS,GAAQ;MAAEoB,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAhD,WAAW,GAAmB,IAAI;IAClC,KAAAiD,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAC,WAAW,GAAY,KAAK,CAAC,CAAC;IAEvB,KAAAxB,WAAW,GAAW,KAAK;IAMlC,KAAAyB,kBAAkB,GAAG,CACnB;MAAE/B,KAAK,EAAE,YAAY;MAAED,KAAK,EAAE;IAAK,CAAE,EACrC;MAAEC,KAAK,EAAE,UAAU;MAAED,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEC,KAAK,EAAE,QAAQ;MAAED,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEC,KAAK,EAAE,WAAW;MAAED,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEC,KAAK,EAAE,WAAW;MAAED,KAAK,EAAE;IAAW,CAAE,CAC3C;IAgBC,IAAI,CAACuB,aAAa,CAACU,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACf,eAAe,CAACgB,IAAI,EAAE;IAC3B,IAAI,CAACf,oBAAoB,CAACgB,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAAC9B,OAAO,GAAG8B,IAAI;MACnB,IAAI,CAACnC,QAAQ,GAAG,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC9C,EAAE;MAClC,IAAI,CAAC6E,eAAe,EAAE,CAAC,CAAC;MACxB,IAAI,IAAI,CAACC,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;MAEJ,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC;IACrC,CAAC,EACAC,KAAK,IAAI;MACR7F,IAAI,CAAC8F,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAChC,iBAAiB,CAACiC,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAACpC,eAAe,CAACqC,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAACpC,YAAY,CAACqC,WAAW,EAAE,CAACrB,SAAS,CACtCsB,GAAG,IAAI;MACN,IAAI,CAAClD,KAAK,GAAGkD,GAAG,CAACrB,IAAI;MACrB;MACA,IAAI,IAAI,CAACE,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;IAEN,CAAC,EACAI,KAAK,IAAI;MACR7F,IAAI,CAAC8F,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAChC,iBAAiB,CAACiC,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAK,cAAcA,CAACC,MAAM;IACnB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAAC7D,QAAQ,GAAG0D,MAAM;MACtB,IAAI,CAACtB,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAAClC,YAAY,GAAG,IAAI;MACxB,IAAI,CAAC4D,gBAAgB,EAAE;MACvBF,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACL,MAAM;IACjBM,OAAO,CAACC,GAAG,CAAC,iBAAiBP,MAAM,EAAE,CAAC;IACtC,IAAI,CAACzD,MAAM,GAAGyD,MAAM;IACpB,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAI,kBAAkBA,CAACR,MAAM;IACvBM,OAAO,CAACC,GAAG,CAAC,uBAAuBP,MAAM,EAAE,CAAC;IAC5C,IAAI,CAACxD,YAAY,GAAGwD,MAAM;IAC1B,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAK,mBAAmBA,CAACT,MAAM;IACxBM,OAAO,CAACC,GAAG,CAAC,wBAAwBP,MAAM,EAAE,CAAC;IAC7C,IAAI,CAACtD,WAAW,GAAGsD,MAAM;IACzB,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAM,eAAeA,CAAC/F,IAAa;IAE3B,IAAI,CAAC,IAAI,CAACqD,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClCqC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAACvC,QAAQ,GAAGrD,IAAI;MACpB,IAAI,CAACuD,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZtD,IAAI,IACJA,IAAI,CAACgG,KAAK,CAAC,IAAI,CAAC3C,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGtD,IAAI;MAClB,IAAI,CAACuD,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC0C,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC9E,KAAK,EAAE;UACtD,IAAI,CAAC8E,eAAe,CAAC9E,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACLsE,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAACtC,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAGrD,IAAI;MACpB,IAAI,CAACuD,WAAW,GAAG,IAAI;;EAE3B;EAEA6C,cAAcA,CAAA;IACZ,IAAI,CAAC7C,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAAC4C,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACE,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGN,UAAU,CAAC,MAAK;MACzC,IAAI,CAACQ,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACJ,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACQ,MAAM,IAC5B,CAAC,IAAI,CAACR,eAAe,CAACQ,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAACpD,WAAW,EAAE;MACpB;;IAGF,MAAMqD,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACT,eAAe,CAAC9E,KAAK,EAAE;EAC9B;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAACiC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC0C,eAAe,EAAE;EACxB;EAEAoB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAACvE,SAAS,CAACwE,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAACxE,QAAQ,CAAC0E,OAAO,CAAChJ,OAAO,CAACiJ,IAAI,CAACH,MAAM,CAAC,CAAC,GACxD9I,OAAO,CAACiJ,IAAI,CAACH,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEA3G,SAASA,CAACX,IAAa;IACrB,OACE,IAAI,CAACqD,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAAClD,WAAW,IAChBJ,IAAI,CAACgG,KAAK,CAAC,IAAI,CAAC3C,QAAQ,CAAC,IACzBrD,IAAI,CAAC4H,MAAM,CAAC,IAAI,CAACxH,WAAW,CAAC;EAEjC;EAEAQ,QAAQA,CAACZ,IAAa;IACpB,OAAO,IAAI,CAACsD,MAAM,IAAItD,IAAI,CAACgG,KAAK,CAAC,IAAI,CAAC3C,QAAQ,CAAC,IAAIrD,IAAI,CAAC4H,MAAM,CAAC,IAAI,CAACtE,MAAM,CAAC;EAC7E;EAEA5C,OAAOA,CAACV,IAAa;IACnB,OACEA,IAAI,CAAC6H,MAAM,CAAC,IAAI,CAACxE,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAItD,IAAI,CAAC6H,MAAM,CAAC,IAAI,CAACvE,MAAM,CAAE,IACzC,IAAI,CAAC1C,QAAQ,CAACZ,IAAI,CAAC,IACnB,IAAI,CAACW,SAAS,CAACX,IAAI,CAAC;EAExB;EAEA8H,iBAAiBA,CAAA;IACfnC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC9D,SAAS,CAAC;IACjD,IAAI,CAAC2D,gBAAgB,EAAE;EACzB;EAEAQ,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC5C,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAMyE,UAAU,GAAGtJ,MAAM,CACvB,GAAG,IAAI,CAAC4E,QAAQ,CAAC2E,IAAI,IAAI,IAAI,CAAC3E,QAAQ,CAAC4E,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC9E,QAAQ,CAACxC,GAAG,CAACqH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAMC,QAAQ,GAAG3J,MAAM,CACrB,GAAG,IAAI,CAAC6E,MAAM,CAAC0E,IAAI,IAAI,IAAI,CAAC1E,MAAM,CAAC2E,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC7E,MAAM,CAACzC,GAAG,CAACqH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAACrG,SAAS,CAACoB,UAAU,GAAG6E,UAAU,CAACM,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACvG,SAAS,CAACqB,QAAQ,GAAGiF,QAAQ,CAACC,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAACjF,cAAc,GAAG,GAAG2E,UAAU,CAACM,MAAM,CACxC,cAAc,CACf,MAAMD,QAAQ,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACP,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAACzE,QAAQ,EAAE;MACxB,MAAM0E,UAAU,GAAGtJ,MAAM,CACvB,GAAG,IAAI,CAAC4E,QAAQ,CAAC2E,IAAI,IAAI,IAAI,CAAC3E,QAAQ,CAAC4E,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC9E,QAAQ,CAACxC,GAAG,CAACqH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAACrG,SAAS,CAACoB,UAAU,GAAG6E,UAAU,CAACM,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACvG,SAAS,CAACqB,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG2E,UAAU,CAACM,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAACvG,SAAS,CAACoB,UAAU,GAAG,IAAI;MAChC,IAAI,CAACpB,SAAS,CAACqB,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAAC0E,iBAAiB,EAAE;;EAE5B;EAEA/D,eAAeA,CAAA;IACb,IAAI,IAAI,CAACpC,QAAQ,EAAE;MACjB,IAAI,CAACa,KAAK,CACP8F,GAAG,CACF,GAAG9J,WAAW,CAAC+J,MAAM,YAAY,IAAI,CAAC5G,QAAQ,+BAA+B,CAC9E,CACAkC,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAAC5B,WAAW,GAAG4B,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAU,KAAK,IAAI;QACR7F,IAAI,CAAC8F,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAAChC,iBAAiB,CAACiC,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAyD,QAAQA,CAAA;IAAA,IAAAC,KAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAAClG,MAAM,CAACsC,OAAO,CAAC,gBAAgB,CAAC;MAClD6D,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEzJ,IAAI,EAAE,IAAI,CAACmD,MAAM,CAACsC,OAAO,CAAC,MAAM,CAAC;UACjCiE,MAAM,EAAE;SACT,EACD;UACE1J,IAAI,EAAE,IAAI,CAACmD,MAAM,CAACsC,OAAO,CAAC,aAAa,CAAC;UACxCiE,MAAM,EAAE;SACT,EACD;UACE1J,IAAI,EAAE,IAAI,CAACmD,MAAM,CAACsC,OAAO,CAAC,gBAAgB,CAAC;UAC3CiE,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACtF,iBAAiB,EAAE;IACxB,IAAI,CAACuB,SAAS,EAAE;IAChB,IAAI,CAACV,SAAS,GAAG;MACf0E,GAAG,EAAE,IAAI,CAACC,eAAe,CAACC,iBAAiB,CAACF,GAAG;MAC/CG,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;QACR,GAAG,IAAI,CAACL,eAAe,CAACC,iBAAiB,CAACK,IAAI;QAC9C;QACA;QACA;QACA;QAEA;QACAF,UAAU,EAAE;OACb;MACDnF,IAAI,EAAEA,CAACsF,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAIC,MAAM,GAAG,IAAIC,eAAe,EAAE;QAElC,IAAI,IAAI,CAAChI,MAAM,IAAIiI,SAAS,IAAI,IAAI,CAACjI,MAAM,KAAK,IAAI,EAAE;UACpD+H,MAAM,CAACG,MAAM,CAAC,SAAS,EAAE,IAAI,CAAClI,MAAM,CAAC;;QAGvC,IAAI,IAAI,CAACC,YAAY,IAAIgI,SAAS,IAAI,IAAI,CAAChI,YAAY,KAAK,IAAI,EAAE;UAChE8H,MAAM,CAACG,MAAM,CAAC,eAAe,EAAE,IAAI,CAACjI,YAAY,CAAC;;QAGnD,IAAI,IAAI,CAACC,SAAS,CAACoB,UAAU,EAAE;UAC7ByG,MAAM,CAACG,MAAM,CAAC,YAAY,EAAE,IAAI,CAAChI,SAAS,CAACoB,UAAU,CAAC;;QAGxD,IAAI,IAAI,CAACpB,SAAS,CAACqB,QAAQ,EAAE;UAC3BwG,MAAM,CAACG,MAAM,CAAC,UAAU,EAAE,IAAI,CAAChI,SAAS,CAACqB,QAAQ,CAAC;;QAGpD,IAAI,IAAI,CAACpB,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,KAAK,EAAE;UAClD4H,MAAM,CAACG,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC/H,WAAW,CAAC;;QAGjD,MAAMgI,WAAW,GAAGJ,MAAM,CAACzB,QAAQ,EAAE;QACrC,MAAM8B,GAAG,GAAG,GAAGxL,WAAW,CAAC+J,MAAM,YAAY,IAAI,CAAC5G,QAAQ,WACxDoI,WAAW,GAAG,GAAG,GAAGA,WAAW,GAAG,EACpC,EAAE;QAEF,IAAI,CAACE,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACzH,KAAK,CAAC0H,IAAI,CAAMF,GAAG,EAAEP,oBAAoB,CAAC,CAAC5F,SAAS,CACtDsG,IAAS,IAAI;UACZ,IAAI,CAACF,cAAc,GAAG,KAAK;UAC3BP,QAAQ,CAAC;YACPU,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCvG,IAAI,EAAEqG,IAAI,CAACrG;WACZ,CAAC;QACJ,CAAC,EACAU,KAAK,IAAI;UACR,IAAI,CAACyF,cAAc,GAAG,KAAK;UAC3BtE,OAAO,CAACnB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDkF,QAAQ,CAAC;YACPU,YAAY,EAAE,CAAC;YACfC,eAAe,EAAE,CAAC;YAClBvG,IAAI,EAAE;WACP,CAAC;QACJ,CAAC,CACF;MACH,CAAC;MACDwG,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CAAC;MACpDC,OAAO,EAAE,CACP;QACEjG,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,KAAK,CAAC;QAC5CjB,IAAI,EAAE,IAAI;QACV8G,SAAS,EAAE,oBAAoB;QAC/B9B,IAAI,EAAE,YAAY;QAClB+B,MAAM,EAAE,SAAAA,CAAU/G,IAAI,EAAEgF,IAAI,EAAEgC,GAAG,EAAEC,QAAQ;UAEzC,OAAOA,QAAQ,CAACD,GAAG,GAAG,CAAC;QACzB;OACD,EACD;QACEpG,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,YAAY,CAAC;QACnDjB,IAAI,EAAE,MAAM;QACZ8G,SAAS,EAAE,oBAAoB;QAC/B9B,IAAI,EAAE,YAAY;QAClB+B,MAAM,EAAE;UACNG,OAAO,EAAEA,CAAClH,IAAI,EAAEgF,IAAI,EAAEgC,GAAG,KAAI;YAC3B,OAAOhH,IAAI,IAAI,KAAK;UACtB,CAAC;UACDmH,MAAM,EAAEA,CAACnH,IAAI,EAAEgF,IAAI,EAAEgC,GAAG,KAAI;YAC1B,OAAOhH,IAAI;UACb;;OAEH,EACD;QACEY,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,OAAO,CAAC;QAC9CjB,IAAI,EAAE,YAAY;QAClB+G,MAAM,EAAE,SAAAA,CAAU/G,IAAI;UACpB,MAAMoH,WAAW,GAAGpH,IAAI,IAAI,KAAK;UACjC,OAAO,uDAAuDoH,WAAW,QAAQ;QACnF;OACD,EACD;QACExG,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,MAAM,CAAC;QAC7CjB,IAAI,EAAE,YAAY;QAClB8G,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAU/G,IAAI,EAAEgF,IAAI,EAAEgC,GAAG;UAC/B,MAAMK,WAAW,GACf,CAACrH,IAAI,IAAIA,IAAI,KAAK,KAAK,GACnB,KAAK,GACLrF,MAAM,CAACqF,IAAI,CAAC,CAACuE,MAAM,CAAC,YAAY,CAAC;UACvC,OAAO,4DAA4D8C,WAAW,QAAQ;QACxF;OACD,EACD;QACEzG,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,YAAY,CAAC;QACnDjB,IAAI,EAAE,YAAY;QAClB8G,SAAS,EAAE,aAAa;QAExBC,MAAM,EAAE,SAAAA,CAAS/G,IAAI,EAAEgF,IAAI,EAAEgC,GAAG;UAC9B,IAAI,CAAChH,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOrF,MAAM,CAACqF,IAAI,CAAC,CAACuE,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACE3D,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,UAAU,CAAC;QACjDjB,IAAI,EAAE,UAAU;QAChB8G,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS/G,IAAI,EAAEgF,IAAI,EAAEgC,GAAG;UAC9B,IAAI,CAAChH,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOrF,MAAM,CAACqF,IAAI,CAAC,CAACuE,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACE3D,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,UAAU,CAAC;QACjDjB,IAAI,EAAE,UAAU;QAChB+G,MAAM,EAAE,SAAAA,CAAS/G,IAAI,EAAEgF,IAAI,EAAEgC,GAAG;UAC9B,IAAI,CAAChH,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOA,IAAI;QACb;OACD,EACD;QACEY,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,WAAW,CAAC;QAClDjB,IAAI,EAAE,gBAAgB;QACtB8G,SAAS,EAAE;OACZ,EACD;QACElG,KAAK,EAAE,IAAI;QACXZ,IAAI,EAAE,IAAI;QACV8G,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS/G,IAAI,EAAEgF,IAAI,EAAEgC,GAAG;UAC9B,OAAO,IAAI;QACb;OACD,EACD;QACEpG,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,WAAW,CAAC;QAClDjB,IAAI,EAAE,gBAAgB;QACtB8G,SAAS,EAAE;OACZ,EACD;QACElG,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,YAAY,CAAC;QACnDjB,IAAI,EAAE,YAAY;QAClB8G,SAAS,EAAE;OACZ,EACD;QACE9G,IAAI,EAAE,IAAI;QACV8G,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS/G,IAAI,EAAEgF,IAAI,EAAEgC,GAAG;UAC9B,OAAO,GAAG;QACZ;OACD,EACD;QACEpG,KAAK,EAAE,IAAI,CAAC5B,iBAAiB,CAACiC,OAAO,CAAC,YAAY,CAAC;QACnDjB,IAAI,EAAE,YAAY;QAClB8G,SAAS,EAAE;OACZ,CACF;MACDQ,OAAO,EAAE;QACPnC,GAAG,EAAE,IAAI,CAACC,eAAe,CAACC,iBAAiB,CAACiC,OAAO,CAACnC,GAAG;QACvDmC,OAAO,EAAE,CACP;UACEzG,IAAI,EAAE,6CAA6C,IAAI,CAAC7B,iBAAiB,CAACiC,OAAO,CAC/E,YAAY,CACb,EAAE;UACHsG,MAAM,EAAE,KAAK;UACbC,MAAM;YAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAM9H,IAAI,GAAG4H,EAAE,CAACN,OAAO,CAACS,UAAU,EAAE;cAEpC;cACA,IAAIC,QAAQ,GAAG,gBAAgB;cAE/B,IAAIrD,KAAI,CAAC9G,QAAQ,EAAE;gBACjB,MAAMoK,MAAM,GAAGtD,KAAI,CAACzG,OAAO,EAAEgK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/M,EAAE,IAAIuJ,KAAI,CAAC9G,QAAQ,CAAC;gBAC7D,IAAIoK,MAAM,EAAE;kBACVD,QAAQ,IAAI,IAAIC,MAAM,CAACzM,IAAI,CAAC4M,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAItD,IAAIzD,KAAI,CAAC5G,YAAY,EAAE;gBACrB,MAAMsK,UAAU,GAAG1D,KAAI,CAACvG,WAAW,EAAE8J,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAClN,EAAE,IAAIuJ,KAAI,CAAC5G,YAAY,CAAC;gBACzE,IAAIsK,UAAU,EAAE;kBACdL,QAAQ,IAAI,IAAIK,UAAU,CAAC7M,IAAI,CAAC4M,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAI1D,IAAIzD,KAAI,CAAC7G,MAAM,EAAE;gBACf,MAAMyK,IAAI,GAAG5D,KAAI,CAACxG,KAAK,EAAE+J,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACpN,EAAE,IAAIuJ,KAAI,CAAC7G,MAAM,CAAC;gBACvD,IAAIyK,IAAI,EAAE;kBACRP,QAAQ,IAAI,IAAIO,IAAI,CAAC5M,IAAI,CAACyM,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAIpD,IAAIzD,KAAI,CAAC1G,WAAW,IAAI0G,KAAI,CAAC1G,WAAW,KAAK,KAAK,EAAE;gBAClD+J,QAAQ,IAAI,IAAIrD,KAAI,CAAC1G,WAAW,EAAE;;cAGpC,IAAI0G,KAAI,CAAC3G,SAAS,CAACoB,UAAU,IAAIuF,KAAI,CAAC3G,SAAS,CAACqB,QAAQ,EAAE;gBACxD2I,QAAQ,IAAI,IAAIrD,KAAI,CAAC3G,SAAS,CAACoB,UAAU,OAAOuF,KAAI,CAAC3G,SAAS,CAACqB,QAAQ,EAAE;eAC1E,MAAM,IAAIsF,KAAI,CAAC3G,SAAS,CAACoB,UAAU,EAAE;gBACpC4I,QAAQ,IAAI,SAASrD,KAAI,CAAC3G,SAAS,CAACoB,UAAU,EAAE;;cAGlD4I,QAAQ,IAAI,MAAM;cAElB,MAAMrD,KAAI,CAAC8D,cAAc,CAACC,SAAS,CAAC1I,IAAI,EAAEgI,QAAQ,CAAC;YACrD,CAAC;YAAA,gBAAAR,OAAAmB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAC,QAAQA,CAACjJ,IAAI;IACX,IAAI,CAAC7B,KAAK,GAAG6B,IAAI;IACjB;IACA,MAAMkJ,SAAS,GAAG,IAAI,CAACC,MAAM,CAACjB,IAAI,CAAEkB,KAAK,IAAKA,KAAK,CAACC,GAAG,KAAK,SAAS,CAAC;IACtE;IACA,IAAIC,aAAa,GAAG,EAAE;IACtBtJ,IAAI,CAACuJ,OAAO,CAAEhB,IAAI,IAAI;MACpB,IAAIiB,SAAS,GAAG,IAAI,CAACxK,iBAAiB,CAACiC,OAAO,CAACsH,IAAI,CAAC/M,IAAI,CAAC;MACzD8N,aAAa,CAACG,IAAI,CAAC;QACjB9L,KAAK,EAAE6L,SAAS;QAChB9L,KAAK,EAAE6K,IAAI,CAACnN;OACb,CAAC;IACJ,CAAC,CAAC;IACF8N,SAAS,CAACQ,KAAK,CAACC,OAAO,GAAGL,aAAa;EACzC;EAEAM,cAAcA,CAACnH,KAAU;IACvB;EAAA;EAGFoH,MAAMA,CAACrC,MAAM,EAAER,GAAI;IACjB,IAAI,CAACmC,MAAM,CAAC,CAAC,CAAC,CAACW,YAAY,GAAG,IAAI,CAAChM,MAAM;IACzC,QAAQ0J,MAAM;MACZ,KAAK,QAAQ;QACX,IAAI,CAAC2B,MAAM,CAAC,CAAC,CAAC,CAACO,KAAK,CAACK,QAAQ,GAAG,KAAK;QACrC;MACF,KAAK,MAAM;QACT,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,CAACO,KAAK,CAACK,QAAQ,GAAG,IAAI;QACpC;MACF,KAAK,QAAQ;QACX;MACF;QACE;;IAEJ,IAAI,CAAClE,MAAM,CAAC2B,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAAC3B,MAAM,CAACmB,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAACgD,mBAAmB,CAACC,kBAAkB,CAAC,IAAI,CAACC,UAAU,CAAC,CAACC,UAAU,EAAE;EAC3E;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAACzL,QAAQ,CAAC0L,MAAM,CAAC,UAAU,EAAE,OAAO,EAAG7H,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACK,MAAM,CAACyH,YAAY,CAAC,eAAe,CAAC,EAAE;QAC9C,IAAIC,aAAa,GAAG/H,KAAK,CAACK,MAAM,CAACK,YAAY,CAAC,eAAe,CAAC;QAC9D,IAAIsH,QAAQ,GAAGhI,KAAK,CAACK,MAAM,CAACK,YAAY,CAAC,UAAU,CAAC;QACpD;QACA,IAAI,CAAC1E,OAAO,CAACiM,QAAQ,CAAC,CAACF,aAAa,EAAE,QAAQ,EAAEC,QAAQ,CAAC,EAAE;UACzDE,UAAU,EAAE,IAAI,CAACnM;SAClB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAoM,gBAAgBA,CAACnI,KAAU;IACzB,IAAI,CAACoI,QAAQ,GAAGpI,KAAK;IACrB,IAAI,IAAI,CAACoI,QAAQ,KAAK,cAAc,IAAI,IAAI,CAAC3K,SAAS,CAACC,UAAU,EAAE;MACjE,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEQqB,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACzB,SAAS,CAACC,UAAU,EAAE;MAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEAwK,WAAWA,CAAA;IACT,IAAI,CAACvK,SAAS,CAACwK,WAAW,EAAE;IAC5B,IAAI,CAACV,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC3H,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;EAAC,QAAAsI,CAAA;qBA5oBU1M,sBAAsB,EAAAxD,EAAA,CAAAmQ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArQ,EAAA,CAAAmQ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAtQ,EAAA,CAAAmQ,iBAAA,CAAAI,EAAA,CAAAC,UAAA,GAAAxQ,EAAA,CAAAmQ,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAA1Q,EAAA,CAAAmQ,iBAAA,CAAAnQ,EAAA,CAAA2Q,SAAA,GAAA3Q,EAAA,CAAAmQ,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA7Q,EAAA,CAAAmQ,iBAAA,CAAAW,EAAA,CAAAC,mBAAA,GAAA/Q,EAAA,CAAAmQ,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAAjR,EAAA,CAAAmQ,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAA1Q,EAAA,CAAAmQ,iBAAA,CAAAe,EAAA,CAAAC,KAAA,GAAAnR,EAAA,CAAAmQ,iBAAA,CAAAiB,EAAA,CAAAC,WAAA,GAAArR,EAAA,CAAAmQ,iBAAA,CAAAiB,EAAA,CAAAE,sBAAA;EAAA;EAAA,QAAAC,EAAA;UAAtB/N,sBAAsB;IAAAgO,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;iBAAtBC,GAAA,CAAAlK,eAAA,CAAAjB,MAAA,CAAuB;QAAA,UAAAzG,EAAA,CAAA6R,iBAAA;;;;;;;;QCzBpC7R,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAAkC,SAAA,4BAAyE;QAEzElC,EAAA,CAAAC,cAAA,iBAAkC;QAQmBD,EAAA,CAAAE,MAAA,IAAsB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrEH,EAAA,CAAAC,cAAA,qBAKoC;QADlCD,EAAA,CAAAc,UAAA,2BAAAgR,oEAAArL,MAAA;UAAA,OAAAmL,GAAA,CAAA7O,QAAA,GAAA0D,MAAA;QAAA,EAAsB,oBAAAsL,6DAAAtL,MAAA;UAAA,OACZmL,GAAA,CAAApL,cAAA,CAAAC,MAAA,CAAsB;QAAA,EADV;;QAEtBzG,EAAA,CAAAgS,UAAA,KAAAC,4CAAA,wBAEY;QACdjS,EAAA,CAAAG,YAAA,EAAY;QAGhBH,EAAA,CAAAC,cAAA,cAAsB;QAEyBD,EAAA,CAAAE,MAAA,IAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7EH,EAAA,CAAAC,cAAA,qBAKwC;QADtCD,EAAA,CAAAc,UAAA,2BAAAoR,oEAAAzL,MAAA;UAAA,OAAAmL,GAAA,CAAA3O,YAAA,GAAAwD,MAAA;QAAA,EAA0B,oBAAA0L,6DAAA1L,MAAA;UAAA,OAChBmL,GAAA,CAAA3K,kBAAA,CAAAR,MAAA,CAA0B;QAAA,EADV;;QAE1BzG,EAAA,CAAAgS,UAAA,KAAAI,4CAAA,wBAEY;QACdpS,EAAA,CAAAG,YAAA,EAAY;QAEdH,EAAA,CAAAC,cAAA,eAAsB;QACiBD,EAAA,CAAAE,MAAA,IAAoB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjEH,EAAA,CAAAC,cAAA,qBAKkC;QADhCD,EAAA,CAAAc,UAAA,2BAAAuR,oEAAA5L,MAAA;UAAA,OAAAmL,GAAA,CAAA5O,MAAA,GAAAyD,MAAA;QAAA,EAAoB,oBAAA6L,6DAAA7L,MAAA;UAAA,OACVmL,GAAA,CAAA9K,YAAA,CAAAL,MAAA,CAAoB;QAAA,EADV;;QAEpBzG,EAAA,CAAAgS,UAAA,KAAAO,4CAAA,wBAEY;QACdvS,EAAA,CAAAG,YAAA,EAAY;QAEdH,EAAA,CAAAC,cAAA,eAAsB;QACsBD,EAAA,CAAAE,MAAA,IAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC5EH,EAAA,CAAAC,cAAA,eAAyB;QASrBD,EAAA,CAAAc,UAAA,mBAAA0R,wDAAA;UAAA,OAASZ,GAAA,CAAApK,cAAA,EAAgB;QAAA,EAAC,wBAAAiL,6DAAAhM,MAAA;UAAA,OAGZmL,GAAA,CAAAzK,eAAA,CAAAV,MAAA,CAAuB;QAAA,EAHX;;QAR5BzG,EAAA,CAAAG,YAAA,EAgBE;QACFH,EAAA,CAAAC,cAAA,eAAgC;QAG5BD,EAAA,CAAAc,UAAA,mBAAA4R,yDAAA;UAAA,OAASd,GAAA,CAAApK,cAAA,EAAgB;QAAA,EAAC;QAE1BxH,EAAA,CAAAkC,SAAA,aAAqC;QACvClC,EAAA,CAAAG,YAAA,EAAS;QAIbH,EAAA,CAAAgS,UAAA,KAAAW,8CAAA,iCAAA3S,EAAA,CAAA4S,sBAAA,CAWc;QAEd5S,EAAA,CAAAgS,UAAA,KAAAa,8CAAA,iCAAA7S,EAAA,CAAA4S,sBAAA,CAkBc;QAChB5S,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,eAAsB;QACwBD,EAAA,CAAAE,MAAA,IAA4B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAChFH,EAAA,CAAAC,cAAA,qBAKyC;QADvCD,EAAA,CAAAc,UAAA,2BAAAgS,oEAAArM,MAAA;UAAA,OAAAmL,GAAA,CAAAzO,WAAA,GAAAsD,MAAA;QAAA,EAAyB,oBAAAsM,6DAAAtM,MAAA;UAAA,OACfmL,GAAA,CAAA1K,mBAAA,CAAAT,MAAA,CAA2B;QAAA,EADZ;;QAEzBzG,EAAA,CAAAgS,UAAA,KAAAgB,4CAAA,wBAEY;QACdhT,EAAA,CAAAG,YAAA,EAAY;QAOpBH,EAAA,CAAAC,cAAA,eAAkB;QAIVD,EAAA,CAAAkC,SAAA,aAA2C;QAC3ClC,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAgS,UAAA,KAAAiB,8CAAA,0BAWc;QAChBjT,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAkC;QAE9BD,EAAA,CAAAkC,SAAA,aAAyC;QACzClC,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAgS,UAAA,KAAAkB,8CAAA,0BAWc;QAChBlT,EAAA,CAAAG,YAAA,EAAK;QAGTH,EAAA,CAAAkC,SAAA,eAA6C;QAC/ClC,EAAA,CAAAG,YAAA,EAAM;;;;;;QA5KUH,EAAA,CAAAO,SAAA,GAA+B;QAA/BP,EAAA,CAAAI,UAAA,kBAAAwR,GAAA,CAAA9H,aAAA,CAA+B;QAUE9J,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAmT,iBAAA,CAAAnT,EAAA,CAAAS,WAAA,mBAAsB;QAI3DT,EAAA,CAAAO,SAAA,GAA2C;QAA3CP,EAAA,CAAAoT,qBAAA,gBAAApT,EAAA,CAAAS,WAAA,0BAA2C;QAF3CT,EAAA,CAAAI,UAAA,oBAAmB,gCAAAwR,GAAA,CAAA7O,QAAA;QAKW/C,EAAA,CAAAO,SAAA,GAAU;QAAVP,EAAA,CAAAI,UAAA,YAAAwR,GAAA,CAAAxO,OAAA,CAAU;QAQCpD,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAAmT,iBAAA,CAAAnT,EAAA,CAAAS,WAAA,uBAA0B;QAInET,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAAoT,qBAAA,gBAAApT,EAAA,CAAAS,WAAA,8BAA+C;QAF/CT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAwR,GAAA,CAAA3O,YAAA;QAKejD,EAAA,CAAAO,SAAA,GAAc;QAAdP,EAAA,CAAAI,UAAA,YAAAwR,GAAA,CAAAtO,WAAA,CAAc;QAMbtD,EAAA,CAAAO,SAAA,GAAoB;QAApBP,EAAA,CAAAmT,iBAAA,CAAAnT,EAAA,CAAAS,WAAA,iBAAoB;QAIvDT,EAAA,CAAAO,SAAA,GAAyC;QAAzCP,EAAA,CAAAoT,qBAAA,gBAAApT,EAAA,CAAAS,WAAA,wBAAyC;QAFzCT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAwR,GAAA,CAAA5O,MAAA;QAKShD,EAAA,CAAAO,SAAA,GAAQ;QAARP,EAAA,CAAAI,UAAA,YAAAwR,GAAA,CAAAvO,KAAA,CAAQ;QAMIrD,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAAmT,iBAAA,CAAAnT,EAAA,CAAAS,WAAA,uBAA0B;QAKhET,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAAoT,qBAAA,gBAAApT,EAAA,CAAAS,WAAA,8BAA+C;QAI/CT,EAAA,CAAAI,UAAA,UAAAwR,GAAA,CAAAvN,SAAA,CAAAoF,MAAA,CAAAmI,GAAA,CAAAnN,QAAA,KAAAmN,GAAA,CAAAlN,MAAA,WAAAkN,GAAA,CAAAvN,SAAA,CAAAoF,MAAA,CAAAmI,GAAA,CAAAlN,MAAA,QAAuF,gBAAA2O,GAAA,oBAAAC,GAAA;QAsD/CtT,EAAA,CAAAO,SAAA,IAA4B;QAA5BP,EAAA,CAAAmT,iBAAA,CAAAnT,EAAA,CAAAS,WAAA,yBAA4B;QAItET,EAAA,CAAAO,SAAA,GAA2C;QAA3CP,EAAA,CAAAoT,qBAAA,gBAAApT,EAAA,CAAAS,WAAA,0BAA2C;QAF3CT,EAAA,CAAAI,UAAA,qBAAoB,gCAAAwR,GAAA,CAAAzO,WAAA;QAKUnD,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAI,UAAA,YAAAwR,GAAA,CAAAhN,kBAAA,CAAqB;QAerD5E,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,4BACF;QAiBET,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,+BACF;QAgBDT,EAAA,CAAAO,SAAA,GAAoB;QAApBP,EAAA,CAAAI,UAAA,iBAAAmT,GAAA,CAAoB", "names": ["environment", "moment", "NgbDate", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "season_r12", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "name", "tournament_r13", "club_r14", "code", "ɵɵlistener", "LeagueReportsComponent_ng_template_42_Template_span_mouseenter_0_listener", "restoredCtx", "ɵɵrestoreView", "_r18", "date_r15", "date", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "hoveredDate", "LeagueReportsComponent_ng_template_42_Template_span_mouseleave_0_listener", "ctx_r19", "ɵɵclassProp", "focused_r16", "ctx_r5", "isRange", "isHovered", "isInside", "day", "ɵɵelement", "LeagueReportsComponent_ng_template_44_Template_button_click_2_listener", "_r21", "ctx_r20", "_r3", "ɵɵreference", "clearDateRange", "close", "LeagueReportsComponent_ng_template_44_Template_button_click_4_listener", "status_r23", "value", "label", "ctx_r10", "seasonId", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "matchStatus", "seasons", "clubs", "tournaments", "ctx_r11", "LeagueReportsComponent", "constructor", "route", "_router", "_http", "_trans", "renderer", "_loadingService", "_registrationService", "_clubService", "_translateService", "_titleService", "calendar", "formatter", "start_date", "end_date", "dateRangeValue", "fromDate", "toDate", "isSelecting", "matchStatusOptions", "setTitle", "_getCurrentSeason", "show", "getAllSeasonActive", "subscribe", "data", "_getTournaments", "dtElement", "dtInstance", "then", "ajax", "reload", "dtTrigger", "next", "dtOptions", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "instant", "dismiss", "_getClubs", "getAllClubs", "res", "onSelectSeason", "$event", "Promise", "resolve", "reject", "refreshDataTable", "onSelectClub", "console", "log", "onSelectTournament", "onSelectMatchStatus", "onDateSelection", "after", "updateDateRange", "setTimeout", "dateRangePicker", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "from", "before", "equals", "onDateRangeChange", "fromMoment", "year", "month", "toString", "padStart", "toMoment", "format", "get", "apiUrl", "ngOnInit", "_this", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "isLink", "dom", "_commonsService", "dataTableDefaults", "select", "rowId", "processing", "language", "lang", "dataTablesParameters", "callback", "params", "URLSearchParams", "undefined", "append", "queryString", "url", "isTableLoading", "post", "resp", "recordsTotal", "recordsFiltered", "responsive", "scrollX", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "row", "metadata", "display", "filter", "displayData", "displayDate", "buttons", "extend", "action", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "filename", "season", "find", "s", "replace", "tournament", "t", "club", "c", "_exportService", "exportCsv", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "setClubs", "clubField", "fields", "field", "key", "current_clubs", "for<PERSON>ach", "club_name", "push", "props", "options", "onCaptureEvent", "editor", "defaultValue", "disabled", "_coreSidebarService", "getSidebarRegistry", "table_name", "toggle<PERSON><PERSON>", "ngAfterViewInit", "unlistener", "listen", "hasAttribute", "tournament_id", "stage_id", "navigate", "relativeTo", "onSelectViewType", "viewType", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "HttpClient", "i3", "TranslateService", "Renderer2", "i4", "LoadingService", "i5", "RegistrationService", "i6", "ClubService", "i7", "Title", "i8", "NgbCalendar", "NgbDateParserFormatter", "_2", "selectors", "viewQuery", "LeagueReportsComponent_Query", "rf", "ctx", "ɵɵresolveDocument", "LeagueReportsComponent_Template_ng_select_ngModelChange_13_listener", "LeagueReportsComponent_Template_ng_select_change_13_listener", "ɵɵtemplate", "LeagueReportsComponent_ng_option_15_Template", "LeagueReportsComponent_Template_ng_select_ngModelChange_21_listener", "LeagueReportsComponent_Template_ng_select_change_21_listener", "LeagueReportsComponent_ng_option_23_Template", "LeagueReportsComponent_Template_ng_select_ngModelChange_28_listener", "LeagueReportsComponent_Template_ng_select_change_28_listener", "LeagueReportsComponent_ng_option_30_Template", "LeagueReportsComponent_Template_input_click_36_listener", "LeagueReportsComponent_Template_input_dateSelect_36_listener", "LeagueReportsComponent_Template_button_click_40_listener", "LeagueReportsComponent_ng_template_42_Template", "ɵɵtemplateRefExtractor", "LeagueReportsComponent_ng_template_44_Template", "LeagueReportsComponent_Template_ng_select_ngModelChange_50_listener", "LeagueReportsComponent_Template_ng_select_change_50_listener", "LeagueReportsComponent_ng_option_52_Template", "LeagueReportsComponent_ng_template_61_Template", "LeagueReportsComponent_ng_template_67_Template", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate", "_r4", "_r6", "_r9"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  HostListener,\r\n  OnDestroy\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit, OnDestroy\r\n{\r\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\r\n\r\n  public seasonId: any;\r\n  public clubId: any = null;\r\n  public tournamentId: any = null;\r\n  public dateRange: any = { start_date: null, end_date: null };\r\n  public dateRangeValue: string = '';\r\n  hoveredDate: NgbDate | null = null;\r\n  fromDate: NgbDate | null = null;\r\n  toDate: NgbDate | null = null;\r\n  isSelecting: boolean = false; // Track if we're in the middle of selecting range\r\n  private clickOutsideTimeout: any;\r\n  public matchStatus: string = 'all';\r\n  public contentHeader: object;\r\n  public seasons;\r\n  public clubs;\r\n  public tournaments;\r\n\r\n  matchStatusOptions = [\r\n    { label: 'All Status', value: 'all' },\r\n    { label: 'Upcoming', value: 'upcoming' },\r\n    { label: 'Active', value: 'active' },\r\n    { label: 'Completed', value: 'completed' },\r\n    { label: 'Cancelled', value: 'cancelled' },\r\n  ];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _loadingService: LoadingService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _titleService: Title,\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n        this.dtTrigger.next(this.dtOptions);\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n        // this.clubId = this.clubs[0];\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectTournament($event) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      rowId: 'id',\r\n      processing: true,\r\n      language: {\r\n        ...this._commonsService.dataTableDefaults.lang,\r\n        // processing: `<div class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\r\n        //               <div class=\"spinner-border text-primary\" role=\"status\">\r\n        //                 <span class=\"sr-only\">Loading...</span>\r\n        //               </div>\r\n                      \r\n        //             </div>`,\r\n        processing: ``,\r\n      },\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        // Build query parameters for all filters\r\n        let params = new URLSearchParams();\r\n\r\n        if (this.clubId != undefined && this.clubId !== null) {\r\n          params.append('club_id', this.clubId);\r\n        }\r\n\r\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\r\n          params.append('tournament_id', this.tournamentId);\r\n        }\r\n\r\n        if (this.dateRange.start_date) {\r\n          params.append('start_date', this.dateRange.start_date);\r\n        }\r\n\r\n        if (this.dateRange.end_date) {\r\n          params.append('end_date', this.dateRange.end_date);\r\n        }\r\n\r\n        if (this.matchStatus && this.matchStatus !== 'all') {\r\n          params.append('match_status', this.matchStatus);\r\n        }\r\n\r\n        const queryString = params.toString();\r\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${\r\n          queryString ? '?' + queryString : ''\r\n        }`;\r\n\r\n        this.isTableLoading = true;\r\n        this._http.post<any>(url, dataTablesParameters).subscribe(\r\n          (resp: any) => {\r\n            this.isTableLoading = false;\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          },\r\n          (error) => {\r\n            this.isTableLoading = false;\r\n            console.error('Error loading table data:', error);\r\n            callback({\r\n              recordsTotal: 0,\r\n              recordsFiltered: 0,\r\n              data: [],\r\n            });\r\n          }\r\n        );\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      columnDefs: [{ responsivePriority: 1, targets: -1 }],\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('No.'),\r\n          data: null,\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: function (data, type, row, metadata) {\r\n\r\n            return metadata.row + 1\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Tournament'),\r\n          data: 'name',\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: {\r\n            display: (data, type, row) => {\r\n              return data ?? 'TBD';\r\n            },\r\n            filter: (data, type, row) => {\r\n              return data;\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          render: function (data) {\r\n            const displayData = data || 'TBD';\r\n            return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Date'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n          render: function (data, type, row) {\r\n            const displayDate =\r\n              !data || data === 'TBD'\r\n                ? 'TBD'\r\n                : moment(data).format('YYYY-MM-DD');\r\n            return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Start time'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('End time'),\r\n          data: 'end_time',\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Location'),\r\n          data: 'location',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home Team'),\r\n          data: 'home_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: 'VS',\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return 'vs';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away Team'),\r\n          data: 'away_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home score'),\r\n          data: 'home_score',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return '-';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away score'),\r\n          data: 'away_score',\r\n          className: 'text-center'\r\n        }\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant(\r\n              'Export CSV'\r\n            )}`,\r\n            extend: 'csv',\r\n            action: async (e: any, dt: any, button: any, config: any) => {\r\n              const data = dt.buttons.exportData();\r\n              \r\n              // Generate filename with current filter information\r\n              let filename = 'Matches_Report';\r\n              \r\n              if (this.seasonId) {\r\n                const season = this.seasons?.find(s => s.id == this.seasonId);\r\n                if (season) {\r\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.tournamentId) {\r\n                const tournament = this.tournaments?.find(t => t.id == this.tournamentId);\r\n                if (tournament) {\r\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.clubId) {\r\n                const club = this.clubs?.find(c => c.id == this.clubId);\r\n                if (club) {\r\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.matchStatus && this.matchStatus !== 'all') {\r\n                filename += `_${this.matchStatus}`;\r\n              }\r\n              \r\n              if (this.dateRange.start_date && this.dateRange.end_date) {\r\n                filename += `_${this.dateRange.start_date}_to_${this.dateRange.end_date}`;\r\n              } else if (this.dateRange.start_date) {\r\n                filename += `_from_${this.dateRange.start_date}`;\r\n              }\r\n              \r\n              filename += '.csv';\r\n              \r\n              await this._exportService.exportCsv(data, filename);\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  setClubs(data) {\r\n    this.clubs = data;\r\n    // get field has key club_id\r\n    const clubField = this.fields.find((field) => field.key === 'club_id');\r\n    // set options for club field\r\n    let current_clubs = [];\r\n    data.forEach((club) => {\r\n      let club_name = this._translateService.instant(club.name);\r\n      current_clubs.push({\r\n        label: club_name,\r\n        value: club.id\r\n      });\r\n    });\r\n    clubField.props.options = current_clubs;\r\n  }\r\n\r\n  onCaptureEvent(event: any) {\r\n    // console.log(event);\r\n  }\r\n\r\n  editor(action, row?) {\r\n    this.fields[0].defaultValue = this.clubId;\r\n    switch (action) {\r\n      case 'create':\r\n        this.fields[2].props.disabled = false;\r\n        break;\r\n      case 'edit':\r\n        this.fields[2].props.disabled = true;\r\n        break;\r\n      case 'remove':\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('tournament_id')) {\r\n        let tournament_id = event.target.getAttribute('tournament_id');\r\n        let stage_id = event.target.getAttribute('stage_id');\r\n        //  navigate to path ':tournament_id/stages/:stage_id'\r\n        this._router.navigate([tournament_id, 'stages', stage_id], {\r\n          relativeTo: this.route\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  onSelectViewType(event: any) {\r\n    this.viewType = event;\r\n    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  private refreshDataTable() {\r\n    if (this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n    this.unlistener();\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <section id=\"league-reports-page\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <!-- Filters Section -->\r\n          <div class=\"card mb-2\">\r\n            <div class=\"card-body\">\r\n              <div class=\"row mb-1\">\r\n                <div class=\"col-12\">\r\n                  <label for=\"season\" class=\"form-label\">{{'Season'|translate}}</label>\r\n                  <ng-select\r\n                    [searchable]=\"true\"\r\n                    [clearable]=\"false\"\r\n                    placeholder=\"{{'Select Season'|translate}}\"\r\n                    [(ngModel)]=\"seasonId\"\r\n                    (change)=\"onSelectSeason($event)\">\r\n                    <ng-option *ngFor=\"let season of seasons\" [value]=\"season.id\">\r\n                      {{ season.name | translate }}\r\n                    </ng-option>\r\n                  </ng-select>\r\n                </div>\r\n              </div>\r\n              <div class=\"row mb-1\">\r\n                <div class=\"col-md-3\">\r\n                  <label for=\"tournament\" class=\"form-label\">{{'Tournament'|translate}}</label>\r\n                  <ng-select\r\n                    [searchable]=\"true\"\r\n                    [clearable]=\"true\"\r\n                    placeholder=\"{{'Select Tournament'|translate}}\"\r\n                    [(ngModel)]=\"tournamentId\"\r\n                    (change)=\"onSelectTournament($event)\">\r\n                    <ng-option *ngFor=\"let tournament of tournaments\" [value]=\"tournament.id\">\r\n                      {{ tournament.name | translate }}\r\n                    </ng-option>\r\n                  </ng-select>\r\n                </div>\r\n                <div class=\"col-md-3\">\r\n                  <label for=\"club\" class=\"form-label\">{{'Club'|translate}}</label>\r\n                  <ng-select\r\n                    [searchable]=\"true\"\r\n                    [clearable]=\"true\"\r\n                    placeholder=\"{{'Select Club'|translate}}\"\r\n                    [(ngModel)]=\"clubId\"\r\n                    (change)=\"onSelectClub($event)\">\r\n                    <ng-option *ngFor=\"let club of clubs\" [value]=\"club.id\">\r\n                      {{ club.code | translate }}\r\n                    </ng-option>\r\n                  </ng-select>\r\n                </div>\r\n                <div class=\"col-md-3\">\r\n                  <label for=\"dateRange\" class=\"form-label\">{{'Date Range'|translate}}</label>\r\n                  <div class=\"input-group\">\r\n                    <input\r\n                      name=\"daterange\"\r\n                      class=\"form-control\"\r\n                      placeholder=\"{{'Select Date Range'|translate}}\"\r\n                      ngbDatepicker\r\n                      readonly\r\n                      #dateRangePicker=\"ngbDatepicker\"\r\n                      [value]=\"formatter.format(fromDate) + (toDate ? ' - ' + formatter.format(toDate) : '')\"\r\n                      (click)=\"openDatePicker()\"\r\n                      [dayTemplate]=\"dayTemplate\"\r\n                      [footerTemplate]=\"footerTemplate\"\r\n                      (dateSelect)=\"onDateSelection($event)\"\r\n                      [firstDayOfWeek]=\"1\"\r\n                      [displayMonths]=\"2\"\r\n                      outsideDays=\"hidden\"\r\n                      [autoClose]=\"false\"\r\n                    />\r\n                    <div class=\"input-group-append\">\r\n                      <button\r\n                        class=\"btn btn-outline-secondary\"\r\n                        (click)=\"openDatePicker()\"\r\n                        type=\"button\">\r\n                        <i class=\"feather icon-calendar\"></i>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <ng-template #dayTemplate let-date=\"date\" let-focused=\"focused\">\r\n                    <span\r\n                      class=\"custom-day\"\r\n                      [class.focused]=\"focused\"\r\n                      [class.range]=\"isRange(date)\"\r\n                      [class.faded]=\"isHovered(date) || isInside(date)\"\r\n                      (mouseenter)=\"hoveredDate = date\"\r\n                      (mouseleave)=\"hoveredDate = null\"\r\n                    >\r\n                      {{ date.day }}\r\n                    </span>\r\n                  </ng-template>\r\n\r\n                  <ng-template #footerTemplate>\r\n                    <hr class=\"my-0\">\r\n                    <div class=\"d-flex justify-content-between p-2\">\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"btn btn-outline-secondary btn-sm\"\r\n                        (click)=\"clearDateRange(); dateRangePicker.close()\"\r\n                      >\r\n                        Clear\r\n                      </button>\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"btn btn-primary btn-sm\"\r\n                        (click)=\"dateRangePicker.close()\"\r\n                      >\r\n                        Close\r\n                      </button>\r\n                    </div>\r\n                  </ng-template>\r\n                </div>\r\n                <div class=\"col-md-3\">\r\n                  <label for=\"matchStatus\" class=\"form-label\">{{'Match Status'|translate}}</label>\r\n                  <ng-select\r\n                    [searchable]=\"false\"\r\n                    [clearable]=\"false\"\r\n                    placeholder=\"{{'Select Status'|translate}}\"\r\n                    [(ngModel)]=\"matchStatus\"\r\n                    (change)=\"onSelectMatchStatus($event)\">\r\n                    <ng-option *ngFor=\"let status of matchStatusOptions\" [value]=\"status.value\">\r\n                      {{ status.label | translate }}\r\n                    </ng-option>\r\n                  </ng-select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Tabs Section -->\r\n          <div class=\"card\">\r\n            <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs m-0\">\r\n              <li ngbNavItem=\"league_table\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-table-list mr-1\"></i>\r\n                  {{ 'Table View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-league-table-view\r\n                    [seasonId]=\"seasonId\"\r\n                    [clubId]=\"clubId\"\r\n                    [tournamentId]=\"tournamentId\"\r\n                    [dateRange]=\"dateRange\"\r\n                    [matchStatus]=\"matchStatus\"\r\n                    [seasons]=\"seasons\"\r\n                    [clubs]=\"clubs\"\r\n                    [tournaments]=\"tournaments\"\r\n                  ></app-league-table-view>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem=\"schedule_matches\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-calendar mr-1\"></i>\r\n                  {{ 'Schedule View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-schedule-view\r\n                    [seasonId]=\"seasonId\"\r\n                    [clubId]=\"clubId\"\r\n                    [tournamentId]=\"tournamentId\"\r\n                    [dateRange]=\"dateRange\"\r\n                    [matchStatus]=\"matchStatus\"\r\n                    [seasons]=\"seasons\"\r\n                    [clubs]=\"clubs\"\r\n                    [tournaments]=\"tournaments\"\r\n                  ></app-schedule-view>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}