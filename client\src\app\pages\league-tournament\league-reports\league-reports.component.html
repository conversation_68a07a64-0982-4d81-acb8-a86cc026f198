<div class="content-wrapper container-xxl p-0">
  <div class="content-body">
    <!-- content-header component -->
    <app-content-header [contentHeader]="contentHeader"></app-content-header>

    <section id="league-reports-page">
      <div class="row">
        <div class="col-12">
          <!-- Filters Section -->
          <div class="card mb-2">
            <div class="card-body">
              <div class="row mb-1">
                <div class="col-12">
                  <label for="season" class="form-label">{{'Season'|translate}}</label>
                  <ng-select
                    [searchable]="true"
                    [clearable]="false"
                    placeholder="{{'Select Season'|translate}}"
                    [(ngModel)]="seasonId"
                    (change)="onSelectSeason($event)">
                    <ng-option *ngFor="let season of seasons" [value]="season.id">
                      {{ season.name | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="row mb-1">
                <div class="col-md-3">
                  <label for="tournament" class="form-label">{{'Tournament'|translate}}</label>
                  <ng-select
                    [searchable]="true"
                    [clearable]="true"
                    placeholder="{{'Select Tournament'|translate}}"
                    [(ngModel)]="tournamentId"
                    (change)="onSelectTournament($event)">
                    <ng-option *ngFor="let tournament of tournaments" [value]="tournament.id">
                      {{ tournament.name | translate }}
                    </ng-option>
                  </ng-select>
                </div>
                <div class="col-md-3">
                  <label for="club" class="form-label">{{'Club'|translate}}</label>
                  <ng-select
                    [searchable]="true"
                    [clearable]="true"
                    placeholder="{{'Select Club'|translate}}"
                    [(ngModel)]="clubId"
                    (change)="onSelectClub($event)">
                    <ng-option *ngFor="let club of clubs" [value]="club.id">
                      {{ club.code | translate }}
                    </ng-option>
                  </ng-select>
                </div>
                <div class="col-md-3">
                  <label for="dateRange" class="form-label">{{'Date Range'|translate}}</label>
                  <div class="input-group">
                    <input
                      name="daterange"
                      class="form-control"
                      placeholder="{{'Select Date Range'|translate}}"
                      ngbDatepicker
                      readonly
                      #dateRangePicker="ngbDatepicker"
                      [value]="formatter.format(fromDate) + (toDate ? ' - ' + formatter.format(toDate) : '')"
                      (click)="openDatePicker()"
                      [dayTemplate]="dayTemplate"
                      [footerTemplate]="footerTemplate"
                      (dateSelect)="onDateSelection($event)"
                      [firstDayOfWeek]="1"
                      [displayMonths]="2"
                      outsideDays="hidden"
                      [autoClose]="false"
                    />
                    <div class="input-group-append">
                      <button
                        class="btn btn-outline-secondary"
                        (click)="openDatePicker()"
                        type="button">
                        <i class="feather icon-calendar"></i>
                      </button>
                    </div>
                  </div>

                  <ng-template #dayTemplate let-date="date" let-focused="focused">
                    <span
                      class="custom-day"
                      [class.focused]="focused"
                      [class.range]="isRange(date)"
                      [class.faded]="isHovered(date) || isInside(date)"
                      (mouseenter)="hoveredDate = date"
                      (mouseleave)="hoveredDate = null"
                    >
                      {{ date.day }}
                    </span>
                  </ng-template>

                  <ng-template #footerTemplate>
                    <hr class="my-0">
                    <div class="d-flex justify-content-between p-2">
                      <button
                        type="button"
                        class="btn btn-outline-secondary btn-sm"
                        (click)="clearDateRange(); dateRangePicker.close()"
                      >
                        Clear
                      </button>
                      <button
                        type="button"
                        class="btn btn-primary btn-sm"
                        (click)="dateRangePicker.close()"
                      >
                        Close
                      </button>
                    </div>
                  </ng-template>
                </div>
                <div class="col-md-3">
                  <label for="matchStatus" class="form-label">{{'Match Status'|translate}}</label>
                  <ng-select
                    [searchable]="false"
                    [clearable]="false"
                    placeholder="{{'Select Status'|translate}}"
                    [(ngModel)]="matchStatus"
                    (change)="onSelectMatchStatus($event)">
                    <ng-option *ngFor="let status of matchStatusOptions" [value]="status.value">
                      {{ status.label | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>

          <!-- Tabs Section -->
          <div class="card">
            <ul ngbNav #nav="ngbNav" class="nav-tabs m-0">
              <li ngbNavItem="league_table">
                <a ngbNavLink>
                  <i class="fa-light fa-table-list mr-1"></i>
                  {{ 'Table View' | translate }}
                </a>
                <ng-template ngbNavContent>
                  <app-league-table-view
                    [seasonId]="seasonId"
                    [clubId]="clubId"
                    [tournamentId]="tournamentId"
                    [dateRange]="dateRange"
                    [matchStatus]="matchStatus"
                    [seasons]="seasons"
                    [clubs]="clubs"
                    [tournaments]="tournaments"
                  ></app-league-table-view>
                </ng-template>
              </li>
              <li ngbNavItem="schedule_matches">
                <a ngbNavLink>
                  <i class="fa-light fa-calendar mr-1"></i>
                  {{ 'Schedule View' | translate }}
                </a>
                <ng-template ngbNavContent>
                  <app-schedule-view
                    [seasonId]="seasonId"
                    [clubId]="clubId"
                    [tournamentId]="tournamentId"
                    [dateRange]="dateRange"
                    [matchStatus]="matchStatus"
                    [seasons]="seasons"
                    [clubs]="clubs"
                    [tournaments]="tournaments"
                  ></app-schedule-view>
                </ng-template>
              </li>
            </ul>
          </div>
          <div [ngbNavOutlet]="nav" class="mt-2"></div>
        </div>
      </div>
    </section>
  </div>
</div>

