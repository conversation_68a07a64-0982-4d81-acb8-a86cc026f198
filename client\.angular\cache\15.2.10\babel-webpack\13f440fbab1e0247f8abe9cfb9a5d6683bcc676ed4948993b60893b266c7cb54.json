{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport { forkJoin } from 'rxjs';\nimport moment from \"moment\";\nimport { AppConfig } from \"../../../../app-config\";\nimport Swal from 'sweetalert2';\nimport decodeHtmlText from 'app/helpers/decode-html-special-chars';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../services/tournament.service\";\nimport * as i4 from \"../../../../services/location.service\";\nimport * as i5 from \"../../../../services/auto-schedule.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"app/services/season.service\";\nimport * as i8 from \"@angular/platform-browser\";\nexport class ModalSetupScheduleComponent {\n  constructor(_modalService, _translateService, _tournamentService, _locationService, _autoSchedule, _loadingService, _seasonService, _sanitizer) {\n    this._modalService = _modalService;\n    this._translateService = _translateService;\n    this._tournamentService = _tournamentService;\n    this._locationService = _locationService;\n    this._autoSchedule = _autoSchedule;\n    this._loadingService = _loadingService;\n    this._seasonService = _seasonService;\n    this._sanitizer = _sanitizer;\n    this.setupScheduleForm = new FormGroup({});\n    this.setupScheduleModel = {};\n    this.listStages = [];\n    this.refereeIds = [];\n    this.listGroups = [];\n    this.setupScheduleFields = [{\n      key: 'list_stages',\n      type: 'ng-select',\n      props: {\n        required: true,\n        multiple: false,\n        closeOnSelect: true,\n        label: 'Stage',\n        placeholder: 'Please select a stage to schedule',\n        options: []\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('Please select a stage to schedule.')\n        }\n      },\n      hooks: {\n        onChanges: field => {\n          field.form.get('list_stages').valueChanges.subscribe(value => {\n            this.setupScheduleModel['stage_id'] = this.listStages.find(item => item.name === value)?.id || -1;\n          });\n        }\n      }\n    }, {\n      key: 'list_group_names',\n      type: 'ng-select',\n      props: {\n        multiple: true,\n        hideOnMultiple: true,\n        defaultValue: [],\n        label: 'Groups',\n        placeholder: 'Select groups to schedule (leave empty to include all)',\n        options: [],\n        hide: true\n      },\n      hooks: {},\n      expressions: {\n        hide: 'model.list_stages !== \"Groups\"'\n      }\n    }, {\n      fieldGroupClassName: 'row',\n      fieldGroup: [{\n        className: 'col-md-4',\n        key: 'begin_date',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('Date'),\n          placeholder: this._translateService.instant('Select start date'),\n          required: true,\n          type: 'date'\n        },\n        defaultValue: moment().format('YYYY-MM-DD')\n      }, {\n        className: 'col-md-4',\n        key: 'begin_time',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('First Match Start Time'),\n          placeholder: this._translateService.instant('Select start time'),\n          required: true,\n          type: 'time'\n        },\n        defaultValue: moment().format('HH:mm')\n      }, {\n        className: 'col-md-4',\n        key: 'end_time',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('Latest End Time'),\n          placeholder: this._translateService.instant('Select end time'),\n          required: true,\n          type: 'time'\n        },\n        defaultValue: moment().format('HH:mm'),\n        validation: {\n          messages: {\n            required: this._translateService.instant('Begin time is required.')\n          }\n        },\n        validators: {\n          endTimeValidator: {\n            expression: control => {\n              const form = control.parent;\n              if (!form) return true;\n              const beginTime = form.get('begin_time').value;\n              const endTime = control.value;\n              return !beginTime || !endTime || endTime > beginTime;\n            },\n            message: this._translateService.instant('End time must be later than start time')\n          },\n          matchDurationValidator: {\n            expression: control => {\n              const form = control.parent;\n              if (!form) return true;\n              const beginTime = form.get('begin_time').value;\n              const endTime = control.value;\n              const matchDuration = form.get('match_duration')?.value || 0;\n              if (!beginTime || !endTime) return true;\n              const start = moment(beginTime, 'HH:mm');\n              const end = moment(endTime, 'HH:mm');\n              const diffInMinutes = end.diff(start, 'minutes');\n              return diffInMinutes >= matchDuration;\n            },\n            message: this._translateService.instant('Time slot must be equal or larger than match duration')\n          }\n        }\n      }]\n    }, {\n      fieldGroupClassName: 'row',\n      fieldGroup: [{\n        className: 'col-md-6',\n        key: 'match_duration',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('Match Duration (minutes)'),\n          placeholder: this._translateService.instant('Enter match duration'),\n          required: true,\n          type: 'number',\n          min: 1,\n          max: 1440,\n          step: 1,\n          pattern: '[0-9]*'\n        },\n        defaultValue: 25,\n        validation: {\n          messages: {\n            min: this._translateService.instant('Match duration must be at least 1 minute.'),\n            pattern: this._translateService.instant('Match duration must be an integer number.'),\n            max: this._translateService.instant('Match duration must be less than 1 day (1440 minutes).')\n          }\n        }\n      }, {\n        className: 'col-md-6',\n        key: 'break_duration',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('Break Duration (minutes)'),\n          placeholder: this._translateService.instant('Enter break duration between matches'),\n          required: true,\n          type: 'number',\n          min: 0,\n          max: 1440,\n          step: 1,\n          pattern: '[0-9]*'\n        },\n        defaultValue: 0,\n        validation: {\n          messages: {\n            min: this._translateService.instant('Break duration must be at least 0 minutes.'),\n            pattern: this._translateService.instant('Break duration must be an integer number.'),\n            max: this._translateService.instant('Break duration must be less than 1 day (1440 minutes).')\n          }\n        }\n      }]\n    }, {\n      key: 'list_location_ids',\n      type: 'ng-select',\n      props: {\n        multiple: true,\n        required: true,\n        hideOnMultiple: true,\n        defaultValue: [],\n        label: 'Locations',\n        placeholder: 'Select locations for scheduling',\n        options: []\n      },\n      hooks: {},\n      validation: {\n        messages: {\n          required: this._translateService.instant('Please select at least one location.')\n        }\n      }\n    }, {\n      fieldGroupClassName: \"row\",\n      fieldGroup: [{\n        key: 'nums_of_referees',\n        type: 'input',\n        className: 'col-md-4',\n        defaultValue: 0,\n        props: {\n          label: this._translateService.instant('Referees per Match'),\n          placeholder: this._translateService.instant('Enter number of referees'),\n          required: true,\n          type: 'number',\n          min: 0,\n          max: 100\n        },\n        validation: {\n          messages: {\n            min: this._translateService.instant('Number of referees must be at least 0.'),\n            max: this._translateService.instant('Number of referees must be less than 100.')\n          }\n        }\n      }, {\n        key: 'list_referee_ids',\n        type: 'ng-select',\n        className: 'col-md-8',\n        props: {\n          multiple: true,\n          hideOnMultiple: true,\n          defaultValue: [],\n          label: this._translateService.instant('Referees'),\n          placeholder: this._translateService.instant('Select referees'),\n          options: []\n        },\n        expressions: {\n          \"props.disabled\": 'model.nums_of_referees === 0',\n          \"props.required\": 'model.nums_of_referees > 0'\n        },\n        validation: {\n          messages: {\n            required: this._translateService.instant('Please select at least one referee.')\n          }\n        }\n      }]\n    }, {\n      key: 'tournament_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'stage_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }];\n    this.onSubmit = new EventEmitter();\n  }\n  ngOnInit() {\n    this._loadingService.show();\n    this.setupScheduleFields[this.setupScheduleFields.length - 2].defaultValue = this.tournamentId;\n    const observables = [this._tournamentService.getGroupInTournament(this.tournamentId), this._locationService.getAllLocations(), this._seasonService.getListSeasonReferees(this.seasonId), this._tournamentService.getStagesInTournament(this.tournamentId)];\n    forkJoin(observables).subscribe({\n      next: ([groupRes, locationRes, refereeRes, stagesRes]) => {\n        this.listGroups = groupRes.data;\n        this.setupScheduleFields[4].props.options = locationRes['data'].map(location => ({\n          label: decodeHtmlText(location.name),\n          value: location.id\n        }));\n        this.setupScheduleFields[5].fieldGroup[1].props.options = refereeRes['data'].map(referee => {\n          this.refereeIds.push(referee.id);\n          return {\n            label: referee.user ? `${referee.user.first_name} ${referee.user.last_name}` : referee.referee_name,\n            value: referee.id\n          };\n        });\n        this.listStages = stagesRes;\n        if (this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\n          this.setupScheduleFields[0].props.options = [{\n            label: 'All',\n            value: 'All'\n          }, ...stagesRes.map(item => ({\n            label: item.name,\n            value: item.name\n          }))];\n        } else {\n          this.setupScheduleFields[0].props.options = [...stagesRes.map(item => ({\n            label: item.name,\n            value: item.name\n          }))];\n        }\n      },\n      complete: () => {\n        this._loadingService.dismiss();\n      }\n    });\n  }\n  onSubmitSetup(model) {\n    // handle check model is all valids\n    if (this.setupScheduleForm.invalid) {\n      return;\n    }\n    if (model.list_stages === AppConfig.TOURNAMENT_TYPES.groups && !model.list_group_names) {\n      model.list_group_names = this.listGroups;\n    }\n    this._loadingService.show();\n    this._autoSchedule.scheduleTournamentAsync(model).subscribe(res => {\n      this.onSubmit.emit(\"fetchDates\");\n      this._modalService.dismissAll();\n      if (res.schedule_status === 'scheduling') {\n        Swal.fire({\n          title: 'Success',\n          text: res.message,\n          icon: 'success',\n          confirmButtonText: 'Ok'\n        });\n      }\n    }, error => {\n      Swal.fire({\n        title: 'Cannot Auto Schedule!',\n        text: error.message,\n        icon: 'warning',\n        confirmButtonText: 'Ok'\n      });\n    });\n  }\n  closeModal() {\n    this.setupScheduleModel = {};\n    this._modalService.dismissAll();\n  }\n  clearForm() {\n    this.setupScheduleForm.reset();\n  }\n  static #_ = this.ɵfac = function ModalSetupScheduleComponent_Factory(t) {\n    return new (t || ModalSetupScheduleComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.TournamentService), i0.ɵɵdirectiveInject(i4.LocationService), i0.ɵɵdirectiveInject(i5.AutoScheduleService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.SeasonService), i0.ɵɵdirectiveInject(i8.DomSanitizer));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalSetupScheduleComponent,\n    selectors: [[\"app-modal-setup-schedule\"]],\n    inputs: {\n      tournamentId: \"tournamentId\",\n      tournamentInfo: \"tournamentInfo\",\n      seasonId: \"seasonId\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 14,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"modalSetupSchedule\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"w-100\", \"btn\", \"btn-primary\", 3, \"disabled\"]],\n    template: function ModalSetupScheduleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalSetupScheduleComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalSetupScheduleComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitSetup(ctx.setupScheduleModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"formly-form\", 6);\n        i0.ɵɵlistener(\"submit\", function ModalSetupScheduleComponent_Template_formly_form_submit_9_listener() {\n          return ctx.onSubmitSetup(ctx.setupScheduleModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Setup Schedule\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.setupScheduleForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.setupScheduleForm)(\"fields\", ctx.setupScheduleFields)(\"model\", ctx.setupScheduleModel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", !ctx.setupScheduleForm.valid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Plan Match\"), \" \");\n      }\n    },\n    styles: [\".btn-clear[_ngcontent-%COMP%] {\\n  color: rgb(234, 84, 85);\\n}\\n\\n.w-max[_ngcontent-%COMP%] {\\n  width: max-content;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvYXV0by1zY2hlZHVsZS9tb2RhbC1zZXR1cC1zY2hlZHVsZS9tb2RhbC1zZXR1cC1zY2hlZHVsZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLHVCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmJ0bi1jbGVhciB7XHJcbiAgY29sb3I6IHJnYmEoMjM0LCA4NCwgODUsIDEpO1xyXG59XHJcblxyXG4udy1tYXgge1xyXG4gIHdpZHRoOiBtYXgtY29udGVudDtcclxufVxyXG5cclxuLm1vZGFsLWZvb3RlciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogLjI1cmVtO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAmBA,YAAY,QAAsB,eAAe;AAMpE,SAAQC,SAAS,QAAO,gBAAgB;AAGxC,SAAQC,QAAQ,QAAO,MAAM;AAE7B,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAAQC,SAAS,QAAO,wBAAwB;AAChD,OAAOC,IAAI,MAAM,aAAa;AAE9B,OAAOC,cAAc,MAAM,uCAAuC;;;;;;;;;;AAOlE,OAAM,MAAOC,2BAA2B;EA6QpCC,YACYC,aAAuB,EACvBC,iBAAmC,EACnCC,kBAAqC,EACrCC,gBAAiC,EACjCC,aAAkC,EAClCC,eAA+B,EAC/BC,cAA6B,EAC7BC,UAAwB;IAPxB,KAAAP,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,UAAU,GAAVA,UAAU;IA9QtB,KAAAC,iBAAiB,GAAG,IAAIhB,SAAS,CAAC,EAAE,CAAC;IACrC,KAAAiB,kBAAkB,GAAG,EAAE;IAEvB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,UAAU,GAAG,EAAE;IAER,KAAAC,mBAAmB,GAAwB,CAC9C;MACIC,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACHC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,IAAI;QACnBC,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,mCAAmC;QAChDC,OAAO,EAAE;OACZ;MACDC,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNP,QAAQ,EAAE,IAAI,CAAChB,iBAAiB,CAACwB,OAAO,CAAC,oCAAoC;;OAEpF;MACDC,KAAK,EAAE;QACHC,SAAS,EAAGC,KAAK,IAAI;UACjBA,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,aAAa,CAAC,CAACC,YAAY,CAACC,SAAS,CAACC,KAAK,IAAG;YACzD,IAAI,CAACxB,kBAAkB,CAAC,UAAU,CAAC,GAAG,IAAI,CAACC,UAAU,CAACwB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKH,KAAK,CAAC,EAAEI,EAAE,IAAI,CAAC,CAAC;UACrG,CAAC,CAAC;QACN;;KAEP,EACD;MACIvB,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACHE,QAAQ,EAAE,IAAI;QACdoB,cAAc,EAAE,IAAI;QACpBC,YAAY,EAAE,EAAE;QAChBnB,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,wDAAwD;QACrEC,OAAO,EAAE,EAAE;QACXkB,IAAI,EAAE;OACT;MACDd,KAAK,EAAE,EAAE;MACTe,WAAW,EAAE;QACTD,IAAI,EAAE;;KAEb,EACD;MACIE,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,CACR;QACIC,SAAS,EAAE,UAAU;QACrB9B,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACwB,OAAO,CAAC,MAAM,CAAC;UAC7CJ,WAAW,EAAE,IAAI,CAACpB,iBAAiB,CAACwB,OAAO,CAAC,mBAAmB,CAAC;UAChER,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE;SACT;QACDwB,YAAY,EAAE7C,MAAM,EAAE,CAACmD,MAAM,CAAC,YAAY;OAC7C,EACD;QACID,SAAS,EAAE,UAAU;QACrB9B,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACwB,OAAO,CAAC,wBAAwB,CAAC;UAC/DJ,WAAW,EAAE,IAAI,CAACpB,iBAAiB,CAACwB,OAAO,CAAC,mBAAmB,CAAC;UAChER,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE;SACT;QACDwB,YAAY,EAAE7C,MAAM,EAAE,CAACmD,MAAM,CAAC,OAAO;OACxC,EACD;QACID,SAAS,EAAE,UAAU;QACrB9B,GAAG,EAAE,UAAU;QACfC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACwB,OAAO,CAAC,iBAAiB,CAAC;UACxDJ,WAAW,EAAE,IAAI,CAACpB,iBAAiB,CAACwB,OAAO,CAAC,iBAAiB,CAAC;UAC9DR,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE;SACT;QACDwB,YAAY,EAAE7C,MAAM,EAAE,CAACmD,MAAM,CAAC,OAAO,CAAC;QACtCtB,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNP,QAAQ,EAAE,IAAI,CAAChB,iBAAiB,CAACwB,OAAO,CAAC,yBAAyB;;SAEzE;QACDqB,UAAU,EAAE;UACRC,gBAAgB,EAAE;YACdC,UAAU,EAAGC,OAAO,IAAI;cACpB,MAAMpB,IAAI,GAAGoB,OAAO,CAACC,MAAM;cAC3B,IAAI,CAACrB,IAAI,EAAE,OAAO,IAAI;cACtB,MAAMsB,SAAS,GAAGtB,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC,CAACG,KAAK;cAC9C,MAAMmB,OAAO,GAAGH,OAAO,CAAChB,KAAK;cAC7B,OAAO,CAACkB,SAAS,IAAI,CAACC,OAAO,IAAIA,OAAO,GAAGD,SAAS;YACxD,CAAC;YACDE,OAAO,EAAE,IAAI,CAACpD,iBAAiB,CAACwB,OAAO,CAAC,wCAAwC;WACnF;UACD6B,sBAAsB,EAAE;YACpBN,UAAU,EAAGC,OAAO,IAAI;cACpB,MAAMpB,IAAI,GAAGoB,OAAO,CAACC,MAAM;cAC3B,IAAI,CAACrB,IAAI,EAAE,OAAO,IAAI;cACtB,MAAMsB,SAAS,GAAGtB,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC,CAACG,KAAK;cAC9C,MAAMmB,OAAO,GAAGH,OAAO,CAAChB,KAAK;cAC7B,MAAMsB,aAAa,GAAG1B,IAAI,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAEG,KAAK,IAAI,CAAC;cAE5D,IAAI,CAACkB,SAAS,IAAI,CAACC,OAAO,EAAE,OAAO,IAAI;cAEvC,MAAMI,KAAK,GAAG9D,MAAM,CAACyD,SAAS,EAAE,OAAO,CAAC;cACxC,MAAMM,GAAG,GAAG/D,MAAM,CAAC0D,OAAO,EAAE,OAAO,CAAC;cACpC,MAAMM,aAAa,GAAGD,GAAG,CAACE,IAAI,CAACH,KAAK,EAAE,SAAS,CAAC;cAEhD,OAAOE,aAAa,IAAIH,aAAa;YACzC,CAAC;YACDF,OAAO,EAAE,IAAI,CAACpD,iBAAiB,CAACwB,OAAO,CAAC,uDAAuD;;;OAG1G;KAER,EACD;MACIiB,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,CAER;QACIC,SAAS,EAAE,UAAU;QACrB9B,GAAG,EAAE,gBAAgB;QACrBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACwB,OAAO,CAAC,0BAA0B,CAAC;UACjEJ,WAAW,EAAE,IAAI,CAACpB,iBAAiB,CAACwB,OAAO,CAAC,sBAAsB,CAAC;UACnER,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE,QAAQ;UACd6C,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,CAAC;UACPC,OAAO,EAAE;SACZ;QACDxB,YAAY,EAAE,EAAE;QAChBhB,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNoC,GAAG,EAAE,IAAI,CAAC3D,iBAAiB,CAACwB,OAAO,CAAC,2CAA2C,CAAC;YAChFsC,OAAO,EAAE,IAAI,CAAC9D,iBAAiB,CAACwB,OAAO,CAAC,2CAA2C,CAAC;YACpFoC,GAAG,EAAE,IAAI,CAAC5D,iBAAiB,CAACwB,OAAO,CAAC,wDAAwD;;;OAGvG,EACD;QACImB,SAAS,EAAE,UAAU;QACrB9B,GAAG,EAAE,gBAAgB;QACrBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACwB,OAAO,CAAC,0BAA0B,CAAC;UACjEJ,WAAW,EAAE,IAAI,CAACpB,iBAAiB,CAACwB,OAAO,CAAC,sCAAsC,CAAC;UACnFR,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE,QAAQ;UACd6C,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,CAAC;UACPC,OAAO,EAAE;SACZ;QACDxB,YAAY,EAAE,CAAC;QACfhB,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNoC,GAAG,EAAE,IAAI,CAAC3D,iBAAiB,CAACwB,OAAO,CAAC,4CAA4C,CAAC;YACjFsC,OAAO,EAAE,IAAI,CAAC9D,iBAAiB,CAACwB,OAAO,CAAC,2CAA2C,CAAC;YACpFoC,GAAG,EAAE,IAAI,CAAC5D,iBAAiB,CAACwB,OAAO,CAAC,wDAAwD;;;OAGvG;KAER,EACD;MACIX,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACHE,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAE,IAAI;QACdqB,cAAc,EAAE,IAAI;QACpBC,YAAY,EAAE,EAAE;QAChBnB,KAAK,EAAE,WAAW;QAClBC,WAAW,EAAE,iCAAiC;QAC9CC,OAAO,EAAE;OACZ;MACDI,KAAK,EAAE,EAAE;MACTH,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNP,QAAQ,EAAE,IAAI,CAAChB,iBAAiB,CAACwB,OAAO,CAAC,sCAAsC;;;KAG1F,EACD;MACIiB,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,CACR;QACI7B,GAAG,EAAE,kBAAkB;QACvBC,IAAI,EAAE,OAAO;QACb6B,SAAS,EAAE,UAAU;QACrBL,YAAY,EAAE,CAAC;QACfvB,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACwB,OAAO,CAAC,oBAAoB,CAAC;UAC3DJ,WAAW,EAAE,IAAI,CAACpB,iBAAiB,CAACwB,OAAO,CAAC,0BAA0B,CAAC;UACvER,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE,QAAQ;UACd6C,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE;SACR;QACDtC,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNoC,GAAG,EAAE,IAAI,CAAC3D,iBAAiB,CAACwB,OAAO,CAAC,wCAAwC,CAAC;YAC7EoC,GAAG,EAAE,IAAI,CAAC5D,iBAAiB,CAACwB,OAAO,CAAC,2CAA2C;;;OAG1F,EACD;QACIX,GAAG,EAAE,kBAAkB;QACvBC,IAAI,EAAE,WAAW;QACjB6B,SAAS,EAAE,UAAU;QACrB5B,KAAK,EAAE;UACHE,QAAQ,EAAE,IAAI;UACdoB,cAAc,EAAE,IAAI;UACpBC,YAAY,EAAE,EAAE;UAChBnB,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACwB,OAAO,CAAC,UAAU,CAAC;UACjDJ,WAAW,EAAE,IAAI,CAACpB,iBAAiB,CAACwB,OAAO,CAAC,iBAAiB,CAAC;UAC9DH,OAAO,EAAE;SACZ;QACDmB,WAAW,EAAE;UACT,gBAAgB,EAAE,8BAA8B;UAChD,gBAAgB,EAAE;SACrB;QACDlB,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNP,QAAQ,EAAE,IAAI,CAAChB,iBAAiB,CAACwB,OAAO,CAAC,qCAAqC;;;OAIzF;KAER,EACD;MACIX,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,CACJ;IAES,KAAAiD,QAAQ,GAAG,IAAIzE,YAAY,EAAE;EAavC;EAGA0E,QAAQA,CAAA;IACJ,IAAI,CAAC5D,eAAe,CAAC6D,IAAI,EAAE;IAE3B,IAAI,CAACrD,mBAAmB,CAAC,IAAI,CAACA,mBAAmB,CAACsD,MAAM,GAAG,CAAC,CAAC,CAAC5B,YAAY,GAAG,IAAI,CAAC6B,YAAY;IAE9F,MAAMC,WAAW,GAAG,CAChB,IAAI,CAACnE,kBAAkB,CAACoE,oBAAoB,CAAC,IAAI,CAACF,YAAY,CAAC,EAC/D,IAAI,CAACjE,gBAAgB,CAACoE,eAAe,EAAE,EACvC,IAAI,CAACjE,cAAc,CAACkE,qBAAqB,CAAC,IAAI,CAACC,QAAQ,CAAC,EACxD,IAAI,CAACvE,kBAAkB,CAACwE,qBAAqB,CAAC,IAAI,CAACN,YAAY,CAAC,CACnE;IAED3E,QAAQ,CAAC4E,WAAW,CAAC,CAACrC,SAAS,CAAC;MAC5B2C,IAAI,EAAEA,CAAC,CAACC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,CAAC,KAAI;QACrD,IAAI,CAACnE,UAAU,GAAGgE,QAAQ,CAACI,IAAI;QAC/B,IAAI,CAACnE,mBAAmB,CAAC,CAAC,CAAC,CAACG,KAAK,CAACM,OAAO,GAAGuD,WAAW,CAAC,MAAM,CAAC,CAACI,GAAG,CAAEC,QAGpE,KAAM;UACH9D,KAAK,EAAEvB,cAAc,CAACqF,QAAQ,CAAC9C,IAAI,CAAC;UACpCH,KAAK,EAAEiD,QAAQ,CAAC7C;SACnB,CAAC,CAAC;QAEH,IAAI,CAACxB,mBAAmB,CAAC,CAAC,CAAC,CAAC8B,UAAU,CAAC,CAAC,CAAC,CAAC3B,KAAK,CAACM,OAAO,GAAGwD,UAAU,CAAC,MAAM,CAAC,CAACG,GAAG,CAAEE,OAAO,IAAI;UACzF,IAAI,CAACxE,UAAU,CAACyE,IAAI,CAACD,OAAO,CAAC9C,EAAE,CAAC;UAChC,OAAO;YACHjB,KAAK,EAAE+D,OAAO,CAACE,IAAI,GACb,GAAGF,OAAO,CAACE,IAAI,CAACC,UAAU,IAAIH,OAAO,CAACE,IAAI,CAACE,SAAS,EAAE,GACtDJ,OAAO,CAACK,YAAY;YAC1BvD,KAAK,EAAEkD,OAAO,CAAC9C;WAClB;QACL,CAAC,CAAC;QAEF,IAAI,CAAC3B,UAAU,GAAGqE,SAAS;QAE3B,IAAI,IAAI,CAACU,cAAc,CAAC,MAAM,CAAC,KAAK9F,SAAS,CAAC+F,gBAAgB,CAACC,gBAAgB,EAAE;UAC7E,IAAI,CAAC9E,mBAAmB,CAAC,CAAC,CAAC,CAACG,KAAK,CAACM,OAAO,GAAG,CACxC;YACIF,KAAK,EAAE,KAAK;YACZa,KAAK,EAAE;WACV,EACD,GAAG8C,SAAS,CAACE,GAAG,CAAE9C,IAAI,KAAM;YACxBf,KAAK,EAAEe,IAAI,CAACC,IAAI;YAChBH,KAAK,EAAEE,IAAI,CAACC;WACf,CAAC,CAAC,CACN;SACJ,MAAM;UACH,IAAI,CAACvB,mBAAmB,CAAC,CAAC,CAAC,CAACG,KAAK,CAACM,OAAO,GAAG,CACxC,GAAGyD,SAAS,CAACE,GAAG,CAAE9C,IAAI,KAAM;YACxBf,KAAK,EAAEe,IAAI,CAACC,IAAI;YAChBH,KAAK,EAAEE,IAAI,CAACC;WACf,CAAC,CAAC,CACN;;MAET,CAAC;MACDwD,QAAQ,EAAEA,CAAA,KAAK;QACX,IAAI,CAACvF,eAAe,CAACwF,OAAO,EAAE;MAClC;KACH,CAAC;EACN;EAEAC,aAAaA,CAACC,KAAK;IACf;IACA,IAAI,IAAI,CAACvF,iBAAiB,CAACwF,OAAO,EAAE;MAChC;;IAGJ,IAAID,KAAK,CAACE,WAAW,KAAKtG,SAAS,CAAC+F,gBAAgB,CAACQ,MAAM,IAAI,CAACH,KAAK,CAACI,gBAAgB,EAAE;MACpFJ,KAAK,CAACI,gBAAgB,GAAG,IAAI,CAACvF,UAAU;;IAG5C,IAAI,CAACP,eAAe,CAAC6D,IAAI,EAAE;IAE3B,IAAI,CAAC9D,aAAa,CAACgG,uBAAuB,CAACL,KAAK,CAAC,CAAC/D,SAAS,CAAEqE,GAAG,IAAI;MAChE,IAAI,CAACrC,QAAQ,CAACsC,IAAI,CAAC,YAAY,CAAC;MAChC,IAAI,CAACtG,aAAa,CAACuG,UAAU,EAAE;MAC/B,IAAIF,GAAG,CAACG,eAAe,KAAK,YAAY,EAAE;QACtC5G,IAAI,CAAC6G,IAAI,CAAC;UACNC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAEN,GAAG,CAAChD,OAAO;UACjBuD,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE;SACtB,CAAC;;IAEV,CAAC,EAAGC,KAAK,IAAI;MACTlH,IAAI,CAAC6G,IAAI,CAAC;QACNC,KAAK,EAAE,uBAAuB;QAC9BC,IAAI,EAAEG,KAAK,CAACzD,OAAO;QACnBuD,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE;OACtB,CAAC;IACN,CAAC,CAAC;EACN;EAGAE,UAAUA,CAAA;IACN,IAAI,CAACtG,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACT,aAAa,CAACuG,UAAU,EAAE;EACnC;EAEAS,SAASA,CAAA;IACL,IAAI,CAACxG,iBAAiB,CAACyG,KAAK,EAAE;EAClC;EAAC,QAAAC,CAAA;qBAjYQpH,2BAA2B,EAAAqH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,mBAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAb,EAAA,CAAAC,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAAf,EAAA,CAAAC,iBAAA,CAAAe,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA;UAA3BvI,2BAA2B;IAAAwI,SAAA;IAAAC,MAAA;MAAAnE,YAAA;MAAAqB,cAAA;MAAAhB,QAAA;IAAA;IAAA+D,OAAA;MAAAxE,QAAA;IAAA;IAAAyE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCtBxC3B,EAAA,CAAA6B,cAAA,aAA0B;QACwB7B,EAAA,CAAA8B,MAAA,GAAkC;;QAAA9B,EAAA,CAAA+B,YAAA,EAAK;QACvF/B,EAAA,CAAA6B,cAAA,gBAKC;QAFC7B,EAAA,CAAAgC,UAAA,mBAAAC,6DAAA;UAAA,OAASL,GAAA,CAAAhC,UAAA,EAAY;QAAA,EAAC;QAGtBI,EAAA,CAAA6B,cAAA,cAAyB;QAAA7B,EAAA,CAAA8B,MAAA,aAAO;QAAA9B,EAAA,CAAA+B,YAAA,EAAO;QAG3C/B,EAAA,CAAA6B,cAAA,cAGC;QADC7B,EAAA,CAAAgC,UAAA,sBAAAE,8DAAA;UAAA,OAAYN,GAAA,CAAAjD,aAAA,CAAAiD,GAAA,CAAAtI,kBAAA,CAAiC;QAAA,EAAC;QAE9C0G,EAAA,CAAA6B,cAAA,aAAkD;QAK9C7B,EAAA,CAAAgC,UAAA,oBAAAG,mEAAA;UAAA,OAAUP,GAAA,CAAAjD,aAAA,CAAAiD,GAAA,CAAAtI,kBAAA,CAAiC;QAAA,EAAC;QAC7C0G,EAAA,CAAA+B,YAAA,EAAc;QAEjB/B,EAAA,CAAA6B,cAAA,cAA0B;QAItB7B,EAAA,CAAA8B,MAAA,IACF;;QAAA9B,EAAA,CAAA+B,YAAA,EAAS;;;QA3BqC/B,EAAA,CAAAoC,SAAA,GAAkC;QAAlCpC,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAsC,WAAA,yBAAkC;QAWlFtC,EAAA,CAAAoC,SAAA,GAA+B;QAA/BpC,EAAA,CAAAuC,UAAA,cAAAX,GAAA,CAAAvI,iBAAA,CAA+B;QAK3B2G,EAAA,CAAAoC,SAAA,GAA0B;QAA1BpC,EAAA,CAAAuC,UAAA,SAAAX,GAAA,CAAAvI,iBAAA,CAA0B,WAAAuI,GAAA,CAAAlI,mBAAA,WAAAkI,GAAA,CAAAtI,kBAAA;QAQpB0G,EAAA,CAAAoC,SAAA,GAAqC;QAArCpC,EAAA,CAAAuC,UAAA,cAAAX,GAAA,CAAAvI,iBAAA,CAAAmJ,KAAA,CAAqC;QAE3CxC,EAAA,CAAAoC,SAAA,GACF;QADEpC,EAAA,CAAAyC,kBAAA,MAAAzC,EAAA,CAAAsC,WAAA,2BACF", "names": ["EventEmitter", "FormGroup", "fork<PERSON><PERSON>n", "moment", "AppConfig", "<PERSON><PERSON>", "decodeHtmlText", "ModalSetupScheduleComponent", "constructor", "_modalService", "_translateService", "_tournamentService", "_locationService", "_autoSchedule", "_loadingService", "_seasonService", "_sanitizer", "setupScheduleForm", "setupScheduleModel", "listStages", "refereeIds", "listGroups", "setupScheduleFields", "key", "type", "props", "required", "multiple", "closeOnSelect", "label", "placeholder", "options", "validation", "messages", "instant", "hooks", "onChanges", "field", "form", "get", "valueChanges", "subscribe", "value", "find", "item", "name", "id", "hideOnMultiple", "defaultValue", "hide", "expressions", "fieldGroupClassName", "fieldGroup", "className", "format", "validators", "endTimeValidator", "expression", "control", "parent", "beginTime", "endTime", "message", "matchDurationValidator", "matchDuration", "start", "end", "diffInMinutes", "diff", "min", "max", "step", "pattern", "onSubmit", "ngOnInit", "show", "length", "tournamentId", "observables", "getGroupInTournament", "getAllLocations", "getListSeasonReferees", "seasonId", "getStagesInTournament", "next", "groupRes", "locationRes", "referee<PERSON>es", "stagesRes", "data", "map", "location", "referee", "push", "user", "first_name", "last_name", "referee_name", "tournamentInfo", "TOURNAMENT_TYPES", "groups_knockouts", "complete", "dismiss", "onSubmitSetup", "model", "invalid", "list_stages", "groups", "list_group_names", "scheduleTournamentAsync", "res", "emit", "dismissAll", "schedule_status", "fire", "title", "text", "icon", "confirmButtonText", "error", "closeModal", "clearForm", "reset", "_", "i0", "ɵɵdirectiveInject", "i1", "NgbModal", "i2", "TranslateService", "i3", "TournamentService", "i4", "LocationService", "i5", "AutoScheduleService", "i6", "LoadingService", "i7", "SeasonService", "i8", "Dom<PERSON><PERSON><PERSON>zer", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ModalSetupScheduleComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ModalSetupScheduleComponent_Template_button_click_4_listener", "ModalSetupScheduleComponent_Template_form_ngSubmit_7_listener", "ModalSetupScheduleComponent_Template_formly_form_submit_9_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵproperty", "valid", "ɵɵtextInterpolate1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-setup-schedule\\modal-setup-schedule.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-setup-schedule\\modal-setup-schedule.component.html"], "sourcesContent": ["import {Component, EventEmitter, Input, Output} from '@angular/core';\r\nimport {NgbModal} from '@ng-bootstrap/ng-bootstrap';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {FormlyFieldConfig} from '@ngx-formly/core';\r\nimport {TournamentService} from '../../../../services/tournament.service';\r\nimport {LocationService} from '../../../../services/location.service';\r\nimport {FormGroup} from '@angular/forms';\r\nimport {AutoScheduleService} from '../../../../services/auto-schedule.service';\r\nimport {LoadingService} from 'app/services/loading.service';\r\nimport {forkJoin} from 'rxjs';\r\nimport {SeasonService} from 'app/services/season.service';\r\nimport moment from \"moment\";\r\nimport {AppConfig} from \"../../../../app-config\";\r\nimport Swal from 'sweetalert2';\r\nimport {DomSanitizer} from '@angular/platform-browser';\r\nimport decodeHtmlText from 'app/helpers/decode-html-special-chars';\r\n\r\n@Component({\r\n    selector: 'app-modal-setup-schedule',\r\n    templateUrl: './modal-setup-schedule.component.html',\r\n    styleUrls: ['./modal-setup-schedule.component.scss']\r\n})\r\nexport class ModalSetupScheduleComponent {\r\n\r\n    @Input() tournamentId: string;\r\n    @Input() tournamentInfo: string;\r\n    @Input() seasonId: string;\r\n\r\n\r\n    setupScheduleForm = new FormGroup({});\r\n    setupScheduleModel = {};\r\n\r\n    listStages = [];\r\n    refereeIds = [];\r\n    listGroups = [];\r\n\r\n    public setupScheduleFields: FormlyFieldConfig[] = [\r\n        {\r\n            key: 'list_stages',\r\n            type: 'ng-select',\r\n            props: {\r\n                required: true,\r\n                multiple: false,\r\n                closeOnSelect: true,\r\n                label: 'Stage',\r\n                placeholder: 'Please select a stage to schedule',\r\n                options: []\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Please select a stage to schedule.')\r\n                }\r\n            },\r\n            hooks: {\r\n                onChanges: (field) => {\r\n                    field.form.get('list_stages').valueChanges.subscribe(value => {\r\n                        this.setupScheduleModel['stage_id'] = this.listStages.find(item => item.name === value)?.id || -1;\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'list_group_names',\r\n            type: 'ng-select',\r\n            props: {\r\n                multiple: true,\r\n                hideOnMultiple: true,\r\n                defaultValue: [],\r\n                label: 'Groups',\r\n                placeholder: 'Select groups to schedule (leave empty to include all)',\r\n                options: [],\r\n                hide: true\r\n            },\r\n            hooks: {},\r\n            expressions: {\r\n                hide: 'model.list_stages !== \"Groups\"',\r\n            }\r\n        },\r\n        {\r\n            fieldGroupClassName: 'row',\r\n            fieldGroup: [\r\n                {\r\n                    className: 'col-md-4',\r\n                    key: 'begin_date',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('Date'),\r\n                        placeholder: this._translateService.instant('Select start date'),\r\n                        required: true,\r\n                        type: 'date'\r\n                    },\r\n                    defaultValue: moment().format('YYYY-MM-DD')\r\n                },\r\n                {\r\n                    className: 'col-md-4',\r\n                    key: 'begin_time',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('First Match Start Time'),\r\n                        placeholder: this._translateService.instant('Select start time'),\r\n                        required: true,\r\n                        type: 'time'\r\n                    },\r\n                    defaultValue: moment().format('HH:mm')\r\n                },\r\n                {\r\n                    className: 'col-md-4',\r\n                    key: 'end_time',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('Latest End Time'),\r\n                        placeholder: this._translateService.instant('Select end time'),\r\n                        required: true,\r\n                        type: 'time'\r\n                    },\r\n                    defaultValue: moment().format('HH:mm'),\r\n                    validation: {\r\n                        messages: {\r\n                            required: this._translateService.instant('Begin time is required.')\r\n                        }\r\n                    },\r\n                    validators: {\r\n                        endTimeValidator: {\r\n                            expression: (control) => {\r\n                                const form = control.parent;\r\n                                if (!form) return true;\r\n                                const beginTime = form.get('begin_time').value;\r\n                                const endTime = control.value;\r\n                                return !beginTime || !endTime || endTime > beginTime;\r\n                            },\r\n                            message: this._translateService.instant('End time must be later than start time')\r\n                        },\r\n                        matchDurationValidator: {\r\n                            expression: (control) => {\r\n                                const form = control.parent;\r\n                                if (!form) return true;\r\n                                const beginTime = form.get('begin_time').value;\r\n                                const endTime = control.value;\r\n                                const matchDuration = form.get('match_duration')?.value || 0;\r\n\r\n                                if (!beginTime || !endTime) return true;\r\n\r\n                                const start = moment(beginTime, 'HH:mm');\r\n                                const end = moment(endTime, 'HH:mm');\r\n                                const diffInMinutes = end.diff(start, 'minutes');\r\n\r\n                                return diffInMinutes >= matchDuration;\r\n                            },\r\n                            message: this._translateService.instant('Time slot must be equal or larger than match duration')\r\n                        }\r\n                    }\r\n                },\r\n            ]\r\n        },\r\n        {\r\n            fieldGroupClassName: 'row',\r\n            fieldGroup: [\r\n\r\n                {\r\n                    className: 'col-md-6',\r\n                    key: 'match_duration',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('Match Duration (minutes)'),\r\n                        placeholder: this._translateService.instant('Enter match duration'),\r\n                        required: true,\r\n                        type: 'number',\r\n                        min: 1,\r\n                        max: 1440,\r\n                        step: 1,\r\n                        pattern: '[0-9]*'\r\n                    },\r\n                    defaultValue: 25,\r\n                    validation: {\r\n                        messages: {\r\n                            min: this._translateService.instant('Match duration must be at least 1 minute.'),\r\n                            pattern: this._translateService.instant('Match duration must be an integer number.'),\r\n                            max: this._translateService.instant('Match duration must be less than 1 day (1440 minutes).')\r\n                        }\r\n                    }\r\n                },\r\n                {\r\n                    className: 'col-md-6',\r\n                    key: 'break_duration',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('Break Duration (minutes)'),\r\n                        placeholder: this._translateService.instant('Enter break duration between matches'),\r\n                        required: true,\r\n                        type: 'number',\r\n                        min: 0,\r\n                        max: 1440,\r\n                        step: 1,\r\n                        pattern: '[0-9]*'\r\n                    },\r\n                    defaultValue: 0,\r\n                    validation: {\r\n                        messages: {\r\n                            min: this._translateService.instant('Break duration must be at least 0 minutes.'),\r\n                            pattern: this._translateService.instant('Break duration must be an integer number.'),\r\n                            max: this._translateService.instant('Break duration must be less than 1 day (1440 minutes).')\r\n                        }\r\n                    }\r\n                },\r\n            ]\r\n        },\r\n        {\r\n            key: 'list_location_ids',\r\n            type: 'ng-select',\r\n            props: {\r\n                multiple: true,\r\n                required: true,\r\n                hideOnMultiple: true,\r\n                defaultValue: [],\r\n                label: 'Locations',\r\n                placeholder: 'Select locations for scheduling',\r\n                options: []\r\n            },\r\n            hooks: {},\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Please select at least one location.')\r\n                }\r\n            },\r\n        },\r\n        {\r\n            fieldGroupClassName: \"row\",\r\n            fieldGroup: [\r\n                {\r\n                    key: 'nums_of_referees',\r\n                    type: 'input',\r\n                    className: 'col-md-4',\r\n                    defaultValue: 0,\r\n                    props: {\r\n                        label: this._translateService.instant('Referees per Match'),\r\n                        placeholder: this._translateService.instant('Enter number of referees'),\r\n                        required: true,\r\n                        type: 'number',\r\n                        min: 0,\r\n                        max: 100,\r\n                    },\r\n                    validation: {\r\n                        messages: {\r\n                            min: this._translateService.instant('Number of referees must be at least 0.'),\r\n                            max: this._translateService.instant('Number of referees must be less than 100.')\r\n                        }\r\n                    }\r\n                },\r\n                {\r\n                    key: 'list_referee_ids',\r\n                    type: 'ng-select',\r\n                    className: 'col-md-8',\r\n                    props: {\r\n                        multiple: true,\r\n                        hideOnMultiple: true,\r\n                        defaultValue: [],\r\n                        label: this._translateService.instant('Referees'),\r\n                        placeholder: this._translateService.instant('Select referees'),\r\n                        options: []\r\n                    },\r\n                    expressions: {\r\n                        \"props.disabled\": 'model.nums_of_referees === 0',\r\n                        \"props.required\": 'model.nums_of_referees > 0'\r\n                    },\r\n                    validation: {\r\n                        messages: {\r\n                            required: this._translateService.instant('Please select at least one referee.')\r\n                        }\r\n                    }\r\n\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            key: 'tournament_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        },\r\n        {\r\n            key: 'stage_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        }\r\n    ];\r\n\r\n    @Output() onSubmit = new EventEmitter();\r\n\r\n    constructor(\r\n        private _modalService: NgbModal,\r\n        private _translateService: TranslateService,\r\n        private _tournamentService: TournamentService,\r\n        private _locationService: LocationService,\r\n        private _autoSchedule: AutoScheduleService,\r\n        private _loadingService: LoadingService,\r\n        private _seasonService: SeasonService,\r\n        private _sanitizer: DomSanitizer\r\n    ) {\r\n\r\n    }\r\n\r\n\r\n    ngOnInit() {\r\n        this._loadingService.show();\r\n\r\n        this.setupScheduleFields[this.setupScheduleFields.length - 2].defaultValue = this.tournamentId;\r\n\r\n        const observables = [\r\n            this._tournamentService.getGroupInTournament(this.tournamentId),\r\n            this._locationService.getAllLocations(),\r\n            this._seasonService.getListSeasonReferees(this.seasonId),\r\n            this._tournamentService.getStagesInTournament(this.tournamentId)\r\n        ];\r\n\r\n        forkJoin(observables).subscribe({\r\n            next: ([groupRes, locationRes, refereeRes, stagesRes]) => {\r\n                this.listGroups = groupRes.data;\r\n                this.setupScheduleFields[4].props.options = locationRes['data'].map((location: {\r\n                    name: string,\r\n                    id: any\r\n                }) => ({\r\n                    label: decodeHtmlText(location.name),\r\n                    value: location.id\r\n                }));\r\n\r\n                this.setupScheduleFields[5].fieldGroup[1].props.options = refereeRes['data'].map((referee) => {\r\n                    this.refereeIds.push(referee.id);\r\n                    return {\r\n                        label: referee.user\r\n                            ? `${referee.user.first_name} ${referee.user.last_name}`\r\n                            : referee.referee_name,\r\n                        value: referee.id\r\n                    }\r\n                });\r\n\r\n                this.listStages = stagesRes;\r\n\r\n                if (this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\r\n                    this.setupScheduleFields[0].props.options = [\r\n                        {\r\n                            label: 'All',\r\n                            value: 'All',\r\n                        },\r\n                        ...stagesRes.map((item) => ({\r\n                            label: item.name,\r\n                            value: item.name\r\n                        }))\r\n                    ];\r\n                } else {\r\n                    this.setupScheduleFields[0].props.options = [\r\n                        ...stagesRes.map((item) => ({\r\n                            label: item.name,\r\n                            value: item.name\r\n                        }))\r\n                    ];\r\n                }\r\n            },\r\n            complete: () => {\r\n                this._loadingService.dismiss();\r\n            }\r\n        });\r\n    }\r\n\r\n    onSubmitSetup(model) {\r\n        // handle check model is all valids\r\n        if (this.setupScheduleForm.invalid) {\r\n            return;\r\n        }\r\n\r\n        if (model.list_stages === AppConfig.TOURNAMENT_TYPES.groups && !model.list_group_names) {\r\n            model.list_group_names = this.listGroups;\r\n        }\r\n\r\n        this._loadingService.show();\r\n\r\n        this._autoSchedule.scheduleTournamentAsync(model).subscribe((res) => {\r\n            this.onSubmit.emit(\"fetchDates\");\r\n            this._modalService.dismissAll();\r\n            if (res.schedule_status === 'scheduling') {\r\n                Swal.fire({\r\n                    title: 'Success',\r\n                    text: res.message,\r\n                    icon: 'success',\r\n                    confirmButtonText: 'Ok'\r\n                })\r\n            }\r\n        }, (error) => {\r\n            Swal.fire({\r\n                title: 'Cannot Auto Schedule!',\r\n                text: error.message,\r\n                icon: 'warning',\r\n                confirmButtonText: 'Ok'\r\n            });\r\n        });\r\n    }\r\n\r\n\r\n    closeModal() {\r\n        this.setupScheduleModel = {};\r\n        this._modalService.dismissAll();\r\n    }\r\n\r\n    clearForm() {\r\n        this.setupScheduleForm.reset();\r\n    }\r\n\r\n}\r\n", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\" id=\"modalSetupSchedule\">{{ \"Setup Schedule\" | translate }}</h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form\r\n  [formGroup]=\"setupScheduleForm\"\r\n  (ngSubmit)=\"onSubmitSetup(setupScheduleModel)\"\r\n>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <formly-form\r\n      [form]=\"setupScheduleForm\"\r\n      [fields]=\"setupScheduleFields\"\r\n      [model]=\"setupScheduleModel\"\r\n      (submit)=\"onSubmitSetup(setupScheduleModel)\"\r\n    ></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"w-100 btn btn-primary\" rippleEffect\r\n            [disabled]=\"!setupScheduleForm.valid\"\r\n    >\r\n      {{ 'Plan Match' | translate }}\r\n    </button>\r\n  </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}