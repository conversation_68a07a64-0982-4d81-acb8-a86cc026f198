/*!
 * 
 *   red5pro-sdk - Red5 Pro HTML Publisher and Subscriber SDK.
 *   Author: Infrared5 Inc.
 *   Version: 11.2.0-beta1
 *   Url: https://github.com/red5pro/red5pro-html-sdk#readme
 * 
 *   Copyright © 2015 Infrared5, Inc. All rights reserved.
 * 
 *   The accompanying code comprising examples for use solely in conjunction with Red5 Pro (the "Example Code") 
 *   is  licensed  to  you  by  Infrared5  Inc.  in  consideration  of  your  agreement  to  the  following  
 *   license terms  and  conditions.  Access,  use,  modification,  or  redistribution  of  the  accompanying  
 *   code  constitutes your acceptance of the following license terms and conditions.
 * 
 *   Permission is hereby granted, free of charge, to you to use the Example Code and associated documentation 
 *   files (collectively, the "Software") without restriction, including without limitation the rights to use, 
 *   copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit 
 *   persons to whom the Software is furnished to do so, subject to the following conditions:
 * 
 *   The Software shall be used solely in conjunction with Red5 Pro. Red5 Pro is licensed under a separate end 
 *   user  license  agreement  (the  "EULA"),  which  must  be  executed  with  Infrared5,  Inc.   
 *   An  example  of  the EULA can be found on our website at: https://account.red5pro.com/assets/LICENSE.txt.
 * 
 *   The above copyright notice and this license shall be included in all copies or portions of the Software.
 * 
 *   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,  INCLUDING  BUT  
 *   NOT  LIMITED  TO  THE  WARRANTIES  OF  MERCHANTABILITY, FITNESS  FOR  A  PARTICULAR  PURPOSE  AND  
 *   NONINFRINGEMENT.   IN  NO  EVENT  SHALL INFRARED5, INC. BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, 
 *   WHETHER IN  AN  ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN CONNECTION 
 *   WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * 
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.red5prosdk=t():e.red5prosdk=t()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1)}([function(e,t,n){"use strict";function r(e){if(null==e)return e;if(Array.isArray(e))return e.slice();if("object"==typeof e){var t={};return Object.keys(e).forEach((function(n){t[n]=e[n]})),t}return e}var o=function(e){if(null===e)return"null";if("string"!=typeof e)return e.toString();for(var t=/%[sdj%]/g,n=1,r=arguments,o=r.length,i=String(e).replace(t,(function(e){if("%%"===e)return"%";if(n>=o)return e;switch(e){case"%s":return String(r[n++]);case"%d":return Number(r[n++]);case"%j":try{return JSON.stringify(r[n++])}catch(e){return"[Circular]"}break;default:return e}})),a=r[n];n<o;a=r[++n])i+=" "+a;return i};function i(){var e={},t=Error.stackTraceLimit,n=Error.prepareStackTrace;return Error.stackTraceLimit=3,Error.prepareStackTrace=function(t,n){var r=n[2];e.file=r.getFileName(),e.line=r.getLineNumber();var o=r.getFunctionName();o&&(e.func=o)},Error.stackTraceLimit=t,Error.prepareStackTrace=n,e}function a(e,t){if(t){if(c[t])return;c[t]=!0}console.error(e+"\n")}function s(e){return c[e]}var c={};function u(){}function l(){}u.prototype.write=function(e){e.level<f?console.log(e):e.level<h?console.info(e):e.level<p?console.warn(e):console.error(e),e.err&&e.err.stack&&console.error(e.err.stack)},l.prototype.write=function(e){var t;t=e.level<d?"color: DeepPink":e.level<f?"color: GoldenRod":e.level<h?"color: DarkTurquoise":e.level<p?"color: Purple":e.level<v?"color: Crimson":"color: Black";var n=e.childName?e.name+"/"+e.childName:e.name,r=y[e.level].toUpperCase();function o(e,t){return Array(t+1-(e+"").length).join("0")+e}r=Array(6-r.length).join(" ")+r,console.log("[%s:%s:%s:%s] %c%s%c: %s: %c%s",o(e.time.getHours(),2),o(e.time.getMinutes(),2),o(e.time.getSeconds(),2),o(e.time.getMilliseconds(),4),t,r,"color: DimGray",n,"color: SteelBlue",e.msg),e.err&&e.err.stack&&console.log("%c%s,",t,e.err.stack)};var d=20,f=30,h=40,p=50,v=60,m={trace:10,debug:d,info:f,warn:h,error:p,fatal:v},y={};function b(e){return"string"==typeof e?m[e.toLowerCase()]:e}function g(e,t,n){if(!(this instanceof g))return new g(e,t);var o,i,a,s;if(void 0!==t&&(o=e,e=t,!(o instanceof g)))throw new TypeError("invalid Logger creation: do not pass a second arg");if(!e)throw new TypeError("options (object) is required");if(o){if(e.name)throw new TypeError("invalid options.name: child cannot set logger name")}else if(!e.name)throw new TypeError("options.name (string) is required");if(e.stream&&e.streams)throw new TypeError('cannot mix "streams" and "stream" options');if(e.streams&&!Array.isArray(e.streams))throw new TypeError("invalid options.streams: must be an array");if(e.serializers&&("object"!=typeof e.serializers||Array.isArray(e.serializers)))throw new TypeError("invalid options.serializers: must be an object");if(o&&n){this._isSimpleChild=!0,this._level=o._level,this.streams=o.streams,this.serializers=o.serializers,this.src=o.src,i=this.fields={};var c=Object.keys(o.fields);for(s=0;s<c.length;s++)a=c[s],i[a]=o.fields[a];var l=Object.keys(e);for(s=0;s<l.length;s++)a=l[s],i[a]=e[a]}else{var d=this;if(o){for(this._level=o._level,this.streams=[],s=0;s<o.streams.length;s++){var f=r(o.streams[s]);f.closeOnExit=!1,this.streams.push(f)}this.serializers=r(o.serializers),this.src=o.src,this.fields=r(o.fields),e.level&&this.level(e.level)}else this._level=Number.POSITIVE_INFINITY,this.streams=[],this.serializers=null,this.src=!1,this.fields={};e.stream?d.addStream({type:"stream",stream:e.stream,closeOnExit:!1,level:e.level}):e.streams?e.streams.forEach((function(t){d.addStream(t,e.level)})):o&&e.level?this.level(e.level):o||d.addStream({type:"raw",stream:new u,closeOnExit:!1,level:e.level}),e.serializers&&d.addSerializers(e.serializers),e.src&&(this.src=!0),delete(i=r(e)).stream,delete i.level,delete i.streams,delete i.serializers,delete i.src,this.serializers&&this._applySerializers(i),Object.keys(i).forEach((function(e){d.fields[e]=i[e]}))}}function _(e){return function(){var t=this;function n(n){var a;n[0]instanceof Error?(c={err:t.serializers&&t.serializers.err?t.serializers.err(n[0]):g.stdSerializers.err(n[0])},a={err:!0},u=1===n.length?[c.err.message]:Array.prototype.slice.call(n,1)):"object"!=typeof n[0]&&null!==n[0]||Array.isArray(n[0])?(c=null,u=Array.prototype.slice.call(n)):(c=n[0],u=Array.prototype.slice.call(n,1));var s=r(t.fields);s.level=e;var l=c?r(c):null;return l&&(t.serializers&&t._applySerializers(l,a),Object.keys(l).forEach((function(e){s[e]=l[e]}))),s.levelName=y[e],s.msg=o.apply(t,u),s.time||(s.time=new Date),t.src&&!s.src&&(s.src=i()),s.v=0,s}var c=null,u=arguments,l=null;if(this._emit){if(0===arguments.length)return this._level<=e;this._level>e||(l=n(u),this._emit(l))}else{var d="unbound";if(!s[d]){var f=i();a(o("bunyan usage error: %s:%s: attempt to log with an unbound log method: `this` is: %s",f.file,f.line,this.toString()),d)}}}}function w(e){var t=e.stack||e.toString();if(e.cause&&"function"==typeof e.cause){var n=e.cause();n&&(t+="\nCaused by: "+w(n))}return t}function S(){var e=[];return function(t,n){return n&&"object"==typeof n?-1!==e.indexOf(n)?"[Circular]":(e.push(n),n):n}}Object.keys(m).forEach((function(e){y[m[e]]=e})),g.prototype.addStream=function(e,t){switch(null==t&&(t=f),!(e=r(e)).type&&e.stream&&(e.type="raw"),e.raw="raw"===e.type,e.level?e.level=b(e.level):e.level=b(t),e.level<this._level&&(this._level=e.level),e.type){case"stream":case"raw":e.closeOnExit||(e.closeOnExit=!1);break;default:throw new TypeError('unknown stream type "'+e.type+'"')}this.streams.push(e),delete this.haveNonRawStreams},g.prototype.addSerializers=function(e){var t=this;t.serializers||(t.serializers={}),Object.keys(e).forEach((function(n){var r=e[n];if("function"!=typeof r)throw new TypeError(o('invalid serializer for "%s" field: must be a function',n));t.serializers[n]=r}))},g.prototype.child=function(e,t){return new this.constructor(this,e||{},t)},g.prototype.level=function(e){if(void 0===e)return this._level;for(var t=b(e),n=this.streams.length,r=0;r<n;r++)this.streams[r].level=t;this._level=t},g.prototype.levels=function(e,t){if(void 0===e)return this.streams.map((function(e){return e.level}));var n;if("number"==typeof e){if(void 0===(n=this.streams[e]))throw new Error("invalid stream index: "+e)}else{for(var r=this.streams.length,i=0;i<r;i++){var a=this.streams[i];if(a.name===e){n=a;break}}if(!n)throw new Error(o('no stream with name "%s"',e))}if(void 0===t)return n.level;var s=b(t);n.level=s,s<this._level&&(this._level=s)},g.prototype._applySerializers=function(e,t){var n=this;Object.keys(this.serializers).forEach((function(r){if(!(void 0===e[r]||t&&t[r]))try{e[r]=n.serializers[r](e[r])}catch(t){a(o('bunyan: ERROR: Exception thrown from the "%s" Bunyan serializer. This should never happen. This is a bugin that serializer function.\n%s',r,t.stack||t)),e[r]=o('(Error in Bunyan log "%s" serializer broke field. See stderr for details.)',r)}}))},g.prototype._emit=function(e,t){var n,r;if(void 0===this.haveNonRawStreams)for(this.haveNonRawStreams=!1,n=0;n<this.streams.length;n++)if(!this.streams[n].raw){this.haveNonRawStreams=!0;break}if(t||this.haveNonRawStreams)try{r=JSON.stringify(e,S())+"\n"}catch(t){var i=t.stack.split(/\n/g,2).join("\n");a('bunyan: ERROR: Exception in `JSON.stringify(rec)`. You can install the "safe-json-stringify" module to have Bunyan fallback to safer stringification. Record:\n'+function(e,t){return t||(t="    "),t+e.split(/\r?\n/g).join("\n"+t)}(o("%s\n%s",e,t.stack)),i),r=o("(Exception in JSON.stringify(rec): %j. See stderr for details.)\n",t.message)}if(t)return r;var s=e.level;for(n=0;n<this.streams.length;n++){var c=this.streams[n];c.level<=s&&c.stream.write(c.raw?e:r)}return r},g.prototype.trace=_(10),g.prototype.debug=_(d),g.prototype.info=_(f),g.prototype.warn=_(h),g.prototype.error=_(p),g.prototype.fatal=_(v),g.stdSerializers={},g.stdSerializers.err=function(e){return e&&e.stack?{message:e.message,name:e.name,stack:w(e),code:e.code,signal:e.signal}:e},e.exports=g,e.exports.TRACE=10,e.exports.DEBUG=d,e.exports.INFO=f,e.exports.WARN=h,e.exports.ERROR=p,e.exports.FATAL=v,e.exports.resolveLevel=b,e.exports.levelFromName=m,e.exports.nameFromLevel=y,e.exports.VERSION="0.2.3",e.exports.LOG_VERSION=0,e.exports.createLogger=function(e){return new g(e)},e.exports.safeCycles=S,e.exports.ConsoleFormattedStream=l,e.exports.ConsoleRawStream=u},function(e,t,n){e.exports=n(2)},function(e,t,n){"use strict";n.r(t),n.d(t,"RTCConferenceParticipant",(function(){return Xc})),n.d(t,"Red5ProSubscriber",(function(){return Qc})),n.d(t,"RTCSubscriber",(function(){return $c})),n.d(t,"WHEPClient",(function(){return Zc})),n.d(t,"RTMPSubscriber",(function(){return eu})),n.d(t,"HLSSubscriber",(function(){return tu})),n.d(t,"PlaybackView",(function(){return Wr})),n.d(t,"Red5ProPublisher",(function(){return nu})),n.d(t,"RTCPublisher",(function(){return ru})),n.d(t,"WHIPClient",(function(){return ou})),n.d(t,"RTMPPublisher",(function(){return iu})),n.d(t,"PublisherView",(function(){return ya})),n.d(t,"PlaybackControls",(function(){return On})),n.d(t,"Red5ProSharedObject",(function(){return au})),n.d(t,"Red5ProSharedObjectSocket",(function(){return su})),n.d(t,"Red5ProSharedObjectPeerConnection",(function(){return cu})),n.d(t,"PublisherEventTypes",(function(){return uu})),n.d(t,"RTCPublisherEventTypes",(function(){return lu})),n.d(t,"FailoverPublisherEventTypes",(function(){return du})),n.d(t,"SubscriberEventTypes",(function(){return fu})),n.d(t,"RTCSubscriberEventTypes",(function(){return hu})),n.d(t,"RTMPSubscriberEventTypes",(function(){return pu})),n.d(t,"FailoverSubscriberEventTypes",(function(){return vu})),n.d(t,"SharedObjectEventTypes",(function(){return mu})),n.d(t,"MessageTransportStateEventTypes",(function(){return yu})),n.d(t,"RTCConferenceParticipantEventType",(function(){return bu})),n.d(t,"PlaybackAudioEncoder",(function(){return gu})),n.d(t,"PlaybackVideoEncoder",(function(){return _u})),n.d(t,"IceTransportTypes",(function(){return wu})),n.d(t,"TransformFrameTypes",(function(){return Su})),n.d(t,"setLogLevel",(function(){return Eu})),n.d(t,"getRecordedLogs",(function(){return f})),n.d(t,"LOG_LEVELS",(function(){return Cu})),n.d(t,"getLogger",(function(){return Ou}));var r=n(0);function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,a=void 0,a=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===o(a)?a:String(a)),r)}var i,a}var a,s,c=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,o;return t=e,(n=[{key:"write",value:function(e){console.log("%s - [%s] %s: %s",e.time.toISOString(),e.name,r.nameFromLevel[e.level],e.msg)}}])&&i(t.prototype,n),o&&i(t,o),Object.defineProperty(t,"prototype",{writable:!1}),e}(),u=function(e){return function(t,n){var r;a[e]((r=t,function(e){return"(".concat(r,") ").concat(e)})(n))}},l={TRACE:"trace",INFO:"info",DEBUG:"debug",WARN:"warn",ERROR:"error",FATAL:"fatal"},d=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,o=[];if(o.push({level:e,stream:new c,type:"raw"}),n){var i=n.map((function(t){t.level=e}));o=o.concat(i)}t&&(s=[],o.push({level:e,stream:{write:function(e){var t="[".concat(e.time.toISOString(),"] ").concat(r.nameFromLevel[e.level],": ").concat(e.msg);s.push(t)}}})),a=Object(r.createLogger)({level:e,name:"red5pro-sdk",streams:o})},f=function(){return s},h=(u(l.TRACE),u(l.INFO)),p=u(l.DEBUG),v=u(l.WARN),m=u(l.ERROR);u(l.FATAL);function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function b(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==y(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==y(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===y(i)?i:String(i)),r)}var o,i}function g(e,t,n){return t&&b(e.prototype,t),n&&b(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var _=g((function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.resolve=void 0,this.reject=void 0,this.promise=new Promise((function(e,n){t.resolve=e,t.reject=n}))})),w={createIfNotExist:function(e){var t=e;return t||(t=new _),t}},S=_,E=w;function C(e){return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return k(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return k(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function P(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */P=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new k(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(T([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==C(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return R()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function T(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:R}}function R(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=T,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:T(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function T(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==C(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==C(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===C(i)?i:String(i)),r)}var o,i}var R=new WeakMap,j=function(e){return R.has(e)||R.set(e,{}),R.get(e)},A=function(){function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.listorder=P().mark((function e(t){return P().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t.length>0)){e.next=5;break}return e.next=3,t.shift();case 3:e.next=0;break;case 5:case"end":return e.stop()}}),e)})),j(this).find=function(e,n,r,o){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,s=j(t).next(e,n),c=O(s,2),u=c[0],l=c[1];if(l){var d=r[u];d=d||r,(i?(new l)[i](d):new l(d)).then((function(e){o.resolve(e)})).catch((function(s){a=s,j(t).find(e,n,r,o,i,a)}))}else o.reject(a)},j(this).next=function(e,t){var n,r,o=e.next();return o.done||(r=o.value,n=t.get(r)),[r,n]}}var t,n,r;return t=e,(n=[{key:"create",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=new S;return j(this).find(this.listorder(e.slice()),t,n,o,r),o.promise}}])&&T(t.prototype,n),r&&T(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function L(e){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function N(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==L(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==L(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===L(i)?i:String(i)),r)}var o,i}var H=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._callbacks={},this._callbacks.RED5PRO=[]}var t,n,r;return t=e,(n=[{key:"_notify",value:function(e,t){var n,r=e.length;for(n=0;n<r;n++)e[n](t)}},{key:"on",value:function(e,t){"function"==typeof t&&("*"!==e?(void 0===this._callbacks[e]&&(this._callbacks[e]=[]),-1===this._callbacks[e].indexOf(t)&&this._callbacks[e].push(t)):this._callbacks.RED5PRO.push(t))}},{key:"off",value:function(e,t){var n=this._callbacks[e];if("*"===e&&(n=this._callbacks.RED5PRO),n){var r=n.indexOf(t);-1!==r&&n.splice(r,1)}}},{key:"trigger",value:function(e){var t=e.type;this._callbacks[t]&&this._notify(this._callbacks[t],e),this._notify(this._callbacks.RED5PRO,e)}}])&&N(t.prototype,n),r&&N(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function I(e){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function x(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==I(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==I(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===I(i)?i:String(i)),r)}var o,i}function D(e,t,n){return t&&x(e.prototype,t),n&&x(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function M(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var F=D((function e(t){M(this,e),this.name="NoElementFound",this.message=t})),U=D((function e(t){M(this,e),this.name="InvalidNameError",this.message=t})),B=/['"](.*?)['"]:/gi,V=/:['"](.*?)['"]/gi,G=new WeakMap;function W(e,t){try{return JSON.parse(e)[t]}catch(o){var n,r=B.exec(e);return r&&r.length>1&&(n=V.exec(e),r[1]===t&&n&&n.length>1)?n[1]:void 0}}var Y=function(e){var t="function"==typeof e.textTracks?e.textTracks():e.textTracks;t&&(e.addTextTrack("metadata"),t.addEventListener("addtrack",(function(t){var n=t.track;n.mode="hidden",n.addEventListener("cuechange",(function(t){var r,o;r=(r=t&&t.currentTarget?t.currentTarget.cues:(r=n.cues)&&r.length>0?r:n.activeCues)||[];var i=function(){var t=r[o];if(t.value){var n="string"==typeof t.value.data?t.value.data:function(e,t,n){var r="",o=t,i=t+n;do{r+=String.fromCharCode(e[o++])}while(o<i);return r}(t.value.data,0,t.size),i=function(e){var t=W(e,"orientation");if(t)return{orientation:parseInt(t)}}(n),a=function(e){var t=W(e,"streamingMode");if(t)return{streamingMode:t}}(n),s=function(e){return G.get(e)}(e);i&&s&&s.orientation&&s.orientation.forEach((function(e){e(i)})),a&&s&&s.streamingMode&&s.streamingMode.forEach((function(e){e(a)}))}};for(o=0;o<r.length;o++)i()}))})))};function z(e){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var J=[],K=[],q=!1;var X=[];function Q(e){var t,n=screen.orientation?screen.orientation.angle:void 0,r=void 0===n?window.matchMedia("(orientation: portrait)").matches?0:90:n,o=X.length;for(p("[window:onorientationchange]","orientation(".concat(r,").")),t=0;t<o;t++)X[t]((r+90)%360)}var $=[],Z=!1;function ee(e){var t,n=$.length;for(t=0;t<n;t++)$[t]();Z=!0}var te=function(){var e="0,0,0";try{e=new window.ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version").replace(/\D+/g,",").match(/^,?(.+),?$/)[1]}catch(t){try{navigator.mimeTypes["application/x-shockwave-flash"].enabledPlugin&&(e=(navigator.plugins["Shockwave Flash 2.0"]||navigator.plugins["Shockwave Flash"]).description.replace(/\D+/g,",").match(/^,?(.+),?$/)[1])}catch(e){}}return e.split(",")},ne=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||function(e){return window.setTimeout(e,1e3)},re=window.adapter,oe=!!navigator.mozGetUserMedia,ie=!!document.documentMode,ae=re?"edge"===window.adapter.browserDetails.browser.toLowerCase():!ie&&!!window.StyleMedia,se="ontouchstart"in window||window.DocumentTouch&&window.document instanceof window.DocumentTouch;re||(navigator.getUserMedia=navigator.mediaDevices&&navigator.mediaDevices.getUserMedia||navigator.getUserMedia||navigator.mozGetUserMedia||navigator.webkitGetUserMedia||navigator.msGetUserMedia);var ce={requestFrame:ne,getIsMoz:function(){return oe},getIsEdge:function(){return ae},isTouchEnabled:function(){return se},supportsWebSocket:function(){return!!window.WebSocket},supportsHLS:function(){var e=document.createElement("video");return e.canPlayType("application/vnd.apple.mpegURL").length>0||e.canPlayType("application/x-mpegURL").length>0||e.canPlayType("audio/mpegurl").length>0||e.canPlayType("audio/x-mpegurl").length>0},supportsNonNativeHLS:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;if(e)try{return e.isSupported()}catch(e){return v("Could not access Hls.js."),!1}return!!window.Hls&&window.Hls.isSupported()},createHLSClient:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new window.Hls(e)},getHLSClientEventEnum:function(){return window.Hls.Events},supportsFlashVersion:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:".";return te()[0]>=e.split(t)[0]},resolveElement:function(e){try{var t=document.getElementById(e);if(!t)throw new F("Element with id(".concat(e,") could not be found."));return t}catch(t){throw new F("Error in accessing element with id(".concat(e,"). ").concat(t.message))}},createWebSocket:function(e){return new WebSocket(e)},setVideoSource:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{e.srcObject=t}catch(n){v("[setVideoSource:obj]","Could not set srcObject: ".concat(n.message)),oe?e.mozSrcObject=t:e.src=window.URL.createObjectURL(t)}if(n)try{var r=e.play();r&&r.then((function(){return p("[setVideoSource:action]","play (START)")})).catch((function(e){return v("[setVideoSource:action]","play (FAULT) "+(e.message?e.message:e))}))}catch(t){v("[setVideoSource:action]","play (CATCH::FAULT) "+t.message);try{e.setAttribute("autoplay",!1),e.pause()}catch(e){v("[setVideoSource:action]","pause (CATCH::FAULT) "+e.message)}}else try{e.setAttribute("autoplay",!1),e.pause()}catch(e){}},injectScript:function(e){var t=new S,n=document.createElement("script");return n.type="text/javascript",n.onload=function(){t.resolve()},n.onreadystatechange=function(){"loaded"!==n.readyState&&"complete"!==n.readyState||(n.onreadystatechange=null,t.resolve())},n.src=e,document.getElementsByTagName("head")[0].appendChild(n),t.promise},gUM:function(e){return(navigator.mediaDevices||navigator).getUserMedia(e)},setGlobal:function(e,t){window[e]=t},getSwfObject:function(){return window.swfobject},getEmbedObject:function(e){return document.getElementById(e)},getElementId:function(e){return e.getAttribute("id")},addOrientationChangeHandler:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n="onorientationchange"in window;n&&(p("[window:orientation]","[addOrientationChangeHandler]","adding responder."),X.push(e),t&&Q()),1===X.length&&(p("[window:orientation]","[addOrientationChangeHandler]","onorientationchange added."),window.addEventListener("orientationchange",Q))},removeOrientationChangeHandler:function(e){for(var t=X.length;--t>-1;)if(X[t]===e){X.slice(t,1);break}0===X.length&&(p("[window:orientation]","[removeOrientationChangeHandler]:: onorientationchange removed."),window.removeEventListener("onorientationchange",Q))},addCloseHandler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;$.splice(-1===t?$.length:t,0,e),Z||window.addEventListener("unload",ee)},removeCloseHandler:function(e){for(var t=$.length;--t>-1;)if($[t]===e){$.slice(t,1);break}},invoke:function(e,t){window.hasOwnProperty(e)&&window[e].call(window,t)},toggleFullScreen:function(e){window.screenfull&&window.screenfull.enabled&&window.screenfull.toggle(e)},onFullScreenStateChange:function(e){K.push(e),window.screenfull,!q&&window.screenfull&&window.screenfull.enabled&&(q=!0,window.screenfull.onchange((function(){var e,t=K.length;for(e=0;e<t;e++)K[e](window.screenfull.isFullscreen)})))},onOrientationMetadata:function(e,t){var n=G.get(e);G.has(e)?n.hasOwnProperty("orientation")||(G.get(e).orientation=[]):(Y(e),G.set(e,{orientation:[]})),G.get(e).orientation.push(t)},onStreamingModeMetadata:function(e,t){var n=G.get(e);G.has(e)?n.hasOwnProperty("streamingMode")||(G.get(e).streamingMode=[]):(Y(e),G.set(e,{streamingMode:[]})),G.get(e).streamingMode.push(t)},hasAttributeDefined:function(e,t){var n=e.getAttribute(t);return null!==n&&void 0!==z(n)&&"string"==typeof n&&(""===n||"true"===n||n===t)},hasClassDefined:function(e,t){return e.classList.contains(t)},createElement:function(e,t){return"text"===e?document.createTextNode(t.toString()):document.createElement(e,t)},addSubscriptionAssignmentHandler:function(e){J.push(e),void 0===window.setSubscriberId&&(window.setSubscriberId=function(e){J.shift()(e)})},getMouseXFromEvent:function(e){return e||(e=window.event),e.pageX?e.pageX:e.clientX?e.clientX+document.body.scrollLeft+document.documentElement.scrollLeft:0},getScrollX:function(){return void 0!==window.pageXOffset?window.pageXOffset:(document.documentElement||document.body.parentNode||document.body).scrollLeft},createEvent:function(e){return document.createEvent(e)},getGlobal:function(){return window}},ue=ce.requestFrame,le=["webkitTransformOrigin","mozTransformOrigin","msTransformOrigin","oTransformOrigin","transformOrigin"],de=["webkitTransform","mozTransform","msTransform","oTransform","transform"],fe=["webkitTransition","mozTransition","msTransition","oTransition","transition"],he={0:{origin:"center center",transform:"rotate(0deg)"},90:{origin:"left top",transform:"rotate(90deg) translateY(-100%)"},180:{origin:"center center",transform:"rotate(180deg)"},270:{origin:"top left",transform:"rotate(270deg) translateX(-100%) translateY(0%)"},"-90":{origin:"left top",transform:"rotate(-90deg) translateX(-100%)"},"-180":{origin:"center center",transform:"rotate(-180deg)"},"-270":{origin:"top left",transform:"rotate(-270deg) translateY(-100%)"}},pe=function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){var o=t.parentNode;if(o){var i=o.clientWidth,a=o.clientHeight;t.style.width=r?a+"px":i+"px";var s=t.clientWidth,c=t.clientHeight,u=.5*(r?i-c:i-s);t.style.position="relative",t.style.left=u+"px"}n&&n(e(t,n,r))}},ve=function(e,t,n){var r,o=de.length,i=(t%=360)%180!=0,a=e.parentNode,s=e.width?e.width:a.clientWidth,c=e.height?e.height:a.clientHeight,u=he[t.toString()];for(r=0;r<o;r++)e.style[le[r]]=u.origin,e.style[de[r]]=u.transform,e.style[fe[r]]="transform 0.0s linear";i?(e.style.width=c+"px",a.style.height=c+"px"):(e.style.width=s+"px",e.style.height=c+"px",a.style.height="unset"),document.attachEvent?a.attachEvent("resize",pe(e,ue,i)):pe(e,ue,i)()},me=function(e){var t=e.length;return function n(){var r=Array.prototype.slice.call(arguments,0);return r.length>=t?e.apply(null,r):function(){var e=Array.prototype.slice.call(arguments,0);return n.apply(null,r.concat(e))}}},ye=me((function(e,t){for(var n=0,r=t.length,o=[];n<r;)e(t[n])&&(o[o.length]=t[n]),n+=1;return o})),be=function(e){return"string"==typeof e?parseInt(e,10):Math.round(e)},ge=function(e){var t=JSON.stringify(e);return JSON.parse(t)},_e=function(e){if("string"==typeof e){var t=e.split(",");return{width:parseInt(t[0]),height:parseInt(t[1])}}return e},we=function(e){return 0===Object.keys(e).length&&e.constructor===Object};function Se(e){return(Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ee(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Ee=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==Se(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function Ce(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function Oe(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){Ce(i,r,o,a,s,"next",e)}function s(e){Ce(i,r,o,a,s,"throw",e)}a(void 0)}))}}var ke,Pe=[{label:"4K(UHD)",width:3840,height:2160},{label:"1080p(FHD)",width:1920,height:1080},{label:"UXGA",width:1600,height:1200},{label:"720p(HD)",width:1280,height:720},{label:"SVGA",width:800,height:600},{label:"VGA",width:640,height:480},{label:"360p(nHD)",width:640,height:360},{label:"CIF",width:352,height:288},{label:"QVGA",width:320,height:240},{label:"QCIF",width:176,height:144},{label:"QQVGA",width:160,height:120}],Te=function(e){return e.exact||e.ideal||e.max||e.min||e},Re=me((function(e,t){if("boolean"==typeof e.video)return!0;var n=e.video.hasOwnProperty("width")?Te(e.video.width):0,r=e.video.hasOwnProperty("height")?Te(e.video.height):0,o=n===t.width&&r===t.height;return o&&p("[gum:isExact]","Found matching resolution for ".concat(t.width,", ").concat(t.height,".")),o})),je=me((function(e,t){var n=(e.video.hasOwnProperty("width")?Te(e.video.width):0)*(e.video.hasOwnProperty("height")?Te(e.video.height):0);return t.width*t.height<n})),Ae=me((function(e,t){var n=ye(Re(t))(e);return p("[gum:hasMatchingFormat]","Filtered list: "+JSON.stringify(n,null,2)),n.length>0})),Le=me((function(e,t){var n=je(t);return ye(n)(e)})),Ne=function e(t,n,r){if(0!=n.length){var o=n.shift();t.video.width={exact:o.width},t.video.height={exact:o.height},ce.gUM(t).then((function(e){r.resolve({media:e,constraints:t})})).catch((function(o){var i="string"==typeof o?o:[o.name,o.message].join(": ");p("[gum:getUserMedia]","Failure in getUserMedia: ".concat(i,". Attempting other resolution tests...")),p("[gUM:findformat]","Constraints declined by browser: ".concat(JSON.stringify(t,null,2))),e(t,n,r)}))}else!function(e,t){e.video=!0,ce.gUM(e).then((function(n){t.resolve({media:n,constraints:e})})).catch((function(n){var r="string"==typeof n?n:[n.name,n.message].join(": ");p("[gum:getUserMedia]","Failure in getUserMedia: ".concat(r,". Attempting other resolution tests...")),p("[gUM:findformat]","Constraints declined by browser: ".concat(JSON.stringify(e,null,2))),t.reject("Could not find proper camera for provided constraints.")}))}(t,r)},He=function(){return at&&st&&ct},Ie=function(){try{var e=new at(null);return e.createDataChannel({name:"test"}).close(),e.close(),!!He()}catch(e){return p("Could not detect RTCDataChannel support: ".concat(e.message)),!1}},xe=(window.RTCRtpScriptTransform,!!window.MediaStreamTrackGenerator),De=(window.MediaStreamTrackProcessor,function(){return!!ut.prototype.createEncodedStreams}),Me=function(e,t,n,r){var o=new dt(t),i=new ft(t.kind),a=o.readable,s=i.writable;return n.postMessage({type:e,readable:a,writable:s,options:r},[a,s]),{processor:o,generator:i}},Fe=function(e,t,n,r){var o=t.createEncodedStreams(),i=o.readable,a=o.writable;return n.postMessage({type:e,readable:i,writable:a,options:r},[i,a]),{readable:i,writable:a}},Ue=function(e,t,n,r){var o=t.createEncodedStreams(),i=o.readable,a=o.writable;return n.postMessage({type:e,readable:i,writable:a,options:r},[i,a]),{readable:i,writable:a}},Be=function(){var e=Oe(Ee().mark((function e(t,n,r){var o,i,a;return Ee().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=new dt(t),i=new ft(t.kind),a=new lt({transform:n}),o.readable.pipeThrough(a,r).pipeTo(i.writable).catch((function(e){if(r){var t=r.signal;t&&t.abort(e)}m("[PIPE:pipeGeneratorTransform]",e.message)})),e.abrupt("return",{processor:o,generator:i});case 6:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}(),Ve=function(){var e=Oe(Ee().mark((function e(t,n,r){var o,i,a,s;return Ee().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=new lt({transform:n}),i=t.createEncodedStreams(),a=i.readable,s=i.writable,a.pipeThrough(o,r).pipeTo(s).catch((function(e){if(a.cancel(e),r){var t=r.signal;t&&t.abort(e)}m("[PIPE:pipeSenderTransform]",e.message)})),e.abrupt("return",{readable:a,writable:s});case 6:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}(),Ge=function(){var e=Oe(Ee().mark((function e(t,n,r){var o,i,a,s;return Ee().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=new lt({transform:n}),i=t.createEncodedStreams(),a=i.readable,s=i.writable,a.pipeThrough(o,r).pipeTo(s).catch((function(e){if(a.cancel(e),r){var t=r.signal;t&&t.abort(e)}m("[PIPE:pipeReceiverTransform",e.message)})),e.abrupt("return",{readable:a,writable:s});case 6:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}(),We=function(e,t){var n=new S,r=Ae(Pe);p("[gum:getUserMedia]","Is Available in format listing: "+r(e));var o=function(r){if(r){var o="string"==typeof r?r:[r.name,r.message].join(": ");p("[gum:getUserMedia]","Failure in getUserMedia: ".concat(o,". Attempting other resolution tests..."))}(function(e){p("[gum:determineSupportedResolution]","Determine next neighbor based on constraints: "+JSON.stringify(e,null,2));var t=new S,n=Le(Pe)(e),r=ge(e);return Ne(r,n,t),t.promise})(e).then((function(e){n.resolve({media:e.media,constraints:e.constraints})})).catch((function(r){t&&t(e),n.reject({error:r,constraints:e})}))};if(function(e){return e.hasOwnProperty("video")&&(e.video.hasOwnProperty("width")||e.video.hasOwnProperty("height"))}(e))if(r(e)){p("[gum:getUserMedia]","Found constraints in list. Checking quick support for faster setup with: "+JSON.stringify(e,null,2));var i=function(e){var t=ge(e);return"boolean"==typeof e.video||(e.video.width&&(t.video.width={exact:Te(e.video.width)}),e.video.height&&(t.video.height={exact:Te(e.video.height)})),t}(e);ce.gUM(i).then((function(e){n.resolve({media:e,constraints:i})})).catch(o)}else p("[gum:getUserMedia]","Could not find contraints in list. Attempting failover..."),t&&t(e),o();else p("[gum:getUserMedia]","Constraints were not defined properly. Attempting failover..."),ce.gUM(e).then((function(t){n.resolve({media:t,constraints:e})})).catch(o);return n.promise},Ye=function(e,t){for(var n=e.split("\r\n"),r=n.length;--r>-1;)t.indexOf(n[r])>-1&&n.splice(r,1);return n.join("\r\n")},ze=function(e){return Ye(e,["a=ice-options:trickle"])},Je=function(e){var t="a=ice-options:trickle";if(e.indexOf(t)>-1)return e;var n=e.split("\r\n"),r=n.map((function(e,t){return e.match(/^a=ice-ufrag:(.*)/)?t:-1})).filter((function(e){return e>-1})),o=r.length>0?r[r.length-1]:-1;return o>-1&&n.splice(o+1,0,t),n.join("\r\n")},Ke=function(e){var t="a=end-of-candidates";if(e.indexOf(t)>-1)return e;var n=e.split("\r\n"),r=n.map((function(e,t){return e.match(/^a=candidate:(.*)/)?t:-1})).filter((function(e){return e>-1})),o=r.length>0?r[r.length-1]:-1;return o>-1&&n.splice(o+1,0,t),n.join("\r\n")},qe=function(e){for(var t=/^a=extmap/,n=e.split("\r\n"),r=n.length;--r>-1;)t.exec(n[r])&&n.splice(r,1);return n.join("\r\n")},Xe=(ke=[],["rtpmap:(\\d{1,}) ISAC","rtpmap:(\\d{1,}) G722","rtpmap:(\\d{1,}) CN","rtpmap:(\\d{1,}) PCMU","rtpmap:(\\d{1,}) PCMA","rtpmap:(\\d{1,}) telephone-event"].forEach((function(e){return ke.push(new RegExp("a=(".concat(e,")"),"g"))})),ke),Qe=function(e){for(var t,n,r,o,i,a=e.split("\r\n"),s=a.length,c=[];--s>-1;)for(t=0;t<Xe.length;t++)if((n=Xe[t]).lastIndex=0,r=n.exec(a[s])){i=r[r.length-1],o="^a=(rtcp-fb|fmtp):".concat(i," .*"),-1===c.indexOf(o)&&c.push(o),a.splice(s,1);break}for(n&&(n.lastIndex=0),s=a.length;--s>-1;)for(t=0;t<c.length;t++)if((n=new RegExp(c[t],"g")).lastIndex=0,n.exec(a[s])){a.splice(s,1);break}return a.join("\r\n")},$e=function(e){var t=/^video/g;return e.split("m=").map((function(e){return e.match(t)&&-1!==e.indexOf("a=sendrecv")?function(e){for(var t,n=/^a=((rtpmap|rtcp-fb|fmtp):(\d{1,}).*)/gi,r=/^a=fmtp:(\d{1,}).*profile-level-id=\b42/gi,o=[],i=e.split("\r\n"),a=i.length,s=0;--a>-1;)r.lastIndex=0,(t=r.exec(i[a]))&&-1===o.indexOf(t[t.length-1])&&o.push(t[t.length-1]);for(a=i.length;--a>-1;)for(s=0;s<o.length;s++)if(n.lastIndex=0,(t=n.exec(i[a]))&&-1===o.indexOf(t[t.length-1])){i.splice(a,1);break}return i.join("\r\n")}(e):e})).join("m=")},Ze=/a=ssrc/gi,et=function(e,t,n){var r=e.split(t),o=0;return r.map((function(e){if(e.match(n)&&++o>1){for(var t=e.replace("a=sendrecv","a=recvonly").split("\r\n"),r=t.length;--r>-1;)Ze.lastIndex=0,Ze.exec(t[r])&&t.splice(r,1);return t.join("\r\n")}return e})).join(t)},tt=function(e){return et(e,"m=",/^audio/g)},nt=function(e){return et(e,"m=",/^video/g)},rt=function(e,t){var n,r,o,i=t.indexOf("m=audio"),a=t.indexOf("m=video"),s=t.indexOf("m=application");return i>-1&&e.audio&&(n=t.indexOf("\r\n",i),r=t.slice(0,n),o=t.slice(n+"\r\n".length,t.length),a=(t=[r,"b=AS:"+e.audio,o].join("\r\n")).indexOf("m=video"),s=t.indexOf("m=application")),a>-1&&e.video&&(n=t.indexOf("\r\n",a),r=t.slice(0,n),o=t.slice(n+"\r\n".length,t.length),s=(t=[r,"b=AS:"+e.video,o].join("\r\n")).indexOf("m=application")),s>-1&&e.dataChannel&&(n=t.indexOf("\r\n",s),r=t.slice(0,n),o=t.slice(n+"\r\n".length,t.length),t=[r,"b=AS:"+e.dataChannel,o].join("\r\n")),t},ot=function(e){return e.includes("stereo=1")?e:e.replace("useinbandfec=1","useinbandfec=1;stereo=1;sprop-stereo=1")},it=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o="a=end-of-candidates",i=/^a=candidate:/,a=/^a=ice-ufrag:/,s=/^a=ice-pwd:/,c=/^m=(audio|video|application)\ /,u=e.split("\r\n"),l="",d="",f="a=mid:0",h=[];u.forEach((function(e){!t&&c.exec(e)?t=e:a.exec(e)?l=e:s.exec(e)?d=e:i.exec(e)&&(n&&-1!=e.indexOf(n)?h.push(e):n||h.push(e))})),r&&h[h.length-1]!==o&&h.push(o);var p=[l,d,t,f].concat(h);return p.join("\r\n")},at=window.RTCPeerConnection||window.mozRTCPeerConnection||window.webkitRTCPeerConnection,st=window.RTCIceCandidate||window.mozRTCIceCandidate||window.webkitRTCIceCandidate,ct=window.RTCSessionDescription||window.mozRTCSessionDescription||window.webkitRTCSessionDescription,ut=window.RTCRtpSender||window.RTCRtpSender||window.RTCRtpSender,lt=window.TransformStream,dt=window.MediaStreamTrackProcessor,ft=window.MediaStreamTrackGenerator,ht=at,pt=st,vt=ct,mt=function(){return ce.supportsWebSocket()},yt=Object.freeze({CONNECT_SUCCESS:"Connect.Success",CONNECT_FAILURE:"Connect.Failure",PUBLISH_START:"Publish.Start",PUBLISH_FAIL:"Publish.Fail",PUBLISH_INVALID_NAME:"Publish.InvalidName",UNPUBLISH_SUCCESS:"Unpublish.Success",PUBLISH_METADATA:"Publish.Metadata",PUBLISH_STATUS:"Publish.Status",PUBLISH_AVAILABLE:"Publish.Available",PUBLISH_INSUFFICIENT_BANDWIDTH:"Publish.InsufficientBW",PUBLISH_SUFFICIENT_BANDWIDTH:"Publish.SufficientBW",PUBLISH_RECOVERING_BANDWIDTH:"Publish.RecoveringBW",PUBLISH_SEND_INVOKE:"Publish.Send.Invoke",CONNECTION_CLOSED:"Publisher.Connection.Closed",DIMENSION_CHANGE:"Publisher.Video.DimensionChange"}),bt=Object.freeze({PUBLISHER_REJECT:"Publisher.Reject",PUBLISHER_ACCEPT:"Publisher.Accept"}),gt=Object.freeze({CONSTRAINTS_ACCEPTED:"WebRTC.MediaConstraints.Accepted",CONSTRAINTS_REJECTED:"WebRTC.MediaConstraints.Rejected",MEDIA_STREAM_AVAILABLE:"WebRTC.MediaStream.Available",PEER_CONNECTION_AVAILABLE:"WebRTC.PeerConnection.Available",OFFER_START:"WebRTC.Offer.Start",OFFER_END:"WebRTC.Offer.End",PEER_CANDIDATE_END:"WebRTC.PeerConnection.CandidateEnd",ICE_TRICKLE_COMPLETE:"WebRTC.IceTrickle.Complete",SOCKET_MESSAGE:"WebRTC.Socket.Message",DATA_CHANNEL_OPEN:"WebRTC.DataChannel.Open",DATA_CHANNEL_AVAILABLE:"WebRTC.DataChannel.Available",DATA_CHANNEL_CLOSE:"WebRTC.DataChannel.Close",DATA_CHANNEL_MESSAGE:"WebRTC.DataChannel.Message",DATA_CHANNEL_ERROR:"WebRTC.DataChannel.Error",PEER_CONNECTION_OPEN:"WebRTC.PeerConnection.Open",TRACK_ADDED:"WebRTC.PeerConnection.OnTrack",UNSUPPORTED_FEATURE:"WebRTC.Unsupported.Feature",TRANSFORM_ERROR:"WebRTC.Transform.Error"}),_t=Object.freeze({EMBED_SUCCESS:"FlashPlayer.Embed.Success",EMBED_FAILURE:"FlashPlayer.Embed.Failure"}),wt=Object.freeze({CONNECT_SUCCESS:"Connect.Success",CONNECT_FAILURE:"Connect.Failure",SUBSCRIBE_START:"Subscribe.Start",SUBSCRIBE_STOP:"Subscribe.Stop",SUBSCRIBE_FAIL:"Subscribe.Fail",SUBSCRIBE_INVALID_NAME:"Subscribe.InvalidName",SUBSCRIBE_METADATA:"Subscribe.Metadata",SUBSCRIBE_STATUS:"Subscribe.Status",SUBSCRIBE_SEND_INVOKE:"Subscribe.Send.Invoke",SUBSCRIBE_PUBLISHER_CONGESTION:"Subscribe.Publisher.NetworkCongestion",SUBSCRIBE_PUBLISHER_RECOVERY:"Subscribe.Publisher.NetworkRecovery",PLAY_UNPUBLISH:"Subscribe.Play.Unpublish",CONNECTION_CLOSED:"Subscribe.Connection.Closed",ORIENTATION_CHANGE:"Subscribe.Orientation.Change",STREAMING_MODE_CHANGE:"Subscribe.StreamingMode.Change",VIDEO_DIMENSIONS_CHANGE:"Subscribe.VideoDimensions.Change",VOLUME_CHANGE:"Subscribe.Volume.Change",SEEK_CHANGE:"Subscribe.Seek.Change",PLAYBACK_TIME_UPDATE:"Subscribe.Time.Update",PLAYBACK_STATE_CHANGE:"Subscribe.Playback.Change",FULL_SCREEN_STATE_CHANGE:"Subscribe.FullScreen.Change",AUTO_PLAYBACK_FAILURE:"Subscribe.Autoplay.Failure",AUTO_PLAYBACK_MUTED:"Subscribe.Autoplay.Muted"}),St=Object.freeze({SUBSCRIBER_REJECT:"Subscriber.Reject",SUBSCRIBER_ACCEPT:"Subscriber.Accept"}),Et=Object.freeze({PEER_CONNECTION_AVAILABLE:"WebRTC.PeerConnection.Available",OFFER_START:"WebRTC.Offer.Start",OFFER_END:"WebRTC.Offer.End",ANSWER_START:"WebRTC.Answer.Start",ANSWER_END:"WebRTC.Answer.End",CANDIDATE_START:"WebRTC.Candidate.Start",CANDIDATE_END:"WebRTC.Candidate.End",PEER_CANDIDATE_END:"WebRTC.PeerConnection.CandidateEnd",ICE_TRICKLE_COMPLETE:"WebRTC.IceTrickle.Complete",SOCKET_MESSAGE:"WebRTC.Socket.Message",DATA_CHANNEL_MESSAGE:"WebRTC.DataChannel.Message",DATA_CHANNEL_OPEN:"WebRTC.DataChannel.Open",DATA_CHANNEL_AVAILABLE:"WebRTC.DataChannel.Available",DATA_CHANNEL_CLOSE:"WebRTC.DataChannel.Close",DATA_CHANNEL_ERROR:"WebRTC.DataChannel.Error",PEER_CONNECTION_OPEN:"WebRTC.PeerConnection.Open",ON_ADD_STREAM:"WebRTC.Add.Stream",TRACK_ADDED:"WebRTC.PeerConnection.OnTrack",SUBSCRIBE_STREAM_SWITCH:"WebRTC.Subscribe.StreamSwitch",LIVE_SEEK_UNSUPPORTED:"WebRTC.LiveSeek.Unsupported",LIVE_SEEK_ERROR:"WebRTC.LiveSeek.Error",LIVE_SEEK_ENABLED:"WebRTC.LiveSeek.Enabled",LIVE_SEEK_DISABLED:"WebRTC.LiveSeek.Disabled",LIVE_SEEK_LOADING:"WebRTC.LiveSeek.FragmentLoading",LIVE_SEEK_LOADED:"WebRTC.LiveSeek.FragmentLoaded",LIVE_SEEK_CHANGE:"WebRTC.LiveSeek.Change",TRANSFORM_ERROR:"WebRTC.Transform.Error"}),Ct=Object.freeze({EMBED_SUCCESS:"FlashPlayer.Embed.Success",EMBED_FAILURE:"FlashPlayer.Embed.Failure",ABR_LEVEL_CHANGE:"RTMP.AdaptiveBitrate.Level"}),Ot=Object.freeze({CONNECT_SUCCESS:"Connect.Success",CONNECT_FAILURE:"Connect.Failure",PROPERTY_UPDATE:"SharedObject.PropertyUpdate",PROPERTY_REMOVE:"SharedObject.PropertyRemove",METHOD_UPDATE:"SharedObject.MethodUpdate",CONNECTION_CLOSED:"SharedObject.Connection.Closed"}),kt=Object.freeze({OPEN:"MessageTransport.Open",CLOSE:"MessageTransport.Close",CHANGE:"MessageTransport.Change",ERROR:"MessageTransport.Error"}),Pt=Object.freeze({AUDIO_STREAM:"Conference.AudioStream",VIDEO_STREAM:"Conference.VideoStream",MEDIA_STREAM:"Conference.MediaStream"});function Tt(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rt(e,t)}function Rt(e,t){return(Rt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function jt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Lt(e);if(t){var o=Lt(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return At(this,n)}}function At(e,t){if(t&&("object"===Nt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Lt(e){return(Lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Nt(e){return(Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ht(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function It(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Nt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Nt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Nt(i)?i:String(i)),r)}var o,i}function xt(e,t,n){return t&&It(e.prototype,t),n&&It(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var Dt=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;Ht(this,e),this._type=t,this._data=n}return xt(e,[{key:"type",get:function(){return this._type}},{key:"data",get:function(){return this._data}}]),e}(),Mt=function(e){Tt(n,e);var t=jt(n);function n(e,r,o){var i;return Ht(this,n),(i=t.call(this,e,o))._publisher=r,i}return xt(n,[{key:"publisher",get:function(){return this._publisher}}]),n}(Dt),Ft=function(e){Tt(n,e);var t=jt(n);function n(e,r,o){var i;return Ht(this,n),(i=t.call(this,e,o))._subscriber=r,i}return xt(n,[{key:"subscriber",get:function(){return this._subscriber}}]),n}(Dt),Ut=function(e){Tt(n,e);var t=jt(n);function n(e,r,o){var i;return Ht(this,n),(i=t.call(this,e,o))._name=r,i}return xt(n,[{key:"name",get:function(){return this._name}}]),n}(Dt),Bt=function(e){Tt(n,e);var t=jt(n);function n(e,r,o){var i;return Ht(this,n),(i=t.call(this,e,o))._name=r,i}return xt(n,[{key:"name",get:function(){return this._name}}]),n}(Dt),Vt=function(e){Tt(n,e);var t=jt(n);function n(e,r,o){var i;return Ht(this,n),(i=t.call(this,e,o))._participant=r,i}return xt(n,[{key:"participant",get:function(){return this._participant}}]),n}(Dt);function Gt(e){return(Gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Wt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Gt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Gt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Gt(i)?i:String(i)),r)}var o,i}function Yt(e,t){return(Yt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function zt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Kt(e);if(t){var o=Kt(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Jt(this,n)}}function Jt(e,t){if(t&&("object"===Gt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Kt(e){return(Kt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var qt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yt(e,t)}(i,e);var t,n,r,o=zt(i);function i(e,t,n){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(r=o.call(this,e,n))._control=t,r}return t=i,(n=[{key:"control",get:function(){return this._control}}])&&Wt(t.prototype,n),r&&Wt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Dt),Xt=Object.freeze({CHANGE_START:"changestart",CHANGE:"change",CHANGE_COMPLETE:"changecomplete"}),Qt=Object.freeze({SEEK_START:"seekstart",SEEK_END:"seekend"});function $t(e){return($t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Zt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==$t(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==$t(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===$t(i)?i:String(i)),r)}var o,i}function en(e,t){return(en=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=on(e);if(t){var o=on(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return nn(this,n)}}function nn(e,t){if(t&&("object"===$t(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return rn(e)}function rn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function on(e){return(on=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var an="ControlSlider",sn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&en(e,t)}(i,e);var t,n,r,o=tn(i);function i(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(t=o.call(this)).name=[an,e].join("::"),p(t.name,"[init]"),t._container=ce.createElement("div"),t._button=t.createButton(),t._track=t.createTrack(),t._progressBar=t.createProgressBar(),t._container.appendChild(t._track),t._container.appendChild(t._progressBar),t._container.appendChild(t._button),t._value=0,t._disabled=!1,t._eventStartPosition=0,t._layout(),t._mouseupHandler=t._mouseup.bind(rn(t)),t._mousedownHandler=t._mousedown.bind(rn(t)),t._mousemoveHandler=t._mousemove.bind(rn(t)),t._touchupHandler=t._touchproxy.bind(rn(t)),t._touchdownHandler=t._touchproxy.bind(rn(t)),t._touchmoveHandler=t._touchproxy.bind(rn(t)),t._updateHandlers(t._disabled),t}return t=i,(n=[{key:"_touchproxy",value:function(e){p(this.name,e.type,e.changedTouches);try{e.preventDefault()}catch(e){v(this.name,"Failed to prevent default on touch event.")}if(!(e.touches.length>1||"touchend"==e.type&&e.touches.length>0)){var t,n,r=ce.createEvent("MouseEvent"),o=e.originalTarget||e.target;switch(e.type){case"touchstart":t="mousedown",n=e.changedTouches[0];break;case"touchmove":t="mousemove",n=e.changedTouches[0];break;case"touchend":t="mouseup",n=e.changedTouches[0]}r.initMouseEvent(t,!0,!0,o.ownerDocument.defaultView,0,n.screenX,n.screenY,n.clientX,n.clientY,e.ctrlKey,e.altKey,e.shiftKey,e.metaKey,0,null),o.dispatchEvent(r)}}},{key:"_mouseup",value:function(){this._eventStartPosition=0,document.removeEventListener("mousemove",this._mousemoveHandler),document.removeEventListener("mouseup",this._mouseupHandler),document.removeEventListener("touchmove",this._touchmoveHandler),document.removeEventListener("touchup",this._touchupHandler),document.removeEventListener("touchend",this._touchupHandler),this.trigger(new qt(Xt.CHANGE_COMPLETE,this))}},{key:"_mousemove",value:function(e){var t=ce.getMouseXFromEvent(e)-this._eventStartPosition,n=this._button.parentNode.getBoundingClientRect(),r=this._eventStartPosition+t-n.left;r=Math.max(0,r);var o=(r=Math.min(r,n.width))/n.width;this.trigger(new qt(Xt.CHANGE,this,o))}},{key:"_mousedown",value:function(e){this._eventStartPosition=ce.getMouseXFromEvent(e),this.trigger(new qt(Xt.CHANGE_START,this)),document.addEventListener("mousemove",this._mousemoveHandler),document.addEventListener("mouseup",this._mouseupHandler),document.addEventListener("touchmove",this._touchmoveHandler),document.addEventListener("touchup",this._touchupHandler),document.addEventListener("touchend",this._touchupHandler)}},{key:"_updateHandlers",value:function(e){this._eventStartPosition=0,e?(this._track.removeEventListener("click",this._mousemoveHandler),this._progressBar.removeEventListener("click",this._mousemoveHandler),this._button.removeEventListener("mousedown",this._mousedownHandler),document.removeEventListener("mousemove",this._mousemoveHandler),document.removeEventListener("mouseup",this._mouseupHandler),document.removeEventListener("touchmove",this._touchmoveHandler),document.removeEventListener("touchup",this._touchupHandler),document.removeEventListener("touchend",this._touchupHandler),this._track.classList.add("red5pro-media-slider-disabled"),this._progressBar.classList.add("red5pro-media-slider-disabled"),this._button.classList.add("red5pro-media-slider-disabled")):(this._track.addEventListener("click",this._mousemoveHandler),this._progressBar.addEventListener("click",this._mousemoveHandler),this._button.addEventListener("mousedown",this._mousedownHandler),this._button.addEventListener("touchstart",this._touchdownHandler),this._track.classList.remove("red5pro-media-slider-disabled"),this._progressBar.classList.remove("red5pro-media-slider-disabled"),this._button.classList.remove("red5pro-media-slider-disabled"))}},{key:"_layout",value:function(){var e=this._progressBar.parentNode.clientWidth*this._value;this._progressBar.style.width=e+"px",this._button.style.left=e-.5*this._button.clientWidth+"px"}},{key:"createButton",value:function(){var e=ce.createElement("span");return e.classList.add("red5pro-media-slider-button"),e}},{key:"createProgressBar",value:function(){var e=ce.createElement("span");return e.classList.add("red5pro-media-slider-progress"),e}},{key:"createTrack",value:function(){var e=ce.createElement("span");return e.classList.add("red5pro-media-slider-track"),e}},{key:"value",get:function(){return this._value},set:function(e){this._value=e,this._layout()}},{key:"disabled",get:function(){return this._disabled},set:function(e){this._disabled=e,this._updateHandlers(e)}},{key:"view",get:function(){return this._container}}])&&Zt(t.prototype,n),r&&Zt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(H),cn=Object.freeze({UNAVAILABLE:1e3,AVAILABLE:0,IDLE:1,PLAYING:2,PAUSED:3}),un=Object.freeze({1e3:"Playback.UNAVAILABLE",0:"Playback.AVAILABLE",1:"Playback.IDLE",2:"Playback.PLAYING",3:"Playback.PAUSED"}),ln=Object.freeze({LIVE:0,VOD:1}),dn=Object.freeze({0:"LiveSeek.LIVE",1:"LiveSeek.VOD"});function fn(e){return(fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function pn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==fn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==fn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===fn(i)?i:String(i)),r)}var o,i}function vn(e,t,n){return t&&pn(e.prototype,t),n&&pn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function mn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yn(e,t)}function yn(e,t){return(yn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function bn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=wn(e);if(t){var o=wn(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return gn(this,n)}}function gn(e,t){if(t&&("object"===fn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _n(e)}function _n(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function wn(e){return(wn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Sn=function(e){mn(n,e);var t=bn(n);function n(){return hn(this,n),t.call(this)}return vn(n,[{key:"play",value:function(){}},{key:"pause",value:function(){}},{key:"resume",value:function(){}},{key:"stop",value:function(){}},{key:"mute",value:function(){}},{key:"unmute",value:function(){}},{key:"setVolume",value:function(e){}},{key:"seekTo",value:function(e){}},{key:"toggleFullScreen",value:function(){}}]),n}(H),En=function(e){mn(n,e);var t=bn(n);function n(){return hn(this,n),t.call(this)}return vn(n,[{key:"getVolume",value:function(){}},{key:"setVolume",value:function(e){}},{key:"setSeekTime",value:function(e){}},{key:"setPlaybackDuration",value:function(e){}},{key:"getState",value:function(){}},{key:"setState",value:function(e){}},{key:"setAsVOD",value:function(e){}},{key:"enable",value:function(e){}}]),n}(H),Cn=Sn,On=En,kn=function(e){mn(n,e);var t=bn(n);function n(e,r){var o;return hn(this,n),(o=t.call(this)).player=e,o.container=r,o._controlbar=void 0,o._playPauseButton=void 0,o._muteButton=void 0,o._volumeField=void 0,o._seekTimeField=void 0,o._timeField=void 0,o._fullScreenButton=void 0,o._state=cn.IDLE,o._mutedState=!1,o._resumeAfterSeek=!1,o._playbackDuration=0,o._volumeValue=1,o._onPlayPauseClickBound=o._onPlayPauseClick.bind(_n(o)),o.decorate(o.container),o}return vn(n,[{key:"decorate",value:function(e){p("PlaybackControls","[decorate]");var t,n=ce.createElement("div");n.classList.add("red5pro-media-control-bar"),this._playPauseButton=this._createPlayPauseButton(),this._muteButton=this._createMuteButton(),this._volumeField=this._createVolumeControl(),this._seekTimeField=this._createSeekControl(),this._timeField=this._createPlaybackTime(),this._fullScreenButton=this._createFullScreenToggle(),n.appendChild(this._playPauseButton),n.appendChild(this._timeField),n.appendChild(this._seekTimeField.view),n.appendChild(this._muteButton),n.appendChild(this._volumeField.view),n.appendChild(this._fullScreenButton),e.appendChild(n),this._controlbar=n;var r=function(){clearTimeout(t),t=setTimeout((function(){n.classList.remove("red5pro-media-control-bar-show")}),6e3)};ce.isTouchEnabled()?(n.classList.add("red5pro-media-control-bar-show"),this.container.addEventListener("touchend",(function(){n.classList.toggle("red5pro-media-control-bar-show"),r()})),r()):(this.container.addEventListener("mouseover",(function(){n.classList.add("red5pro-media-control-bar-show")})),this.container.addEventListener("mouseout",(function(){n.classList.remove("red5pro-media-control-bar-show")}))),this.setState(cn.IDLE).onFullScreenChange(!1).setSeekTime(0).enable(!1)}},{key:"_onPlayPauseClick",value:function(){this.getState()===cn.PLAYING?this.player.pause(!0):this.getState()===cn.PAUSED?this.player.resume(!0):this.player.play(!0)}},{key:"_createPlayPauseButton",value:function(){var e=ce.createElement("button");return e.setAttribute("aria-label","Toggle Playback"),e.classList.add("red5pro-media-control-element"),e.classList.add("red5pro-media-element-button"),e.classList.add("red5pro-media-playpause-button"),e}},{key:"_createMuteButton",value:function(){var e=this,t=ce.createElement("button");return t.setAttribute("aria-label","Toggle Mute Audio"),t.classList.add("red5pro-media-control-element"),t.classList.add("red5pro-media-element-button"),t.classList.add("red5pro-media-muteunmute-button"),t.addEventListener("click",(function(){e.getMutedState()?(e.player.unmute(),e.setMutedState(!1)):(e.player.mute(),e.setMutedState(!0))})),t}},{key:"_createVolumeControl",value:function(){var e=this,t=new sn("volume");return t.view.classList.add("red5pro-media-control-element"),t.view.classList.add("red5pro-media-volume-slider"),t.view.classList.add("red5pro-media-slider"),t.on(Xt.CHANGE,(function(t){var n=Number(t.data);e.player.setVolume(n)})),t}},{key:"_createSeekControl",value:function(){var e=this,t=new sn("seek");return t.view.classList.add("red5pro-media-control-element"),t.view.classList.add("red5pro-media-seektime-slider"),t.view.classList.add("red5pro-media-slider"),t.on(Xt.CHANGE_START,(function(){e.getState()===cn.PLAYING&&(e._resumeAfterSeek=!0,e.player.pause(!0,!0)),e.trigger(new Dt(Qt.SEEK_START))})),t.on(Xt.CHANGE,(function(t){var n=Number(t.data);e.player.seekTo(n,0===e._playbackDuration?void 0:e._playbackDuration),e.setSeekTime(n*e._playbackDuration,e._playbackDuration)})),t.on(Xt.CHANGE_COMPLETE,(function(){e._resumeAfterSeek&&e.getState()===cn.PAUSED&&(e._resumeAfterSeek=!1,e.player.resume(!0,!0)),e.trigger(new Dt(Qt.SEEK_END))})),t}},{key:"_createPlaybackTime",value:function(){var e=ce.createElement("span"),t=ce.createElement("text","hello!");return e.classList.add("red5pro-media-control-element"),e.classList.add("red5pro-media-time-field"),e.appendChild(t),e}},{key:"_createFullScreenToggle",value:function(){var e=this,t=ce.createElement("button");return t.setAttribute("aria-label","Toggle Fullscreen"),t.classList.add("red5pro-media-control-element"),t.classList.add("red5pro-media-element-button"),t.classList.add("red5pro-media-fullscreen-button"),t.addEventListener("click",(function(){e.player.toggleFullScreen()})),t}},{key:"enable",value:function(e){e?(this._playPauseButton.classList.remove("red5pro-media-element-button-disabled"),this._playPauseButton.addEventListener("click",this._onPlayPauseClickBound)):(this._playPauseButton.classList.add("red5pro-media-element-button-disabled"),this._playPauseButton.removeEventListener("click",this._onPlayPauseClickBound))}},{key:"formatTime",value:function(e){var t,n=0,r=0===e||isNaN(e)?0:parseInt(e/60);r>=60&&(n=parseInt(r/60),r%=60),t=0===e||isNaN(e)?0:parseInt(e%60);var o=n<10?["0"+n]:[n];return o.push(r<10?["0"+r]:[r]),o.push(t<10?["0"+t]:[t]),o.join(":")}},{key:"getVolume",value:function(){return this._volumeValue}},{key:"setVolume",value:function(e){return this._volumeField.value=e,this._volumeValue=e,0===e?this.setMutedState(!0):this.getMutedState()&&this.setMutedState(!1),this}},{key:"setSeekTime",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._seekTimeField.value=0===t?0:e/t,0!==this._playbackDuration&&parseInt(this._playbackDuration)<=parseInt(e)&&(this._seekTimeField.value=1),this._timeField.innerText=this.formatTime(Math.floor(e)),this}},{key:"setPlaybackDuration",value:function(e){p("PlaybackControls","[setplaybackduration]: "+e),this._playbackDuration=e}},{key:"getPlaybackDuration",value:function(){return this._playbackDuration}},{key:"getState",value:function(){return this._state}},{key:"setState",value:function(e){return p("PlaybackControls","[setState]: "+un[e]),this._state=e,this.onStateChange(this._state),this}},{key:"getMutedState",value:function(){return"muted"in this.player?this.player.muted:this._mutedState}},{key:"setMutedState",value:function(e){return this._mutedState=e,this.onMutedStateChange(this._mutedState),this}},{key:"onStateChange",value:function(e){return e===cn.PLAYING?(this._playPauseButton.classList.remove("red5pro-media-play-button"),this._playPauseButton.classList.add("red5pro-media-pause-button")):(this._playPauseButton.classList.add("red5pro-media-play-button"),this._playPauseButton.classList.remove("red5pro-media-pause-button")),this}},{key:"onMutedStateChange",value:function(e){e?(this._muteButton.classList.add("red5pro-media-mute-button"),this._muteButton.classList.remove("red5pro-media-unmute-button"),this._volumeField.value=0):(this._muteButton.classList.remove("red5pro-media-mute-button"),this._muteButton.classList.add("red5pro-media-unmute-button"),this._volumeField.value=this._volumeValue)}},{key:"onFullScreenChange",value:function(e){return e?(this._fullScreenButton.classList.add("red5pro-media-exit-fullscreen-button"),this._fullScreenButton.classList.remove("red5pro-media-fullscreen-button")):(this._fullScreenButton.classList.remove("red5pro-media-exit-fullscreen-button"),this._fullScreenButton.classList.add("red5pro-media-fullscreen-button")),this}},{key:"setAsVOD",value:function(e){p("PlaybackControls","[setAsVOD]: "+e),e?this._seekTimeField.disabled=!1:(this._seekTimeField.value=0,this._seekTimeField.disabled=!0)}},{key:"detach",value:function(){this.enable(!1),this._controlbar&&this._controlbar.parentNode&&this._controlbar.parentNode.removeChild(this._controlbar),this._controlbar=void 0,this.container=void 0}}]),n}(En);function Pn(e){return(Pn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Tn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Pn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Pn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Pn(i)?i:String(i)),r)}var o,i}function Rn(e,t){return(Rn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function jn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Nn(e);if(t){var o=Nn(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return An(this,n)}}function An(e,t){if(t&&("object"===Pn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ln(e)}function Ln(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Nn(e){return(Nn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Hn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rn(e,t)}(i,e);var t,n,r,o=jn(i);function i(e,t){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(n=o.call(this)).media=e,ce.hasAttributeDefined(n.media,"controls")&&ce.hasClassDefined(n.media,"red5pro-media")&&(n.clone=n.media.cloneNode(!0),n.parent=n.media.parentNode,n.holder=n._determineHolder(n.media)),n.playerType=t,n._isVOD=!1,n._controls=void 0,n._playbackNotificationCenter=n.media,ce.onFullScreenStateChange(n._handleFullScreenChange.bind(Ln(n))),n}return t=i,(n=[{key:"_determineHolder",value:function(e){if(e.parentNode.classList.contains("red5pro-media-container"))return e.parentNode;var t=e.parentNode,n=ce.createElement("div");return n.classList.add("red5pro-media-container"),t.insertBefore(n,e),t.removeChild(e),n.appendChild(e),n}},{key:"_cleanUp",value:function(){if(this.clone){var e=this.media,t=e.parentNode,n=this.holder;if(t)t.removeChild(e),t!==this.parent&&(t.parentNode.removeChild(t),n=this.parent);else try{e.remove()}catch(e){v("RTCSourceHandler","Issue in DOM cleanup of WebRTC video object: ".concat(e.message))}this.media=this.clone.cloneNode(!0),n.appendChild(this.media),this._controls&&this._controls.detach()}}},{key:"_addPlaybackHandlers",value:function(e){var t=this,n=this.getControls(),r=void 0!==n;e.oncanplay=function(){p("RTCSourceHandler","[videoelement:event] canplay"),n&&n.enable(!0),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.AVAILABLE,state:un[cn.AVAILABLE]})),t.trigger(new Ft(wt.VOLUME_CHANGE,void 0,{volume:e.volume}))},e.ondurationchange=function(o){p("RTCSourceHandler","[videoelement:event] durationchange"),!isNaN(e.duration)&&Number.isFinite(e.duration)&&(t.isVOD=!0),r&&n.setPlaybackDuration(e.duration)},e.onended=function(){p("RTCSourceHandler","[videoelement:event] ended"),r&&n.setState(cn.IDLE),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.IDLE,state:un[cn.IDLE]}))},e.ontimeupdate=function(o){r&&n.setSeekTime(e.currentTime,self.isVOD?e.duration:void 0),t.trigger(new Ft(wt.PLAYBACK_TIME_UPDATE,void 0,{time:e.currentTime,duration:e.duration}))},e.onseeked=function(e){},e.onseeking=function(e){},e.onplay=function(){p("RTCSourceHandler","[videoelement:event] play"),r&&n.setState(cn.PLAYING),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.PLAYING,state:un[cn.PLAYING]}))},e.onpause=function(){p("RTCSourceHandler","[videoelement:event] pause"),r&&n.setState(cn.PAUSED),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.PAUSED,state:un[cn.PAUSED]}))},e.onvolumechange=function(o){r&&n.getVolume()!==t.media.volume&&n.setVolume(t.media.volume),t.trigger(new Ft(wt.VOLUME_CHANGE,void 0,{volume:e.muted?0:e.volume}))},e.onencrypted=function(){p("RTCSourceHandler","[videoelement:event] encrypted")},e.onemptied=function(){p("RTCSourceHandler","[videoelement:event] emptied")},e.onloadeddata=function(){p("RTCSourceHandler","[videoelement:event] loadeddata"),t.trigger(new Ft(wt.VIDEO_DIMENSIONS_CHANGE,void 0,{width:t.media.videoWidth,height:t.media.videoHeight}))},e.onresize=function(){p("RTCSourceHandler","[videoelement:event] resize"),t.trigger(new Ft(wt.VIDEO_DIMENSIONS_CHANGE,void 0,{width:t.media.videoWidth,height:t.media.videoHeight}))},e.onloadedmetadata=function(){p("RTCSourceHandler","[videoelement:event] loadedmetadata")},e.onloadstart=function(){p("RTCSourceHandler","[videoelement:event] loadedstart")},e.onstalled=function(){p("RTCSourceHandler","[videoelement:event] stalled")},e.onsuspend=function(){p("RTCSourceHandler","[videoelement:event] suspend")},e.onwaiting=function(){p("RTCSourceHandler","[videoelement:event] waiting")}}},{key:"_handleFullScreenChange",value:function(e){e?(this.holder&&this.holder.classList.add("red5pro-media-container-full-screen"),this.media.classList.add("red5pro-media-container-full-screen")):(this.holder&&this.holder.classList.remove("red5pro-media-container-full-screen"),this.media.classList.remove("red5pro-media-container-full-screen")),this.trigger(new Ft(wt.FULL_SCREEN_STATE_CHANGE,void 0,e))}},{key:"addSource",value:function(e){p("RTCSourceHandler","[addsource]");var t=ce.hasAttributeDefined(this.media,"controls")&&ce.hasClassDefined(this.media,"red5pro-media");t&&(this.holder=this._determineHolder(this.media));var n=new S,r=e.controls,o=ce.hasAttributeDefined(this.media,"muted");return(r||t)&&(this._controls=r?e.controls:new kn(this,this.holder),this.media.controls=!1,this._controls.setAsVOD(this.isVOD),this._controls.setMutedState(o)),this._addPlaybackHandlers(this._playbackNotificationCenter),n.resolve(),n.promise}},{key:"connect",value:function(){p("RTCSourceHandler","[connect]")}},{key:"attemptAutoplay",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.play().catch((function(n){t?(e.mute(),e.play().then((function(){e.trigger(new Ft(wt.AUTO_PLAYBACK_MUTED,void 0,{element:e.media}))})).catch((function(t){e.trigger(new Ft(wt.AUTO_PLAYBACK_FAILURE,void 0,{error:t.message?t.message:t,element:e.media}))}))):e.trigger(new Ft(wt.AUTO_PLAYBACK_FAILURE,void 0,{error:n.message?n.message:n,element:e.media}))}))}},{key:"play",value:function(){p("RTCSourceHandler","[videoelement:action] play");var e=new S;try{var t=this.media.play();t?t.then((function(){p("RTCSourceHandler","[videoelement:action] play (START)"),e.resolve()})).catch(e.reject):(p("RTCSourceHandler","[videoelement:action] play (START)"),e.resolve())}catch(t){m("RTCSourceHandler","[videoelement:action] play (FAULT) - "+t.message),e.reject(t)}return e.promise}},{key:"pause",value:function(){p("RTCSourceHandler","[videoelement:action] pause");try{this.media.pause()}catch(e){v("RTCSourceHandler","[videoelement:action] pause (CATCH::FAULT) - "+e.message)}}},{key:"resume",value:function(){p("RTCSourceHandler","[videoelement:action] resume");try{var e=this.media.play();e&&e.then((function(){return p("RTCSourceHandler","[videoelement:action] play (START)")})).catch((function(e){return v("RTCSourceHandler","[videoelement:action] play (CATCH::FAULT) "+(e.message?e.message:e))}))}catch(e){v("RTCSourceHandler","[videoelement:action] resume (CATCH::FAULT) - "+e.message)}}},{key:"stop",value:function(){p("RTCSourceHandler","[videoelement:action] stop");try{this.media.stop()}catch(e){}}},{key:"mute",value:function(){this.media.muted=!0;var e=this.getControls();e&&e.setMutedState(!0)}},{key:"unmute",value:function(){this.media.muted=!1;var e=this.getControls();e&&e.setMutedState(!1)}},{key:"setVolume",value:function(e){this.unmute(),this.media.volume=e}},{key:"seekTo",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;this.media.currentTime=t?e*t:e}},{key:"toggleFullScreen",value:function(){try{this.holder&&ce.toggleFullScreen(this.holder)}catch(e){throw e}}},{key:"unpublish",value:function(){try{this.stop(),this.media.onended.call(this.media)}catch(e){}}},{key:"disconnect",value:function(){this._cleanUp()}},{key:"handleOrientationChange",value:function(e){this._controls&&e%180!=0&&(this.holder&&this.holder.classList.add("red5pro-media-background"),this.media.classList.remove("red5pro-media-background"))}},{key:"addSharedObjectResponseHandler",value:function(e){}},{key:"removeSharedObjectResponseHandler",value:function(e){}},{key:"sendToSharedObject",value:function(e,t,n){}},{key:"sendPropertyToSharedObject",value:function(e,t,n){}},{key:"getRemoteSharedObject",value:function(e){}},{key:"connectToSharedObject",value:function(e){}},{key:"closeSharedObject",value:function(e){}},{key:"getControls",value:function(){return this._controls}},{key:"getType",value:function(){return this.playerType}},{key:"isVOD",get:function(){return this._isVOD},set:function(e){this._isVOD=e,this._controls&&this._controls.setAsVOD(e)}}])&&Tn(t.prototype,n),r&&Tn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Cn);function In(e){return(In="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function xn(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */xn=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==In(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function Dn(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function Mn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Fn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==In(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==In(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===In(i)?i:String(i)),r)}var o,i}function Un(e,t){return(Un=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Bn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Wn(e);if(t){var o=Wn(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Vn(this,n)}}function Vn(e,t){if(t&&("object"===In(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Gn(e)}function Gn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wn(e){return(Wn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Yn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Un(e,t)}(s,e);var t,n,r,o,i,a=Bn(s);function s(e,t,n){var r,o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return Mn(this,s),(r=a.call(this)).media=e,r.clone=r.media.cloneNode(!0),r.parent=r.media.parentNode,r.holder=o?r._determineHolder(r.media):void 0,r.playerType=t,r.hlsOptions=n,r.subscriberId=void 0,r.hlsElement=void 0,r._usePlaybackControls=o,r._isVOD=!1,r._isSeekable=!1,r._isHLSPlaybackActive=!1,r._isFragLoading=!1,r._hlsRecoverFlop=!1,r._hlsRecoverAttempts=0,r._lastDurationUpdate=0,r._controls=void 0,r._resizeObserver=void 0,r._playbackNotificationCenter=r.media,r._pendingUnpublish=!1,r._wallOffset=NaN,r._averageSegmentDuration=6,r._manifestLoadTimeout=0,ce.onFullScreenStateChange(r._handleFullScreenChange.bind(Gn(r))),r.hls=void 0,r.hlsElement=void 0,r}return t=s,(n=[{key:"_determineHolder",value:function(e){if(e.parentNode.classList.contains("red5pro-media-container"))return e.parentNode;var t=e.parentNode,n=ce.createElement("div");return n.classList.add("red5pro-media-container"),t.insertBefore(n,e),t.removeChild(e),n.appendChild(e),n}},{key:"_generateHLSLivePlayback",value:function(e,t,n){var r="".concat(n,"-hls-vod"),o=document.querySelector("#".concat(r));return o||((o=document.createElement("video")).id=r,o.classList.add("red5pro-hls-vod"),o.playsinline="playsinline",o.style.width="100%",o.style.height="100%",o.style.position="absolute",o.style.display="none",e.insertBefore(o,t)),o}},{key:"_loadManifest",value:(o=xn().mark((function e(t){var n,r,o,i,a,s,c,u,l,d,f=this;return xn().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return clearTimeout(this._manifestLoadTimeout),e.prev=1,n=/^#EXT-X-TARGETDURATION:(.*)/,r=/^.*_(.*)\.\.*/,e.next=6,fetch(t,{method:"GET",mode:"cors",cache:"no-cache",headers:{"Content-Type":"text/plain;charset=UTF-8"}});case 6:return o=e.sent,e.next=9,o.text();case 9:i=e.sent,a=i.split("\r\n"),s=a.reverse().find((function(e){return r.test(e)})),c=a.find((function(e){return n.test(e)})).match(n)[1],u=parseFloat(c),l=s.match(r)[1],d=parseInt(l,10),isNaN(d)&&(d=1),d=d>1?d-1:d,this._averageSegmentDuration=u,console.log("MANIFEST: ",i),this.isSeekable=!0,this.trigger(new Ft(Et.LIVE_SEEK_ENABLED,void 0,{hlsElement:this.hlsElement,hlsControl:void 0})),this._onHLSDurationChange({target:this.hlsElement,duration:u*d}),this._manifestLoadTimeout=setTimeout((function(){clearTimeout(f._manifestLoadTimeout),f._loadManifest(t)}),1e3*u),e.next=31;break;case 26:e.prev=26,e.t0=e.catch(1),m("Could not load manifest: ".concat(e.t0.message,".")),this.trigger(new Ft(Et.LIVE_SEEK_DISABLED,void 0,{hlsElement:this.hlsElement,hlsControl:void 0})),this.isSeekable=!1;case 31:case"end":return e.stop()}}),e,this,[[1,26]])})),i=function(){var e=this,t=arguments;return new Promise((function(n,r){var i=o.apply(e,t);function a(e){Dn(i,n,r,a,s,"next",e)}function s(e){Dn(i,n,r,a,s,"throw",e)}a(void 0)}))},function(e){return i.apply(this,arguments)})},{key:"_showHLSLivePlayback",value:function(e,t,n,r){if(this._isHLSPlaybackActive!==e){this._isHLSPlaybackActive=e;var o=e?n.muted:t.muted;if(e){t.volume=n.volume,t.muted=o,n.muted=!0,t.style.display="inline-block",r.style.position="relative";try{n.paused?this.pause(!1):(this.pause(!0),this.play())}catch(e){v("Could not start playback: ".concat(e.message,"."))}n.style.display="none"}else{n.volume=t.volume,t.muted=!0,n.muted=o,n.style.display="inline-block";try{t.paused?this.pause(!1):(this.pause(!0),this.play())}catch(e){v("Could not start playback: ".concat(e.message,"."))}t.style.display="none"}var i=e?ln.VOD:ln.LIVE;this.trigger(new Ft(Et.LIVE_SEEK_CHANGE,void 0,{code:i,state:dn[i]}))}}},{key:"_cleanUp",value:function(){if(this.clone){var e=this.media,t=e.parentNode,n=this.holder;if(this._removePlaybackHandlers(e),this.hls&&(this.hls.detachMedia(),this.hls=void 0),this.hlsElement&&(this._removeSeekableHandlers(this.hlsElement),this.hlsElement.parentNode&&this.hlsElement.parentNode.removeChild(this.hlsElement),this.hlsElement=void 0),this._controls&&(this._controls.detach(),this._controls=void 0),t)t.removeChild(e),t!==this.parent&&(t.parentNode.removeChild(t),n=this.parent);else try{e.remove()}catch(e){v("RTCSeekableSourceHandler","Issue in DOM cleanup of WebRTC video object: ".concat(e.message))}this.media=this.clone.cloneNode(!0),n.appendChild(this.media),this._resizeObserver&&(this._resizeObserver.unobserve(),this._resizeObserver=void 0),this._isVOD=!1,this._isSeekable=!1,this._isHLSPlaybackActive=!1,this._isFragLoading=!1,this._hlsRecoverFlop=!1,this._hlsRecoverAttempts=0}}},{key:"_addPlaybackHandlers",value:function(e){var t=this,n=this.getControls();e.oncanplay=function(){p("RTCSeekableSourceHandler","[videoelement:event] canplay"),n&&n.enable(!0),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.AVAILABLE,state:un[cn.AVAILABLE]})),t.trigger(new Ft(wt.VOLUME_CHANGE,void 0,{volume:e.volume}))},e.onended=function(){return t._onRTCEnded.bind(t)},e.ondurationchange=this._onRTCDurationChange.bind(this),e.ontimeupdate=this._onRTCTimeUpdate.bind(this),e.onplay=this._onRTCPlay.bind(this),e.onpause=this._onRTCPause.bind(this),e.onvolumechange=function(r){n&&n.getVolume()!==t.media.volume&&n.setVolume(t.media.volume),t.trigger(new Ft(wt.VOLUME_CHANGE,void 0,{volume:e.muted?0:e.volume}))},e.onencrypted=function(){p("RTCSeekableSourceHandler","[videoelement:event] encrypted")},e.onemptied=function(){p("RTCSeekableSourceHandler","[videoelement:event] emptied")},e.onloadeddata=function(){p("RTCSeekableSourceHandler","[videoelement:event] loadeddata"),t.trigger(new Ft(wt.VIDEO_DIMENSIONS_CHANGE,void 0,{width:t.media.videoWidth,height:t.media.videoHeight}))},e.onresize=function(){p("RTCSeekableSourceHandler","[videoelement:event] resize"),t.trigger(new Ft(wt.VIDEO_DIMENSIONS_CHANGE,void 0,{width:t.media.videoWidth,height:t.media.videoHeight}))},e.onloadedmetadata=function(){p("RTCSeekableSourceHandler","[videoelement:event] loadedmetadata")},e.onloadstart=function(){p("RTCSeekableSourceHandler","[videoelement:event] loadedstart")},e.onstalled=function(){p("RTCSeekableSourceHandler","[videoelement:event] stalled")},e.onsuspend=function(){p("RTCSeekableSourceHandler","[videoelement:event] suspend")},e.onwaiting=function(){p("RTCSeekableSourceHandler","[videoelement:event] waiting")}}},{key:"_removePlaybackHandlers",value:function(e){e.oncanplay=void 0,e.onended=void 0,e.ondurationchange=void 0,e.ontimeupdate=void 0,e.onplay=void 0,e.onpause=void 0,e.onvolumechange=void 0,e.onencrypted=void 0,e.onemptied=void 0,e.onloadeddata=void 0,e.onresize=void 0,e.onloadedmetadata=void 0,e.onloadstart=void 0,e.onstalled=void 0,e.onsuspend=void 0,e.onwaiting=void 0}},{key:"_addSeekableHandlers",value:function(e,t){var n=this,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t&&(t.on(ce.getHLSClientEventEnum().ERROR,(function(r,o){var i=o.type,a=o.details,s=o.fatal,c=o.url;if("networkerror"===i.toLowerCase()){if("levelemptyerror"===a.toLowerCase()){n.trigger(new Ft(Et.LIVE_SEEK_DISABLED,void 0,{hlsElement:e,hlsControl:t})),n.isSeekable=!1,t.destroy();var u=setTimeout((function(){clearTimeout(u),n.enableLiveSeek(c,n.subscriberId,n.hlsElement,!1)}),3e3);return}n.trigger(new Ft(Et.LIVE_SEEK_ERROR,void 0,{hlsElement:e,hlsControl:t,error:o}))}else n.trigger(new Ft(Et.LIVE_SEEK_ERROR,void 0,{hlsElement:e,hlsControl:t,error:o}));"mediaerror"===i.toLowerCase()&&(n._hlsRecoverFlop&&t.swapAudioCodec(),n._hlsRecoverFlop=!n._hlsRecoverFlop,n._hlsRecoverAttempts=n._hlsRecoverAttempts+1,t.recoverMediaError()),s&&"networkerror"===i.toLowerCase()&&t.startLoad()})),t.on(ce.getHLSClientEventEnum().MANIFEST_PARSED,(function(){try{e.pause()}catch(e){p("RTCSeekableSourceHandler","Could not pause seekable live stream: ".concat(e.message))}n.isSeekable=!0,n.trigger(new Ft(Et.LIVE_SEEK_ENABLED,void 0,{hlsElement:e,hlsControl:t}))})),t.on(ce.getHLSClientEventEnum().FRAG_LOADING,(function(r,o){var i=o.frag.stats,a=i.loaded,s=i.total;n.trigger(new Ft(Et.LIVE_SEEK_LOADING,void 0,{hlsElement:e,hlsControl:t,progress:a/s*100})),(n.isHLSPlaybackActive||n._isFragLoading)&&(n._isFragLoading=a/s>=1)})),t.on(ce.getHLSClientEventEnum().FRAG_LOADED,(function(r,o){n._isFragLoading=!1;var i=o.frag,a=i.endDTS,s=i.loader;if(n.isHLSPlaybackActive||a){var c=6,u=0;if(s&&s.stats&&s.stats.segments){for(var l=s.stats.segments,d=0;d<l.length;d++)u+=l[d].duration;c=u/l.length,n._averageSegmentDuration=c}p("RTCSeekableSourceHandler","fragload, [HLS:videoelement:duration] ".concat(a," + ").concat(c)),n.trigger(new Ft(Et.LIVE_SEEK_LOADED,void 0,{hlsElement:e,hlsControl:t}))}})),t.on(ce.getHLSClientEventEnum().FRAG_PARSED,(function(r,o){n._isFragLoading=!1;var i=o.frag,a=i.endDTS,s=i.loader;if(n.isHLSPlaybackActive||a){var c=6,u=0;if(s&&s.stats&&s.stats.segments){for(var l=s.stats.segments,d=0;d<l.length;d++)u+=l[d].duration;c=u/l.length,n._averageSegmentDuration=c}p("RTCSeekableSourceHandler","fragparsed, [HLS:videoelement:duration] ".concat(a," + ").concat(c)),n.trigger(new Ft(Et.LIVE_SEEK_LOADED,void 0,{hlsElement:e,hlsControl:t}))}}))),r&&(e.ondurationchange=this._onHLSDurationChange.bind(this)),e.ontimeupdate=this._onHLSTimeUpdate.bind(this),e.onplay=this._onHLSPlay.bind(this),e.onpause=this._onHLSPause.bind(this)}},{key:"_removeSeekableHandlers",value:function(e){e.ondurationchange=void 0,e.ontimeupdate=void 0,e.onplay=void 0,e.onpause=void 0}},{key:"_onRTCEnded",value:function(){if(p("RTCSeekableSourceHandler","[videoelement:event] ended"),!this.isHLSPlaybackActive){var e=this.getControls();e&&e.setState(cn.IDLE),this.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.IDLE,state:un[cn.IDLE]}))}}},{key:"_onRTCDurationChange",value:function(e){var t=e.target;p("RTCSeekableSourceHandler","video, [videoelement:event] durationchange ("+t.duration+")"),!this.isSeekable&&this.getControls()&&this.getControls().setPlaybackDuration(t.duration)}},{key:"_onRTCTimeUpdate",value:function(e){var t=e.target,n=this.getControls();if(this.isSeekable){if(!this._isHLSPlaybackActive){var r=this.hlsElement.duration,o=t.currentTime-this._lastDurationUpdate,i=isNaN(r)||0===r?t.currentTime:r+this._averageSegmentDuration+o;n&&n.setSeekTime(i,i),this.trigger(new Ft(wt.PLAYBACK_TIME_UPDATE,void 0,{time:i,duration:i,action:"rtc time update (1)"}))}}else this.isSeekable||(n&&this.getControls().setSeekTime(t.currentTime,void 0),this.trigger(new Ft(wt.PLAYBACK_TIME_UPDATE,void 0,{time:t.currentTime,duration:t.duration,action:"rtc time update (2)"})))}},{key:"_onRTCPlay",value:function(){p("RTCSeekableSourceHandler","[videoelement:event] play");var e=this.getControls();e&&e.setState(cn.PLAYING),this.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.PLAYING,state:un[cn.PLAYING]}))}},{key:"_onRTCPause",value:function(){p("RTCSeekableSourceHandler","[videoelement:event] pause");var e=this.getControls();e&&e.setState(cn.PAUSED),this.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.PAUSED,state:un[cn.PAUSED]}))}},{key:"_onHLSDurationChange",value:function(e){var t=e.target,n=e.duration||t.duration;isNaN(this._wallOffset)&&(this._wallOffset=n-this.media.currentTime),this._lastDurationUpdate=this.media.currentTime;var r=n+this._averageSegmentDuration;p("RTCSeekableSourceHandler","[HLS:videoelement:duration] ".concat(n,", ").concat(this._averageSegmentDuration));var o=this.getControls();o&&o.setPlaybackDuration(r),this._isHLSPlaybackActive?this.trigger(new Ft(wt.PLAYBACK_TIME_UPDATE,void 0,{time:t.currentTime,duration:r,action:"hls time update"})):this.trigger(new Ft(wt.PLAYBACK_TIME_UPDATE,void 0,{time:r,duration:r,action:"hls time update"}))}},{key:"_onHLSTimeUpdate",value:function(e){var t=e.target,n=this.getControls();n&&n.setSeekTime(t.currentTime,n.getPlaybackDuration()),t.currentTime>=t.duration?this._showHLSLivePlayback(!1,this.hlsElement,this.media,this.holder):!isNaN(t.duration)&&this._isHLSPlaybackActive&&this.trigger(new Ft(wt.PLAYBACK_TIME_UPDATE,void 0,{time:t.currentTime,duration:t.duration+this._averageSegmentDuration,action:"hls time update"}))}},{key:"_onHLSPlay",value:function(){p("RTCSeekableSourceHandler","[HLS:videoelement:event] play");var e=this.getControls();e&&e.setState(cn.PLAYING),this.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.PLAYING,state:un[cn.PLAYING]}))}},{key:"_onHLSPause",value:function(){p("RTCSeekableSourceHandler","[HLS:videoelement:event] pause");var e=this.getControls();e&&e.setState(cn.PAUSED),this.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.PAUSED,state:un[cn.PAUSED]}))}},{key:"_handleFullScreenChange",value:function(e){e?(this.holder&&this.holder.classList.add("red5pro-media-container-full-screen"),this.hlsElement&&this.hlsElement.classList.add("red5pro-media-container-full-screen"),this.media.classList.add("red5pro-media-container-full-screen")):(this.holder&&this.holder.classList.remove("red5pro-media-container-full-screen"),this.hlsElement&&this.hlsElement.classList.remove("red5pro-media-container-full-screen"),this.media.classList.remove("red5pro-media-container-full-screen")),this.trigger(new Ft(wt.FULL_SCREEN_STATE_CHANGE,void 0,e))}},{key:"addSource",value:function(e){p("RTCSeekableSourceHandler","[addsource]"),this.media.controls=!0,this.media.classList.add("red5pro-media"),ce.hasAttributeDefined(this.media,"controls")&&ce.hasClassDefined(this.media,"red5pro-media")&&(this.media.classList.add("red5pro-media"),this.holder=this._determineHolder(this.media));var t=new S,n=e.controls,r=ce.hasAttributeDefined(this.media,"muted");return n||this._usePlaybackControls?(this._controls=n?e.controls:new kn(this,this.holder),this.media.controls=!1,this._controls.setAsVOD(this.isSeekable),this._controls.setMutedState(r)):this.media.controls=!1,this._addPlaybackHandlers(this._playbackNotificationCenter),t.resolve(),t.promise}},{key:"connect",value:function(){p("RTCSeekableSourceHandler","[connect]")}},{key:"attemptAutoplay",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.play().catch((function(n){t?(e.mute(),e.play().then((function(){e.trigger(new Ft(wt.AUTO_PLAYBACK_MUTED,void 0,{element:e.media}))})).catch((function(t){e.trigger(new Ft(wt.AUTO_PLAYBACK_FAILURE,void 0,{error:t.message?t.message:t,element:e.media}))}))):e.trigger(new Ft(wt.AUTO_PLAYBACK_FAILURE,void 0,{error:n.message?n.message:n,element:e.media}))}))}},{key:"enableLiveSeek",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,o=this.getControls();if(o&&o.setSeekTime(1,1),this.subscriberId=t,this.hlsElement=n||this._generateHLSLivePlayback(this.holder,this.media,this.subscriberId),this._showHLSLivePlayback(this.isHLSPlaybackActive,this.hlsElement,this.media,this.holder),r){this._addSeekableHandlers(this.hlsElement,null,!1);var i=document.createElement("source");i.src=e,this.hlsElement.appendChild(i),this._loadManifest(e)}else{var a=this.hlsOptions,s=this._hlsjsRef?new this._hlsjsRef(a):ce.createHLSClient(a);this._addSeekableHandlers(this.hlsElement,s),s.attachMedia(this.hlsElement),s.on(ce.getHLSClientEventEnum().MEDIA_ATTACHED,(function(){s.loadSource(e)})),this.hls=s}ce.setGlobal("r5pro_media_element",this.media),ce.setGlobal("r5pro_hls_element",this.hlsElement),ce.setGlobal("r5pro_hls_control",this.hls)}},{key:"switchLiveSeek",value:function(e){this.hls&&(this.hls.detachMedia(),this.hls=void 0),this.enableLiveSeek(e,this.subscriberId,this.hlsElement),this.seekTo(1);try{this.media.play()}catch(e){v("RTCSeekableSourceHandler","[videoelement:action] play (FAULT) - "+e.message)}}},{key:"play",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];p("RTCSeekableSourceHandler","[videoelement:action] play");var t=new S;try{var n;(n=e&&this.hlsElement&&this.hlsElement.paused?this.hlsElement.play():this.media.play())?n.then((function(){p("RTCSeekableSourceHandler","[videoelement:action] play (START)"),t.resolve()})).catch(t.reject):(p("RTCSeekableSourceHandler","[videoelement:action] play (START)"),t.resolve())}catch(e){m("RTCSeekableSourceHandler","[videoelement:action] play (FAULT) - "+e.message),t.reject(e)}return t.promise}},{key:"pause",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];p("RTCSeekableSourceHandler","[videoelement:action] pause");try{e&&t&&this.hlsElement?(this.hlsElement.pause(),this.media.pause()):e&&!this.hlsElement.paused&&this.hlsElement?this.hlsElement.pause():this.media.pause()}catch(e){v("RTCSeekableSourceHandler","[videoelement:action] pause (CATCH::FAULT) - "+e.message)}}},{key:"resume",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];p("RTCSeekableSourceHandler","[videoelement:action] resume");try{var t=this.isHLSPlaybackActive&&this.hlsElement?this.hlsElement.play():this.media.play();e&&this.isHLSPlaybackActive&&this.media.play().catch((function(e){return v("RTCSeekableSourceHandler","[videoelement:action] play (CATCH::FAULT) "+(e.message?e.message:e))})),t&&t.then((function(){return p("RTCSeekableSourceHandler","[videoelement:action] play (START)")})).catch((function(e){return v("RTCSeekableSourceHandler","[videoelement:action] play (CATCH::FAULT) "+(e.message?e.message:e))}))}catch(e){v("RTCSeekableSourceHandler","[videoelement:action] resume (CATCH::FAULT) - "+e.message)}}},{key:"stop",value:function(){p("RTCSeekableSourceHandler","[videoelement:action] stop");try{this.hlsElement&&this.hlsElement.stop(),this.media.stop()}catch(e){}}},{key:"mute",value:function(){this.hlsElement&&(this.hlsElement.muted=this.isHLSPlaybackActive),this.media.muted=!0;var e=this.getControls();e&&e.setMutedState(!0)}},{key:"unmute",value:function(){this.hlsElement?(this.hlsElement.muted=!this.isHLSPlaybackActive,this.media.muted=this.isHLSPlaybackActive):this.media.muted=!1;var e=this.getControls();e&&e.setMutedState(!1)}},{key:"setVolume",value:function(e){this.unmute(),this.hlsElement&&this.isHLSPlaybackActive?this.hlsElement.volume=e:this.media.volume=e}},{key:"seekTo",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(this.isSeekable)if(this._controls&&this._controls.setSeekTime(e,t),this.trigger(new Ft(wt.SEEK_CHANGE,void 0,{seek:e,duration:t})),this.hlsElement&&e<1)try{this.hlsElement.classList.remove("hidden"),this.hlsElement.currentTime=this.hlsElement.duration*e,this._isFragLoading=!0,this._showHLSLivePlayback(!0,this.hlsElement,this.media,this.holder),this.media.paused||(p("RTCSeekableSourceHandler","[hlsvod:action] play (START) - (seekTo)"),this.play(!0))}catch(e){v("RTCSeekableSourceHandler","[hlsvod:action] play (CATCH::FAULT) - "+e.message)}else this.hlsElement&&e>=1&&(this._isFragLoading=!1,this._showHLSLivePlayback(!1,this.hlsElement,this.media,this.holder));else this.media.currentTime=t?e*t:e}},{key:"toggleFullScreen",value:function(){try{this.holder&&ce.toggleFullScreen(this.holder)}catch(e){throw e}}},{key:"unpublish",value:function(){if(this._pendingUnpublish||!this.isHLSPlaybackActive)try{this.stop(),this.media.onended.call(this.media)}catch(e){}finally{this._pendingUnpublish=!1}else this._pendingUnpublish=!0}},{key:"disconnect",value:function(){this._cleanUp()}},{key:"handleOrientationChange",value:function(e){this._controls&&e%180!=0&&(this.holder&&this.holder.classList.add("red5pro-media-background"),this.media.classList.remove("red5pro-media-background"))}},{key:"addSharedObjectResponseHandler",value:function(e){}},{key:"removeSharedObjectResponseHandler",value:function(e){}},{key:"sendToSharedObject",value:function(e,t,n){}},{key:"sendPropertyToSharedObject",value:function(e,t,n){}},{key:"getRemoteSharedObject",value:function(e){}},{key:"connectToSharedObject",value:function(e){}},{key:"closeSharedObject",value:function(e){}},{key:"getControls",value:function(){return this._controls}},{key:"getType",value:function(){return this.playerType}},{key:"isVOD",get:function(){return!1},set:function(e){}},{key:"isSeekable",get:function(){return this._isSeekable},set:function(e){this._isSeekable=e,this._controls&&this._controls.setAsVOD(e)}},{key:"isHLSPlaybackActive",get:function(){return this._isHLSPlaybackActive}}])&&Fn(t.prototype,n),r&&Fn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),s}(Cn);function zn(e){return(zn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Jn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==zn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==zn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===zn(i)?i:String(i)),r)}var o,i}function Kn(e,t){return(Kn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function qn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qn(e);if(t){var o=Qn(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Xn(this,n)}}function Xn(e,t){if(t&&("object"===zn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Qn(e){return(Qn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var $n=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Kn(e,t)}(i,e);var t,n,r,o=qn(i);function i(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(t=o.call(this))._name=e||"MessageTransport",t._responseHandlers=[],t._asyncTickets=[],t}return t=i,(n=[{key:"getJsonFromSocketMessage",value:function(e){try{return"string"==typeof e.data?JSON.parse(e.data):e.data}catch(t){v(this._name,"Could not parse message as JSON. Message= "+e.data+". Error= "+t.message)}return null}},{key:"addResponseHandler",value:function(e){this._responseHandlers.push(e)}},{key:"respond",value:function(e){v(this._name,"respond() should be overriden.")}},{key:"post",value:function(e){v(this._name,"post() should be overriden.")}},{key:"postAsync",value:function(e){var t=new S,n=Math.floor(65536*Math.random()).toString(16);return e.id=n,e.async=!0,this._asyncTickets.push({id:n,promise:t}),this.post(e),t.promise}},{key:"addSharedObjectResponseHandler",value:function(e){this._responseHandlers.push(e)}},{key:"removeSharedObjectResponseHandler",value:function(e){for(var t=this._responseHandlers.length;--t>-1;)if(this._responseHandlers[t]===e)return void this._responseHandlers.splice(t,1)}},{key:"handleMessageResponse",value:function(e){var t,n=this._responseHandlers.length;for(t=0;t<n;t++)if(this._responseHandlers[t].respond(e))return!0;return!1}},{key:"getRemoteSharedObject",value:function(e){this.post({sharedObjectGetRemote:{name:e}})}},{key:"connectToSharedObject",value:function(e){this.post({sharedObjectConnect:{name:e}})}},{key:"sendToSharedObject",value:function(e,t,n){this.post({sharedObjectSend:{name:e,method:t,message:n}})}},{key:"sendPropertyToSharedObject",value:function(e,t,n){this.post({sharedObjectSetProperty:{name:e,key:t,value:n}})}},{key:"closeSharedObject",value:function(e){this.post({sharedObjectClose:{name:e}})}}])&&Jn(t.prototype,n),r&&Jn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(H);function Zn(e){return(Zn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function er(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Zn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Zn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Zn(i)?i:String(i)),r)}var o,i}function tr(e,t){return(tr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function nr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ir(e);if(t){var o=ir(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return rr(this,n)}}function rr(e,t){if(t&&("object"===Zn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return or(e)}function or(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ir(e){return(ir=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ar=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tr(e,t)}(i,e);var t,n,r,o=nr(i);function i(e,t){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(n=o.call(this,t||"R5ProSocketProxy"))._responder=e,n._pendingPostRequests=[],n._websocket=void 0,n._connectionPromise=void 0,n._responseHandlers=[],n._isTerminated=!1,n._retryCount=0,n._retryLimit=1,n._readyCheckCount=0,n._readyCheckLimit=10,n._openState=0,n._onclose=n.tearDown.bind(or(n)),n._onopenTimeout=0,n}return t=i,(n=[{key:"_resetOnopenTimeout",value:function(e,t){var n=this,r=setTimeout((function(){if(clearTimeout(r),1===e.readyState){for(h(n._name,"[websocketopen]"),n._openState=1;n._pendingPostRequests.length>0;)n.post(n._pendingPostRequests.shift());n._responder&&n._responder.onSocketOpen&&n._responder.onSocketOpen(),n.trigger(new Bt(kt.OPEN,n._name,{socket:n}))}else 0===e.readyState?++n._readyCheckCount>n._readyCheckLimit?(v(n._name,"WebSocket connection issue. We have waited for ".concat(n._readyCheckCount-1," samples, without any connection.")),n.clearRetry(),t.reject({type:"Timeout"}),n.tearDown()):(h(n._name,"WebSocket connection is still opening, will let it continue (".concat(n._readyCheckCount,")...")),n._onopenTimeout=n._resetOnopenTimeout(e,t)):h(n._name,"WebSocket connection attempts have ended with state (".concat(e.readyState,")."))}),500);return r}},{key:"_removeSocketHandlers",value:function(e){e&&(e.onopen=void 0,e.onmessage=void 0,e.onerror=void 0,e.onclose=void 0)}},{key:"_addSocketHandlers",value:function(e,t){var n=this;this._openState=0,this._readyCheckCount=0,clearTimeout(this._onopenTimeout),this._onopenTimeout=this._resetOnopenTimeout(e,t),e.onerror=function(e){v(n._name,"[websocketerror]: Error from WebSocket. ".concat(e.type,".")),n.clearRetry(),t.reject(e),n.trigger(new Bt(kt.ERROR,n._name,{socket:n,error:e}))},e.onmessage=function(e){n.respond(e)},e.onclose=function(t){t.code>1e3?v(n._name,"[websocketclose]: ".concat(t.code)):p(n._name,"[websocketclose]: ".concat(t.code)),n._responder&&n._responder.onSocketClose&&n._responder.onSocketClose(t),n.clearRetry(),n._removeSocketHandlers(e||n._websocket),n._openState=0,n.trigger(new Bt(kt.CLOSE,n._name,{socket:n,event:t}))}}},{key:"_onUnexpectedSocketError",value:function(e){this._responder&&this._responder.onSocketClose&&this._responder.onSocketClose(e),this.trigger(new Bt(kt.CLOSE,this._name,{socket:this})),v(this._name,"[websocketerror]: Possible Unexpected Error from WebSocket. ".concat(e.type,", ").concat(e.detail)),this.clearRetry(),this._removeSocketHandlers(this._websocket)}},{key:"clearRetry",value:function(){this._retryCount=0,this._readyCheckCount=0,clearTimeout(this._onopenTimeout)}},{key:"setUp",value:function(e,t){var n=this,r=ce.getIsMoz()||ce.getIsEdge();if(p(this._name,"[websocket:setup] ".concat(e,".")),this.tearDown(),this._isTerminated=!1,this._connectionPromise=t,ce.addCloseHandler(this._onclose),this._websocket=function(e){return ce.createWebSocket(e)}(e),this._addSocketHandlers(this._websocket,this._connectionPromise),r&&this._retryCount++<this._retryLimit){p(this._name,"We have determined it is Firefox and are setting up a retry limit.");var o=setTimeout((function(){n._websocket&&0===n._websocket.readyState&&(p(n._name,"[websocket:FF-timeout]"),p(n._name,"Our connection on Firefox to the wss endpoint has timed out. Let's try that again."),n._removeSocketHandlers(n._websocket),n.setUp(e,t)),clearTimeout(o)}),2e3)}}},{key:"setUpWithSocket",value:function(e,t){p(this._name,"[websocket:setupWithSocket] ".concat(e.url,".")),this.tearDown(),this._isTerminated=!1,this._connectionPromise=t,ce.addCloseHandler(this._onclose),this._websocket=e,this._addSocketHandlers(this._websocket,this._connectionPromise)}},{key:"sever",value:function(e){this._websocket&&(p(this._name,"[websocket:sever]"),e&&this.post(e),this._removeSocketHandlers(this._websocket),this.tearDown())}},{key:"tearDown",value:function(){if(this._pendingPostRequests.length=0,void 0!==this._websocket&&!this._isTerminated){p(this._name,"[teardown] >>"),p(this._name,"[WebSocket(".concat(this._websocket.url,")] close() >>"));try{this._websocket.close()}catch(e){v(this._name,"Attempt to close WebSocket failed: ".concat(e.message,".")),this._removeSocketHandlers(this._websocket)}finally{this._websocket&&p(this._name,"<< [WebSocket(".concat(this._websocket.url,")] close()"))}p(this._name,"<< [teardown]")}for(this._websocket=void 0,this._isTerminated=!0,this._openState=0;this._responseHandlers.length>0;)this._responseHandlers.shift();ce.removeCloseHandler(this._onclose)}},{key:"postEndOfCandidates",value:function(e){this.post({handleCandidate:e,data:{candidate:{type:"candidate",candidate:""}}})}},{key:"post",value:function(e){if(void 0===this._websocket||1!==this._openState)return(void 0===this._websocket||2!==this._websocket.readyState&&3!==this._websocket.readyState)&&!this._isTerminated&&(this._pendingPostRequests.push(e),!0);try{return p(this._name,"[websocket-post]: "+JSON.stringify(e,null,2)),this._websocket.send(JSON.stringify(e)),!0}catch(t){return p(this._name,"Could not send request: ".concat(e,". ").concat(t)),!1}}},{key:"respond",value:function(e){var t=this.handleMessageResponse(e);if(!t&&e.data){var n=this.getJsonFromSocketMessage(e);if(null===n)return v(this._name,"Determined websocket response not in correct format. Aborting message handle."),!0;if(p(this._name,"[websocket-response]: "+JSON.stringify(n,null,2)),void 0!==n.isAvailable)return"boolean"==typeof n.isAvailable&&n.isAvailable?(this._responder&&this._responder.onStreamAvailable(n),!0):(this._responder&&this._responder.onStreamUnavailable(n),!0);if(n.async&&n.id){var r=this._asyncTickets.find((function(e){return e.id===n.id})).promise;r&&n.data?r.resolve(n.data):r&&n.error&&r.reject(n.error)}else if(void 0!==n.data){var o=n.data;if(void 0!==o.message){if("error"===o.type&&this._responder)return this._responder.onSocketMessageError(o.message,o.detail),!0}else if("status"===o.type){if("NetConnection.Connect.Success"===o.code)return this._websocket.onerror=this._onUnexpectedSocketError.bind(this),this._connectionPromise.resolve(this),!0;if("NetConnection.DataChannel.Available"===o.code)return this._responder.onDataChannelAvailable(o.description),!0;if("NetConnection.Connect.Rejected"===o.code)return this._connectionPromise.reject("NetConnection.Connect.Rejected"),!0}else if("error"===o.type){if("NetConnection.Connect.Rejected"===o.code)return this._connectionPromise.reject("NetConnection.Connect.Rejected"),!0;if("NetConnection.Connect.Failed"===o.code)return this._connectionPromise.reject("NetConnection.Connect.Failed"),!0}}}return t}},{key:"isTerminated",get:function(){return this._isTerminated}}])&&er(t.prototype,n),r&&er(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}($n);function sr(e){return(sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function cr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ur(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cr(Object(n),!0).forEach((function(t){lr(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function lr(e,t,n){return(t=fr(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function dr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,fr(r.key),r)}}function fr(e){var t=function(e,t){if("object"!==sr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==sr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===sr(t)?t:String(t)}function hr(){return(hr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=pr(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function pr(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=br(e)););return e}function vr(e,t){return(vr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=br(e);if(t){var o=br(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return yr(this,n)}}function yr(e,t){if(t&&("object"===sr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function br(e){return(br=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var gr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vr(e,t)}(i,e);var t,n,r,o=mr(i);function i(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),o.call(this,e,"R5ProSubscriptionSocket")}return t=i,(n=[{key:"respond",value:function(e){if(e.data){var t=this.getJsonFromSocketMessage(e);if(!hr(br(i.prototype),"respond",this).call(this,e)){var n=t.data;if(void 0!==n){if(void 0!==t.data.sdp&&"offer"===t.data.sdp.type)return this._responder.onSDPOffer(t.data),!0;if(void 0!==t.data.candidate)return we(t.data.candidate)?(this._responder.onEmptyCandidate(),!0):(this._responder.onAddIceCandidate(t.data.candidate),!0);if("status"===t.data.type)return"NetConnection.ICE.TricleCompleted"===t.data.code||"NetConnection.ICE.TrickleCompleted"===t.data.code?(this._responder.onSocketIceCandidateEnd(),!0):"NetStream.Play.UnpublishNotify"===t.data.code?(this._responder.onUnpublish(),!0):"NetConnection.Connect.Closed"===t.data.code?(this._responder.onConnectionClosed(),!0):(this._responder.onSubscriberStatus(t.data),!0);if(t.data.hasOwnProperty("status")&&"NetStream.Play.UnpublishNotify"===t.data.status)return this._responder.onUnpublish(),!0;if("result"===n.type&&"Stream switch: Success"===n.message)try{return this._responder.onStreamSwitchComplete(),!0}catch(e){}if(void 0!==t.type){if("metadata"===t.type)return void 0!==t.method?(this._responder.onSendReceived(t.method,t.data),!0):(this._responder.onMetaData(t.data),!0)}else if(void 0!==t.send){var r=t.send,o=t.senderName,a=t.dcLabel,s=r.data,c=r.method;return s=ur(ur({},s),{},{senderName:o,dcLabel:a}),this._responder.onSendReceived(c,s),!0}}else if(void 0!==t.type&&"metadata"===t.type)return this._responder.onMetaData(t.metadata),!0;this._responder.onSocketMessage(this,e)}}else v("R5ProSubscriptionSocket","[ws.onmessage] - No Message Data.")}}])&&dr(t.prototype,n),r&&dr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(ar);function _r(e){return(_r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function wr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Sr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wr(Object(n),!0).forEach((function(t){Er(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Er(e,t,n){return(t=Or(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Cr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Or(r.key),r)}}function Or(e){var t=function(e,t){if("object"!==_r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==_r(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===_r(t)?t:String(t)}function kr(e,t){return(kr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Pr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=jr(e);if(t){var o=jr(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Tr(this,n)}}function Tr(e,t){if(t&&("object"===_r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Rr(e)}function Rr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function jr(e){return(jr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Ar=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&kr(e,t)}(i,e);var t,n,r,o=Pr(i);function i(e,t){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(n=o.call(this,t||"R5ProWebRTCPeer"))._responder=e,n._dataChannel=void 0,n._peerConnection=void 0,n._onDataChannelMessage=n._onDataChannelMessage.bind(Rr(n)),n}return t=i,(n=[{key:"_removeDataChannelHandlers",value:function(e){e.onopen=void 0,e.onerror=void 0,e.onclose=void 0,e.onmessage=void 0}},{key:"_addDataChannelHandlers",value:function(e){var t=this;e.onerror=function(n){t._responder.onDataChannelError(e,n.error.message)},e.onmessage=this._onDataChannelMessage,e.onopen=function(){t._responder.onDataChannelOpen(e)},e.onclose=function(n){t._responder.onDataChannelClose(e),t.trigger(new Bt(kt.CLOSE,t._name,{socket:t,event:n}))}}},{key:"_removeConnectionHandlers",value:function(e){v(this._name,"_removeConnectionHandlers is abstract.")}},{key:"_addConnectionHandlers",value:function(e,t){v(this._name,"_addConnectionHandlers is abstract.")}},{key:"_onDataChannelMessage",value:function(e){var t=e;if(this.handleMessageResponse(t))return!0;var n=this.getJsonFromSocketMessage(t);if(null===n)return v(this._name,"Determined websocket response not in correct format. Aborting message handle."),!0;p(this._name,"[datachannel-response]: "+JSON.stringify(n,null,2));var r=n.data,o=n.method,i=n.send,a=n.type,s=n.id;if(r&&r.message&&"error"===r.type)return this._responder.onDataChannelError(this._dataChannel,r.message),!0;if(r&&r.code&&"error"===r.type)return this._responder.onDataChannelError(this._dataChannel,r.code),!0;if(o)return this._responder.onSendReceived(o,r),!0;if(i){var c=n.senderName,u=n.dcLabel,l=i.data,d=i.method;l=Sr(Sr({},l),{},{senderName:c,dcLabel:u}),this._responder.onSendReceived(d,l)}else{if("metadata"===a)return this._responder.onMetaData(r),!0;if(r&&"status"===r.type){if("NetConnection.Connect.Closed"===r.code)return this._responder.onConnectionClosed(),!0;p("R5ProWebRTCPeer","[datachannel.message] status :: ".concat(r.code))}else if(n.async&&s){var f=this._asyncTickets.find((function(e){return e.id===s})).promise;if(f&&n.data)return f.resolve(n.data),!0;if(f&&n.error)return f.reject(n.error),!0}}return!1}},{key:"setUp",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;this.tearDown();var r=t||new S;try{var o={iceServers:e,iceCandidatePoolSize:2,bundlePolicy:"max-bundle"};void 0!==n&&(o.rtcpMuxPolicy=n),p("R5ProWebRTCPeer","[peerconnection:setup]: ".concat(JSON.stringify(o,null,2)));var i=new ht(o,{optional:[{RtpDataChannels:!1},{googCpuOveruseDetection:!0}]});this._addConnectionHandlers(i),this._peerConnection=i,r.resolve(i)}catch(e){v("R5ProWebRTCPeer","Could not establish a PeerConnection. ".concat(e.message)),r.reject(e.message)}return r.hasOwnProperty("promise")?r.promise:r}},{key:"setUpWithPeerConfiguration",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;this.tearDown();var r=n||new S;try{p("R5ProWebRTCPeer","[peerconnection:setUpWithPeerConfiguration]: ".concat(JSON.stringify(e,null,2)));var o=new ht(e);t&&(this._dataChannel=o.createDataChannel(t.name,{ordered:!0}),this._addDataChannelHandlers(this._dataChannel)),this._addConnectionHandlers(o),this._peerConnection=o,r.resolve(o)}catch(e){v("R5ProWebRTCPeer","Could not establish a PeerConnection. ".concat(e.message)),r.reject(e.message)}return r.hasOwnProperty("promise")?r.promise:r}},{key:"tearDown",value:function(){if(this._dataChannel){this._removeDataChannelHandlers(this._dataChannel);try{this._dataChannel.close()}catch(e){v("R5ProWebRTCPeer","[datachannel.close] error: ".concat(e.message))}finally{this._dataChannel=void 0}}if(this._peerConnection){p("R5ProWebRTCPeer","[teardown]"),this._removeConnectionHandlers(this._peerConnection);try{this._peerConnection.close()}catch(e){v("R5ProWebRTCPeer","[peerconnection.close] error: ".concat(e.message))}finally{this._peerConnection=void 0}}}},{key:"setLocalDescription",value:function(e){return p("R5ProWebRTCPeer","[setlocaldescription]"),this._peerConnection.setLocalDescription(e)}},{key:"setRemoteDescription",value:function(e){return p("R5ProWebRTCPeer","[setremotedescription]"),this._peerConnection.setRemoteDescription(new vt(e))}},{key:"addIceCandidate",value:function(e){return p("R5ProWebRTCPeer","[addcandidate]"),this._peerConnection.addIceCandidate(e)}},{key:"post",value:function(e){if(this._dataChannel){var t="string"==typeof e?e:JSON.stringify(e,null,2);p("R5ProWebRTCPeer","[datachannel.send] message: ".concat(t));try{return this._dataChannel.send(t),!0}catch(e){m("R5ProWebRTCPeer",e.hasOwnProperty("message")?e.message:e)}}return!1}},{key:"connection",get:function(){return this._peerConnection}},{key:"dataChannel",get:function(){return this._dataChannel}}])&&Cr(t.prototype,n),r&&Cr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}($n);function Lr(e){return(Lr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Nr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Lr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Lr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Lr(i)?i:String(i)),r)}var o,i}function Hr(){return(Hr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Ir(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function Ir(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Fr(e)););return e}function xr(e,t){return(xr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Dr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Fr(e);if(t){var o=Fr(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Mr(this,n)}}function Mr(e,t){if(t&&("object"===Lr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Fr(e){return(Fr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Ur=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xr(e,t)}(i,e);var t,n,r,o=Dr(i);function i(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),o.call(this,e,"R5ProSubscriptionPeer")}return t=i,(n=[{key:"_removeConnectionHandlers",value:function(e){e.onconnectionstatechange=void 0,e.oniceconnectionstatechange=void 0,e.onicecandidate=void 0,e.ontrack=void 0,e.ondatachannel=void 0}},{key:"_addConnectionHandlers",value:function(e,t){var n,r=this;e.onconnectionstatechange=function(){p("R5ProSubscriptionPeer","[peer.onconnectionstatechange] - State: ".concat(e.connectionState)),"connected"===e.connectionState?(p("R5ProSubscriptionPeer","[peerconnection:open]"),t?t.resolve(r):r._responder.onPeerConnectionOpen()):"failed"!==e.connectionState&&"disconnected"!==e.connectionState||(v("R5ProSubscriptionPeer","[peerconnection:error]"),t&&t.reject())},e.onicecandidate=function(e){p("R5ProSubscriptionPeer","[peer.onicecandidate] - Peer Candidate: ".concat(e.candidate)),e.candidate?r._responder.onIceCandidate(e.candidate):null===e.candidate&&r._pendingMediaStream&&(r._responder.onIceCandidateTrickleEnd(r._pendingMediaStream),r._pendingMediaStream=void 0)},e.ontrack=function(e){p("R5ProSubscriptionPeer","(ontrack) Peer Add Stream: ".concat(e.streams)),e.streams&&e.streams.length>0&&void 0===r._pendingMediaStream&&(r._pendingMediaStream=e.streams[0],r._responder.onAnswerMediaStream(e.streams[0]))},e.oniceconnectionstatechange=function(t){var o=e.iceConnectionState;p("R5ProSubscriptionPeer","[peer.oniceconnectionstatechange] - State: ".concat(o)),"connected"===o&&ce.getIsEdge()?(p("R5ProSubscriptionPeer","[edge/ortc:notify complete]"),r._responder.onPeerGatheringComplete(),e.onicecandidate({candidate:null})):"failed"===o?(n&&clearTimeout(n),r._responder.onPeerConnectionFail(),r._responder.onPeerConnectionClose(t)):"disconnected"===o?n=setTimeout((function(){p("R5ProSubscriptionPeer","[peer.oniceconnectionstatechange] - Reconnect timeout reached. Closing PeerConnection."),clearTimeout(n),r._responder.onPeerConnectionClose(t)}),5e3):n&&(p("R5ProSubscriptionPeer","[peer.oniceconnectionstatechange] - Clearing timeout for reconnect."),clearTimeout(n))},e.onicegatheringstatechange=function(){var t=e.iceGatheringState;p("R5ProSubscriptionPeer","[peer.onicegatheringstatechange] - State: ".concat(t)),"complete"===t&&r._responder.onPeerGatheringComplete()},e.onremovestream=function(){p("R5ProSubscriptionPeer","[peer.onremovestream]")}}},{key:"_onDataChannelMessage",value:function(e){var t=e;if(Hr(Fr(i.prototype),"_onDataChannelMessage",this).call(this,e))return!0;var n=this.getJsonFromSocketMessage(t);if(null===n)return v(this._name,"Determined websocket response not in correct format. Aborting message handle."),!0;p(this._name,"[datachannel-response]: "+JSON.stringify(n,null,2));var r=n.data;if(r&&"status"===r.type)return"NetStream.Play.UnpublishNotify"===r.code?(this._responder.onUnpublish(),this._responder.onConnectionClosed(),!0):(p("R5ProSubscriptionPeer","[datachannel.message] status :: ".concat(r.code)),this._responder.onSubscriberStatus(r),!0);if(r&&r.status&&"NetStream.Play.UnpublishNotify"===r.status)return this._responder.onUnpublish(),this._responder.onConnectionClosed(),!0;if(r&&"result"===r.type&&"Stream switch: Success"===r.message)try{return this._responder.onStreamSwitchComplete(),!0}catch(e){}return this._responder.onDataChannelMessage(this._dataChannel,t),!1}},{key:"createAnswer",value:function(e){var t=this;p("R5ProSubscriptionPeer","[createanswer]");var n=new S;return this._peerConnection.setRemoteDescription(e).then(this._responder.onSDPSuccess).catch((function(e){t._responder.onSDPError(e)})),this._peerConnection.createAnswer().then((function(e){e.sdp=ot(e.sdp),t._peerConnection.setLocalDescription(e).then(t._responder.onSDPSuccess).catch((function(e){t._responder.onSDPError(e)})),n.resolve(e)})).catch(n.reject),n.promise}},{key:"addIceCandidate",value:function(e){if(p("R5ProSubscriptionPeer","checking if empty..."),function(e){return void 0===e||"string"==typeof e&&0===e.length}(e))p("R5ProSubscriptionPeer","[addicecandidate]:: empty");else if(null!==e){p("R5ProSubscriptionPeer","[addicecandidate] :: non-empty");var t=new pt({sdpMLineIndex:e.sdpMLineIndex,candidate:e.candidate});this._peerConnection.addIceCandidate(t).then((function(){})).catch((function(e){m("R5ProSubscriptionPeer","Error in add of ICE Candidiate + ".concat(e))}))}else p("R5ProSubscriptionPeer","[addicecandidate] :: null"),this._peerConnection.addIceCandidate(e).then((function(){})).catch((function(e){m("R5ProSubscriptionPeer","Error in add of ICE Candidiate + ".concat(e))}))}}])&&Nr(t.prototype,n),r&&Nr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Ar);function Br(e){return(Br="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Vr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Gr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Br(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Br(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Br(i)?i:String(i)),r)}var o,i}var Wr=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"red5pro-subscriber";Vr(this,e);try{this._targetElement=ce.resolveElement(t)}catch(e){throw m("R5ProPlaybackView","Could not instantiate a new instance of Red5ProSubscriber. Reason: ".concat(e.message)),e}}var t,n,r;return t=e,(n=[{key:"attachSubscriber",value:function(e){p("R5ProPlaybackView","[attachsubscriber]"),e.setView(this,ce.getElementId(this._targetElement))}},{key:"attachStream",value:function(e){var t=this.isAutoplay;p("R5ProPlaybackView","[attachstream]"),ce.setVideoSource(this._targetElement,e,t)}},{key:"detachStream",value:function(){p("R5ProPlaybackView","[detachstream]"),ce.setVideoSource(this._targetElement,null,this.isAutoplay)}},{key:"isAutoplay",get:function(){return ce.hasAttributeDefined(this._targetElement,"autoplay")}},{key:"view",get:function(){return this._targetElement}}])&&Gr(t.prototype,n),r&&Gr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Yr=function(e){switch(e){case 8083:case"8083":return console.warn("The default WebSocket port on the server has changed from 8083 to 443 for secure connections."),443;case 8081:case"8081":return console.warn("The default WebSocket port on the server has changed from 8081 to 5080 or 80 for secure connections."),5080}return e},zr=function(e){var t={};return Object.keys(e).forEach((function(n,r){t[n]=encodeURIComponent(e[n])})),t},Jr=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=e.wsprotocol||e.protocol,r=Yr(e.wsport||e.port),o=e.context?[e.app,e.context].join("/"):e.app,i="".concat(n,"://").concat(e.host,":").concat(r,"/").concat(o,"/");if(void 0!==e.connectionParams){var a=zr(e.connectionParams);t=Object.assign(t,a)}if(void 0!==t){var s=[];Object.keys(t).forEach((function(e,n){s.push([e,t[e]].join("="))})),s.length>0&&(i+="?"+s.join("&"))}return i},Kr=function(e){var t=e.host,n=e.hlsprotocol,r=e.protocol,o=e.hlsport,i=e.port,a=e.context,s=e.app,c=e.streamName,u=e.connectionParams,l=e.apiVersion,d=t,f=n||("ws"===r?"http":"https"),h=o||(5080===i?5080:443),p=a?[s,a].join("/"):s,v=l||"4.0";return u&&"streammanager"===s?"".concat(f,"://").concat(d,":").concat(h,"/streammanager/api/").concat(v,"/file/").concat(u.app,"/").concat(c,".m3u8"):"".concat(f,"://").concat(d,":").concat(h,"/").concat(p,"/").concat(c,".m3u8")},qr=function(e){var t,n,r=new URL(e),o=r.pathname.split("/").filter((function(e){return e.length>0}));return n=r.protocol,t=r.hostname,{protocol:n,port:r.port.length>0?r.port:443,app:o[0],host:t,streamName:o[o.length-1]}},Xr=function(e,t,n){var r=e.host,o=e.hlsprotocol,i=e.protocol,a=e.hlsport,s=e.port,c=e.context,u=e.app,l=e.streamName,d=r,f=o||("ws"===i?"http":"https"),h=a||(5080===s?5080:443),p=c?[u,c].join("/"):u;if(n)return n;if(t){var v=t.length-1,m="/"===t.charAt(v)?t.substr(0,v):t;return"".concat(m,"/").concat(p,"/").concat(l,".m3u8")}return"".concat(f,"://").concat(d,":").concat(h,"/").concat(p,"/").concat(l,".m3u8")},Qr=Object.freeze({RTC:"rtc",RTMP:"rtmp",HLS:"hls"}),$r=Object.freeze({OPUS:"Opus",PCMU:"PCMU",PCMA:"PCMA",SPEEX:"Speex",NONE:"NONE"}),Zr=Object.freeze({VP8:"VP8",H264:"H264",NONE:"NONE"}),eo=Object.freeze({UDP:"udp",TCP:"tcp"}),to=Object.freeze({ENCODED_FRAME:"EncodedFrame",PACKET:"Packet"}),no=Object.freeze({VIDEO:"encodeVideo",AUDIO:"encodeAudio"}),ro=Object.freeze({VIDEO:"decodeVideo",AUDIO:"decodeAudio"});function oo(e){return(oo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function io(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */io=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==oo(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function ao(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function so(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){ao(i,r,o,a,s,"next",e)}function s(e){ao(i,r,o,a,s,"throw",e)}a(void 0)}))}}var co=function(e){p("MediaTransformPipeline",e)},uo=function(){var e=so(io().mark((function e(t,n,r){var o,i,a,s,c,u,l;return io().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=t.video,i=t.worker,a=t.workerOperationType,s=t.transformFrameType,c=t.pipeOptions,u=s||to.BUFFER,l=a||no.VIDEO,!o){e.next=12;break}if(co("[track.insertablestream]::video"),u!==to.PACKET){e.next=9;break}return e.abrupt("return",Be(r.getVideoTracks()[0],o,c));case 9:return e.abrupt("return",Ve(n,o,c));case 10:e.next=19;break;case 12:if(!i||!i.video){e.next=19;break}if(co("[track.insertablestream]::worker(encode video)"),u!==to.PACKET){e.next=18;break}return e.abrupt("return",Me(l,r.getVideoTracks()[0],i.video,c));case 18:return e.abrupt("return",Fe(l,n,i.video,c));case 19:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}(),lo=function(){var e=so(io().mark((function e(t,n,r){var o,i,a,s,c,u,l;return io().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=t.video,i=t.worker,a=t.workerOperationType,s=t.transformFrameType,c=t.pipeOptions,u=s||to.BUFFER,l=a||ro.VIDEO,!o){e.next=12;break}if(co("[track.insertablestream]::video"),u!==to.PACKET){e.next=9;break}return e.abrupt("return",Be(r,o,c));case 9:return e.abrupt("return",Ge(n,o,c));case 10:e.next=19;break;case 12:if(!i||!i.video){e.next=19;break}if(co("[track.insertablestream]::worker(decode video)"),u!==to.PACKET){e.next=18;break}return e.abrupt("return",Me(l,r,i.video,c));case 18:return e.abrupt("return",Ue(l,n,i.video,c));case 19:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}(),fo=function(){var e=so(io().mark((function e(t,n,r){var o,i,a,s,c,u,l;return io().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=t.audio,i=t.worker,a=t.workerOperationType,s=t.transformFrameType,c=t.pipeOptions,u=s||to.BUFFER,l=a||no.AUDIO,!o){e.next=12;break}if(co("[track.insertablestream]::audio"),u!==to.PACKET||!xe){e.next=9;break}return e.abrupt("return",Be(r.getAudioTracks()[0],o,c));case 9:return e.abrupt("return",Ve(n,o,c));case 10:e.next=19;break;case 12:if(!i||!i.audio){e.next=19;break}if(co("[track.insertablestream]::worker(encode audio)"),u!==to.PACKET||!xe){e.next=18;break}return e.abrupt("return",Me(l,r.getAudioTracks()[0],i.audio,c));case 18:return e.abrupt("return",Fe(l,n,i.audio,c));case 19:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}(),ho=function(){var e=so(io().mark((function e(t,n,r){var o,i,a,s,c,u,l;return io().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=t.audio,i=t.worker,a=t.workerOperationType,s=t.transformFrameType,c=t.pipeOptions,u=s||to.BUFFER,l=a||ro.AUDIO,!o){e.next=12;break}if(co("[track.insertablestream]::audio"),u!==to.PACKET){e.next=9;break}return e.abrupt("return",Be(r,o,c));case 9:return e.abrupt("return",Ge(n,o,c));case 10:e.next=19;break;case 12:if(!i||!i.audio){e.next=19;break}if(co("[track.insertablestream]::worker(decode audio)"),u!==to.PACKET){e.next=18;break}return e.abrupt("return",Me(l,r,i.audio,c));case 18:return e.abrupt("return",Ue(l,n,i.audio,c));case 19:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}();function po(e){return(po="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function vo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function mo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?vo(Object(n),!0).forEach((function(t){yo(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):vo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function yo(e,t,n){return(t=wo(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function bo(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */bo=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==po(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function go(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function _o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,wo(r.key),r)}}function wo(e){var t=function(e,t){if("object"!==po(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==po(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===po(t)?t:String(t)}function So(e,t){return(So=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Eo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Oo(e);if(t){var o=Oo(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Co(this,n)}}function Co(e,t){if(t&&("object"===po(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Oo(e){return(Oo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ko=/(.*) starting/i,Po=function(){var e=Math.floor(65536*Math.random()).toString(16);return"subscriber-".concat(e)},To={protocol:"wss",port:443,app:"live",autoLayoutOrientation:!0,mediaElementId:"red5pro-subscriber",rtcConfiguration:{iceServers:[{urls:"stun:stun2.l.google.com:19302"}],iceCandidatePoolSize:2,bundlePolicy:"max-bundle"},iceServers:void 0,iceTransport:eo.UDP,muteOnAutoplayRestriction:!0,maintainConnectionOnSubscribeErrors:!1,signalingSocketOnly:!0,dataChannelConfiguration:void 0,socketSwitchDelay:1e3,bypassAvailable:!1,maintainStreamVariant:!1,buffer:0,liveSeek:{enabled:!1,baseURL:void 0,fullURL:void 0,hlsjsRef:void 0,hlsElement:void 0,usePlaybackControlsUI:!0,options:{debug:!1,backBufferLength:0}}},Ro=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&So(e,t)}(s,e);var t,n,r,o,i,a=Eo(s);function s(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(e=a.call(this))._view=void 0,e._options=void 0,e._peerHelper=void 0,e._socketHelper=void 0,e._messageTransport=void 0,e._connectionClosed=!0,e._sourceHandler=void 0,e._mediaStream=void 0,e._mediaTransform=void 0,e._viewResolver=new S,e._availabilityResolver=new S,e._subscriptionResolver=new S,e._orientation=0,e._streamingMode="Video/Audio",e._switchChannelRequest=void 0,e._requestedStreamSwitch=void 0,e}return t=s,(n=[{key:"_getViewResolverPromise",value:function(){return this._viewResolver.promise}},{key:"_getAvailabilityResolverPromise",value:function(){return this._availabilityResolver.promise}},{key:"_getSubscriptionResolverPromise",value:function(){return this._subscriptionResolver.promise}},{key:"_glomSourceHandlerAPI",value:function(e){var t=this;this.play=e.play.bind(e),this.pause=e.pause.bind(e),this.resume=e.resume.bind(e),this.stop=e.stop.bind(e),this.mute=e.mute.bind(e),this.unmute=e.unmute.bind(e),this.setVolume=e.setVolume.bind(e),this.seekTo=e.seekTo.bind(e),this.toggleFullScreen=e.toggleFullScreen.bind(e),e.on("*",(function(e){t.trigger(new Ft(e.type,t,e.data))}))}},{key:"_setViewIfNotExist",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(void 0===e&&void 0!==t){var n=new Wr(t);n.attachSubscriber(this)}}},{key:"_initHandler",value:function(e,t){e&&t&&(t.on("*",this._boundBubbleSubscriberEvents),t.addSource(e))}},{key:"_requestAvailability",value:function(e){p("RTCSubscriber","[requestavailability]"),this._socketHelper.post({isAvailable:e})}},{key:"_requestOffer",value:function(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:void 0;p("RTCSubscriber","[requestoffer]");var s={requestOffer:e,requestId:t,transport:n,datachannel:r,doNotSwitch:o};return void 0!==i&&i!==Zr.NONE?s.videoEncoding=i:ce.getIsEdge(),void 0!==a&&a!==$r.NONE&&(s.audioEncoding=a),this.trigger(new Ft(Et.OFFER_START,this)),this._socketHelper.post(s),!0}},{key:"_requestAnswer",value:function(e){var t=this;p("RTCSubscriber","[requestanswer]"),this._peerHelper.createAnswer(e).then((function(e){p("RTCSubscriber","[onanswercreated]"),p("RTCSubscriber","[> sendanswer]"),t._sendAnswer(t._options.streamName,t._options.subscriptionId,e)})).catch((function(e){t.onSDPError(e)}))}},{key:"_sendAnswer",value:function(e,t,n){p("RTCSubscriber","[sendanswer]: streamname(".concat(e,"), subscriptionid(").concat(t,")")),this.trigger(new Ft(Et.ANSWER_START,this,n)),this._socketHelper.post({handleAnswer:e,requestId:t,data:{sdp:n}})}},{key:"_sendCandidate",value:function(e){p("RTCSubscriber","[sendcandidate]"),this.trigger(new Ft(Et.CANDIDATE_START,this,e)),this._socketHelper.post({handleCandidate:this._options.streamName,requestId:this._options.subscriptionId,data:{candidate:e}})}},{key:"_setUpConnectionHandlers",value:function(e){var t=this;e.addEventListener("track",(function(e){p("RTCSubscriber","[peerconnection.ontrack]");var n=e.streams,r=e.track,o=e.receiver,i=e.transceiver;r.kind,o.playoutDelayHint=o.jitterBufferDelayHint=t._options.buffer,t.trigger(new Ft(Et.TRACK_ADDED,t,{streams:n,track:r,receiver:o,transceiver:i}))}))}},{key:"_setUpMediaTransform",value:(o=bo().mark((function e(t,n){var r,o,i,a,s;return bo().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return",!0);case 2:if(e.prev=2,r=n.getReceivers().find((function(e){return e.track&&"video"===e.track.kind})),o=n.getReceivers().find((function(e){return e.track&&"audio"===e.track.kind})),i=t.audio,a=t.video,s=t.worker,!r||!a&&!s){e.next=16;break}return e.prev=7,e.next=10,lo(t,r,r.track);case 10:e.sent,e.next=16;break;case 13:e.prev=13,e.t0=e.catch(7),this.trigger(new Ft(Et.TRANSFORM_ERROR,this,{type:"video",error:e.t0}));case 16:if(!o||!i&&!s){e.next=26;break}return e.prev=17,e.next=20,ho(t,o,o.track);case 20:e.sent,e.next=26;break;case 23:e.prev=23,e.t1=e.catch(17),this.trigger(new Ft(Et.TRANSFORM_ERROR,this,{type:"audio",error:e.t1}));case 26:return e.abrupt("return",!0);case 29:return e.prev=29,e.t2=e.catch(2),this.trigger(new Ft(Et.TRANSFORM_ERROR,this,{error:e.t2})),e.abrupt("return",!1);case 33:case"end":return e.stop()}}),e,this,[[2,29],[7,13],[17,23]])})),i=function(){var e=this,t=arguments;return new Promise((function(n,r){var i=o.apply(e,t);function a(e){go(i,n,r,a,s,"next",e)}function s(e){go(i,n,r,a,s,"throw",e)}a(void 0)}))},function(e,t){return i.apply(this,arguments)})},{key:"_connect",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return p("RTCSubscriber","[connect]"),r&&e&&(v("The iceServers configuration property is considered deprecated. Please use the rtcConfiguration configuration property upon which you can assign iceServers. Reference: https://www.red5pro.com/docs/streaming/migrationguide.html"),e.iceServers=r),this._options.iceServers=e?e.iceServers:r,(void 0!==e?this._peerHelper.setUpWithPeerConfiguration(e,n,void 0):this._peerHelper.setUp(this._options.iceServers,void 0,this._options.rtcpMuxPolicy)).then((function(e){return t._setUpConnectionHandlers(e),t.trigger(new Ft(Et.PEER_CONNECTION_AVAILABLE,t,e)),t._requestOffer(t._options.streamName,t._options.subscriptionId,t._options.iceTransport,t._options.signalingSocketOnly,t._options.maintainStreamVariant,t._options.videoEncoding,t._options.audioEncoding)})).catch((function(e){v("RTCSubscriber","Could not establish RTCPeerConnection."),t.trigger(new Ft(wt.CONNECT_FAILURE,t))})),this}},{key:"_disconnect",value:function(){this._socketHelper&&(p("RTCSubscriber","[disconnect:socket]"),this._socketHelper.tearDown()),this._peerHelper&&(p("RTCSubscriber","[disconnect:peer]"),this._peerHelper.tearDown()),this._view&&this._view.detachStream(),this._socketHelper=void 0,this._peerHelper=void 0,this._messageTransport=void 0,this._sourceHandler&&(p("RTCSubscriber","[disconnect:source]"),this._sourceHandler.disconnect(),this._sourceHandler=void 0),this._connectionClosed=!0}},{key:"_manageStreamMeta",value:function(e){var t,n=e.getTracks(),r=n.find((function(e){return"audio"===e.kind})),o=n.find((function(e){return"video"===e.kind}));o&&(o.muted||(t="Video")),r&&(r.muted||(t=t?"".concat(t,"/Audio"):"Audio")),t||(t="Empty"),this.onMetaData({streamingMode:t,method:"onMetaData"})}},{key:"_addStreamHandlers",value:function(e){var t=this,n=e.getTracks(),r=n.find((function(e){return"audio"===e.kind})),o=n.find((function(e){return"video"===e.kind}));o&&(o.addEventListener("mute",(function(){t._manageStreamMeta(e)})),o.addEventListener("unmute",(function(){t._manageStreamMeta(e)}))),r&&(r.addEventListener("mute",(function(){t._manageStreamMeta(e)})),r.addEventListener("unmute",(function(){t._manageStreamMeta(e)}))),this._manageStreamMeta(e)}},{key:"_playIfAutoplaySet",value:function(e,t){e&&t&&(e.autoplay=ce.hasAttributeDefined(t.view,"autoplay"),e.autoplay&&this._sourceHandler.attemptAutoplay(e.muteOnAutoplayRestriction))}},{key:"_startSeekable",value:function(e){var t=e.liveSeek,n=e.subscriptionId;if(t){var r=t.enabled,o=t.baseURL,i=t.fullURL,a=t.hlsjsRef,s=t.hlsElement;if(r)try{if(!ce.supportsHLS()&&!ce.supportsNonNativeHLS(a))throw new Error;var c=Xr(e,o,i);this._sourceHandler.enableLiveSeek(c,n,s,!ce.supportsNonNativeHLS())}catch(e){m("RTCSubscriber","Could not utilize the 'LiveSeek' request. This feature requires either native HLS playback or hls.js as a depenency.")}}}},{key:"_sendSubscribe",value:function(){p("RTCSubscriber","[sendsubscribe]"),this._socketHelper.post({subscribe:this._options.streamName,requestId:this._options.subscriptionId})}},{key:"init",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=new S;if(He()&&mt()){this._disconnect(),this._options=Object.assign({},To,e),this._options.subscriptionId=this._options.subscriptionId||Po(),this._mediaTransform=n,this._mediaTransform&&!De()&&(this.trigger(new Ft(Et.UNSUPPORTED_FEATURE,this,{feature:"Insertable Streams",message:"You provided Media Transforms for track processing, but your current browser does not support the Insertable Streams API."})),this._mediaTransform=void 0),this._peerHelper=new Ur(this),this._socketHelper=new gr(this),this._messageTransport=this._messageTransport||this._socketHelper;var o=new S,i=Jr(this._options,{id:this._options.subscriptionId});o.promise.then((function(){r.resolve(t),t._connectionClosed=!1,t.trigger(new Ft(wt.CONNECT_SUCCESS,t))})).catch((function(e){r.reject(e),t.trigger(new Ft(wt.CONNECT_FAILURE,t,e))})),this._socketHelper.setUp(i,o)}else r.reject("Cannot create WebRTC playback instance. Your environment does not support WebRTC and/or WebSockets.");return r.promise}},{key:"setView",value:function(e){return this._view=e,this._viewResolver.resolve(this._view),this}},{key:"subscribe",value:function(){var e=this,t=this._options,n=t.streamName,r=t.mediaElementId,o=t.rtcConfiguration,i=t.liveSeek,a=this._options,s=a.signalingSocketOnly,c=a.dataChannelConfiguration,u=s&&Ie();return u&&!c&&(c={name:"red5pro"}),this._options.signalingSocketOnly=u,this._getViewResolverPromise().then((function(t){if(i&&i.enabled){var n=i.hlsjsRef,r=i.usePlaybackControlsUI,o=i.options;ce.supportsNonNativeHLS(n)?e._sourceHandler=new Yn(t.view,e.getType(),o,r):(m("RTCSubscriber","Could not utilize the 'LiveSeek' request. This feature requires either native HLS playback or hls.js as a depenency."),e.trigger(new Ft(Et.LIVE_SEEK_UNSUPPORTED,e,{feature:"Live Seek",message:"Live Seek requires integration with the HLS.JS plugin in order work properly. Most likely you are viewing this on a browser that does not support the use of HLS.JS."})),e._sourceHandler=new Hn(t.view,e.getType()))}else e._sourceHandler=new Hn(t.view,e.getType());e._glomSourceHandlerAPI(e._sourceHandler),e._initHandler(e._options,e._sourceHandler)})).catch((function(){})),this._getAvailabilityResolverPromise().then((function(){var t=o;void 0===o.encodedInsertableStreams&&(t=Object.assign(o,{encodedInsertableStreams:!!e._mediaTransform})),e._connect(t,c,e._options.iceServers)})).catch((function(){})),this._setViewIfNotExist(this._view,r),this._options.bypassAvailable?this._availabilityResolver.resolve(this):this._requestAvailability(n),this._getSubscriptionResolverPromise()}},{key:"unsubscribe",value:function(){p("RTCSubscriber","[unsubscribe]");var e=new S;return this.stop(),this._disconnect(),this._mediaStream=void 0,e.resolve(this),this.trigger(new Ft(wt.SUBSCRIBE_STOP,this)),e.promise}},{key:"transform",value:function(e){!e||De()?this.getPeerConnection()?this._setUpMediaTransform(e,this.getPeerConnection()):this._mediaTransform=e:this.trigger(new Ft(wt.UNSUPPORTED_FEATURE,this,{feature:"Insertable Streams",message:"You provided Media Transforms for track processing, but your current browser does not support the Insertable Streams API."}))}},{key:"onStreamAvailable",value:function(e){p("RTCSubscriber","[onstreamavailable]: "+JSON.stringify(e,null,2)),this._availabilityResolver.resolve(this)}},{key:"onStreamUnavailable",value:function(e){p("RTCSubscriber","Stream ".concat(this._options.streamName," does not exist.")),p("RTCSubscriber","[onstreamunavailable]: "+JSON.stringify(e,null,2)),this.trigger(new Ft(wt.SUBSCRIBE_INVALID_NAME,this)),this._availabilityResolver.reject("Stream ".concat(this._options.streamName," does not exist.")),this._subscriptionResolver.reject("Stream ".concat(this._options.streamName," does not exist.")),this._options.maintainConnectionOnSubscribeErrors?(this._availabilityResolver=new S,this._subscriptionResolver=new S):this._disconnect()}},{key:"onSDPSuccess",value:function(e){p("RTCSubscriber","[onsdpsuccess]: "+JSON.stringify(e,null,2))}},{key:"onSDPOffer",value:function(e){p("RTCSubscriber","[onsdpoffer]: "+JSON.stringify(e,null,2));var t=new vt(e.sdp);this.trigger(new Ft(Et.OFFER_END,this)),this._requestAnswer(t)}},{key:"onSDPError",value:function(e){this.trigger(new Ft(wt.SUBSCRIBE_FAIL,this,e)),this._subscriptionResolver.reject("Invalid SDP."),m("RTCSubscriber","[onsdperror]"),m("RTCSubscriber",e)}},{key:"onAnswerMediaStream",value:function(){this.trigger(new Ft(Et.ANSWER_END,this))}},{key:"onIceCandidate",value:function(e){p("RTCSubscriber","[onicecandidate]"),this.trigger(new Ft(Et.CANDIDATE_END,this)),this._sendCandidate(e)}},{key:"onIceCandidateTrickleEnd",value:function(e){var t=this;p("RTCSubscriber","[onicetrickleend]"),this._getViewResolverPromise().then((function(n){n.attachStream(e),t._mediaStream=e,t._setUpMediaTransform(t._mediaTransform,t.getPeerConnection()),t.trigger(new Ft(Et.ON_ADD_STREAM,t,t._mediaStream))}))}},{key:"onAddIceCandidate",value:function(e){p("RTCSubscriber","[onaddicecandidate]"),this._peerHelper.addIceCandidate(e)}},{key:"onEmptyCandidate",value:function(){p("RTCSubscriber","[icecandidatetrickle:empty]"),this.trigger(new Ft(Et.PEER_CANDIDATE_END))}},{key:"onPeerGatheringComplete",value:function(){p("RTCSubscriber","[icecandidategathering:end]"),this._socketHelper&&this._socketHelper.postEndOfCandidates(this._options.streamName)}},{key:"onSocketIceCandidateEnd",value:function(){p("RTCSubscriber","[onsocketicecandidateend]"),this.trigger(new Ft(Et.ICE_TRICKLE_COMPLETE,this)),this._sendSubscribe()}},{key:"onSocketMessage",value:function(e,t){this.trigger(new Ft(Et.SOCKET_MESSAGE,this,{socket:e,message:t}))}},{key:"onSocketMessageError",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;m("RTCSubscriber","Error in stream subscription: ".concat(e,".\n[Optional detail]: ").concat(t)),this._subscriptionResolver.reject("Error in stream subscription: ".concat(e,".")),this.trigger(new Ft(wt.SUBSCRIBE_FAIL,this,e))}},{key:"onSocketClose",value:function(e){p("RTCSubscriber","[onsocketclose]"),this._peerHelper&&this._peerHelper.tearDown(),this.onConnectionClosed(e)}},{key:"onPeerConnectionFail",value:function(){p("RTCSubscriber","[onpeerconnectionfail]"),this.trigger(new Ft(wt.SUBSCRIBE_FAIL,this,"fail")),this._subscriptionResolver&&this._subscriptionResolver.reject("Peer Connection Failed.")}},{key:"onPeerConnectionClose",value:function(e){p("RTCSubscriber","[onpeerconnectionclose]");var t=this._options.liveSeek;this._socketHelper&&this._socketHelper.tearDown(),t&&t.enabled||this.onSocketClose(e)}},{key:"onPeerConnectionOpen",value:function(){p("RTCSubscriber","[onpeerconnectionopen]"),this.trigger(new Ft(Et.PEER_CONNECTION_OPEN),this,this.getPeerConnection())}},{key:"onUnpublish",value:function(){p("RTCSubscriber","[onunpublish]"),this.trigger(new Ft(wt.PLAY_UNPUBLISH,this));var e=this._options.liveSeek;this._sourceHandler&&this._sourceHandler.unpublish(),e&&e.enabled||this.unsubscribe()}},{key:"onConnectionClosed",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=this._options.liveSeek;if(!this._connectionClosed){if(p("RTCSubscriber","[onconnectionclosed]"),t){var n=t.enabled;n||this._disconnect()}else this._disconnect();this.trigger(new Ft(wt.CONNECTION_CLOSED,this,e))}}},{key:"onSendReceived",value:function(e,t){"onMetaData"===e?this.onMetaData(t):"onPublisherNetworkCongestion"===e?this.onPublisherNetworkCongestion(t):"onPublisherNetworkRecovery"===e?this.onPublisherNetworkRecovery(t):this.trigger(new Ft(wt.SUBSCRIBE_SEND_INVOKE,this,{methodName:e,data:t}))}},{key:"onSubscriberStatus",value:function(e){p("RTCSubscriber","[subscriberstatus] - "+JSON.stringify(e,null,2));var t=ko.exec(e.message);t&&t[1]===this._options.streamName?(this._subscriptionResolver.resolve(this),this.trigger(new Ft(wt.SUBSCRIBE_START,this)),this._playIfAutoplaySet(this._options,this._view),this._startSeekable(this._options,this._view)):this.trigger(new Ft(wt.SUBSCRIBE_STATUS,this,e))}},{key:"onDataChannelAvailable",value:function(e){var t=this;if(p("RTCSubscriber","[ondatachannel::available]"),this._switchChannelRequest={switchChannel:e||"red5pro"},this._options.signalingSocketOnly)var n=setTimeout((function(){clearTimeout(n),t._socketHelper&&t._socketHelper.sever(t._switchChannelRequest),t._messageTransport=t._peerHelper,t.trigger(new Bt(kt.CHANGE,t,{controller:t,transport:t._messageTransport}))}),this._socketHelper?this._options.socketSwitchDelay:100);this.trigger(new Ft(Et.DATA_CHANNEL_AVAILABLE,this,{name:e,dataChannel:this.getDataChannel()}))}},{key:"onDataChannelError",value:function(e,t){this.trigger(new Ft(Et.DATA_CHANNEL_ERROR,this,{dataChannel:e,error:t}))}},{key:"onDataChannelMessage",value:function(e,t){this.trigger(new Ft(Et.DATA_CHANNEL_MESSAGE,this,{dataChannel:e,message:t}))}},{key:"onDataChannelOpen",value:function(e){this.trigger(new Ft(Et.DATA_CHANNEL_OPEN,this,{dataChannel:e}))}},{key:"onDataChannelClose",value:function(e){this.trigger(new Ft(Et.DATA_CHANNEL_CLOSE,this,{dataChannel:e}))}},{key:"onMetaData",value:function(e){var t=e.orientation,n=e.streamingMode,r=this._streamingMode;void 0!==t&&t!==this._orientation&&(this._orientation=t,this._options.autoLayoutOrientation&&(ve(this._view.view,parseInt(t,10),_e(e.resolution)),this._sourceHandler&&this._sourceHandler.handleOrientationChange(parseInt(t,10))),this.trigger(new Ft(wt.ORIENTATION_CHANGE,this,{orientation:parseInt(t,10),viewElement:this._view.view}))),void 0!==n&&n!==r&&(this._streamingMode=n,this.trigger(new Ft(wt.STREAMING_MODE_CHANGE,this,{streamingMode:n,previousStreamingMode:r,viewElement:this._view.view}))),this.trigger(new Ft(wt.SUBSCRIBE_METADATA,this,e))}},{key:"onStreamSwitchComplete",value:function(){p("RTCSubscriber","[streamswitch::complete]");var e=this._options.liveSeek,t=this._requestedStreamSwitch;if(e&&e.enabled){var n=e.baseURL,r=e.fullURL,o=t.split("/").pop(),i=t.substr(0,t.lastIndexOf("/".concat(o))),a=mo(mo({},this._options),{},{app:i,streamName:o}),s=Xr(a,n,r);this._sourceHandler.switchLiveSeek(s)}this.trigger(new Ft(Et.SUBSCRIBE_STREAM_SWITCH,this,{path:t})),this._requestedStreamSwitch=void 0}},{key:"onPublisherNetworkCongestion",value:function(e){this.trigger(new Ft(wt.SUBSCRIBE_PUBLISHER_CONGESTION,this,e))}},{key:"onPublisherNetworkRecovery",value:function(e){this.trigger(new Ft(wt.SUBSCRIBE_PUBLISHER_RECOVERY,this,e))}},{key:"callServer",value:function(e,t){var n="switchStreams"===e,r=this._options,o=r.app,i=r.streamName;if(n){var a=t[0].path;this._requestedStreamSwitch=a,p("RTCSubscriber","[callServer:switch]:: ".concat(e,", ").concat(o,"/").concat(i," -> ").concat(a))}return this.getMessageTransport().postAsync({callAdapter:{method:e,arguments:t}})}},{key:"sendLog",value:function(e,t){try{var n=Object.keys(l).find((function(t){return t.toLowerCase()===e.toLowerCase()}))?e:l.DEBUG,r="string"==typeof t?t:JSON.stringify(t);this.getMessageTransport().post({log:n.toUpperCase(),message:r})}catch(e){var o=e.message||e;m("RTCSubscriber","Could not send log to server. Message parameter expected to be String or JSON-serializable object."),m("RTCSubscriber",o)}}},{key:"enableStandby",value:function(){this.getMessageTransport().post({mute:{muteAudio:!0,muteVideo:!0}})}},{key:"disableStandby",value:function(){this.getMessageTransport().post({mute:{muteAudio:!1,muteVideo:!1}})}},{key:"muteAudio",value:function(){this.getMessageTransport().post({mute:{muteAudio:!0}})}},{key:"unmuteAudio",value:function(){this.getMessageTransport().post({mute:{muteAudio:!1}})}},{key:"muteVideo",value:function(){this.getMessageTransport().post({mute:{muteVideo:!0}})}},{key:"unmuteVideo",value:function(){this.getMessageTransport().post({mute:{muteVideo:!1}})}},{key:"getMessageTransport",value:function(){return this._messageTransport}},{key:"getConnection",value:function(){return this._socketHelper}},{key:"getPeerConnection",value:function(){return this._peerHelper?this._peerHelper.connection:void 0}},{key:"getDataChannel",value:function(){return this._peerHelper?this._peerHelper.dataChannel:void 0}},{key:"getMediaStream",value:function(){return this._mediaStream}},{key:"getControls",value:function(){return this._sourceHandler?this._sourceHandler.getControls():void 0}},{key:"getPlayer",value:function(){return this._view.view}},{key:"getOptions",value:function(){return this._options}},{key:"getType",value:function(){return Qr.RTC.toUpperCase()}}])&&_o(t.prototype,n),r&&_o(t,r),Object.defineProperty(t,"prototype",{writable:!1}),s}(Cn),jo=function(e,t){var n=new S,r=e.id;if("video"===e.nodeName.toLowerCase()){var o=ce.createElement("div");o.id=r+"_rtmp",t.appendChild(o),e.parentElement&&e.parentElement.removeChild(e),n.resolve(o.id)}else n.resolve(r);return n.promise},Ao=function(e,t,n,r,o){var i=new S,a={quality:"high",wmode:"opaque",bgcolor:t.backgroundColor||"#000",allowscriptaccess:"always",allowfullscreen:"true",allownetworking:"all"},s={id:e,name:e,align:"middle"};return r.hasFlashPlayerVersion(t.minFlashVersion)?r.embedSWF(t.swf,o,t.embedWidth||640,t.embedHeight||480,t.minFlashVersion,t.productInstallURL,n,a,s,(function(e){e.success?i.resolve():i.reject("Flash Object embed failed.")})):i.reject("Flash Player Version is not supported."),i.promise};function Lo(e){return(Lo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function No(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Lo(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Lo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Lo(i)?i:String(i)),r)}var o,i}var Ho=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._element=void 0,this._responseHandlers=[]}var t,n,r;return t=e,(n=[{key:"addResponseHandler",value:function(e){this._responseHandlers.push(e)}},{key:"removeResponseHandler",value:function(e){for(var t=this._responseHandlers.length;--t>-1;)if(this._responseHandlers[t]===e)return void this._responseHandlers.splice(t,1)}},{key:"handleSharedObjectEvent",value:function(e){var t;p("R5ProRTMPSharedObjectHandler","[sharedobject:event]");var n=this._responseHandlers.length;for(t=0;t<n&&!this._responseHandlers[t].respond(JSON.parse(e));t++);}},{key:"assignSharedObjectResponder",value:function(e,t){var n=this.handleSharedObjectEvent.bind(this),r=t.id.split("-").join("_"),o=["handleSharedObjectEvent",r].join("_");p("R5ProRTMPSharedObjectHandler","[assign:sharedobject:responder] :: ".concat(r)),p("R5ProRTMPSharedObjectHandler","[sharedoject:responder] :: ".concat(o)),t.setSharedObjectResponder(e,o),ce.setGlobal(o,n)}},{key:"connect",value:function(e){this._element=ce.getEmbedObject(e),this._element||v("Could not locate embedded Flash object for id: ".concat(e))}},{key:"disconnect",value:function(){for(;this._responseHandlers.length>0;)this._responseHandlers.shift()}},{key:"sendToSharedObject",value:function(e,t,n){p("R5ProRTMPSharedObjectHandler","[sendToSharedObject]");try{this._element.sharedObjectSend(e,t,"string"==typeof n?n:JSON.stringify(n))}catch(t){v("Could not send to shared object ("+e+"). Error: "+t.message)}}},{key:"sendPropertyToSharedObject",value:function(e,t,n){p("R5ProRTMPSharedObjectHandler","[sendPropertyToSharedObject]");try{this._element.sharedObjectSendProperty(e,t,n)}catch(t){v("Could not send to shared object ("+e+"). Error: "+t.message)}}},{key:"getRemoteSharedObject",value:function(e){p("R5ProRTMPSharedObjectHandler","[getRemoteSharedObject]");try{this.assignSharedObjectResponder(e,this._element),this._element.getRemoteSharedObject(e)}catch(e){v("Could not get remote shared object ("+name+"). Error: "+e.message)}}},{key:"connectToSharedObject",value:function(e){p("R5ProRTMPSharedObjectHandler","[connectToSharedObject]");try{this._element.connectToSharedObject(e)}catch(e){v("Could not connect to shared object ("+name+"). Error: "+e.message)}}},{key:"closeSharedObject",value:function(e){p("R5ProRTMPSharedObjectHandler","[disconnectToSharedObject]");try{this._element.disconnectFromSharedObject(e)}catch(e){v("Could not disconnect to shared object ("+name+"). Error: "+e.message)}}}])&&No(t.prototype,n),r&&No(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Io(e){return(Io="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function xo(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Do(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Io(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Io(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Io(i)?i:String(i)),r)}var o,i}function Mo(e,t){return(Mo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Fo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Vo(e);if(t){var o=Vo(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Uo(this,n)}}function Uo(e,t){if(t&&("object"===Io(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Bo(e)}function Bo(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vo(e){return(Vo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Go=function(e){return"string"==typeof e?parseInt(e,10):Math.round(e)},Wo=function(e){return/^.*\.(flv|mp4|mp3)/.test(e)},Yo=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mo(e,t)}(i,e);var t,n,r,o=Fo(i);function i(e,t,n){var r,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return xo(this,i),(r=o.call(this)).media=t,r.clone=r.media.cloneNode(!0),r.parent=r.media.parentNode,r.holder=r._determineHolder(r.media),r.playerType=n,r._swfId=void 0,r._controls=void 0,r._soResponder=a||new Ho,r._playbackNotificationCenter=e,ce.onFullScreenStateChange(r._handleFullScreenChange.bind(Bo(r))),r}return t=i,(n=[{key:"_determineHolder",value:function(e){if(e.parentNode.classList.contains("red5pro-media-container"))return e.parentNode;var t=e.parentNode,n=ce.createElement("div");return n.classList.add("red5pro-media-container"),t.insertBefore(n,e),t.removeChild(e),n.appendChild(e),n}},{key:"_cleanUp",value:function(){var e=this.getEmbeddedView(),t=e.parentNode,n=this.holder;if(t)t.removeChild(e),t!==this.parent&&(t.parentNode.removeChild(t),n=this.parent);else try{e.remove()}catch(e){v("RTMPSourceHandler","Issue in DOM cleanup of flash object embed: ".concat(e.message))}this.media=this.clone.cloneNode(!0),n.appendChild(this.media),this._controls&&this._controls.detach()}},{key:"_addPlaybackHandlers",value:function(e){var t=this.getControls();void 0!==t&&(e.on(wt.SUBSCRIBE_START,(function(){t.setState(cn.PLAYING)})),e.on(wt.SUBSCRIBE_STOP,(function(){t.setState(cn.IDLE)})),e.on(wt.PLAY_UNPUBLISH,(function(){t.setState(cn.IDLE)})),e.on(wt.SUBSCRIBE_METADATA,(function(e){e.data.duration&&t.setPlaybackDuration(e.data.duration)})),e.on(wt.VOLUME_CHANGE,(function(e){t.setVolume(e.data.volume)})),e.on(wt.PLAYBACK_STATE_CHANGE,(function(e){e.data.code===cn.AVAILABLE&&t.enable(!0),t.setState(e.data.code)})),e.on(wt.PLAYBACK_TIME_UPDATE,(function(e){t.setSeekTime(e.data.time,e.data.duration)})),e.on(wt.FULL_SCREEN_STATE_CHANGE,(function(e){t.onFullScreenChange(e.data)})))}},{key:"_handleFullScreenChange",value:function(e){var t=this.getEmbeddedView();e?(this.holder.classList.add("red5pro-media-container-full-screen"),t.classList.add("red5pro-media-container-full-screen")):(this.holder.classList.remove("red5pro-media-container-full-screen"),t.classList.remove("red5pro-media-container-full-screen")),this.trigger(new Ft(wt.FULL_SCREEN_STATE_CHANGE,void 0,e))}},{key:"_setUpInitCallback",value:function(e){var t=this;ce.addSubscriptionAssignmentHandler((function(n){p("RTMPSourceHandler","Embed and init() complete for subscriber swf. successId(".concat(n,").")),e.resolve(n),t._tearDownInitCallback()}))}},{key:"_tearDownInitCallback",value:function(){}},{key:"addSource",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;p("RTMPSourceHandler","[addsource]"),this._swfId=e,this.holder=this._determineHolder(this.media);var i=this._soResponder,a=new S,s=t.controls,c=ce.hasAttributeDefined(this.media,"muted"),u=ce.hasAttributeDefined(this.media,"controls")&&ce.hasClassDefined(this.media,"red5pro-media");t.swf=r||t.swf,t.minFlashVersion=o||t.minFlashVersion,this._setUpInitCallback(a);var l=this.media.classList;return jo(this.media,this.holder).then((function(r){var o={stream:t.streamName,app:t.context?"".concat(t.app,"/").concat(t.context):t.app,host:t.host,muted:ce.hasAttributeDefined(n.media,"muted"),autoplay:ce.hasAttributeDefined(n.media,"autoplay"),useAdaptiveBitrateController:t.useAdaptiveBitrateController};return t.backgroundColor&&(o.backgroundColor=t.backgroundColor),t.buffer&&!isNaN(Number(t.buffer))&&(o.buffer=t.buffer),t.width&&!isNaN(t.width)&&(o.width=Go(t.width)),t.height&&!isNaN(t.height)&&(o.height=Go(t.height)),"100%"!==t.embedWidth&&"100%"!==t.embedHeight||(o.autosize=!0),n._swfId=e,void 0!==t.connectionParams&&(o.connectionParams=encodeURIComponent(JSON.stringify(t.connectionParams))),void 0!==t.abrVariants&&(o.abrVariants=encodeURIComponent(JSON.stringify(t.abrVariants))),void 0!==t.abrVariantUpgradeSettings&&(o.abrVariantUpgradeSettings=encodeURIComponent(JSON.stringify(t.abrVariantUpgradeSettings))),Ao(e,t,o,ce.getSwfObject(),r)})).then((function(){if(s||u){n._controls=s?t.controls:new kn(n,n.holder),n.media.controls=!1,n._controls.setAsVOD(Wo(t.streamName)),n._controls.setMutedState(c);for(var e,r=n.getEmbeddedView(),o=l.length;--o>-1;)e=l.item(o),r.classList.add(e)}return n._addPlaybackHandlers(n._playbackNotificationCenter),n.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.AVAILABLE,state:un[cn.AVAILABLE]})),!0})).then((function(){return i.connect(e),!0})).catch((function(e){return a.reject(e)})),a.promise}},{key:"connect",value:function(){p("RTMPSourceHandler","[connect]");try{this.getEmbeddedView().connect()}catch(e){throw e}}},{key:"play",value:function(){try{this.getEmbeddedView().play()}catch(e){throw e}}},{key:"pause",value:function(){try{this.getEmbeddedView().pause()}catch(e){throw e}}},{key:"resume",value:function(){try{this.getEmbeddedView().resume()}catch(e){throw e}}},{key:"stop",value:function(){try{this.getEmbeddedView().stop()}catch(e){throw e}}},{key:"mute",value:function(){try{this.getEmbeddedView().mute()}catch(e){throw e}}},{key:"unmute",value:function(){try{this.getEmbeddedView().unmute()}catch(e){throw e}}},{key:"setVolume",value:function(e){try{this.getEmbeddedView().setVolume(e)}catch(e){throw e}}},{key:"seekTo",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;try{this.getEmbeddedView().seekTo(e,t)}catch(e){throw e}}},{key:"toggleFullScreen",value:function(){try{ce.toggleFullScreen(this.holder)}catch(e){throw e}}},{key:"unpublish",value:function(){this.stop()}},{key:"disconnect",value:function(){try{this.getEmbeddedView().disconnect(),p("RTMPSourceHandler","[disconnect]")}catch(e){}this._cleanUp(),this._soResponder.disconnect()}},{key:"addSharedObjectResponseHandler",value:function(e){this._soResponder.addResponseHandler(e)}},{key:"removeSharedObjectResponseHandler",value:function(e){this._soResponder.removeResponseHandler(e)}},{key:"sendToSharedObject",value:function(e,t,n){this._soResponder.sendToSharedObject(e,t,n)}},{key:"sendPropertyToSharedObject",value:function(e,t,n){this._soResponder.sendPropertyToSharedObject(e,t,n)}},{key:"getRemoteSharedObject",value:function(e){this._soResponder.getRemoteSharedObject(e)}},{key:"connectToSharedObject",value:function(e){this._soResponder.connectToSharedObject(e)}},{key:"closeSharedObject",value:function(e){this._soResponder.closeSharedObject(e)}},{key:"startABRController",value:function(){try{this.getEmbeddedView().startABRController()}catch(e){p("RTMPSourceHandler","Could not start the Adaptive Bitrate Controller: ".concat(e.message))}}},{key:"stopABRController",value:function(){try{this.getEmbeddedView().stopABRController()}catch(e){p("RTMPSourceHandler","Could not stop the Adaptive Bitrate Controller: ".concat(e.message))}}},{key:"setABRVariants",value:function(e,t){try{var n="string"==typeof e?encodeURIComponent(e):encodeURIComponent(JSON.stringify(e));this.getEmbeddedView().setABRVariants(n,t||1)}catch(e){p("RTMPSourceHandler","Could not set ABR Variants: ".concat(e.message))}}},{key:"setABRLevel",value:function(e,t){try{this.getEmbeddedView().setABRLevel(e,!!t)}catch(e){p("RTMPSourceHandler","Could not set ABR level: ".concat(e.message))}}},{key:"setABRVariantUpgradeSettings",value:function(e){try{var t="string"==typeof abrVariants?encodeURIComponent(e):encodeURIComponent(JSON.stringify(e));this.getEmbeddedView().setABRVariantUpgradeSettings(t)}catch(e){p("RTMPSourceHandler","Could not set ABR Variants: ".concat(e.message))}}},{key:"getEmbeddedView",value:function(){return ce.getEmbedObject(this._swfId)}},{key:"getControls",value:function(){return this._controls}},{key:"getType",value:function(){return this.playerType}}])&&Do(t.prototype,n),r&&Do(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Cn);function zo(e){return(zo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Jo(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==zo(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==zo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===zo(i)?i:String(i)),r)}var o,i}function Ko(e,t){return(Ko=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function qo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qo(e);if(t){var o=Qo(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Xo(this,n)}}function Xo(e,t){if(t&&("object"===zo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Qo(e){return(Qo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var $o={protocol:"rtmp",port:1935,width:320,height:240,embedWidth:"100%",embedHeight:"100%",minFlashVersion:"10.0.0",swf:"lib/red5pro/red5pro-subscriber.swf",swfobjectURL:"lib/swfobject/swfobject.js",productInstallURL:"lib/swfobject/playerProductInstall.swf",mediaElementId:"red5pro-subscriber",useAdaptiveBitrateController:!1,abrVariants:void 0,abrVariantUpgradeSettings:{minimumDowngradePlaybackSpan:1e4,upgrade:[{level:1,retryTimeout:0},{level:2,retryTimeout:1e4},{level:3,retryTimeout:2e4}]}},Zo=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ko(e,t)}(i,e);var t,n,r,o=qo(i);function i(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(e=o.call(this))._options=void 0,e._view=void 0,e._sourceHandler=void 0,e._elementId=void 0,e._embedPromise=void 0,e._getEmbedPromise().then((function(){e.onEmbedComplete()})).catch((function(t){return e.onEmbedFailure(t)})),e._viewResolver=new S,e._subscriptionResolver=new S,e._orientation=0,e._streamingMode="Video/Audio",e}return t=i,(n=[{key:"_getViewResolverPromise",value:function(){return this._viewResolver.promise}},{key:"_getSubscriptionResolverPromise",value:function(){return this._subscriptionResolver.promise}},{key:"_getEmbedPromise",value:function(){return this._embedPromise=E.createIfNotExist(this._embedPromise),this._embedPromise.promise}},{key:"_glomSourceHandlerAPI",value:function(e){var t=this;this.pause=e.pause.bind(e),this.resume=e.resume.bind(e),this.stop=e.stop.bind(e),this.mute=e.mute.bind(e),this.unmute=e.unmute.bind(e),this.setVolume=e.setVolume.bind(e),this.seekTo=e.seekTo.bind(e),this.toggleFullScreen=e.toggleFullScreen.bind(e),this.setABRLevel=e.setABRLevel.bind(e),this.setABRVariants=e.setABRVariants.bind(e),this.stopABRController=e.stopABRController.bind(e),this.startABRController=e.startABRController.bind(e),this.setABRVariantUpgradeSettings=e.setABRVariantUpgradeSettings.bind(e),e.on("*",(function(e){t.trigger(new Ft(e.type,t,e.data))}))}},{key:"_setViewIfNotExist",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(void 0===e&&void 0!==t){var n=new Wr(t);n.attachSubscriber(this)}}},{key:"_establishExtIntHandlers",value:function(e){var t=this;p("RTMPSubcriber","Subscriber ID provided to client: (".concat(e,")."));var n=function(t){return["subscriber",t,e.split("-").join("_")].join("_")};window[n("r5proConnectClosed")]=function(){return t.trigger(new Ft(wt.CONNECTION_CLOSED,t))},window[n("r5proConnectSuccess")]=function(){return t.trigger(new Ft(wt.CONNECT_SUCCESS,t))},window[n("r5proConnectFailure")]=function(){t.trigger(new Ft(wt.CONNECT_FAILURE,t))},window[n("r5proSubscribeStop")]=function(){return t.trigger(new Ft(wt.SUBSCRIBE_STOP,t))},window[n("r5proSubscribeMetadata")]=function(e){var n=JSON.parse(e),r=n.orientation,o=n.streamingMode,i=parseInt(r,10),a=t._streamingMode;t._orientation!==i&&(t._orientation=i,t.trigger(new Ft(wt.ORIENTATION_CHANGE,t,{orientation:i}))),a!==o&&(t._streamingMode=o,t.trigger(new Ft(wt.STREAMING_MODE_CHANGE,t,{streamingMode:o,previousStreamingMode:a}))),t.trigger(new Ft(wt.SUBSCRIBE_METADATA,t,JSON.parse(e)))},window[n("r5proSubscribeUnpublish")]=function(){t.onUnpublish()},window[n("r5proSubscribePublisherCongestion")]=function(e){return t.trigger(new Ft(wt.SUBSCRIBE_PUBLISHER_CONGESTION,t,JSON.parse(e)))},window[n("r5proSubscribePublisherRecovery")]=function(e){return t.trigger(new Ft(wt.SUBSCRIBE_PUBLISHER_RECOVERY,t,JSON.parse(e)))},window[n("r5proSubscribeSendInvoke")]=function(e){t.trigger(new Ft(wt.SUBSCRIBE_SEND_INVOKE,t,"string"==typeof e?JSON.parse(e):e))},window[n("r5proSubscribePlayRequest")]=function(){t.play()},window[n("r5proSubscribeStart")]=function(){t._subscriptionResolver.resolve(t),t.trigger(new Ft(wt.SUBSCRIBE_START,t))},window[n("r5proSubscribeInvalidName")]=function(){t._subscriptionResolver.reject("NetStream.Play.StreamNotFound",t),t.trigger(new Ft(wt.SUBSCRIBE_INVALID_NAME,t))},window[n("r5proSubscribeFail")]=function(){t._subscriptionResolver.reject("NetStream.Failed",t),t.trigger(new Ft(wt.SUBSCRIBE_FAIL,t))},window[n("r5proSubscribeVolumeChange")]=function(e){t.trigger(new Ft(wt.VOLUME_CHANGE,t,{volume:JSON.parse(e).volume}))},window[n("r5proSubscribePlaybackStalled")]=function(){p("RTMPSubcriber","playback has stalled...")},window[n("r5proSubscribePlaybackTimeChange")]=function(e){var n=JSON.parse(e);t.trigger(new Ft(wt.PLAYBACK_TIME_UPDATE,t,{time:n.value,duration:n.duration}))},window[n("r5proSubscribePlaybackStateChange")]=function(e){var n=JSON.parse(e).code;t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,t,{code:n,state:un[n]}))},window[n("r5proSubscribeABRLevelChange")]=function(e){var n=JSON.parse(e),r=n.level,o=n.stream,i=JSON.parse(decodeURIComponent(o));t.trigger(new Ft(Ct.ABR_LEVEL_CHANGE,t,{level:r,stream:i}))}}},{key:"init",value:function(e){var t=this,n=new S,r=e.minFlashVersion||$o.minFlashVersion;if(ce.supportsFlashVersion(r)){this._options=Object.assign({},$o,e);try{ce.injectScript(this._options.swfobjectURL).then((function(){var e=t._embedPromise;return p("RTMPSubcriber","SWFObject embedded."),t._sourceHandler?(t._sourceHandler.addSource(t._elementId,t._options).then((function(n){t._establishExtIntHandlers(n),e.resolve(t)})).catch((function(t){e.reject(t)})),t._getEmbedPromise()):(t._getViewResolverPromise().then((function(e){if(t._sourceHandler=new Yo(t,e.view,t.getType()),t._glomSourceHandlerAPI(t._sourceHandler),t._options){var n=t._embedPromise;t._sourceHandler.addSource(t._elementId,t._options).then((function(e){t._establishExtIntHandlers(e),n.resolve(t)})).catch((function(e){return n.reject(e)}))}})),!0)})).then((function(){t._setViewIfNotExist(t._view,t._options.mediaElementId),n.resolve(t)})).catch((function(e){m("RTMPSubcriber","Could not embed Flash-based RTMP Player. Reason: ".concat(e)),t._sourceHandler&&t._sourceHandler.disconnect(),n.reject(e),t.trigger(new Ft(Ct.EMBED_FAILURE,t))}))}catch(e){n.reject("Could not inject Flash-based Player into the page. Reason: ".concat(e.message)),this.trigger(new Ft(Ct.EMBED_FAILURE,this))}}else v("RTMPSubcriber","Could not resolve RTMPSubscriber instance. Requires minimum Flash Player install of ".concat(r,".")),n.reject("Could not resolve RTMPSubscriber instance. Requires minimum Flash Player install of ".concat(r,"."));return n.promise}},{key:"setView",value:function(e,t){return this._view=e,this._elementId=t,this._viewResolver.resolve(this._view),this}},{key:"subscribe",value:function(){return this._getSubscriptionResolverPromise()}},{key:"unsubscribe",value:function(){var e=this;return p("RTMPSubcriber","[unsubscribe]"),new Promise((function(t,n){try{e._sourceHandler.disconnect(),t()}catch(e){n(e.message)}}))}},{key:"play",value:function(){var e=this;p("RTMPSubcriber","[play]"),this._getEmbedPromise().then((function(){e._sourceHandler.play()}))}},{key:"onEmbedComplete",value:function(){p("RTMPSubcriber","[embed:complete]"),this.trigger(new Ft(Ct.EMBED_SUCCESS,this))}},{key:"onEmbedFailure",value:function(e){p("RTMPSubcriber","[embed:failure] - ".concat(e)),this.trigger(new Ft(Ct.EMBED_FAILURE,this))}},{key:"onUnpublish",value:function(){p("RTMPSubcriber","[onunpublish]"),this._sourceHandler&&this._sourceHandler.unpublish(),this.trigger(new Ft(wt.PLAY_UNPUBLISH,this)),this._sourceHandler&&this._sourceHandler.disconnect()}},{key:"getConnection",value:function(){return this._sourceHandler}},{key:"getControls",value:function(){return this._sourceHandler?this._sourceHandler.getControls():void 0}},{key:"getOptions",value:function(){return this._options}},{key:"getPlayer",value:function(){return this._sourceHandler?this._sourceHandler.getEmbeddedView():void 0}},{key:"getType",value:function(){return Qr.RTMP.toUpperCase()}}])&&Jo(t.prototype,n),r&&Jo(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Cn);function ei(e){return(ei="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ti(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==ei(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ei(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===ei(i)?i:String(i)),r)}var o,i}function ni(e,t){return(ni=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ri(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ai(e);if(t){var o=ai(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return oi(this,n)}}function oi(e,t){if(t&&("object"===ei(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ii(e)}function ii(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ai(e){return(ai=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var si=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ni(e,t)}(i,e);var t,n,r,o=ri(i);function i(e,t){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(n=o.call(this)).media=e,n.clone=n.media.cloneNode(!0),n.parent=n.media.parentNode,n.holder=n._determineHolder(e),n.playerType=t,n._mediaSource=void 0,n._isVOD=!1,n._controls=void 0,n._orientation=0,n._streamingMode="Video/Audio",n._autoLayoutOrientation=!0,n._playbackNotificationCenter=n.media,n._handleOrientationChange=n._handleOrientationChange.bind(ii(n)),n._onOrientationMetadata=n._onOrientationMetadata.bind(ii(n)),n._onStreamingModeMetadata=n._onStreamingModeMetadata.bind(ii(n)),ce.onFullScreenStateChange(n._handleFullScreenChange.bind(ii(n))),n}return t=i,(n=[{key:"_determineHolder",value:function(e){if(e.parentNode.classList.contains("red5pro-media-container"))return e.parentNode;var t=e.parentNode,n=ce.createElement("div");return n.classList.add("red5pro-media-container"),t.insertBefore(n,e),t.removeChild(e),n.appendChild(e),n}},{key:"_cleanUp",value:function(){var e=this.media,t=this._mediaSource,n=e.parentNode,r=this.holder;if(t&&e.removeChild(t),n)n.removeChild(e),n!==this.parent&&(n.parentNode.removeChild(n),r=this.parent);else try{e.remove()}catch(e){v("HLSSourceHandler","Issue in DOM cleanup of HLS video object: ".concat(e.message))}this.media=this.clone.cloneNode(!0),r.appendChild(this.media),this._controls&&this._controls.detach(),this.media.setAttribute("autoplay",!0),this.clone=this.media.cloneNode(!0),this.parent=this.media.parentNode,this.holder=this._determineHolder(this.media),this._orientation=0,this.isVOD=!1}},{key:"_addPlaybackHandlers",value:function(e){var t=this,n=this.getControls(),r=void 0!==n;e.oncanplay=function(){n&&n.enable(!0),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.AVAILABLE,state:un[cn.AVAILABLE]})),t.trigger(new Ft(wt.VOLUME_CHANGE,void 0,{volume:e.volume}))},e.ondurationchange=function(o){!isNaN(e.duration)&&Number.isFinite(e.duration)&&(t.isVOD=!0),!t.isVOD&&r?n.setPlaybackDuration(1):r&&n.setPlaybackDuration(e.duration)},e.onended=function(){p("HLSSourceHandler","[videoelement:onended]"),r&&n.setState(cn.IDLE),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.IDLE,state:un[cn.IDLE]}))},e.ontimeupdate=function(o){r&&(self.isVOD?n.setSeekTime(e.currentTime,self.isVOD?e.duration:void 0):n.setSeekTime(1,1)),t.trigger(new Ft(wt.PLAYBACK_TIME_UPDATE,void 0,{time:e.currentTime,duration:e.duration}))},e.onseeked=function(e){},e.onseeking=function(e){},e.onplay=function(){r&&n.setState(cn.PLAYING),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.PLAYING,state:un[cn.PLAYING]}))},e.onpause=function(){r&&n.setState(cn.PAUSED),t.trigger(new Ft(wt.PLAYBACK_STATE_CHANGE,void 0,{code:cn.PAUSED,state:un[cn.PAUSED]}))},e.onvolumechange=function(o){r&&n.getVolume()!==t.media.volume&&n.setVolume(t.media.volume),t.trigger(new Ft(wt.VOLUME_CHANGE,void 0,{volume:e.muted?0:e.volume}))},e.onencrypted=function(){p("HLSSourceHandler","[videoelement:event] encrypted")},e.onemptied=function(){p("HLSSourceHandler","[videoelement:event] emptied")},e.onloadeddata=function(){p("HLSSourceHandler","[videoelement:event] loadeddata")},e.onresize=function(){p("HLSSourceHandler","[videoelement:event] resize"),t.trigger(new Ft(wt.VIDEO_DIMENSIONS_CHANGE,void 0,{width:t.media.videoWidth,height:t.media.videoHeight}))},e.onloadedmetadata=function(){p("HLSSourceHandler","[videoelement:event] loadedmetadata"),t.trigger(new Ft(wt.SUBSCRIBE_START,void 0,{}))},e.onloadstart=function(){p("HLSSourceHandler","[videoelement:event] loadedstart")},e.onstalled=function(){p("HLSSourceHandler","[videoelement:event] stalled")},e.onsuspend=function(){p("HLSSourceHandler","[videoelement:event] suspend")},e.onwaiting=function(){p("HLSSourceHandler","[videoelement:event] waiting")}}},{key:"_handleFullScreenChange",value:function(e){e?(this.holder.classList.add("red5pro-media-container-full-screen"),this.media.classList.add("red5pro-media-container-full-screen")):(this.holder.classList.remove("red5pro-media-container-full-screen"),this.media.classList.remove("red5pro-media-container-full-screen")),this.trigger(new Ft(wt.FULL_SCREEN_STATE_CHANGE,void 0,e))}},{key:"_embedMediaSource",value:function(e,t,n){var r=new S;try{var o=ce.createElement("source");o.type=t,o.src=e,this.media.type=t,this.media.firstChild?this.media.insertBefore(o,this.media.firstChild):this.media.appendChild(o),this._mediaSource=o,this._autoLayoutOrientation=n.autoLayoutOrientation,ce.onOrientationMetadata(this.media,this._onOrientationMetadata),ce.onStreamingModeMetadata(this.media,this._onStreamingModeMetadata),r.resolve()}catch(e){r.reject(e.message)}return r.promise}},{key:"_onOrientationMetadata",value:function(e){var t=e.orientation,n=e.resolution,r=parseInt(t,10);t&&this._orientation!==r&&(p("HLSSourceHandler","Metadata received: "+JSON.stringify(e,null,2)),this._orientation=r,this._autoLayoutOrientation&&(ve(this.media,this._orientation,_e(n)),this._handleOrientationChange(this._orientation)),this.trigger(new Ft(wt.ORIENTATION_CHANGE,void 0,{orientation:this._orientation,viewElement:this.media})),this.trigger(new Ft(wt.SUBSCRIBE_METADATA,void 0,e)))}},{key:"_onStreamingModeMetadata",value:function(e){var t=e.streamingMode,n=this._streamingMode;t&&n!==t&&(p("HLSSourceHandler","Metadata received: "+JSON.stringify(e,null,2)),this._streamingMode=t,this.trigger(new Ft(wt.STREAMING_MODE_CHANGE,void 0,{streamingMode:this._streamingMode,previousStreamingMode:n,viewElement:this.media})),this.trigger(new Ft(wt.SUBSCRIBE_METADATA,void 0,e)))}},{key:"addSource",value:function(e,t,n){var r=this;p("HLSSourceHandler","[addsource]"),this.holder=this._determineHolder(this.media);var o=new S,i=n.controls,a=ce.hasAttributeDefined(this.media,"muted"),s=ce.hasAttributeDefined(this.media,"autoplay");a||this.media.setAttribute("autoplay",!1);var c=ce.hasAttributeDefined(this.media,"controls")&&ce.hasClassDefined(this.media,"red5pro-media");return this._embedMediaSource(e,t,n).then((function(){(i||c)&&(r._controls=i?n.controls:new kn(r,r.holder),r.media.controls=!1,r._controls.setAsVOD(r.isVOD),r._controls.setMutedState(a)),r._addPlaybackHandlers(r._playbackNotificationCenter),s&&r.attemptAutoplay(n.muteOnAutoplayRestriction),o.resolve()})).catch((function(e){return o.reject(e)})),o.promise}},{key:"connect",value:function(){p("HLSSourceHandler","[connect]")}},{key:"attemptAutoplay",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.play().catch((function(n){t?(e.mute(),e.play().then((function(){e.trigger(new Ft(wt.AUTO_PLAYBACK_MUTED,void 0,{element:e.media}))})).catch((function(t){e.trigger(new Ft(wt.AUTO_PLAYBACK_FAILURE,void 0,{error:t.message?t.message:t,element:e.media}))}))):e.trigger(new Ft(wt.AUTO_PLAYBACK_FAILURE,void 0,{error:n.message?n.message:n,element:e.media}))}))}},{key:"play",value:function(){p("HLSSourceHandler","[videoelement:action] play");var e=new S;try{var t=this.media.play();t?t.then((function(){p("HLSSourceHandler","[videoelement:action] play (START)"),e.resolve()})).catch(e.reject):(p("HLSSourceHandler","[videoelement:action] play (START)"),e.resolve())}catch(t){m("HLSSourceHandler","[videoelement:action] play (FAULT) - "+t.message),e.reject(t)}return e.promise}},{key:"pause",value:function(){p("HLSSourceHandler","[videoelement:action] pause");try{this.media.pause()}catch(e){p("HLSSourceHandler","[videoelement:action] pause (FAULT) - "+e.message)}}},{key:"resume",value:function(){p("HLSSourceHandler","[videoelement:action] resume");try{var e=this.media.play();e&&e.then((function(){return p("HLSSourceHandler","[videoelement:action] play (START)")})).catch((function(e){return m("HLSSourceHandler","[videoelement:action] play (FAULT) "+(e.message?e.message:e))}))}catch(e){m("HLSSourceHandler","[videoelement:action] resume (FAULT) - "+e.message)}}},{key:"stop",value:function(){try{this.media.stop()}catch(e){}}},{key:"mute",value:function(){this.media.muted=!0;var e=this.getControls();e&&e.setMutedState(!0)}},{key:"unmute",value:function(){this.media.muted=!1;var e=this.getControls();e&&e.setMutedState(!1)}},{key:"setVolume",value:function(e){this.unmute(),this.media.volume=e}},{key:"seekTo",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;this.media.currentTime=t?e*t:e}},{key:"toggleFullScreen",value:function(){try{ce.toggleFullScreen(this.holder)}catch(e){throw e}}},{key:"unpublish",value:function(){try{this.stop(),this.media.onended.call(this.media)}catch(e){}}},{key:"disconnect",value:function(){this._cleanUp()}},{key:"_handleOrientationChange",value:function(e){this._controls&&e%180!=0&&(this.holder.classList.add("red5pro-media-background"),this.media.classList.remove("red5pro-media-background"))}},{key:"addSharedObjectResponseHandler",value:function(e){}},{key:"removeSharedObjectResponseHandler",value:function(e){}},{key:"sendToSharedObject",value:function(e,t,n){}},{key:"sendPropertyToSharedObject",value:function(e,t,n){}},{key:"getRemoteSharedObject",value:function(e){}},{key:"connectToSharedObject",value:function(e){}},{key:"closeSharedObject",value:function(e){}},{key:"getControls",value:function(){return this._controls}},{key:"getType",value:function(){return this.playerType}},{key:"isVOD",get:function(){return this._isVOD},set:function(e){this._isVOD=e,this._controls&&this._controls.setAsVOD(e)}}])&&ti(t.prototype,n),r&&ti(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Cn);function ci(e){return(ci="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ui(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==ci(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ci(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===ci(i)?i:String(i)),r)}var o,i}function li(e,t){return(li=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function di(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var o=pi(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return fi(this,n)}}function fi(e,t){if(t&&("object"===ci(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return hi(e)}function hi(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pi(e){return(pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var vi,mi={protocol:"https",port:443,app:"live",mimeType:"application/x-mpegURL",mediaElementId:"red5pro-subscriber",autoLayoutOrientation:!0,muteOnAutoplayRestriction:!0,subscriptionId:(vi=Math.floor(65536*Math.random()).toString(16),"subscriber-".concat(vi))},yi=/^http(|s).*\.m3u8/g,bi=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&li(e,t)}(i,e);var t,n,r,o=di(i);function i(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(e=o.call(this))._options=void 0,e._view=void 0,e._sourceHandler=void 0,e._socketHelper=void 0,e._viewResolver=new S,e._subscriptionResolver=new S,e._boundBubbleSubscriberEvents=e.bubbleSubscriberEvents.bind(hi(e)),e}return t=i,(n=[{key:"_getViewResolverPromise",value:function(){return this._viewResolver.promise}},{key:"_getSubscriptionResolverPromise",value:function(){return this._subscriptionResolver.promise}},{key:"bubbleSubscriberEvents",value:function(e){e.type===wt.SUBSCRIBE_START&&this._subscriptionResolver.resolve(this),this.trigger(new Ft(e.type,this,e.data))}},{key:"_glomSourceHandlerAPI",value:function(e){var t=this;this.play=e.play.bind(e),this.pause=e.pause.bind(e),this.resume=e.resume.bind(e),this.stop=e.stop.bind(e),this.mute=e.mute.bind(e),this.unmute=e.unmute.bind(e),this.setVolume=e.setVolume.bind(e),this.seekTo=e.seekTo.bind(e),this.toggleFullScreen=e.toggleFullScreen.bind(e),e.on("*",(function(e){t.trigger(new Ft(e.type,t,e.data))}))}},{key:"_setViewIfNotExist",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(void 0===e&&void 0!==t){var n=new Wr(t);n.attachSubscriber(this)}}},{key:"_initHandler",value:function(e){var t=this,n=this._options,r=n.streamName,o=n.mimeType,i=r.match(yi)?r:Kr(this._options);this._sourceHandler.on("*",this._boundBubbleSubscriberEvents),this._sourceHandler.addSource(i,o,e).then((function(){t.trigger(new Ft(wt.CONNECT_SUCCESS)),t._trackStreamingModeState(t._sourceHandler)})).catch((function(e){m("HLSSubscriber","Could not establish an HLS Subscriber: "+e),t.trigger(new Ft(wt.CONNECT_FAILURE))}))}},{key:"_trackStreamingModeState",value:function(e){var t=this;e.on(wt.STREAMING_MODE_CHANGE,(function(e){var n=e.data,r=n.streamingMode,o=n.previousStreamingMode;if("Empty"!==r&&"Empty"===o){t._sourceHandler.disconnect();var i=t._options,a=i.streamName,s=i.mimeType,c=a.match(yi)?a:Kr(t._options);t._sourceHandler.addSource(c,s,t._options).then((function(){return t.subscribe()})).catch((function(e){return e("HLSSubscriber",e)}))}}))}},{key:"init",value:function(e){var t=this,n=new S;if(ce.supportsHLS())if(e.connectionParams&&!mt())v("HLSSubscriber","Could not resolve HLSSubscriber instance with connection params. WebSocket support is required."),n.reject("HLSSubscriber","Could not resolve HLSSubscriber instance with connection params. WebSocket support is required.");else{this._options=Object.assign({},mi,e);var r=new S;if(this._options.connectionParams)try{this._socketHelper=new ar(this,"HLSSubscriptionSocket");var o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=e.socketParams,r=e.connectionParams,o=n.protocol,i=Yr(n.port||("wss"===o?443:5080)),a="".concat(o,"://").concat(n.host,":").concat(i,"/").concat(n.app,"/");if(r){var s=zr(e.connectionParams);t=Object.assign(t,s)}if(t){var c=[];Object.keys(t).forEach((function(e,n){c.push([e,t[e]].join("="))})),c.length>0&&(a+="?"+c.join("&"))}return a}(this._options,{id:this._options.subscriptionId});this._socketHelper.setUp(o,r)}catch(e){m("HLSSubscriber",e.message),n.reject("HLSSubscriber","Could not set up WebSocket for authentication with connectionParams: ".concat(e.message))}else r.resolve();r.promise.then((function(){t._socketHelper&&(t._socketHelper.tearDown(),t._socketHelper=void 0),t._setViewIfNotExist(t._view,t._options.mediaElementId),t._getViewResolverPromise().then((function(e){t._sourceHandler=new si(e.view,t.getType()),t._glomSourceHandlerAPI(t._sourceHandler),t._options&&t._initHandler(t._options)})),n.resolve(t)})).catch((function(e){n.reject(e),t.trigger(new Ft(wt.CONNECT_FAILURE,t,e))}))}else v("HLSSubscriber","Could not resolve HLSSubscriber instance."),n.reject("Could not resolve HLSSubscriber instance.");return n.promise}},{key:"setView",value:function(e){return this._view=e,this._viewResolver.resolve(e),this}},{key:"subscribe",value:function(){return this._getSubscriptionResolverPromise()}},{key:"unsubscribe",value:function(){p("HLSSubscriber","[unscubscribe]");var e=new S;this._socketHelper&&this._socketHelper.tearDown();try{this._sourceHandler.stop(),this._sourceHandler.disconnect(),e.resolve()}catch(t){e.reject(t.message)}return e.promise}},{key:"getConnection",value:function(){return this._sourceHandler}},{key:"getControls",value:function(){return this._sourceHandler?this._sourceHandler.getControls():void 0}},{key:"getOptions",value:function(){return this._options}},{key:"getPlayer",value:function(){return this._view.view}},{key:"getType",value:function(){return Qr.HLS.toUpperCase()}}])&&ui(t.prototype,n),r&&ui(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Cn);function gi(e){return(gi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_i=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==gi(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function wi(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function Si(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){wi(i,r,o,a,s,"next",e)}function s(e){wi(i,r,o,a,s,"throw",e)}a(void 0)}))}}function Ei(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ci(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==gi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==gi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===gi(i)?i:String(i)),r)}var o,i}var Oi=new Map;Oi.set(400,"Invalid offer SDP."),Oi.set(401,"Not authorized."),Oi.set(404,"Scope resolver failed for the publish name and / or scope."),Oi.set(405,"Remember to update the URL passed into the WHIP or WHEP client."),Oi.set(406,"Scope connection rejected."),Oi.set(409,"Session already initialized."),Oi.set(412,"Invalid request body."),Oi.set(417,"Session lookup or creation failure.");var ki=new Map;ki.set(400,"Offer already sent, double POST assumed."),ki.set(401,"Not authorized."),ki.set(404,"Scope resolver failed for the playback name and / or scope."),ki.set(406,"Playback failed due to an exception during creation."),ki.set(409,"Stream is not available to playback.");var Pi=["transcode"],Ti=function(e){var t=/[?&](.*)=([^&#]*)/.exec(e);return t&&t.length>0},Ri=function(e){return e.split(";").map((function(e){return e.trim()})).map((function(e){return"<"===e.charAt(0)?["url",e.substring(1,e.length-1)]:e.split("=")})).reduce((function(e,t){return e.set(t[0].replaceAll('"',""),t[1].replaceAll('"',""))}),new Map)},ji=function(e){var t=e.split(":");return t.length>1?{protocol:t[0],host:t[1]}:{protocol:void 0,host:e}},Ai=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Ei(this,e),this._url=t,this._resource=void 0,this._enableSignalingChannel=n}var t,n,r,o,i,a,s,c,u;return t=e,(n=[{key:"getOptions",value:(u=Si((function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return _i().mark((function n(){var r,o,i,a,s,c;return _i().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r="".concat(e._url).concat(Ti(e._url)?"&":"?","signal=").concat(e._enableSignalingChannel),t&&Object.keys(t).forEach((function(e){r+="&".concat(e,"=").concat(t[e])})),p("WhipWhepSignalingHelper","[whipwhep-options] ".concat(r)),n.prev=3,n.next=6,fetch(r,{method:"OPTIONS",mode:"cors"});case 6:if(o=n.sent,i=o.status,a=o.headers,200!==i&&204!==i){n.next=14;break}return s=/^(L|l)ink/,c=[],a.forEach((function(e,t){if(s.exec(t)&&e.indexOf('rel="ice-server"')>-1){var n=Ri(e),r=n.get("url"),o=ji(r),i=o.protocol,a=o.host,u=n.get("username"),l=n.get("credential");i&&a&&u&&l?c.push({username:u,credential:l,urls:r}):r&&c.push({urls:r})}})),p("WhipWhepSignalingHelper","[whipwhep-links]: ".concat(c)),n.abrupt("return",{links:c.length>0?c:void 0});case 14:n.next=20;break;case 16:throw n.prev=16,n.t0=n.catch(3),m("WhipWhepSignalingHelper",n.t0.message),n.t0;case 20:case"end":return n.stop()}}),n,null,[[3,16]])}))()})),function(){return u.apply(this,arguments)})},{key:"postSDPOffer",value:(c=Si((function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return _i().mark((function o(){var i,a,s,c,u,l,d,f,h;return _i().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return i="".concat(t._url).concat(Ti(t._url)?"&":"?","signal=").concat(t._enableSignalingChannel),n&&Object.keys(n).forEach((function(e){-1===Pi.indexOf(e)&&(i+="&".concat(e,"=").concat(n[e]))})),p("WhipWhepSignalingHelper","[whipwhep:post-offer] ".concat(i,": ")+JSON.stringify(e,null,2)),o.prev=3,a={method:"POST",mode:"cors",headers:{"Content-Type":"application/sdp"}},e&&e.length>0&&(a.body=e),o.next=8,fetch(i,a);case 8:if(s=o.sent,c=s.status,(u=s.headers)&&u.forEach((function(e,t){p("WhipWhepSignalingHelper","[header] ".concat(t,": ").concat(e))})),!(c>=200&&c<300)){o.next=27;break}return o.next=14,s.text();case 14:if(l=o.sent,!(d=u.get("Location")||u.get("location"))){o.next=22;break}return d.match(/^(http|https)/)?t._resource=d:(p("WhipWhepSignalingHelper","[whipwhep-response] Location provided as relative path: ".concat(d)),(f=new URL(t._url)).pathname=d.split("?")[0],t._resource=f.toString().replace(/\/endpoint\//,"/resource/")),p("WhipWhepSignalingHelper","[whipwhep-response] ".concat(t._resource,": ").concat(l)),o.abrupt("return",{sdp:l,location:t._resource});case 22:return v("WhipWhepSignalingHelper","Location not provided in header response to Offer."),t._resource=new URL(t._url).toString().replace(/\/endpoint\//,"/resource/"),o.abrupt("return",{sdp:l,location:t._resource});case 25:o.next=45;break;case 27:if(!r||!Oi.get(c)){o.next=34;break}if(p("WhipWhepSignalingHelper",Oi.get(c)),404!==c&&409!==c){o.next=31;break}throw new U(Oi.get(c));case 31:throw new Error(Oi.get(c));case 34:if(r||!ki.get(c)){o.next=41;break}if(p("WhipWhepSignalingHelper",ki.get(c)),404!==c&&409!==c){o.next=38;break}throw new U(ki.get(c));case 38:throw new Error(ki.get(c));case 41:return o.next=43,s.text();case 43:throw h=o.sent,Error(h);case 45:o.next=51;break;case 47:throw o.prev=47,o.t0=o.catch(3),m("WhipWhepSignalingHelper",o.t0.message),o.t0;case 51:case"end":return o.stop()}}),o,null,[[3,47]])}))()})),function(e){return c.apply(this,arguments)})},{key:"postSDPAnswer",value:(s=Si(_i().mark((function e(t){var n,r,o;return _i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p("WhipWhepSignalingHelper","[whipwhep:post-answer] ".concat(this._resource,": ")+JSON.stringify(t,null,2)),e.prev=1,e.next=4,fetch(this._resource,{method:"PATCH",mode:"cors",headers:{"Content-Type":"application/sdp"},body:t});case 4:if(n=e.sent,!((r=n.status)>=200&&r<300)){e.next=10;break}return e.abrupt("return",{success:!0,code:r});case 10:if(!ki.get(r)){e.next=15;break}throw p("WhipWhepSignalingHelper",ki.get(r)),new Error(ki.get(r));case 15:return e.next=17,n.text();case 17:throw o=e.sent,Error(o);case 19:e.next=25;break;case 21:throw e.prev=21,e.t0=e.catch(1),m("WhipWhepSignalingHelper",e.t0.message),e.t0;case 25:case"end":return e.stop()}}),e,this,[[1,21]])}))),function(e){return s.apply(this,arguments)})},{key:"trickle",value:(a=Si(_i().mark((function e(t){var n,r,o,i;return _i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p("WhipWhepSignalingHelper","[whipwhep-trickle] ".concat(this._resource,": ")+JSON.stringify(t,null,2)),e.prev=1,e.next=4,fetch(this._resource,{method:"PATCH",mode:"cors",headers:{"Content-Type":"application/trickle-ice-sdpfrag"},body:t});case 4:if(n=e.sent,r=n.status,n.headers,!(r>=200&&r<300)){e.next=14;break}return e.next=9,n.text();case 9:return o=e.sent,p("WhipWhepSignalingHelper","[whipwhep-response] ".concat(this._resource,": ").concat(o)),e.abrupt("return",{candidate:o});case 14:if(405!==r){e.next=19;break}throw console.log("Remember to update the URL passed into the WHIP or WHEP client"),new Error("Remember to update the URL passed into the WHIP or WHEP client");case 19:return e.next=21,n.text();case 21:throw i=e.sent,Error(i);case 23:e.next=29;break;case 25:throw e.prev=25,e.t0=e.catch(1),console.error(e.t0),e.t0;case 29:case"end":return e.stop()}}),e,this,[[1,25]])}))),function(e){return a.apply(this,arguments)})},{key:"tearDown",value:(i=Si(_i().mark((function e(){return _i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._resource){e.next=2;break}return e.abrupt("return");case 2:return p("WhipWhepSignalingHelper","[whipwhep-teardown]"),e.next=5,fetch(this._resource,{method:"DELETE",mode:"cors"});case 5:this._url=void 0,this._resource=void 0;case 7:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"post",value:(o=Si(_i().mark((function e(){return _i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p("WhipWhepSignalingHelper","[whipwhep] transport called."),e.abrupt("return",!0);case 2:case"end":return e.stop()}}),e)}))),function(){return o.apply(this,arguments)})}])&&Ci(t.prototype,n),r&&Ci(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Li(e){return(Li="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ni(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Hi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ni(Object(n),!0).forEach((function(t){Ii(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ni(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ii(e,t,n){return(t=Bi(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xi(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */xi=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==Li(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function Di(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function Mi(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){Di(i,r,o,a,s,"next",e)}function s(e){Di(i,r,o,a,s,"throw",e)}a(void 0)}))}}function Fi(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ui(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Bi(r.key),r)}}function Bi(e){var t=function(e,t){if("object"!==Li(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Li(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Li(t)?t:String(t)}function Vi(){return(Vi="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Gi(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function Gi(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Ji(e)););return e}function Wi(e,t){return(Wi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Yi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ji(e);if(t){var o=Ji(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return zi(this,n)}}function zi(e,t){if(t&&("object"===Li(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Ji(e){return(Ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Ki=function(){var e=Math.floor(65536*Math.random()).toString(16);return"subscriber-".concat(e)},qi={protocol:"wss",port:443,app:"live",autoLayoutOrientation:!0,mediaElementId:"red5pro-subscriber",rtcConfiguration:{iceServers:[{urls:"stun:stun2.l.google.com:19302"}],iceCandidatePoolSize:2,bundlePolicy:"max-bundle"},iceServers:void 0,iceTransport:eo.UDP,muteOnAutoplayRestriction:!0,maintainConnectionOnSubscribeErrors:!1,signalingSocketOnly:!1,dataChannelConfiguration:void 0,socketSwitchDelay:1e3,bypassAvailable:!1,maintainStreamVariant:!1,enableChannelSignaling:!0,trickleIce:!0,postEmptyOffer:!1,mungeOffer:void 0,mungeAnswer:void 0,buffer:0,liveSeek:{enabled:!1,baseURL:void 0,fullURL:void 0,hlsjsRef:void 0,hlsElement:void 0,usePlaybackControlsUI:!0,options:{debug:!1,backBufferLength:0}}},Xi=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Wi(e,t)}(v,e);var t,n,r,o,i,a,s,c,u,l,d,f,h=Yi(v);function v(e,t){var n,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];Fi(this,v),n=h.call(this);var o=e?qr(e):qi;return o.mediaElementId=t?t.id:qi.mediaElementId,o.trickleIce=r,n._whipHelper=void 0,e&&n._internalConnect(o),n}return t=v,(n=[{key:"_internalConnect",value:(f=Mi(xi().mark((function e(t){return xi().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.init(t);case 2:return e.next=4,this.subscribe();case 4:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"waitToGatherIce",value:(d=Mi(xi().mark((function e(t){return xi().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){if("complete"===t.iceGatheringState)t.addIceCandidate({candidate:""}).then((function(){e({local:t.localDescription})})).catch((function(n){m("WHEPClient",n.message||n),e({local:t.localDescription})}));else{var n=setTimeout((function(){clearTimeout(n),t.addIceCandidate({candidate:""}).then((function(){e({local:t.localDescription})})).catch((function(n){m("WHEPClient",n.message||n),e({local:t.localDescription})}))}),5e3);t.addEventListener("icegatheringstatechange",(function(){"complete"===t.iceGatheringState&&(clearTimeout(n),t.addIceCandidate({candidate:""}).then((function(){e({local:t.localDescription})})).catch((function(n){m("WHEPClient",n.message||n),e({local:t.localDescription})})))}))}})));case 1:case"end":return e.stop()}}),e)}))),function(e){return d.apply(this,arguments)})},{key:"_sendCandidate",value:function(e){p("WHEPClient","[sendcandidate]"),this.trigger(new Ft(Et.CANDIDATE_START,this,e))}},{key:"_postOffer",value:(l=Mi((function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return xi().mark((function r(){var o,i,a,s,c,u,l,d,f;return xi().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,o=t._options,i=o.maintainStreamVariant,a=o.videoEncoding,s=o.audioEncoding,c=o.connectionParams,u=o.postEmptyOffer,l=o.mungeOffer,d=Hi(Hi({},c),{},{doNotSwitch:i}),void 0!==a&&a!==Zr.NONE&&(d.videoEncoding=a),void 0!==s&&s!==$r.NONE&&(d.audioEncoding=s),f="",u||(f=e.sdp,l&&(f=l(f)),n||(f=ze(f),f=Ke(f))),r.next=9,t._whipHelper.postSDPOffer(f,d,!1);case 9:return r.abrupt("return",r.sent);case 12:r.prev=12,r.t0=r.catch(0),m("WHEPClient",r.t0.message||r.t0),r.t0 instanceof U?t.onStreamUnavailable(r.t0):(t.trigger(new Ft(wt.CONNECT_FAILURE,t)),t.unsubscribe(),t._subscriptionResolver.reject("Stream failure."));case 16:case"end":return r.stop()}}),r,null,[[0,12]])}))()})),function(e){return l.apply(this,arguments)})},{key:"_postEmptyOffer",value:(u=Mi(xi().mark((function e(){var t,n,r,o,i,a;return xi().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=this._options,n=t.maintainStreamVariant,r=t.videoEncoding,o=t.audioEncoding,i=t.connectionParams,a=Hi(Hi({},i),{},{doNotSwitch:n}),void 0!==r&&r!==Zr.NONE&&(a.videoEncoding=r),void 0!==o&&o!==$r.NONE&&(a.audioEncoding=o),e.next=7,this._whipHelper.postSDPOffer("",a,!1);case 7:return e.abrupt("return",e.sent);case 10:throw e.prev=10,e.t0=e.catch(0),m("WHEPClient",e.t0.message||e.t0),this.onStreamUnavailable(e.t0),e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,10]])}))),function(){return u.apply(this,arguments)})},{key:"_postAnswer",value:(c=Mi(xi().mark((function e(t,n,r){var o,i,a;return xi().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=this._options.mungeAnswer,p("WHEPClient","[sendanswer]: streamname(".concat(t,"), subscriptionid(").concat(n,")")),i=r.sdp,a=i,o&&(a=o(a)),e.next=7,this._whipHelper.postSDPAnswer(a);case 7:return e.abrupt("return",e.sent);case 8:case"end":return e.stop()}}),e,this)}))),function(e,t,n){return c.apply(this,arguments)})},{key:"_postCandidateFragments",value:(s=Mi(xi().mark((function e(t){var n;return xi().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=it(t,void 0,!0),e.next=3,this._whipHelper.trickle(n);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"_requestOffer",value:(a=Mi(xi().mark((function e(){var t,n,r,o,i,a,s,c,u,l,d,f,h,v,y,b,g,_,w,S,E,C;return xi().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(p("WHEPClient","[requestoffer]"),t=this._options,n=t.trickleIce,r=t.postEmptyOffer,o=t.streamName,i=t.subscriptionId,a=t.mungeOffer,s=t.mungeAnswer,c=this.getPeerConnection(),e.prev=3,!r){e.next=17;break}return this.trigger(new Ft(Et.OFFER_START,this)),e.next=8,this._postEmptyOffer();case 8:return u=e.sent,l=u.sdp,this.trigger(new Ft(Et.OFFER_END,this)),d=new vt({type:"offer",sdp:l}),p("WHEPClient","[requestoffer:empty:remote] ".concat(JSON.stringify(d,null,2))),e.next=15,c.setRemoteDescription(d);case 15:e.next=33;break;case 17:return c.addTransceiver("video",{direction:"recvonly"}),c.addTransceiver("audio",{direction:"recvonly"}),this.trigger(new Ft(Et.OFFER_START,this)),e.next=22,c.createOffer();case 22:return f=e.sent,this.trigger(new Ft(Et.OFFER_END,this)),e.next=26,this._postOffer(f,n);case 26:return h=e.sent,v=h.sdp,y=a?a(v):v,b=new vt({type:"offer",sdp:y}),p("WHEPClient","[requestoffer:remote] ".concat(JSON.stringify(b,null,2))),e.next=33,c.setRemoteDescription(b);case 33:return e.next=35,c.createAnswer();case 35:if((g=e.sent).sdp=s?s(g.sdp):g.sdp,!n||!c.canTrickleIceCandidates){e.next=56;break}return p("WHEPClient","[trickle:ice] enabled"),g.sdp=Je(g.sdp),g.sdp=ot(g.sdp),e.next=43,c.setLocalDescription(g);case 43:return p("WHEPClient","[create:answer:local] ".concat(JSON.stringify({type:"answer",sdp:g.sdp},null,2))),this.trigger(new Ft(Et.ANSWER_START,this,g)),e.next=47,this._postAnswer(o,i,g);case 47:return this.trigger(new Ft(Et.ANSWER_END,this)),e.next=50,this.waitToGatherIce(c);case 50:return _=e.sent,w=_.local,e.next=54,this._postCandidateFragments(w.sdp);case 54:e.next=72;break;case 56:return p("WHEPClient","[trickle:ice] disabled"),g.sdp=ze(g.sdp),g.sdp=ot(g.sdp),e.next=61,c.setLocalDescription(g);case 61:return e.next=63,this.waitToGatherIce(c);case 63:return S=e.sent,E=S.local,C=ze(E.sdp),C=Ke(C),p("WHEPClient","[create:answer:local] ".concat(JSON.stringify({type:"answer",sdp:C},null,2))),this.trigger(new Ft(Et.ANSWER_START,this,{type:"answer",sdp:C})),e.next=71,this._postAnswer(o,i,{type:"answer",sdp:C});case 71:this.trigger(new Ft(Et.ANSWER_END,this));case 72:e.next=78;break;case 74:throw e.prev=74,e.t0=e.catch(3),m("WHEPClient",e.t0),e.t0;case 78:case"end":return e.stop()}}),e,this,[[3,74]])}))),function(){return a.apply(this,arguments)})},{key:"_disconnect",value:function(){this._whipHelper&&this._whipHelper.tearDown(),this._whipHelper=void 0,Vi(Ji(v.prototype),"_disconnect",this).call(this)}},{key:"init",value:(i=Mi((function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return xi().mark((function r(){var o,i,a,s,c,u,l,d,f,h;return xi().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(He()){r.next=4;break}throw new Error("Cannot create WebRTC playback instance. Your environment does not support WebRTC and/or WebSockets.");case 4:return t._disconnect(),t._options=Hi(Hi({},qi),e),t._options.subscriptionId=t._options.subscriptionId||Ki(),o=t._options,i=o.protocol,a=o.host,s=o.port,c=o.app,u=o.streamName,l=o.subscriptionId,d=o.enableChannelSignaling,f=i.match(/^http/)?i:"ws"===i?"http":"https",h="".concat(f,"://").concat(a,":").concat(s,"/").concat(c),t._whipUrl="".concat(h,"/whep/endpoint/").concat(u,"?requestId=").concat(l),t._whipHelper=new Ai(t._whipUrl,d),t._peerHelper=new Ur(t),t._messageTransport=t._whipHelper,t._mediaTransform=n,t._mediaTransform&&!De()&&(t.trigger(new Ft(Et.UNSUPPORTED_FEATURE,t,{feature:"Insertable Streams",message:"You provided Media Transforms for track processing, but your current browser does not support the Insertable Streams API."})),t._mediaTransform=void 0),r.abrupt("return",t);case 17:case"end":return r.stop()}}),r)}))()})),function(e){return i.apply(this,arguments)})},{key:"subscribe",value:(o=Mi(xi().mark((function e(){var t,n,r,o,i,a,s,c,u,l,d,f,h,p,v;return xi().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(t=this._options).streamName,n=t.mediaElementId,r=t.rtcConfiguration,o=t.liveSeek,i=this._options,a=i.enableChannelSignaling,s=i.dataChannelConfiguration,(c=a&&Ie())&&!s&&(s={name:"red5pro"},this._options.dataChannelConfiguration=s),this._options.enableChannelSignaling=c,this._options.signalingSocketOnly=this._options.enableChannelSignaling,e.prev=6,this._setViewIfNotExist(this._view,n),e.next=10,this._getViewResolverPromise();case 10:return u=e.sent,o&&o.enabled?(l=o.hlsjsRef,d=o.usePlaybackControlsUI,f=o.options,ce.supportsNonNativeHLS(l)?this._sourceHandler=new Yn(u.view,this.getType(),f,d):(m("WHEPClient","Could not utilize the 'LiveSeek' request. This feature requires either native HLS playback or hls.js as a depenency."),this.trigger(new Ft(Et.LIVE_SEEK_UNSUPPORTED,this,{feature:"Live Seek",message:"Live Seek requires integration with the HLS.JS plugin in order work properly. Most likely you are viewing this on a browser that does not support the use of HLS.JS."})),this._sourceHandler=new Hn(u.view,this.getType()))):this._sourceHandler=new Hn(u.view,this.getType()),this._glomSourceHandlerAPI(this._sourceHandler),this._initHandler(this._options,this._sourceHandler),this._getAvailabilityResolverPromise().catch((function(){})),e.next=17,this._whipHelper.getOptions();case 17:return(h=e.sent)&&h.links&&(this._options.iceServers=h.links),p=this._options.enableChannelSignaling?s:void 0,v=r,void 0===r.encodedInsertableStreams&&(v=Object.assign(r,{encodedInsertableStreams:!!this._mediaTransform})),this._connect(v,p,this._options.iceServers),this._connectionClosed=!1,e.abrupt("return",this._getSubscriptionResolverPromise());case 27:throw e.prev=27,e.t0=e.catch(6),this.trigger(new Ft(wt.CONNECT_FAILURE),this,e.t0),e.t0;case 31:case"end":return e.stop()}}),e,this,[[6,27]])}))),function(){return o.apply(this,arguments)})},{key:"onPeerConnectionOpen",value:function(){var e=this._options.enableChannelSignaling;Vi(Ji(v.prototype),"onPeerConnectionOpen",this).call(this),this._subscriptionResolver.resolve(this),e||this.trigger(new Ft(wt.SUBSCRIBE_START,this)),this._playIfAutoplaySet(this._options,this._view),this._startSeekable(this._options,this._view)}},{key:"onDataChannelOpen",value:function(e){var t=this._options.dataChannelConfiguration;if(Vi(Ji(v.prototype),"onDataChannelOpen",this).call(this,e),t){var n=t.name;Vi(Ji(v.prototype),"onDataChannelAvailable",this).call(this,n)}else Vi(Ji(v.prototype),"onDataChannelAvailable",this).call(this);this.trigger(new Ft(wt.SUBSCRIBE_START,this))}},{key:"getConnection",value:function(){}}])&&Ui(t.prototype,n),r&&Ui(t,r),Object.defineProperty(t,"prototype",{writable:!1}),v}(Ro);function Qi(e){return(Qi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $i(e){return function(e){if(Array.isArray(e))return Zi(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Zi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Zi(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ea(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Qi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Qi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Qi(i)?i:String(i)),r)}var o,i}function ta(e,t){return(ta=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function na(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=oa(e);if(t){var o=oa(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return ra(this,n)}}function ra(e,t){if(t&&("object"===Qi(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function oa(e){return(oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ia,aa=new A,sa=((ia=new Map).set(Qr.RTC,Ro),ia.set(Qr.RTMP,Zo),ia.set(Qr.HLS,bi),ia),ca=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ta(e,t)}(i,e);var t,n,r,o=na(i);function i(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(e=o.call(this))._options=void 0,e._order=[Qr.RTC,Qr.RTMP,Qr.HLS],e}return t=i,(n=[{key:"_getPlaybackFromOrder",value:function(e,t){return aa.create(e,sa,t,"init")}},{key:"getPlaybackOrder",value:function(){return this._order}},{key:"setPlaybackOrder",value:function(e){var t=(e="string"==typeof e?[e]:e).filter((function(e){var t;for(t in Qr)if(Qr[t].toLowerCase()===e.toLowerCase())return!0;return!1})).map((function(e){return e.toLowerCase()}));return this._order=$i(new Set(t)),p("Red5ProSubscriber","[orderupdate]: ".concat(this._order)),this}},{key:"init",value:function(e){this._options=e;var t=new S;return p("Red5ProSubscriber","[init]"),this._getPlaybackFromOrder(this._order,this._options).then((function(e){p("Red5ProSubscriber","[init:success]: subscriber found ".concat(e.getType())),t.resolve(e)})).catch((function(e){v("Red5ProSubscriber","[playerror]: Could not implement a subscriber: ".concat(e)),t.reject(e)})),t.promise}},{key:"playbackTypes",get:function(){return Qr}}])&&ea(t.prototype,n),r&&ea(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(H);function ua(e){return(ua="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function la(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function da(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==ua(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ua(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===ua(i)?i:String(i)),r)}var o,i}var fa=function(e,t){var n,r=ge(t);if("boolean"==typeof e.video)r.video=e.video;else{var o;for(o in e.video)r[o]=(n=e.video[o]).exact||n.ideal||n.max||n.min||n;r.video=!0}return r.audio="boolean"!=typeof e.audio||e.audio,r},ha=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;la(this,e),this.video=t,this.clone=this.video.cloneNode(!0),this.holder=this.video.parentNode,this._publisherType=n,this._swfId=null,this._embedFuture=void 0,this._soResponder=r||new Ho}var t,n,r;return t=e,(n=[{key:"getEmbedOperation",value:function(){return this._embedFuture=E.createIfNotExist(this._embedFuture),this._embedFuture.promise}},{key:"cleanUp",value:function(){this.video.remove(),this.video=this.clone.cloneNode(!0),this.holder.appendChild(this.video),this._embedFuture=void 0}},{key:"addSource",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;p("R5ProPublisherSourceHandler","[addsource]");var o=this;this._swfId=e,this._embedFuture=E.createIfNotExist(this._embedFuture);var i=this._embedFuture;return t.swf=n||t.swf,t.minFlashVersion=r||t.minFlashVersion,jo(this.video,this.holder).then((function(n){p("R5ProPublisherSourceHandler","[element:complete]");var r={buffer:null!=t.buffer?t.buffer:1,streamMode:t.streamMode,streamName:t.streamName,appName:t.app,host:t.host};return t.backgroundColor&&(r.backgroundColor=t.backgroundColor),t.context&&(r.roomName=t.context),"100%"!==t.embedWidth&&"100%"!==t.embedHeight||(r.autosize=!0),void 0!==t.connectionParams&&(r.connectionParams=encodeURIComponent(JSON.stringify(t.connectionParams))),r=fa(t.mediaConstraints,r),Ao(e,t,r,ce.getSwfObject(),n)})).then((function(){p("R5ProPublisherSourceHandler","[embed:complete]"),i.resolve(o)})).catch((function(e){return i.reject(e)})),i.promise}},{key:"connect",value:function(e){p("R5ProPublisherSourceHandler","[connect]");var t=ce.getEmbedObject(this._swfId);t?(t.connect(e),this._soResponder.connect(this._swfId)):v("R5ProPublisherSourceHandler","Could not determine embedded element with swf id: "+this._swfId+".")}},{key:"disconnect",value:function(){p("R5ProPublisherSourceHandler","[disconnect]");try{var e=ce.getEmbedObject(this._swfId);e&&e.disconnect()}catch(e){}this.cleanUp(),this._soResponder.disconnect()}},{key:"send",value:function(e,t){var n=ce.getEmbedObject(this._swfId);n&&n.send(e,t)}},{key:"addSharedObjectResponseHandler",value:function(e){this._soResponder.addResponseHandler(e)}},{key:"removeSharedObjectResponseHandler",value:function(e){this._soResponder.removeResponseHandler(e)}},{key:"sendToSharedObject",value:function(e,t,n){this._soResponder.sendToSharedObject(e,t,n)}},{key:"sendPropertyToSharedObject",value:function(e,t,n){this._soResponder.sendPropertyToSharedObject(e,t,n)}},{key:"getRemoteSharedObject",value:function(e){this._soResponder.getRemoteSharedObject(e)}},{key:"connectToSharedObject",value:function(e){this._soResponder.connectToSharedObject(e)}},{key:"closeSharedObject",value:function(e){this._soResponder.closeSharedObject(e)}},{key:"setMediaQuality",value:function(e){var t=ce.getEmbedObject(this._swfId);if(t&&e.video&&"boolean"!=typeof e.video){var n=isNaN(e.video.width)?Number.isNaN:be(e.video.width),r=isNaN(e.video.height)?Number.isNaN:be(e.video.height);t.updateResolution(n,r)}}},{key:"getType",value:function(){return this._publisherType}}])&&da(t.prototype,n),r&&da(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function pa(e){return(pa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function va(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ma(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==pa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==pa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===pa(i)?i:String(i)),r)}var o,i}var ya=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"red5pro-publisher";va(this,e);try{this._targetElement=ce.resolveElement(t)}catch(e){throw m("R5ProPublishView","Could not instantiate a new instance of Red5ProPublisher. Reason: ".concat(e.message)),e}}var t,n,r;return t=e,(n=[{key:"attachPublisher",value:function(e){p("R5ProPublishView","[attachpublisher]"),e.setView(this,ce.getElementId(this._targetElement))}},{key:"preview",value:function(e){var t=this.isAutoplay;p("R5ProPublishView","[preview]: autoplay(".concat(t,")")),ce.setVideoSource(this._targetElement,e,t)}},{key:"unpreview",value:function(){ce.setVideoSource(this._targetElement,null,this.isAutoplay)}},{key:"isAutoplay",get:function(){return ce.hasAttributeDefined(this._targetElement,"autoplay")}},{key:"view",get:function(){return this._targetElement}}])&&ma(t.prototype,n),r&&ma(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),ba=Object.freeze({RTMP:"rtmp",RTC:"rtc"}),ga=Object.freeze({LIVE:"live",RECORD:"record",APPEND:"append"}),_a=(Object.freeze({OPUS:"Opus",PCMU:"PCMU",PCMA:"PCMA"}),Object.freeze({VP8:"VP8",VP9:"VP9",H264:"H264"}));function wa(e){return(wa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Sa(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==wa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==wa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===wa(i)?i:String(i)),r)}var o,i}function Ea(e,t,n){return t&&Sa(e.prototype,t),n&&Sa(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ca(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var Oa=function(e,t,n){Object.defineProperty(e,t,{value:n,writable:!0,enumerable:!0})},ka=Ea((function e(){Ca(this,e),Oa(this,"width",{exact:640}),Oa(this,"height",{exact:480})})),Pa=Ea((function e(){Ca(this,e),Oa(this,"width",640),Oa(this,"height",480),Oa(this,"force",!1),Oa(this,"framerate",15),Oa(this,"bandwidth",5e4),Oa(this,"quality",80),Oa(this,"profile","baseline"),Oa(this,"level",3.1)})),Ta=Ea((function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;Ca(this,e),Oa(this,"audio",t),Oa(this,"video",n||new ka)}));function Ra(e){return(Ra="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ja(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Ra(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ra(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Ra(i)?i:String(i)),r)}var o,i}function Aa(e,t){return(Aa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function La(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ha(e);if(t){var o=Ha(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Na(this,n)}}function Na(e,t){if(t&&("object"===Ra(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Ha(e){return(Ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Ia={protocol:"rtmp",port:1935,streamMode:"live",mediaElementId:"red5pro-publisher",embedWidth:"100%",embedHeight:"100%",minFlashVersion:"10.0.0",swf:"lib/red5pro/red5pro-publisher.swf",swfobjectURL:"lib/swfobject/swfobject.js",productInstallURL:"lib/swfobject/playerProductInstall.swf",mediaConstraints:new(Ea((function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;Ca(this,e),Oa(this,"audio",t),Oa(this,"video",n||new Pa)})))},xa=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Aa(e,t)}(i,e);var t,n,r,o=La(i);function i(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(e=o.call(this))._options=void 0,e._view=void 0,e._sourceHandler=void 0,e._elementId=void 0,e._connectFuture=void 0,e}return t=i,(n=[{key:"_setViewIfNotExist",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(void 0===e&&void 0!==t){var n=new ya(t);n.attachPublisher(this)}}},{key:"setView",value:function(e,t){var n=this;return this._view=e,this._elementId=t,void 0!==this._sourceHandler&&(this._sourceHandler.disconnect(),this._sourceHandler=void 0),this._view&&(this._sourceHandler=new ha(this._view.view,this.getType())),this._options&&this._sourceHandler&&this._sourceHandler.addSource(this._elementId,this._options).catch((function(e){m("RTMPPublisher","Could not establish proper RTMP publisher: ".concat(e)),n.trigger(new Mt(_t.EMBED_FAILURE,n))})),this}},{key:"_setUpConnectCallback",value:function(e){var t=this;window.setActiveId=function(n){p("RTMPPublisher","Embed and connect() complete for publisher swf. successId(".concat(n,").")),e.resolve(t),t.trigger(new Mt(_t.EMBED_SUCCESS,t)),t._tearDownConnectCallback()}}},{key:"_tearDownConnectCallback",value:function(){window.setActiveId=void 0}},{key:"_establishExtIntHandlers",value:function(){var e=this,t=this._options.streamName,n=function(e){return["publisher",e,t.split("-").join("_")].join("_")};window[n("r5proConnectClosed")]=function(){e.trigger(new Mt(yt.CONNECTION_CLOSED,e))},window[n("r5proConnectSuccess")]=function(){return e.trigger(new Mt(yt.CONNECT_SUCCESS,e))},window[n("r5proUnpublishSuccess")]=function(){return e.trigger(new Mt(yt.UNPUBLISH_SUCCESS,e))},window[n("r5proPublishStart")]=function(){e._connectFuture.resolve(e),e.trigger(new Mt(yt.PUBLISH_START,e))},window[n("r5proPublishMetadata")]=function(t){return e.trigger(new Mt(yt.PUBLISH_METADATA,e,t))},window[n("r5proPublishInsufficientBW")]=function(t){return e.trigger(new Mt(yt.PUBLISH_INSUFFICIENT_BANDWIDTH,e,t))},window[n("r5proPublishSufficientBW")]=function(t){return e.trigger(new Mt(yt.PUBLISH_SUFFICIENT_BANDWIDTH,e,t))},window[n("r5proPublishRecoveringBW")]=function(t){return e.trigger(new Mt(yt.PUBLISH_RECOVERING_BANDWIDTH,e,t))},window[n("r5proConnectFailure")]=function(){e._connectFuture.reject(yt.CONNECT_FAILURE),e.trigger(new Mt(yt.CONNECT_FAILURE,e))},window[n("r5proPublishFail")]=function(){e._connectFuture.reject(yt.PUBLISH_FAIL),e.trigger(new Mt(yt.PUBLISH_FAIL,e))},window[n("r5proPublishInvalidName")]=function(){e._connectFuture.reject(yt.PUBLISH_INVALID_NAME),e.trigger(new Mt(yt.PUBLISH_INVALID_NAME,e))}}},{key:"init",value:function(e){var t=this,n=new S,r=e.minFlashVersion||Ia.minFlashVersion;if(ce.supportsFlashVersion(r)){this._options=Object.assign({},Ia,e);try{ce.injectScript(this._options.swfobjectURL).then((function(){return p("RTMPPublisher","SWFObject embedded."),t._sourceHandler?(p("RTMPPublisher","Publish handler established."),t._sourceHandler.addSource(t._elementId,t._options)):(p("RTMPPublisher","Publish handler not established."),!0)})).then((function(){t._setViewIfNotExist(t._view,t._options.mediaElementId),n.resolve(t)})).catch((function(e){m("RTMPPublisher","Could not embed Flash-based RTMP Publisher. Reason: ".concat(e)),t._sourceHandler&&t._sourceHandler.disconnect(),n.reject(e),t.trigger(new Mt(_t.EMBED_FAILURE,t))}))}catch(e){n.reject("Could not inject Flash-based Publisher into the page. Reason: ".concat(e.message)),t.trigger(new Mt(_t.EMBED_FAILURE,t))}}else n.reject("Could not resolve RTMPPublisher instance. Requires minimum Flash Player install of ".concat(r));return n.promise}},{key:"publish",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=n||new S;this._setUpConnectCallback(r),this._options.streamName=t||this._options.streamName;var o=this._options;try{var i=this._sourceHandler;this._sourceHandler.getEmbedOperation().then((function(){p("RTMPPublisher","[handler:embed:complete]"),ce.getEmbedObject(e._elementId)&&e._establishExtIntHandlers();var t=0;!function e(){var n;n=setTimeout((function(){try{clearTimeout(n),i.connect(JSON.stringify(o))}catch(n){if(t++>100)throw n;e()}}),300)}()})).catch((function(t){r.reject(t),e.trigger(new Mt(yt.CONNECT_FAILURE,e))}))}catch(e){m("RTMPPublisher","[handler:embed:error]"),r.reject("Could not initiate connection sequence. Reason: ".concat(e.message)),this.trigger(new Mt(yt.CONNECT_FAILURE,this)),this._tearDownConnectCallback()}return this._connectFuture=r,r.promise}},{key:"unpublish",value:function(){var e=new S;try{ce.getEmbedObject(this._elementId).unpublish(),e.resolve()}catch(t){m("RTMPPublisher","Could not initiate publish sequence. Reason: ".concat(t.message)),e.reject(t.message)}return this._connectFuture=void 0,e.promise}},{key:"send",value:function(e,t){this._sourceHandler.send(e,"string"==typeof t?t:JSON.stringify(t))}},{key:"setMediaQuality",value:function(e){this._sourceHandler&&this._sourceHandler.setMediaQuality(e)}},{key:"overlayOptions",value:function(e){this._options=Object.assign(this._options,e)}},{key:"getConnection",value:function(){return this._sourceHandler}},{key:"getOptions",value:function(){return this._options}},{key:"getType",value:function(){return ba.RTMP.toUpperCase()}}])&&ja(t.prototype,n),r&&ja(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(H);function Da(e){return(Da="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ma(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Fa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ma(Object(n),!0).forEach((function(t){Ua(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ma(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ua(e,t,n){return(t=Va(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ba(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Va(r.key),r)}}function Va(e){var t=function(e,t){if("object"!==Da(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Da(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Da(t)?t:String(t)}function Ga(){return(Ga="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Wa(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function Wa(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Ka(e)););return e}function Ya(e,t){return(Ya=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function za(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ka(e);if(t){var o=Ka(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Ja(this,n)}}function Ja(e,t){if(t&&("object"===Da(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Ka(e){return(Ka=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var qa=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ya(e,t)}(i,e);var t,n,r,o=za(i);function i(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),o.call(this,e,"R5ProPublisherSocket")}return t=i,(n=[{key:"respond",value:function(e){if(e.data){var t=this.getJsonFromSocketMessage(e);if(!Ga(Ka(i.prototype),"respond",this).call(this,e)){if(void 0!==t.data){if(void 0!==t.data.sdp&&"answer"===t.data.sdp.type)return this._responder.onSDPAnswer(t.data),!0;if(void 0!==t.data.candidate)return we(t.data.candidate)?(this._responder.onEmptyCandidate(),!0):(this._responder.onAddIceCandidate(t.data.candidate),!0);if("status"===t.data.type)if("NetConnection.ICE.TricleCompleted"===t.data.code||"NetConnection.ICE.TrickleCompleted"===t.data.code)this._responder.onSocketIceCandidateEnd();else if("NetConnection.Publish.InsufficientBW"===t.data.code)this._responder.onInsufficientBandwidth(t.data);else if("NetConnection.Publish.SufficientBW"===t.data.code)this._responder.onSufficientBandwidth(t.data);else{if("NetConnection.Publish.RecoveringBW"!==t.data.code)return this._responder.onPublisherStatus(t.data),!0;this._responder.onRecoveringBandwidth(t.data)}if(void 0!==t.type&&"metadata"===t.type.toLowerCase())return void 0!==t.method?(this._responder.onSendReceived(t.method,t.data),!0):(this._responder.onMetaData(t.data),!0);if(void 0!==t.send){var n=t.send,r=t.senderName,o=t.dcLabel,a=n.data,s=n.method;return a=Fa(Fa({},a),{},{senderName:r,dcLabel:o}),this._responder.onSendReceived(s,a),!0}}this._responder.onSocketMessage(this,e)}}else v("R5ProPublisherSocket","[ws.onmessage] - No Message Data.")}},{key:"postUnpublish",value:function(e){return void 0!==this._websocket&&1===this._openState&&(this.post({unpublish:e}),!0)}},{key:"postUnjoin",value:function(e,t){return void 0!==this._websocket&&1===this._openState&&(p("R5ProPublisherSocket","[peerconnection:leavegroup]"),this.post({leaveGroup:e,streamName:t}))}}])&&Ba(t.prototype,n),r&&Ba(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(ar);function Xa(e){return(Xa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Qa(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Qa=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==Xa(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function $a(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function Za(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Xa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Xa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Xa(i)?i:String(i)),r)}var o,i}function es(){return(es="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=ts(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function ts(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=is(e)););return e}function ns(e,t){return(ns=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function rs(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=is(e);if(t){var o=is(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return os(this,n)}}function os(e,t){if(t&&("object"===Xa(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function is(e){return(is=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var as=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ns(e,t)}(s,e);var t,n,r,o,i,a=rs(s);function s(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),a.call(this,e,"R5ProPublishPeer")}return t=s,(n=[{key:"_removeConnectionHandlers",value:function(e){e.onconnectionstatechange=void 0,e.oniceconnectionstatechange=void 0,e.onicecandidate=void 0,e.ontrack=void 0}},{key:"_addConnectionHandlers",value:function(e,t){var n,r=this;e.ontrack=function(e){p("R5ProPublishPeer","[peer:ontrack]"),console.log(e),r._responder.onPeerConnectionTrackAdd(e.track)},e.onconnectionstatechange=function(){p("R5ProPublishPeer","[peer.onconnectionstatechange] - State: ".concat(e.connectionState)),"connected"===e.connectionState?(p("R5ProPublishPeer","[peerconnection:open]"),t?t.resolve(r):r._responder.onPeerConnectionOpen()):"failed"!==e.connectionState&&"disconnected"!==e.connectionState||(v("R5ProPublishPeer","[peerconnection:error]"),t&&t.reject(),"failed"===e.connectionState&&r._responder.onPeerConnectionFail())},e.onicecandidate=function(e){p("R5ProPublishPeer","[peer.onicecandidate] - Peer Candidate: ".concat(e.candidate)),e.candidate?r._responder.onIceCandidate(e.candidate):e.candidate},e.oniceconnectionstatechange=function(t){var o=e.iceConnectionState;p("R5ProPublishPeer","[peer.oniceconnectionstatechange] - State: ".concat(o)),"connected"===o&&ce.getIsEdge()?(p("R5ProPublishPeer","[edge/ortc:notify complete]"),r._responder.onPeerGatheringComplete()):"failed"===o?(n&&clearTimeout(n),r._responder.onPeerConnectionClose(t)):"disconnected"===o?n=setTimeout((function(){p("R5ProPublishPeer","[peer.oniceconnectionstatechange] - Reconnect timeout reached. Closing PeerConnection."),clearTimeout(n),r._responder.onPeerConnectionClose(t)}),5e3):n&&(p("R5ProPublishPeer","[peer.oniceconnectionstatechange] - Clearing timeout for reconnect."),clearTimeout(n))},e.onsignalingstatechange=function(t){var n=e.signalingState;p("R5ProPublishPeer","[peer.onsignalingstatechange] - State: ".concat(n))},e.onicegatheringstatechange=function(){var t=e.iceGatheringState;p("R5ProPublishPeer","[peer.onicegatheringstatechange] - State: ".concat(t)),"complete"===t&&r._responder.onPeerGatheringComplete()},e.onremovestream=function(){p("R5ProPublishPeer","[peer.onremovestream]")}}},{key:"_onDataChannelMessage",value:function(e){var t=e;if(es(is(s.prototype),"_onDataChannelMessage",this).call(this,e))return!0;var n=this.getJsonFromSocketMessage(t);if(null===n)return v(this._name,"Determined websocket response not in correct format. Aborting message handle."),!0;p(this._name,"[datachannel-response]: "+JSON.stringify(n,null,2));var r=n.data;return r&&"status"===r.type?"NetStream.Play.UnpublishNotify"===r.code?(this._responder.onUnpublish(),!0):(p("R5ProPublishPeer","[datachannel.message] status :: ".concat(r.code)),this._responder.onPublisherStatus(r),!0):(this._responder.onDataChannelMessage(this._dataChannel,t),!1)}},{key:"getUserMedia",value:function(e,t){return We(e,t)}},{key:"forceUserMedia",value:function(e){return function(e){return ce.gUM(e)}(e)}},{key:"createOffer",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;p("R5ProPublishPeer","[createoffer]");var o=r||new S;return this._peerConnection.createOffer().then((function(r){e.setLocalDescription(r,t).then((function(){var i=r.sdp;t&&(i=rt(t,i)),n&&(i=Qe(i),i=$e(i),i=tt(i),i=nt(i)),r.sdp=i,e._responder.onSDPSuccess(),o.resolve(r)})).catch((function(t){e._responder.onSDPError(t),o.reject(t)}))})).catch((function(e){p("R5ProPublishPeer","[createoffer:error]"),o.reject(e)})),o.hasOwnProperty("promise")?o.promise:o}},{key:"createOfferWithoutSetLocal",value:(o=function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return Qa().mark((function r(){var o,i;return Qa().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return p("R5ProPublishPeer","[createoffer:withoutlocal]"),r.prev=1,r.next=4,e._peerConnection.createOffer();case 4:return o=r.sent,i=o.sdp,t&&(i=rt(t,i)),n&&(i=Qe(i),i=$e(i),i=tt(i),i=nt(i)),o.sdp=i,e._responder.onSDPSuccess(),r.abrupt("return",o);case 13:throw r.prev=13,r.t0=r.catch(1),p("R5ProPublishPeer","[createoffer:error]"),e._responder.onSDPError(r.t0),r.t0;case 18:case"end":return r.stop()}}),r,null,[[1,13]])}))()},i=function(){var e=this,t=arguments;return new Promise((function(n,r){var i=o.apply(e,t);function a(e){$a(i,n,r,a,s,"next",e)}function s(e){$a(i,n,r,a,s,"throw",e)}a(void 0)}))},function(){return i.apply(this,arguments)})},{key:"postUnpublish",value:function(e){var t=this.post({unpublish:e});return p("R5ProPublishPeer","[peerconnection:unpublish] complete: ".concat(t)),t}},{key:"postUnjoin",value:function(e,t){return p("R5ProPublishPeer","[peerconnection:leavegroup]"),this.post({leaveGroup:e,streamName:t})}}])&&Za(t.prototype,n),r&&Za(t,r),Object.defineProperty(t,"prototype",{writable:!1}),s}(Ar);function ss(e){return(ss="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function cs(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */cs=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==ss(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function us(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function ls(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==ss(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ss(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===ss(i)?i:String(i)),r)}var o,i}function ds(e,t){return(ds=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function fs(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=vs(e);if(t){var o=vs(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return hs(this,n)}}function hs(e,t){if(t&&("object"===ss(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ps(e)}function ps(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function vs(e){return(vs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ms=/(.*) starting/i,ys=/(.*) stopping/i,bs=function(e){p("RTCPublisher",e)},gs=function(e){v("RTCPublisher",e)},_s=function(e){m("RTCPublisher",e)},ws={protocol:"wss",port:443,app:"live",streamMode:ga.LIVE,keyFramerate:3e3,mediaElementId:"red5pro-publisher",rtcConfiguration:{iceServers:[{urls:"stun:stun2.l.google.com:19302"}],iceCandidatePoolSize:2,bundlePolicy:"max-bundle"},iceServers:void 0,iceTransport:eo.UDP,bandwidth:{audio:56,video:512},clearMediaOnUnpublish:!1,mediaConstraints:new Ta,onGetUserMedia:void 0,signalingSocketOnly:!0,dataChannelConfiguration:void 0,forceVP8:!1,socketSwitchDelay:1e3,bypassAvailable:!1},Ss=function(e){var t={audio:!1,video:!1},n={audio:!1,video:!1};return e.getTracks().forEach((function(e){"video"===e.kind?(n.video=e.getSettings(),t.video=e.getConstraints()):"audio"===e.kind&&(n.audio=e.getSettings(),t.audio=e.getConstraints())})),{requested:t,accepted:n}},Es=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ds(e,t)}(s,e);var t,n,r,o,i,a=fs(s);function s(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(e=a.call(this))._options=void 0,e._view=void 0,e._peerHelper=void 0,e._socketHelper=void 0,e._messageTransport=void 0,e._mediaStream=void 0,e._mediaTransform=void 0,e._switchChannelRequest=void 0,e._streamFuture=void 0,e._availableFuture=void 0,e._peerFuture=void 0,e._offerFuture=void 0,e._sendOfferFuture=void 0,e._trickleEndFuture=void 0,e._publishFuture=void 0,e._unpublishFuture=void 0,e._onOrientationChange=e._onOrientationChange.bind(ps(e)),e._gUMRejectionHandler=e._onGUMRejection.bind(ps(e)),e}return t=s,(n=[{key:"_getMediaStream",value:function(){return this._streamFuture=E.createIfNotExist(this._streamFuture),this._streamFuture.promise}},{key:"_getTrickleEnd",value:function(){return E.createIfNotExist(this._trickleEndFuture)}},{key:"_gum",value:function(e){var t=this,n=function(e){if(!e)return!1;var t=e.getTracks();return!!(t&&t.length>1)&&"video"===t[0].kind};void 0!==e.onGetUserMedia?(bs("Requesting gUM from user-defined configuration:onGetUserMedia."),e.onGetUserMedia().then((function(r){if(n(r))return bs("We received a MediaStream with mismatching track listing. Trying again..."),void t._gum(e);t.trigger(new Mt(gt.CONSTRAINTS_ACCEPTED,t,Ss(r))),t._streamFuture.resolve(r)})).catch((function(n){_s("Could not resolve MediaStream from provided gUM. Error - ".concat(n)),t.trigger(new Mt(gt.CONSTRAINTS_REJECTED,t,{constraints:e.mediaConstraints})),t._streamFuture.reject(n)}))):(bs("Requesting gUM using mediaConstraints: ".concat(JSON.stringify(e.mediaConstraints,null,2))),this._peerHelper.getUserMedia(e.mediaConstraints,this._gUMRejectionHandler).then((function(r){if(n(r.media))return bs("We received a MediaStream with mismatching track listing. Trying again..."),void t._gum(e);bs("Found valid constraints: ".concat(JSON.stringify(r.constraints,null,2))),t.trigger(new Mt(gt.CONSTRAINTS_ACCEPTED,t,Ss(r.media))),t.trigger(new Mt(yt.DIMENSION_CHANGE,t,r.constraints)),t._streamFuture.resolve(r.media)})).catch((function(n){bs("Could not find valid constraint resolutions from: ".concat(JSON.stringify(n.constraints,null,2))),_s("Could not resolve MediaStream from provided mediaConstraints. Error - ".concat(n.error)),bs("Attempting to find resolutions from original provided constraints: ".concat(JSON.stringify(n.constraints,null,2))),t.trigger(new Mt(gt.CONSTRAINTS_REJECTED,t,{constraints:n.constraints})),e.onGetUserMedia=function(){return t._peerHelper.forceUserMedia(n.constraints)},t._gum(e)})))}},{key:"_onGUMRejection",value:function(e){this.trigger(new Mt(gt.CONSTRAINTS_REJECTED,this,{constraints:e}))}},{key:"_onOrientationChange",value:function(e){this.getMessageTransport()&&this.getMessageTransport().post({send:{method:"onMetaData",data:{deviceOrientation:e}}})}},{key:"_onMediaStreamReceived",value:function(e){this._mediaStream=e,this.trigger(new Mt(gt.MEDIA_STREAM_AVAILABLE,this,e)),this._view&&this._view.preview(this._mediaStream)}},{key:"_setViewIfNotExist",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(void 0===e&&void 0!==t){var n=new ya(t);n.attachPublisher(this)}}},{key:"_requestAvailability",value:function(e){return bs("[requestavailability]"),this._availableFuture=E.createIfNotExist(this._availableFuture),this._options.bypassAvailable?this._availableFuture.resolve(!0):this._socketHelper.post({isAvailable:e,bundle:!0}),this._availableFuture.promise}},{key:"_createPeerConnection",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return bs("[createpeeer]"),this._peerFuture=void 0,this._peerFuture=E.createIfNotExist(this._peerFuture),n&&e&&(gs("The iceServers configuration property is considered deprecated. Please use the rtcConfiguration configuration property upon which you can assign iceServers. Reference: https://www.red5pro.com/docs/streaming/migrationguide.html"),e.iceServers=n),void 0!==e?this._peerHelper.setUpWithPeerConfiguration(e,t,this._peerFuture):this._peerHelper.setUp(n,this._peerFuture,this._options.rtcpMuxPolicy)}},{key:"_createOffer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return bs("[createoffer]"),this._offerFuture=void 0,this._offerFuture=E.createIfNotExist(this._offerFuture),this._peerHelper.createOffer(e,t,!1,this._offerFuture),this._offerFuture.promise}},{key:"_setRemoteDescription",value:function(e){return bs("[setremotedescription]"),this._peerHelper.setRemoteDescription(e)}},{key:"_sendOffer",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];bs("[sendoffer]");var o={handleOffer:t,transport:n,data:{sdp:e}};return r&&(o.videoEncoding=_a.VP8),this._sendOfferFuture=void 0,this._sendOfferFuture=E.createIfNotExist(this._sendOffFuture),this._socketHelper.post(o),this._sendOfferFuture.promise}},{key:"_sendCandidate",value:function(e,t){bs("[sendcandidate]"),this._socketHelper.post({handleCandidate:t,data:{candidate:e}})}},{key:"_requestPublish",value:function(e,t,n){return bs("[requestpublish]"),this._publishFuture=void 0,this._publishFuture=E.createIfNotExist(this._publishFuture),this._socketHelper.post({publish:e,mode:t,keyFramerate:n}),this._publishFuture.promise}},{key:"_requestUnpublish",value:function(e){return this._unpublishFuture=void 0,this._unpublishFuture=E.createIfNotExist(this._unpublishFuture),this.getMessageTransport().postUnpublish(e)||this._unpublishFuture.resolve(),this._unpublishFuture.promise}},{key:"_setUpMediaTransform",value:(o=cs().mark((function e(t,n,r){var o,i,a,s,c,u,l,d;return cs().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=16;break}if(o=this._mediaTransform,i=o.audio,a=o.video,s=o.worker,c=n.getSenders().find((function(e){return e.track&&"video"===e.track.kind})),u=n.getSenders().find((function(e){return e.track&&"audio"===e.track.kind})),!c||!a&&!s){e.next=15;break}return e.prev=5,e.next=8,uo(t,c,r);case 8:(l=e.sent).generator&&(this._mediaStream.addTrack(l.generator),this._mediaStream.removeTrack(this._mediaStream.getVideoTracks()[0])),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(5),this.trigger(new Mt(gt.TRANSFORM_ERROR,this,{type:"video",error:e.t0}));case 15:if(u&&(i||s))try{(d=fo(t,u,r)).generator&&(this._mediaStream.addTrack(d.generator),this._mediaStream.removeTrack(this._mediaStream.getAudioTracks()[0]))}catch(e){this.trigger(new Mt(gt.TRANSFORM_ERROR,this,{type:"audio",error:e}))}case 16:case"end":return e.stop()}}),e,this,[[5,12]])})),i=function(){var e=this,t=arguments;return new Promise((function(n,r){var i=o.apply(e,t);function a(e){us(i,n,r,a,s,"next",e)}function s(e){us(i,n,r,a,s,"throw",e)}a(void 0)}))},function(e,t,n){return i.apply(this,arguments)})},{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;this._streamFuture=void 0;var n=new S;return He()&&mt()?(this._options=Object.assign({},ws,e),this._peerHelper=new as(this),this._socketHelper=new qa(this),this._messageTransport=this._messageTransport||this._socketHelper,this._mediaTransform=t,this._mediaTransform&&!De()&&(this.trigger(new Mt(gt.UNSUPPORTED_FEATURE,this,{feature:"Insertable Streams",message:"You provided Media Transforms for track processing, but your current browser does not support the Insertable Streams API."})),this._mediaTransform=void 0),this._getMediaStream().then(this._onMediaStreamReceived.bind(this)).catch((function(e){gs("[gum]: ".concat(e))})),this._gum(this._options),this._setViewIfNotExist(this._view,this._options.mediaElementId),n.resolve(this)):n.reject("Cannot create WebRTC playback instance. Your environment does not support WebRTC and/or WebSockets."),n.promise}},{key:"initWithStream",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;bs("[initWithStream]"),this._streamFuture=void 0;var r=new S;if(He()&&mt()){this._mediaTransform=n,this._mediaTransform&&!De()&&(this.trigger(new Mt(gt.UNSUPPORTED_FEATURE,this,{feature:"Insertable Streams",message:"You provided Media Transforms for track processing, but your current browser does not support the Insertable Streams API."})),this._mediaTransform=void 0),this._options=Object.assign({},ws,e),this._peerHelper=new as(this),this._socketHelper=new qa(this),this._messageTransport=this._messageTransport||this._socketHelper,this._setViewIfNotExist(this._view,this._options.mediaElementId);var o=this._getMediaStream();o.then(this._onMediaStreamReceived.bind(this)).catch((function(e){gs("[gum]: ".concat(e))})),this._streamFuture.resolve(t),r.resolve(this)}else r.reject("Cannot create WebRTC playback instance. Your environment does not support WebRTC and/or WebSockets.");return r.promise}},{key:"setView",value:function(e){return this._view=e,this._mediaStream&&this._view&&this._view.preview(this._mediaStream),this}},{key:"preview",value:function(){var e=this;bs("[preview]");var t=new Promise((function(t){t(e)}));return this._setViewIfNotExist(this._view,this._options.mediaElementId),t}},{key:"unpreview",value:function(){bs("[unpreview]"),this._mediaStream&&this._mediaStream.getTracks().forEach((function(e){e.stop()})),this._view&&this._view.unpreview()}},{key:"publish",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;bs("[publish]"),this._options.streamName=t||this._options.streamName;var r=n||new S,o=new S,i=Jr(this._options,{id:this._options.streamName});this._trickleEndFuture=this._getTrickleEnd(),this._peerHelper||(this._peerHelper=new as(this)),this._socketHelper?this._socketHelper.clearRetry():(this._socketHelper=new qa(this),this._messageTransport=this._socketHelper),this._socketHelper.setUp(i,o);var a=this._options,s=a.rtcConfiguration,c=a.signalingSocketOnly,u=a.dataChannelConfiguration,l=c&&Ie();return l&&!u&&(u={name:"red5pro"}),this._options.signalingSocketOnly=l,this._publishFuture=E.createIfNotExist(this._publishFuture),this._publishFuture.promise.catch((function(t){ce.removeOrientationChangeHandler(e._onOrientationChange),r.reject(t),e.trigger(new Mt(yt.CONNECT_FAILURE,e,t))})),o.promise.then((function(){return e.trigger(new Mt(yt.CONNECT_SUCCESS,e)),e._getMediaStream()})).then((function(){return e._requestAvailability(e._options.streamName,e._options.streamType)})).then((function(){var t=s;return void 0===s.encodedInsertableStreams&&(t=Object.assign(s,{encodedInsertableStreams:!!e._mediaTransform})),e._createPeerConnection(t,u,e._options.iceServers)})).then((function(t){if(e.trigger(new Mt(gt.PEER_CONNECTION_AVAILABLE,e,t)),e._mediaStream.getTracks().forEach((function(n){t.addTrack(n,e._mediaStream)})),e._options.forceVP8){var n=e._mediaStream,r=RTCRtpSender.getCapabilities("video").codecs,o=r.findIndex((function(e){return"video/VP8"===e.mimeType}));if(o>-1){var i=r[o];r.splice(o,1),r.unshift(i),t.getTransceivers().find((function(e){return e.sender&&e.sender.track===n.getVideoTracks()[0]})).setCodecPreferences(r)}}return e._createOffer(e._options.bandwidth,e._options.forceVP8)})).then((function(t){var n=e._options,r=n.streamName,o=n.iceTransport,i=n.forceVP8;return e.trigger(new Mt(gt.OFFER_START,e,t)),e._sendOffer(t,r,o,i)})).then((function(t){return e._setRemoteDescription(t.sdp)})).then((function(t){return e.trigger(new Mt(gt.OFFER_END,e,t)),e._getTrickleEnd().promise})).then((function(){return e.trigger(new Mt(gt.ICE_TRICKLE_COMPLETE,e)),e._requestPublish(e._options.streamName,e._options.streamMode,e._options.keyFramerate)})).then((function(){e._setUpMediaTransform(e._mediaTransform,e.getPeerConnection(),e.getMediaStream()),ce.addOrientationChangeHandler(e._onOrientationChange),r.resolve(e),e.trigger(new Mt(yt.PUBLISH_START,e))})).catch((function(t){ce.removeOrientationChangeHandler(e._onOrientationChange),r.reject(t),e.trigger(new Mt(yt.CONNECT_FAILURE,e,t))})),r.hasOwnProperty("promise")?r.promise:r}},{key:"publishWithSocket",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;bs("[publishWithSocket]"),this._options.streamName=n||this._options.streamName;var o=r||new S,i=new S;return this._socketHelper=new qa(this),this._socketHelper.setUpWithSocket(e,i),i.promise.then((function(){return t._requestPublish(t._options.streamName,t._options.streamMode,t._options.keyFramerate)})).then((function(){ce.addOrientationChangeHandler(t._onOrientationChange),o.resolve(t),t.trigger(new Mt(yt.PUBLISH_START,t))})).catch((function(e){ce.removeOrientationChangeHandler(t._onOrientationChange),o.reject(e),t.trigger(new Mt(yt.CONNECT_FAILURE,t,e))})),o.hasOwnProperty("promise")?o.promise:o}},{key:"unpublish",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];bs("[unpublish]");var n=function(){e._socketHelper&&(bs("[unpublish:teardown]"),e._socketHelper.tearDown()),e._peerHelper&&e._peerHelper.tearDown(),e._socketHelper=void 0,e._peerHelper=void 0,e._messageTransport=void 0};(this._options.clearMediaOnUnpublish||t)&&this.unpreview(),this._availableFuture=void 0,this._peerFuture=void 0,this._offerFuture=void 0,this._sendOfferFuture=void 0,this._trickleEndFuture=void 0,this._publishFuture=void 0;var r=this._requestUnpublish(this._options.streamName,this._options.groupName);return r.then((function(){e._unpublishFuture=void 0,n(),e.trigger(new Mt(yt.UNPUBLISH_SUCCESS,e))})),ce.removeOrientationChangeHandler(this._onOrientationChange),r}},{key:"mute",value:function(){this.muteAudio()}},{key:"unmute",value:function(){this.unmuteAudio()}},{key:"muteAudio",value:function(){this.getMessageTransport().post({mute:{muteAudio:!0}})}},{key:"unmuteAudio",value:function(){this.getMessageTransport().post({mute:{muteAudio:!1}})}},{key:"muteVideo",value:function(){this.getMessageTransport().post({mute:{muteVideo:!0}})}},{key:"unmuteVideo",value:function(){this.getMessageTransport().post({mute:{muteVideo:!1}})}},{key:"send",value:function(e,t){this.getMessageTransport().post({send:{method:e,data:"string"==typeof t?JSON.parse(t):t}})}},{key:"callServer",value:function(e,t){return this.getMessageTransport().postAsync({callAdapter:{method:e,arguments:t}})}},{key:"sendLog",value:function(e,t){try{var n=Object.keys(l).find((function(t){return t.toLowerCase()===e.toLowerCase()}))?e:l.DEBUG,r="string"==typeof t?t:JSON.stringify(t);this.getMessageTransport().post({log:n.toUpperCase(),message:r})}catch(e){e.message,_s("RTCPublisher"),_s("RTCPublisher")}}},{key:"onStreamAvailable",value:function(e){bs("[onstreamavailable]: "+JSON.stringify(e,null,2)),this._availableFuture=E.createIfNotExist(this._availableFuture),this._availableFuture.reject("Stream with name ".concat(this._options.streamName," already has a broadcast session.")),this.trigger(new Mt(yt.PUBLISH_INVALID_NAME,this))}},{key:"onStreamUnavailable",value:function(e){bs("Stream ".concat(this._options.streamName," does not exist.")),bs("[onstreamunavailable]: "+JSON.stringify(e,null,2)),this._availableFuture=E.createIfNotExist(this._availableFuture),this._availableFuture.resolve(!0)}},{key:"onSocketMessage",value:function(e,t){this.trigger(new Mt(gt.SOCKET_MESSAGE,this,{socket:e,message:t}))}},{key:"onSocketMessageError",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;_s("Error in stream publish: ".concat(e,".\n[Optional detail]: ").concat(t)),this._publishFuture&&(this.trigger(new Mt(yt.PUBLISH_FAIL,this)),this._publishFuture.reject(e),this.unpublish())}},{key:"onSocketClose",value:function(e){bs("[onsocketclose]"),this._peerHelper&&this._peerHelper.tearDown(),this.trigger(new Mt(yt.CONNECTION_CLOSED,this,e))}},{key:"onConnectionClosed",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;this._connectionClosed||(bs("RTCPublisher"),this.unpublish(),this.trigger(new Mt(yt.CONNECTION_CLOSED,this,e)))}},{key:"onPeerConnectionFail",value:function(){bs("[onpeerconnectionfail]"),this.trigger(new Mt(yt.PUBLISH_FAIL,this)),this._publishFuture&&this._publishFuture.reject("Peer Connection Failed.")}},{key:"onPeerConnectionClose",value:function(e){bs("[onpeerconnectionclose]"),this._socketHelper&&(bs("[onpeerconnectionclose:teardown]"),this._socketHelper.tearDown()),this.onSocketClose(e)}},{key:"onPeerConnectionOpen",value:function(){bs("[onpeerconnection::open]"),this.trigger(new Mt(gt.PEER_CONNECTION_OPEN),this,this.getPeerConnection())}},{key:"onPeerConnectionTrackAdd",value:function(e){bs("[onpeerconnection::track]"),this.trigger(new Mt(gt.TRACK_ADDED,this,{track:e}))}},{key:"onSDPSuccess",value:function(e){var t=e?": "+JSON.stringify(e,null,2):"";bs("[onsdpsuccess]".concat(t))}},{key:"onSDPError",value:function(e){this.trigger(new Mt(yt.PUBLISH_FAIL,this));var t=e?": "+JSON.stringify(e,null,2):"";_s("[onsdperror]".concat(t))}},{key:"onSDPAnswer",value:function(e){bs("[sdpanswer]:: "+JSON.stringify(e,null,2)),this._sendOfferFuture=E.createIfNotExist(this._sendOfferFuture),this._sendOfferFuture.resolve(e)}},{key:"onAddIceCandidate",value:function(e){bs("[addicecandidate]"),this._peerHelper.addIceCandidate(e).then((function(){bs("[addicecandidate:success]")})).catch((function(e){gs("[addicecandidate:error] - ".concat(e))}))}},{key:"onIceCandidate",value:function(e){bs("[icecandidatetrickle]"),this._sendCandidate(e,this._options.streamName)}},{key:"onIceCandidateTrickleEnd",value:function(){bs("[icecandidatetrickle:end]")}},{key:"onEmptyCandidate",value:function(){bs("[icecandidatetrickle:empty]"),this.trigger(new Mt(gt.PEER_CANDIDATE_END))}},{key:"onPeerGatheringComplete",value:function(){bs("[icecandidategathering:end]"),this._socketHelper&&this._socketHelper.postEndOfCandidates(this._options.streamName)}},{key:"onSocketIceCandidateEnd",value:function(){bs("[socketicecandidate:end]"),this._getTrickleEnd().resolve()}},{key:"onPublisherStatus",value:function(e){bs("[publisherstatus] - "+JSON.stringify(e,null,2));var t=ys.exec(e.message),n=ms.exec(e.message);t&&t[1]===this._options.streamName?this._unpublishFuture.resolve():n&&n[1]===this._options.streamName?this._publishFuture.resolve():e.code&&"NetStream.Publish.IsAvailable"===e.code?this.trigger(new Mt(yt.PUBLISH_AVAILABLE,this.status)):this.trigger(new Mt(yt.PUBLISH_STATUS,this,e))}},{key:"onInsufficientBandwidth",value:function(e){this.trigger(new Mt(yt.PUBLISH_INSUFFICIENT_BANDWIDTH,this,e))}},{key:"onSufficientBandwidth",value:function(e){this.trigger(new Mt(yt.PUBLISH_SUFFICIENT_BANDWIDTH,this,e))}},{key:"onRecoveringBandwidth",value:function(e){this.trigger(new Mt(yt.PUBLISH_RECOVERING_BANDWIDTH,this,e))}},{key:"onSendReceived",value:function(e,t){"onMetaData"===e?this.onMetaData(t):this.trigger(new Mt(yt.PUBLISH_SEND_INVOKE,this,{methodName:e,data:t}))}},{key:"onDataChannelAvailable",value:function(e){var t=this;if(bs("[ondatachannel::available]"),this._switchChannelRequest={switchChannel:e||"red5pro"},this._options.signalingSocketOnly)var n=setTimeout((function(){clearTimeout(n),t._socketHelper&&t._socketHelper.sever(t._switchChannelRequest),t._messageTransport=t._peerHelper,t.trigger(new Bt(kt.CHANGE,t,{controller:t,transport:t._messageTransport}))}),this._socketHelper?this._options.socketSwitchDelay:100);this.trigger(new Mt(gt.DATA_CHANNEL_AVAILABLE,this,{name:e,dataChannel:this.getDataChannel()}))}},{key:"onDataChannelError",value:function(e,t){this.trigger(new Mt(gt.DATA_CHANNEL_ERROR,this,{dataChannel:e,error:t}))}},{key:"onDataChannelMessage",value:function(e,t){this.trigger(new Mt(gt.DATA_CHANNEL_MESSAGE,this,{dataChannel:e,message:t}))}},{key:"onDataChannelOpen",value:function(e){this.trigger(new Mt(gt.DATA_CHANNEL_OPEN,this,{dataChannel:e}))}},{key:"onDataChannelClose",value:function(e){this.trigger(new Mt(gt.DATA_CHANNEL_CLOSE,this,{dataChannel:e}))}},{key:"onMetaData",value:function(e){this.trigger(new Mt(yt.PUBLISH_METADATA,this,e))}},{key:"overlayOptions",value:function(e){this._options=Object.assign(this._options,e)}},{key:"getMessageTransport",value:function(){return this._messageTransport}},{key:"getConnection",value:function(){return this._socketHelper}},{key:"getPeerConnection",value:function(){return this._peerHelper?this._peerHelper.connection:void 0}},{key:"getDataChannel",value:function(){return this._peerHelper?this._peerHelper.dataChannel:void 0}},{key:"getMediaStream",value:function(){return this._mediaStream}},{key:"getOptions",value:function(){return this._options}},{key:"getType",value:function(){return ba.RTC.toUpperCase()}}])&&ls(t.prototype,n),r&&ls(t,r),Object.defineProperty(t,"prototype",{writable:!1}),s}(H);function Cs(e){return(Cs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Os(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ks(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Os(Object(n),!0).forEach((function(t){Ps(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Os(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ps(e,t,n){return(t=Ns(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ts(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Ts=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:w(e,n,s)}),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d={};function f(){}function h(){}function p(){}var v={};c(v,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==t&&n.call(y,i)&&(v=y);var b=p.prototype=f.prototype=Object.create(v);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var o;r(this,"_invoke",{value:function(r,i){function a(){return new t((function(o,a){!function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==Cs(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(r,i,o,a)}))}return o=o?o.then(a,a):a()}})}function w(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=l(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return h.prototype=p,r(b,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:h,configurable:!0}),h.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,c(e,s,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},g(_.prototype),c(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new _(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(b),c(b,s,"Generator"),c(b,i,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=k,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function Rs(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function js(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){Rs(i,r,o,a,s,"next",e)}function s(e){Rs(i,r,o,a,s,"throw",e)}a(void 0)}))}}function As(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ls(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ns(r.key),r)}}function Ns(e){var t=function(e,t){if("object"!==Cs(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Cs(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Cs(t)?t:String(t)}function Hs(){return(Hs="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Is(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function Is(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Fs(e)););return e}function xs(e,t){return(xs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Ds(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Fs(e);if(t){var o=Fs(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Ms(this,n)}}function Ms(e,t){if(t&&("object"===Cs(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Fs(e){return(Fs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Us=function(e){p("WHIPClient",e)},Bs=function(e){v("WHIPClient",e)},Vs=function(e){m("WHIPClient",e)},Gs={protocol:"https",port:443,app:"live",streamMode:ga.LIVE,keyFramerate:3e3,mediaElementId:"red5pro-publisher",rtcConfiguration:{iceServers:[{urls:"stun:stun2.l.google.com:19302"}],iceCandidatePoolSize:2,bundlePolicy:"max-bundle"},iceServers:void 0,iceTransport:eo.UDP,bandwidth:{audio:56,video:750},clearMediaOnUnpublish:!1,mediaConstraints:new Ta,onGetUserMedia:void 0,dataChannelConfiguration:void 0,forceVP8:!1,bypassAvailable:!1,signalingSocketOnly:!1,enableChannelSignaling:!0,trickleIce:!0,mungeOffer:void 0,mungeAnswer:void 0},Ws=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xs(e,t)}(h,e);var t,n,r,o,i,a,s,c,u,l,d,f=Ds(h);function h(e,t){var n,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];As(this,h),n=f.call(this);var o=e?qr(e):Gs;return o.mediaElementId=t?t.id:Gs.mediaElementId,o.trickleIce=r,n._whipHelper=void 0,e&&n._internalConnect(o),n}return t=h,(n=[{key:"_internalConnect",value:(d=js(Ts().mark((function e(t){return Ts().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.init(t);case 2:return e.next=4,this.publish();case 4:case"end":return e.stop()}}),e,this)}))),function(e){return d.apply(this,arguments)})},{key:"waitToGatherIce",value:(l=js(Ts().mark((function e(t){return Ts().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Us("[waittogatherice]"),e.abrupt("return",new Promise((function(e,n){if("complete"===t.iceGatheringState)Us("[waittogatherice] ice gathering state complete."),t.addIceCandidate(null).then((function(){e(t.localDescription)})).catch((function(n){Bs("Error adding null candidate: "+n.message||!1),e(t.localDescription)}));else{Us("[waittogatherice] waiting...");var r=setTimeout((function(){clearTimeout(r),t.addIceCandidate(null).then((function(){e(t.localDescription)})).catch((function(n){Bs("Error adding null candidate: "+n.message||!1),e(t.localDescription)}))}),1e3);t.onicegatheringstatechange=function(){clearTimeout(r),Us("[waittogatherice] ice gathering state complete."),"complete"===t.iceGatheringState&&t.addIceCandidate(null).then((function(){e(t.localDescription)})).catch((function(n){Bs("Error adding null candidate: "+n.message||!1),e(t.localDescription)}))}}})));case 2:case"end":return e.stop()}}),e)}))),function(e){return l.apply(this,arguments)})},{key:"_postOffer",value:(u=js((function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Ts().mark((function r(){var o,i,a,s,c,u,l,d,f;return Ts().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,o=e.sdp,i=t._options,a=i.mungeOffer,s=i.streamMode,c=i.keyFramerate,u=i.connectionParams,l=i.forceVP8,d=o,a&&(d=a(d)),n||(d=ze(d),d=Ke(d)),f=ks(ks({},u),{},{mode:s,keyFramerate:c}),l&&(f.videoEncoding=_a.VP8),r.next=10,t._whipHelper.postSDPOffer(d,f);case 10:return r.abrupt("return",r.sent);case 13:throw r.prev=13,r.t0=r.catch(0),Vs(r.t0.message||r.t0),r.t0 instanceof U?t.onStreamAvailable(r.t0):t._publishFuture?t._publishFuture.reject(r.t0.message||r.t0):(t.trigger(new Mt(yt.CONNECT_FAILURE,t,Vs)),t.unpublish()),r.t0;case 18:case"end":return r.stop()}}),r,null,[[0,13]])}))()})),function(e){return u.apply(this,arguments)})},{key:"_postCandidateFragments",value:(c=js(Ts().mark((function e(t){var n;return Ts().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=it(t,void 0,!0),e.next=3,this._whipHelper.trickle(n);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)}))),function(e){return c.apply(this,arguments)})},{key:"_sendCandidate",value:function(e,t){Us(JSON.stringify(e,null,2))}},{key:"init",value:(s=js(Ts().mark((function e(t){var n,r,o,i,a,s,c,u;return Ts().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._options=ks(ks({},Gs),t),n=this._options,r=n.protocol,o=n.host,i=n.port,a=n.app,s=n.streamName,c=n.enableChannelSignaling,u=r.match(/^http/)?r:"ws"===r?"http":"https",this._whipUrl="".concat(u,"://").concat(o,":").concat(i,"/").concat(a,"/whip/endpoint/").concat(s),this._whipHelper=new Ai(this._whipUrl,c),this._messageTransport=this._whipHelper,e.abrupt("return",Hs(Fs(h.prototype),"init",this).call(this,this._options));case 7:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"initWithStream",value:(a=js(Ts().mark((function e(t,n){var r,o,i,a,s,c,u,l;return Ts().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._options=ks(ks({},Gs),t),r=this._options,o=r.protocol,i=r.host,a=r.port,s=r.app,c=r.streamName,u=r.enableChannelSignaling,l="ws"===o?"http":"https",this._whipUrl="".concat(l,"://").concat(i,":").concat(a,"/").concat(s,"/whip/endpoint/").concat(c),this._whipHelper=new Ai(this._whipUrl,u),this._messageTransport=this._whipHelper,e.abrupt("return",Hs(Fs(h.prototype),"initWithStream",this).call(this,t,n));case 7:case"end":return e.stop()}}),e,this)}))),function(e,t){return a.apply(this,arguments)})},{key:"publish",value:(i=js((function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Ts().mark((function r(){var o,i,a,s,c,u,l,d,f,h,p,v,m,y,b,g,_,w,S,E,C;return Ts().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return o=e._options,i=o.bandwidth,a=o.forceVP8,s=o.rtcConfiguration,c=o.enableChannelSignaling,u=o.dataChannelConfiguration,l=o.trickleIce,d=o.mungeAnswer,f=o.connectionParams,(h=c&&Ie())&&!u&&(u={name:"red5pro"},e._options.dataChannelConfiguration=u),e._options.enableChannelSignaling=h,e._options.signalingSocketOnly=e._options.enableChannelSignaling,r.prev=5,p={},t&&(e._options.streamName=t),f&&(v=f.transcode)&&(p.transcode=v),r.next=11,e._whipHelper.getOptions(p);case 11:return(m=r.sent)&&m.links&&(e._options.iceServers=m.links),y=e._options.enableChannelSignaling?u:void 0,r.next=16,e._createPeerConnection(s,y,e._options.iceServers);case 16:return b=r.sent,e.trigger(new Mt(gt.PEER_CONNECTION_AVAILABLE,e,b)),r.next=20,e._getMediaStream();case 20:return r.sent.getTracks().forEach((function(e){var t=b.addTransceiver(e,{direction:"sendonly"});if(t&&"video"===e.kind&&a)try{var n=RTCRtpSender.getCapabilities("video").codecs,r=n.findIndex((function(e){return"video/VP8"===e.mimeType}));if(r>-1){var o=n[r];n.splice(r,1),n.unshift(o),t.setCodecPreferences(n)}}catch(e){Us("[forceVP8] Could not set codec preferences. ".concat(e.message||e))}})),r.next=24,e._peerHelper.createOfferWithoutSetLocal(i,a);case 24:return g=r.sent,r.next=27,e._peerHelper.setLocalDescription(g);case 27:if(l){r.next=31;break}return r.next=30,e.waitToGatherIce(b);case 30:g=r.sent;case 31:return e.trigger(new Mt(gt.OFFER_START,e,g)),r.next=34,e._postOffer(g,l);case 34:return _=r.sent,w=_.sdp,S=d?d(w):w,r.next=39,e._setRemoteDescription({type:"answer",sdp:qe(S)});case 39:if(e.trigger(new Mt(gt.OFFER_END,e,S)),!l){r.next=47;break}return r.next=43,e.waitToGatherIce(b);case 43:return E=r.sent,C=E.sdp,r.next=47,e._postCandidateFragments(C);case 47:return e.trigger(new Mt(gt.ICE_TRICKLE_COMPLETE,e)),ce.addOrientationChangeHandler(e._onOrientationChange),e._options.enableChannelSignaling||e.trigger(new Mt(yt.PUBLISH_START,e)),n&&n.resolve(e),r.abrupt("return",e);case 54:throw r.prev=54,r.t0=r.catch(5),Vs(r.t0),ce.removeOrientationChangeHandler(e._onOrientationChange),e.trigger(new Mt(yt.CONNECT_FAILURE,e,r.t0)),n&&n.reject(r.t0),r.t0;case 61:case"end":return r.stop()}}),r,null,[[5,54]])}))()})),function(){return i.apply(this,arguments)})},{key:"unpublish",value:(o=js((function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return Ts().mark((function n(){return Ts().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return Us("[unpublish:teardown]"),e._whipHelper&&e._whipHelper.tearDown(),e._peerHelper&&e._peerHelper.tearDown(),e._whipHelper=void 0,e._peerHelper=void 0,e._messageTransport=void 0,(e._options.clearMediaOnUnpublish||t)&&e.unpreview(),e.trigger(new Mt(yt.UNPUBLISH_SUCCESS,e)),ce.removeOrientationChangeHandler(e._onOrientationChange),n.abrupt("return",e);case 10:case"end":return n.stop()}}),n)}))()})),function(){return o.apply(this,arguments)})},{key:"onDataChannelOpen",value:function(e){var t=this._options.dataChannelConfiguration;if(Hs(Fs(h.prototype),"onDataChannelOpen",this).call(this,e),t){var n=t.name;Hs(Fs(h.prototype),"onDataChannelAvailable",this).call(this,n)}else Hs(Fs(h.prototype),"onDataChannelAvailable",this).call(this);this.trigger(new Mt(yt.PUBLISH_START,this))}},{key:"getConnection",value:function(){}}])&&Ls(t.prototype,n),r&&Ls(t,r),Object.defineProperty(t,"prototype",{writable:!1}),h}(Es);function Ys(e){return(Ys="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function zs(e){return function(e){if(Array.isArray(e))return Js(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Js(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Js(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Js(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ks(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Ys(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ys(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Ys(i)?i:String(i)),r)}var o,i}function qs(e,t){return(qs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Xs(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=$s(e);if(t){var o=$s(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Qs(this,n)}}function Qs(e,t){if(t&&("object"===Ys(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function $s(e){return($s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Zs=new A,ec=function(){var e=new Map;return e.set(ba.RTC,Es),e.set(ba.RTMP,xa),e}(),tc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qs(e,t)}(i,e);var t,n,r,o=Xs(i);function i(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(e=o.call(this))._options=void 0,e._order=[ba.RTC,ba.RTMP],e}return t=i,(n=[{key:"_getPublisherFromOrder",value:function(e,t){return Zs.create(e,ec,t,"init")}},{key:"getPublishOrder",value:function(){return this._order}},{key:"setPublishOrder",value:function(e){var t=(e="string"==typeof e?[e]:e).filter((function(e){var t;for(t in ba)if(ba[t].toLowerCase()===e.toLowerCase())return!0;return!1})).map((function(e){return e.toLowerCase()}));return this._order=zs(new Set(t)),p("Red5ProPublisher","[orderupdate]: ".concat(this._order)),this}},{key:"init",value:function(e){var t=new S;return p("Red5ProPublisher","[publish]"),this._options=e,this._getPublisherFromOrder(this._order,this._options).then((function(e){t.resolve(e)})).catch((function(e){v("Red5ProPublisher","[publisherror]: Could not implement a publisher: ".concat(e)),t.reject(e)})),t.promise}},{key:"publishTypes",get:function(){return ba}}])&&Ks(t.prototype,n),r&&Ks(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(H);function nc(e,t){return(nc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function rc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ac(e);if(t){var o=ac(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return oc(this,n)}}function oc(e,t){if(t&&("object"===sc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ic(e)}function ic(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ac(e){return(ac=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function sc(e){return(sc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function cc(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function uc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==sc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==sc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===sc(i)?i:String(i)),r)}var o,i}function lc(e,t,n){return t&&uc(e.prototype,t),n&&uc(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var dc="Red5ProSharedObject",fc=function(e,t){p([dc,e].join(":"),t)},hc=function(e,t){v([dc,e].join(":"),t)},pc=function(e){return"string"==typeof e?JSON.parse(e):e},vc=function(){function e(t,n){cc(this,e),this.key=t,this.value=n}return lc(e,[{key:"toObject",value:function(){var e={};return e[this.key]=this.value,e}}]),e}(),mc=function(){function e(t,n){var r;if(cc(this,e),this.methodName=t,"[object Array]"===Object.prototype.toString.call(n)){var o,i,a=n.length;for(r=[],o=0;o<a;o++)i=pc(n[o]),"[object Array]"===Object.prototype.toString.call(n)?r=r.concat(i):r.push(i)}else r=[pc(n)];this.message=1===r.length?r[0]:r}return lc(e,[{key:"toObject",value:function(){return{methodName:this.methodName,message:this.message}}}]),e}(),yc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nc(e,t)}(n,e);var t=rc(n);function n(e,r){var o;return cc(this,n),(o=t.call(this))._name=e,o._transport=void 0,o._transportController=void 0,o._onclose=o.close.bind(ic(o)),o._handleTransportStateEvents=o._handleTransportStateEvents.bind(ic(o)),o._handleMessageTransportChange=o._handleMessageTransportChange.bind(ic(o)),o.setMessageTransportController(r),o}return lc(n,[{key:"_establishTransport",value:function(e){e.addSharedObjectResponseHandler(this);try{fc(this._name,"[sharedobject:connect:attempt]"),e.on("*",this._handleSocketStateEvents),e.getRemoteSharedObject(this._name),ce.addCloseHandler(this._onclose,0)}catch(e){t=this._name,n="[sharedobject:connect:error]: "+e.message,m([dc,t].join(":"),n),this._transport=void 0,this._transportController=void 0}var t,n}},{key:"_handleMessageTransportChange",value:function(e){var t=this,n=e.data.controller,r=setTimeout((function(){clearTimeout(r),t.setMessageTransportController(n)}),1e3)}},{key:"_handleTransportStateEvents",value:function(e){fc(this._name,"".concat(e.name,": ").concat(e.type)),e.type===kt.CLOSE&&(this.trigger(new Ut(Ot.CONNECTION_CLOSED,this._name)),this.close())}},{key:"_getJsonFromSocketMessage",value:function(e){try{return"string"==typeof e.data?JSON.parse(e.data):e.data}catch(t){hc(this._name,"Could not parse message as JSON. Message= "+e.data+". Error= "+t.message)}return null}},{key:"_notifyOfPropertyValues",value:function(e){var t;if(0===Object.keys(e).length)this.trigger(new Ut(Ot.PROPERTY_UPDATE,this._name,{}));else for(t in e)this.trigger(new Ut(Ot.PROPERTY_UPDATE,this._name,new vc(t,e[t]).toObject()))}},{key:"_notifyOfEvents",value:function(e){var t,n,r=!1,o=e.length;for(t=0;t<o;t++)switch((n=e[t]).subtype){case 9:this.trigger(new Ut(Ot.PROPERTY_REMOVE,this._name,{attribute:n.attribute})),r=!0;break;case 4:n.attributes?this._notifyOfPropertyValues(n.attributes):this.trigger(new Ut(Ot.PROPERTY_UPDATE,this._name,new vc(n.attribute,n.value).toObject())),r=!0;break;case 6:this.trigger(new Ut(Ot.METHOD_UPDATE,this._name,new mc(n.method,n.value).toObject())),r=!0}return r}},{key:"respond",value:function(e){if(e.data){var t=this._getJsonFromSocketMessage(e);if(null===t)return hc(this._name,"Determined message response not in correct format. Aborting message handle."),!0;if(void 0!==t.data)if(t.data.name===this._name){if(fc(this._name,"[sharedobject-response]: "+JSON.stringify(t,null,2)),"SharedObject.Status.GetRemote"===t.data.status&&"Success"===t.data.message)return this._transport.connectToSharedObject(this._name),!0;if("SharedObject.Status.GetRemote"===t.data.status&&"Fail"===t.data.message)return this.trigger(new Ut(Ot.CONNECT_FAILURE,this._name)),!0;if("SharedObject.Status.Connect"===t.data.status&&"Success"===t.data.message)return this.trigger(new Ut(Ot.CONNECT_SUCCESS,this._name)),!0;if("SharedObject.Status.Connect"===t.data.status&&"Fail"===t.data.message)return this.trigger(new Ut(Ot.CONNECT_FAILURE,this._name)),!0;if("SharedObject.Status.Disconnect"===t.data.status)return this.trigger(new Ut(Ot.CONNECTION_CLOSED,this._name)),!0;if("sharedobject"===t.data.type){if(t.data.hasOwnProperty("events"))return this._notifyOfEvents(t.data.events);if(t.data.hasOwnProperty("value"))return this._notifyOfPropertyValues(t.data.value)}}else fc(this._name,"Unhandled Message exchange: "+JSON.stringify(t,null,2))}return!1}},{key:"send",value:function(e,t){var n=this._transport;n?n.sendToSharedObject(this._name,e,t):hc(this._name,"No WebSocket connection available!")}},{key:"setProperty",value:function(e,t){this._transport.sendPropertyToSharedObject(this._name,e,t)}},{key:"close",value:function(){var e=this._transport;if(e){try{e.closeSharedObject(this._name)}catch(e){}e.removeSharedObjectResponseHandler(this),e.off("*",this._handleTransportStateEvents)}this._transport=void 0,this._transportController=void 0,ce.removeCloseHandler(this._onclose)}},{key:"getMessageTransportController",value:function(){return this._transportController}},{key:"setMessageTransportController",value:function(e){this._transport&&(this._transport.removeSharedObjectResponseHandler(this),this._transport.off("*",this._handleTransportStateEvents),ce.removeCloseHandler(this._onclose),this._transport=void 0,this._transportController=void 0),this._transportController=e,this._transport=this._transportController.getMessageTransport(),this._transportController&&this._transport&&(this._transportController.on(kt.CHANGE,this._handleMessageTransportChange),this._establishTransport(this._transport))}},{key:"getName",value:function(){return this._name}}]),n}(H);function bc(e){return(bc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function gc(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==bc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==bc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===bc(i)?i:String(i)),r)}var o,i}function wc(){return(wc="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Sc(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function Sc(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=kc(e)););return e}function Ec(e,t){return(Ec=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Cc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=kc(e);if(t){var o=kc(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Oc(this,n)}}function Oc(e,t){if(t&&("object"===bc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function kc(e){return(kc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Pc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ec(e,t)}(i,e);var t,n,r,o=Cc(i);function i(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;gc(this,i);var n=Math.floor(65536*Math.random()).toString(16),r="sharedobject-".concat(n);return(e=o.call(this,t,r)).id=r,e._sharedObjectName=void 0,e}return t=i,(n=[{key:"getRemoteSharedObject",value:function(e){wc(kc(i.prototype),"getRemoteSharedObject",this).call(this,e),this._sharedObjectName=e}},{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,r=new S;this.id=t||this.id,this._name=this.id;var o=Jr(e,{id:this.id,capabilities:n});return this.setUp(o,r),r.promise}},{key:"respond",value:function(e){if(!wc(kc(i.prototype),"respond",this).call(this,e)&&e.data){var t=this.getJsonFromSocketMessage(e);if(t&&t.data){var n=t.data;if("status"===n.type)"NetConnection.Connect.Closed"===n.code?(this.respond({data:{data:{status:"SharedObject.Status.Disconnect",message:n.code,name:this._sharedObjectName}}}),this._sharedObjectName=void 0):"NetConnection.ICE.TrickleCompleted"===n.code&&this._responder?this._responder.onIceTrickleComplete():"NetConnection.DataChannel.Available"===n.code&&this._responder?this._responder.onDataChannelAvailable(n.description):this._responder&&this._responder.onSocketStatus(n);else if(n.sdp){var r=n.sdp;"answer"===r.type&&this._responder&&this._responder.onSDPAnswer(r)}else if(n.candidate&&this._responder){var o=n.candidate;this._responder.onAddIceCandidate(o)}}}}},{key:"close",value:function(){this._sharedObjectName=void 0,this.tearDown()}},{key:"getMessageTransport",value:function(){return this}},{key:"getConnection",value:function(){return this}}])&&_c(t.prototype,n),r&&_c(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(ar);function Tc(e){return(Tc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Rc(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Tc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Tc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Tc(i)?i:String(i)),r)}var o,i}function Ac(){return(Ac="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Lc(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function Lc(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=xc(e)););return e}function Nc(e,t){return(Nc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Hc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=xc(e);if(t){var o=xc(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Ic(this,n)}}function Ic(e,t){if(t&&("object"===Tc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function xc(e){return(xc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Dc=/(.*) starting/i,Mc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nc(e,t)}(i,e);var t,n,r,o=Hc(i);function i(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;Rc(this,i);var n=Math.floor(65536*Math.random()).toString(16),r="sharedobject-".concat(n);return(e=o.call(this,t,r)).id=r,e._options=void 0,e._socket=void 0,e._sharedObjectName=void 0,e._connectionPromise=void 0,e}return t=i,(n=[{key:"_removeConnectionHandlers",value:function(e){e.onconnectionstatechange=void 0,e.oniceconnectionstatechange=void 0,e.onicecandidate=void 0,e.ontrack=void 0,e.ondatachannel=void 0}},{key:"_addConnectionHandlers",value:function(e,t){var n,r=this;t=t||this._connectionPromise,e.onconnectionstatechange=function(){p(r._name,"[peer.onconnectionstatechange] - State: ".concat(e.connectionState)),"connected"===e.connectionState?(p(r._name,"[peerconnection:open]"),r.trigger(new Bt(kt.OPEN,r._name,{peerConnection:r}))):"failed"!==e.connectionState&&"disconnected"!==e.connectionState||(v(r._name,"[peerconnection:error]"),"failed"===e.connectionState&&r.trigger(new Bt(kt.ERROR,r._name,{peerConnection:r,error:"Connection Failed."})),t&&t.reject("Connection Failed."))},e.onicecandidate=function(e){p(r._name,"[peer.onicecandidate] - Peer Candidate: ".concat(e.candidate)),e.candidate?r._socket.post({handleCandidate:r._name,data:{candidate:e.candidate}}):null===e.candidate&&p(r._name,"[icecandidatetrickle:end]")},e.oniceconnectionstatechange=function(o){var i=e.iceConnectionState;p(r._name,"[peer.oniceconnectionstatechange] - State: ".concat(i)),"connected"===i&&ce.getIsEdge()?(p(r._name,"[edge/ortc:notify complete]"),p(r._name,"[icecandidategathering:end]"),r._socket.postEndOfCandidates(r._name)):"failed"===i?(n&&clearTimeout(n),r.trigger(new Bt(kt.ERROR,r._name,{peerConnection:r,error:"Connection Failed."})),r.trigger(new Bt(kt.CLOSE,r._name,{peerConnection:r,event:o})),t&&t.reject("Connection Failed.")):"disconnected"===i?n=setTimeout((function(){p(r._name,"[peer.oniceconnectionstatechange] - Reconnect timeout reached. Closing PeerConnection."),clearTimeout(n),r.trigger(new Bt(kt.CLOSE,r._name,{peerConnection:r,event:o}))}),5e3):n&&(p(r._name,"[peer.oniceconnectionstatechange] - Clearing timeout for reconnect."),clearTimeout(n))},e.onicegatheringstatechange=function(){var t=e.iceGatheringState;p(r._name,"[peer.onicegatheringstatechange] - State: ".concat(t)),"complete"===t&&(p(r._name,"[icecandidategathering:end]"),r._socket.postEndOfCandidates(r._name))}}},{key:"_createOffer",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;p(this._name,"[peer.createoffer]"),this._peerConnection.createOffer().then((function(n){p(e._name,"[peer:offer] ".concat(n.sdp)),e._peerConnection.setLocalDescription(n).then((function(){e._socket.post({handleOffer:e._name,transport:"udp",data:{sdp:n}})})).catch((function(e){t&&t.reject(e)}))}))}},{key:"getRemoteSharedObject",value:function(e){Ac(xc(i.prototype),"getRemoteSharedObject",this).call(this,e),this._sharedObjectName=e}},{key:"init",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=new S;return this.id=n||this.id,this._name=this.id,this._options=e,this._socket=new Pc(this),this._socket.on("MessageTransport.Open",(function(n){p(t._name,"[socket:event] - ".concat(n.type)),t._connectionPromise=r,t.setUpWithPeerConfiguration(e.rtcConfiguration,t.getDataChannelConfiguration(e)).then((function(){t._createOffer(r)})).catch((function(e){t.trigger(new Bt(kt.ERROR,t._name,{peerConnection:t,error:e})),r.reject(e)}))})),this._socket.init(e,this.id,4).catch((function(e){t.trigger(new Bt(kt.ERROR,t._name,{peerConnection:t,error:e})),r.reject(e)})),r.promise}},{key:"getMessageTransport",value:function(){return this._socket||this}},{key:"getDataChannelConfiguration",value:function(e){return e.dataChannelConfiguration||{name:"red5pro"}}},{key:"getDataChannelName",value:function(e){var t=e.dataChannelConfiguration;return t&&t.name?t.name:"red5pro"}},{key:"onIceTrickleComplete",value:function(){p(this._name,"[peer:icetricklecomplete]"),this._socket.post({startChannel:this.getDataChannelName(this._options)})}},{key:"onAddIceCandidate",value:function(e){p(this._name,"[peer:addicecandidate]"),we(e.candidate)?p(this._name,"[peer:onemptycandidate]"):this._peerConnection.addIceCandidate(e)}},{key:"onSDPAnswer",value:function(e){var t=this;p(this._name,"[peer:handleanswer]"),this._peerConnection.setRemoteDescription(new vt(e)).then((function(){})).catch((function(e){t.trigger(new Bt(kt.ERROR,t._name,{peerConnection:t,error:e})),t._connectionPromise.reject(e)}))}},{key:"onDataChannelAvailable",value:function(e){this._socket.sever({switchChannel:e||"red5pro"}),this._socket=void 0,this._connectionPromise.resolve(this)}},{key:"onSocketStatus",value:function(e){if(e.message){var t=Dc.exec(e.message);p(this._name,"[peer:status] ".concat(e.message)),t&&t.length}}}])&&jc(t.prototype,n),r&&jc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Ar);function Fc(e){return(Fc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Uc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(o=r.key,i=void 0,i=function(e,t){if("object"!==Fc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Fc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===Fc(i)?i:String(i)),r)}var o,i}function Bc(){return(Bc="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Vc(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}}).apply(this,arguments)}function Vc(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=zc(e)););return e}function Gc(e,t){return(Gc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Wc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=zc(e);if(t){var o=zc(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Yc(this,n)}}function Yc(e,t){if(t&&("object"===Fc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function zc(e){return(zc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Jc=/participant (.*) starting (.*)/i,Kc=/Leaving group (.*)/i,qc={groupName:"group01",autoGenerateMediaStream:!0,mixAudioDown:!1},Xc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gc(e,t)}(i,e);var t,n,r,o=Wc(i);function i(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(e=o.call(this))._name="RTCConferenceParticipant",e._audioTracks=[],e._videoTracks=[],e._conferenceStream=void 0,e}return t=i,(n=[{key:"_createAudioElement",value:function(){var e=document.createElement("audio");return e.id="red5pro-audio",e.controls="controls",e.autoplay="autoplay",e.playsinline="playsinline",e}},{key:"_packStreamWithAudio",value:function(e,t){var n=e.getVideoTracks()[0].clone();for(e.addTrack(n);--t>-1;){var r=new AudioContext,o=r.createMediaStreamDestination();r.createMediaStreamSource(e).connect(o),e.addTrack(o.stream.getAudioTracks()[0])}}},{key:"_createOffer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return p("[createoffer]"),this._offerFuture=void 0,this._offerFuture=E.createIfNotExist(this._offerFuture),this._peerHelper.createOffer(e,t,!0,this._offerFuture),this._offerFuture.promise}},{key:"_sendOffer",value:function(e,t,n){var r=this._options.groupName;return this._sendOfferFuture=void 0,this._sendOfferFuture=E.createIfNotExist(this._sendOffFuture),this._socketHelper.post({joinGroup:r,streamName:t,transport:n,data:{sdp:e}}),this._sendOfferFuture.promise}},{key:"_requestPublish",value:function(e,t,n){var r=this._options.autoGenerateMediaStream;return this._publishFuture=void 0,this._publishFuture=E.createIfNotExist(this._publishFuture),this._publishFuture.resolve(),r&&(this._conferenceStream=this._startReceivers(),this.trigger(new Vt(Pt.MEDIA_STREAM,this,{stream:this._conferenceStream}))),this._publishFuture.promise}},{key:"unpublish",value:function(){return this._conferenceStream&&this._conferenceStream.getTracks().forEach((function(e){return e.stop()})),Bc(zc(i.prototype),"unpublish",this).call(this)}},{key:"_requestUnpublish",value:function(e){var t=this._options.groupName;return this._unpublishFuture=void 0,this._unpublishFuture=E.createIfNotExist(this._unpublishFuture),this.getMessageTransport().postUnjoin(t,e)||this._unpublishFuture.resolve(),this._unpublishFuture.promise}},{key:"_startReceivers",value:function(){var e=this.getPeerConnection().getTransceivers().map((function(e){if("recvonly"===e.currentDirection)return e.receiver.track})).filter((function(e){return e})),t=this._options.mixAudioDown,n=this._conferenceStream||new MediaStream;if(t){var r=new AudioContext,o=e.map((function(e){if("audio"===e.kind)return r.createMediaStreamSource(new MediaStream([e]))})).filter((function(e){return e})),i=r.createMediaStreamDestination();o.forEach((function(e){return e.connect(i)})),i.stream.getTracks().forEach((function(e){return n.addTrack(e)}))}else e.forEach((function(e){return n.addTrack(e)}));return e.forEach((function(e){"video"===e.kind&&n.addTrack(e)})),n}},{key:"init",value:function(e){return Bc(zc(i.prototype),"init",this).call(this,Object.assign(qc,e))}},{key:"initWithStream",value:function(e,t){return Bc(zc(i.prototype),"initWithStream",this).call(this,Object.assign(qc,e),t)}},{key:"onPeerConnectionTrackAdd",value:function(e){var t=this._options.autoGenerateMediaStream;t&&"audio"===e.kind?this._audioTracks.push(e):t&&"video"===e.kind&&this._videoTracks.push(e),Bc(zc(i.prototype),"onPeerConnectionTrackAdd",this).call(this,e)}},{key:"_onMediaStreamReceived",value:function(e){this._packStreamWithAudio(e,3),Bc(zc(i.prototype),"_onMediaStreamReceived",this).call(this,e)}},{key:"onSDPAnswer",value:function(e){var t=this._options.streamName;this.overlayOptions({streamName:e.participantId,participantId:e.participantId,publisherName:t}),this._sendOfferFuture=E.createIfNotExist(this._sendOfferFuture),this._sendOfferFuture.resolve(e)}},{key:"onPublisherStatus",value:function(e){p(this._name,"[publisherstatus] - "+JSON.stringify(e,null,2));var t=Kc.exec(e.message),n=Jc.exec(e.message);t&&t[1]===this._options.groupName?this._unpublishFuture.resolve():n&&n[1]===this._options.streamName?this._publishFuture.resolve():v(this._name,"Publisher status received, but could not handle.")}}])&&Uc(t.prototype,n),r&&Uc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),i}(Es),Qc=ca,$c=Ro,Zc=Xi,eu=Zo,tu=bi,nu=tc,ru=Es,ou=Ws,iu=xa,au=yc,su=Pc,cu=Mc,uu=yt,lu=gt,du=bt,fu=wt,hu=Et,pu=Ct,vu=St,mu=Ot,yu=kt,bu=Pt,gu=$r,_u=Zr,wu=eo,Su=to;d("".concat("error")||!1);var Eu=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];l.hasOwnProperty(e.toUpperCase())&&(d(e,t),console&&console.log("Red5 Pro SDK Version ".concat("11.2.0-beta1")))},Cu=l,Ou=function(){return a}}])}));