import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-schedule-view',
  templateUrl: './schedule-view.component.html',
  styleUrls: ['./schedule-view.component.scss']
})
export class ScheduleViewComponent implements OnInit {
  @Input() seasonId: any;
  @Input() clubId: any;
  @Input() tournamentId: any;
  @Input() dateRange: any;
  @Input() matchStatus: string;
  @Input() seasons: any[];
  @Input() clubs: any[];
  @Input() tournaments: any[];

  constructor(
    public _translateService: TranslateService
  ) {}

  ngOnInit(): void {
    // Schedule view implementation will go here
  }
}
