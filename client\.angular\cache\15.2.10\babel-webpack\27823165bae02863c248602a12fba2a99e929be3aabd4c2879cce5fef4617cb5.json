{"ast": null, "code": "import { AppConfig, coreConfig } from 'app/app-config';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"app/services/tournament.service\";\nimport * as i5 from \"app/services/commons.service\";\nimport * as i6 from \"app/services/user.service\";\nimport * as i7 from \"@angular/platform-browser\";\nfunction TeamFixturesComponent_section_2_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tab-fixtures\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"matches\", ctx_r2.matches);\n  }\n}\nfunction TeamFixturesComponent_section_2_ng_template_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"h3\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"No results found\"), \" \");\n  }\n}\nfunction TeamFixturesComponent_section_2_ng_template_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 18)(2, \"div\", 31)(3, \"div\", 4)(4, \"div\", 32);\n    i0.ɵɵelement(5, \"img\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"h4\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h6\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const player_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", (player_r6 == null ? null : player_r6.photo) ? player_r6 == null ? null : player_r6.photo : ctx_r5.coreConfig.app.appLogoImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5._userService.fullName(player_r6.user));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"YOB: \", i0.ɵɵpipeBind2(11, 3, player_r6.dob, \"yyyy\"), \"\");\n  }\n}\nfunction TeamFixturesComponent_section_2_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, TeamFixturesComponent_section_2_ng_template_33_div_1_Template, 4, 3, \"div\", 26);\n    i0.ɵɵtemplate(2, TeamFixturesComponent_section_2_ng_template_33_div_2_Template, 12, 6, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.players == null ? null : ctx_r3.players.length) == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.players);\n  }\n}\nfunction TeamFixturesComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7)(5, \"div\", 8);\n    i0.ɵɵelement(6, \"img\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"h2\", 11);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 12);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 13);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 14)(15, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TeamFixturesComponent_section_2_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onClickFollowTeam());\n    });\n    i0.ɵɵelement(16, \"i\", 16);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(20, \"div\", 17)(21, \"div\", 18)(22, \"ul\", 19, 20);\n    i0.ɵɵlistener(\"activeIdChange\", function TeamFixturesComponent_section_2_Template_ul_activeIdChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.activeId = $event);\n    });\n    i0.ɵɵelementStart(24, \"li\", 21)(25, \"a\", 22);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, TeamFixturesComponent_section_2_ng_template_28_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"li\", 21)(30, \"a\", 22);\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, TeamFixturesComponent_section_2_ng_template_33_Template, 3, 2, \"ng-template\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(34, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r1 = i0.ɵɵreference(23);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", (ctx_r0.team == null ? null : ctx_r0.team.club == null ? null : ctx_r0.team.club.logo) ? ctx_r0.team.club.logo : ctx_r0.coreConfig.app.appLogoImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.team == null ? null : ctx_r0.team.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.team == null ? null : ctx_r0.team.group.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.team.club == null ? null : ctx_r0.team.club.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isFollowed ? \"btn-warning\" : \"btn-outline-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isFollowed ? \"feather icon-bell-off\" : \"feather icon-bell\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isFollowed ? i0.ɵɵpipeBind1(18, 13, \"Unfollow\") : i0.ɵɵpipeBind1(19, 15, \"Follow\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"activeId\", ctx_r0.activeId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngbNavItem\", 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(27, 17, \"Fixtures & Results\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngbNavItem\", 2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(32, 19, \"Players\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngbNavOutlet\", _r1);\n  }\n}\nexport class TeamFixturesComponent {\n  constructor(route, _trans, _loading, _tourService, _commonService, _userService, _titleService, _translateService) {\n    this.route = route;\n    this._trans = _trans;\n    this._loading = _loading;\n    this._tourService = _tourService;\n    this._commonService = _commonService;\n    this._userService = _userService;\n    this._titleService = _titleService;\n    this._translateService = _translateService;\n    this.AppConfig = AppConfig;\n    this.coreConfig = coreConfig;\n    this.contentHeader = null;\n    this.activeId = 1;\n    this.matches = [];\n    this.params = {\n      team_id: null\n    };\n    this.team = null;\n    this.players = [];\n    this.favoriteTeams = [];\n    this.isFollowed = false;\n    this.params.team_id = route.snapshot.paramMap.get('team_id');\n    console.log('this.team_id ', this.params.team_id);\n  }\n  ngOnInit() {\n    this.getFixtureResult(this.params);\n    this.onUrlChange();\n    this.getListFavoriteTeams();\n  }\n  getFixtureResult(params) {\n    this._loading.show();\n    this._tourService.fixturesResultsByTeam(this.params.team_id, params).subscribe(res => {\n      this.team = res.team;\n      this._titleService.setTitle(this.team?.name);\n      this.players = res.team.players;\n      this.matches = res.matches;\n    });\n  }\n  // on url change\n  onUrlChange() {\n    this.route.params.subscribe(params => {\n      this.params.team_id = params.team_id;\n      this.getFixtureResult(this.params);\n    });\n  }\n  getListFavoriteTeams() {\n    // get list of favorite teams\n    this._userService.getFavouriteTeams().subscribe(res => {\n      this.favoriteTeams = res.data;\n      console.log('this.favoriteTeams', this.favoriteTeams);\n      this.isFollowed = this.favoriteTeams.find(_v => _v.id === this.team.id) ? true : false;\n      console.log(this.isFollowed, 'isFollowed');\n    });\n  }\n  onClickFollowTeam() {\n    this._userService.toggleFavouriteTeam(this.team.id).subscribe(res => {\n      Swal.fire({\n        icon: 'success',\n        title: this._translateService.instant('Success'),\n        text: res.message\n      });\n      this.getListFavoriteTeams();\n    });\n  }\n  static #_ = this.ɵfac = function TeamFixturesComponent_Factory(t) {\n    return new (t || TeamFixturesComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.TournamentService), i0.ɵɵdirectiveInject(i5.CommonsService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.Title), i0.ɵɵdirectiveInject(i2.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeamFixturesComponent,\n    selectors: [[\"app-team-fixtures\"]],\n    decls: 3,\n    vars: 1,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [\"id\", \"team_fixtures-results\", 4, \"ngIf\"], [\"id\", \"team_fixtures-results\"], [1, \"row\"], [1, \"container-fluid\"], [1, \"card\", \"profile-header\"], [1, \"card-body\", \"team-info_wrapper\"], [1, \"club-logo\"], [\"alt\", \"Card image\", 1, \"rounded\", \"img-fluid\", 3, \"src\"], [1, \"club-info_wrapper\"], [1, \"club-info_name\"], [1, \"club-info_group\"], [1, \"club-info_club\"], [1, \"club-button\"], [\"type\", \"button\", \"rippleEffect\", \"\", 1, \"btn\", 3, \"ngClass\", \"click\"], [1, \"mr-25\", 3, \"ngClass\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\", 3, \"activeId\", \"activeIdChange\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\", 3, \"ngbNavItem\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [\"type\", \"fixtures\", 3, \"matches\"], [\"class\", \"col\", 4, \"ngIf\"], [\"class\", \"col-12 col-md-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col\"], [1, \"text-center\"], [1, \"col-12\", \"col-md-4\"], [1, \"card-body\"], [1, \"col-auto\"], [\"alt\", \"Card image\", 1, \"rounded\", \"img-fluid\", 2, \"width\", \"80px\", 3, \"src\"], [1, \"col\", \"text-dark\"]],\n    template: function TeamFixturesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, TeamFixturesComponent_section_2_Template, 35, 21, \"section\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.team);\n      }\n    },\n    styles: [\".team-info_wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 0.5rem;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-logo[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  padding: 1rem;\\n}\\n@media (min-width: 1024px) {\\n  .team-info_wrapper[_ngcontent-%COMP%]   .club-logo[_ngcontent-%COMP%] {\\n    max-width: 100px;\\n  }\\n}\\n@media (max-width: 1023px) {\\n  .team-info_wrapper[_ngcontent-%COMP%]   .club-logo[_ngcontent-%COMP%] {\\n    max-width: 25%;\\n  }\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-info_wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  width: 100%;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-info_wrapper[_ngcontent-%COMP%]   .club-info_name[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  margin: 0;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-info_wrapper[_ngcontent-%COMP%]   .club-info_group[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-info_wrapper[_ngcontent-%COMP%]   .club-info_club[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-button[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvZml4dHVyZXMtcmVzdWx0cy90ZWFtLWZpeHR1cmVzL3RlYW0tZml4dHVyZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsZUFBQTtBQUNGO0FBQ0U7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7QUFDSjtBQUNJO0VBTEY7SUFNSSxnQkFBQTtFQUVKO0FBQ0Y7QUFESTtFQVJGO0lBU0ksY0FBQTtFQUlKO0FBQ0Y7QUFGSTtFQUNFLFdBQUE7QUFJTjtBQURFO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7QUFHSjtBQURJO0VBQ0UsaUJBQUE7RUFDQSxTQUFBO0FBR047QUFESTtFQUNFLGVBQUE7RUFDQSxTQUFBO0FBR047QUFESTtFQUNFLGVBQUE7RUFDQSxTQUFBO0FBR047QUFBRTtFQUNFLHNCQUFBO0FBRUoiLCJzb3VyY2VzQ29udGVudCI6WyIudGVhbS1pbmZvX3dyYXBwZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDFyZW07XHJcbiAgcGFkZGluZzogMC41cmVtO1xyXG5cclxuICAuY2x1Yi1sb2dvIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgcGFkZGluZzogMXJlbTtcclxuXHJcbiAgICBAbWVkaWEgKG1pbi13aWR0aDogMTAyNHB4KSB7XHJcbiAgICAgIG1heC13aWR0aDogMTAwcHg7XHJcbiAgICB9XHJcbiAgICBAbWVkaWEgKG1heC13aWR0aDogMTAyM3B4KSB7XHJcbiAgICAgIG1heC13aWR0aDogMjUlO1xyXG4gICAgfVxyXG5cclxuICAgIGltZyB7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgfVxyXG4gIH1cclxuICAuY2x1Yi1pbmZvX3dyYXBwZXIge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBnYXA6IDAuNXJlbTtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG5cclxuICAgIC5jbHViLWluZm9fbmFtZSB7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcbiAgICAuY2x1Yi1pbmZvX2dyb3VwIHtcclxuICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcbiAgICAuY2x1Yi1pbmZvX2NsdWIge1xyXG4gICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgIH1cclxuICB9XHJcbiAgLmNsdWItYnV0dG9uIHtcclxuICAgIG1pbi13aWR0aDogbWF4LWNvbnRlbnQ7XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAIA,SAASA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAKtD,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;IC0DZC,EAAA,CAAAC,SAAA,uBACe;;;;IADeD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,OAAA,CAAmB;;;;;IAU/CJ,EAAA,CAAAK,cAAA,cAA8C;IAE1CL,EAAA,CAAAM,MAAA,GACF;;IAAAN,EAAA,CAAAO,YAAA,EAAK;;;IADHP,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAU,WAAA,gCACF;;;;;IAEFV,EAAA,CAAAK,cAAA,cAA4D;IAKlDL,EAAA,CAAAC,SAAA,cASE;IACJD,EAAA,CAAAO,YAAA,EAAM;IAENP,EAAA,CAAAK,cAAA,cAA2B;IACrBL,EAAA,CAAAM,MAAA,GAAwC;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACjDP,EAAA,CAAAK,cAAA,SAAI;IAAAL,EAAA,CAAAM,MAAA,IAAqC;;IAAAN,EAAA,CAAAO,YAAA,EAAK;;;;;IAb5CP,EAAA,CAAAQ,SAAA,GAIC;IAJDR,EAAA,CAAAE,UAAA,SAAAS,SAAA,kBAAAA,SAAA,CAAAC,KAAA,IAAAD,SAAA,kBAAAA,SAAA,CAAAC,KAAA,GAAAC,MAAA,CAAAf,UAAA,CAAAgB,GAAA,CAAAC,YAAA,EAAAf,EAAA,CAAAgB,aAAA,CAIC;IAQChB,EAAA,CAAAQ,SAAA,GAAwC;IAAxCR,EAAA,CAAAiB,iBAAA,CAAAJ,MAAA,CAAAK,YAAA,CAAAC,QAAA,CAAAR,SAAA,CAAAS,IAAA,EAAwC;IACxCpB,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAS,kBAAA,UAAAT,EAAA,CAAAqB,WAAA,QAAAV,SAAA,CAAAW,GAAA,cAAqC;;;;;IAzBrDtB,EAAA,CAAAK,cAAA,aAAiB;IACfL,EAAA,CAAAuB,UAAA,IAAAC,6DAAA,kBAIM;IACNxB,EAAA,CAAAuB,UAAA,IAAAE,6DAAA,mBAwBM;IACRzB,EAAA,CAAAO,YAAA,EAAM;;;;IA9BcP,EAAA,CAAAQ,SAAA,GAA0B;IAA1BR,EAAA,CAAAE,UAAA,UAAAwB,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAC,MAAA,OAA0B;IAKI5B,EAAA,CAAAQ,SAAA,GAAU;IAAVR,EAAA,CAAAE,UAAA,YAAAwB,MAAA,CAAAC,OAAA,CAAU;;;;;;IAhF1E3B,EAAA,CAAAK,cAAA,iBAAiD;IAMrCL,EAAA,CAAAC,SAAA,aAQE;IACJD,EAAA,CAAAO,YAAA,EAAM;IAENP,EAAA,CAAAK,cAAA,cAA+B;IAE3BL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACLP,EAAA,CAAAK,cAAA,aAA2B;IACzBL,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAO,YAAA,EAAI;IACJP,EAAA,CAAAK,cAAA,aAA0B;IACxBL,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAO,YAAA,EAAI;IAENP,EAAA,CAAAK,cAAA,eAAyB;IAMrBL,EAAA,CAAA6B,UAAA,mBAAAC,kEAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjC,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAmC,WAAA,CAAAF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAE7BpC,EAAA,CAAAC,SAAA,aAKK;IACLD,EAAA,CAAAM,MAAA,IAKF;;;IAAAN,EAAA,CAAAO,YAAA,EAAS;IAKjBP,EAAA,CAAAK,cAAA,eAAoB;IAIdL,EAAA,CAAA6B,UAAA,4BAAAQ,uEAAAC,MAAA;MAAAtC,EAAA,CAAA+B,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAvC,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAmC,WAAA,CAAAI,MAAA,CAAAC,QAAA,GAAAF,MAAA;IAAA,EAAuB;IAIvBtC,EAAA,CAAAK,cAAA,cAAgC;IAE5BL,EAAA,CAAAM,MAAA,IACF;;IAAAN,EAAA,CAAAO,YAAA,EAAI;IACJP,EAAA,CAAAuB,UAAA,KAAAkB,uDAAA,0BAGc;IAChBzC,EAAA,CAAAO,YAAA,EAAK;IACLP,EAAA,CAAAK,cAAA,cAAgC;IAE5BL,EAAA,CAAAM,MAAA,IACF;;IAAAN,EAAA,CAAAO,YAAA,EAAI;IACJP,EAAA,CAAAuB,UAAA,KAAAmB,uDAAA,0BAiCc;IAChB1C,EAAA,CAAAO,YAAA,EAAK;IAGTP,EAAA,CAAAC,SAAA,eAA6C;IAC/CD,EAAA,CAAAO,YAAA,EAAM;;;;;IAxGIP,EAAA,CAAAQ,SAAA,GAIC;IAJDR,EAAA,CAAAE,UAAA,SAAAyC,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAC,IAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAC,IAAA,IAAAH,MAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAC,IAAA,GAAAH,MAAA,CAAA7C,UAAA,CAAAgB,GAAA,CAAAC,YAAA,EAAAf,EAAA,CAAAgB,aAAA,CAIC;IAQDhB,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAkC,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAG,IAAA,MACF;IAEE/C,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAkC,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAI,KAAA,CAAAD,IAAA,MACF;IAEE/C,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAkC,MAAA,CAAAC,IAAA,CAAAC,IAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAE,IAAA,MACF;IAME/C,EAAA,CAAAQ,SAAA,GAA8D;IAA9DR,EAAA,CAAAE,UAAA,YAAAyC,MAAA,CAAAM,UAAA,yCAA8D;IAM5DjD,EAAA,CAAAQ,SAAA,GAEC;IAFDR,EAAA,CAAAE,UAAA,YAAAyC,MAAA,CAAAM,UAAA,iDAEC;IAEHjD,EAAA,CAAAQ,SAAA,GAKF;IALER,EAAA,CAAAS,kBAAA,MAAAkC,MAAA,CAAAM,UAAA,GAAAjD,EAAA,CAAAU,WAAA,uBAAAV,EAAA,CAAAU,WAAA,wBAKF;IASFV,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAE,UAAA,aAAAyC,MAAA,CAAAH,QAAA,CAAuB;IAIRxC,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAE,UAAA,iBAAgB;IAE3BF,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAU,WAAA,oCACF;IAMaV,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAE,UAAA,iBAAgB;IAE3BF,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAU,WAAA,yBACF;IAsCDV,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAE,UAAA,iBAAAgD,GAAA,CAAoB;;;ADhGnC,OAAM,MAAOC,qBAAqB;EAehCC,YACSC,KAAqB,EACrBC,MAAwB,EACxBC,QAAwB,EACxBC,YAA+B,EAC/BC,cAA8B,EAC9BvC,YAAyB,EACzBwC,aAAoB,EACpBC,iBAAmC;IAPnC,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAvC,YAAY,GAAZA,YAAY;IACZ,KAAAwC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAtB1B,KAAA9D,SAAS,GAAGA,SAAS;IACrB,KAAAC,UAAU,GAAGA,UAAU;IACvB,KAAA8D,aAAa,GAAG,IAAI;IACpB,KAAApB,QAAQ,GAAG,CAAC;IACZ,KAAApC,OAAO,GAAG,EAAE;IACZ,KAAAyD,MAAM,GAAG;MACPC,OAAO,EAAE;KACV;IACD,KAAAlB,IAAI,GAAG,IAAI;IACX,KAAAjB,OAAO,GAAG,EAAE;IAEZ,KAAAoC,aAAa,GAAG,EAAE;IAClB,KAAAd,UAAU,GAAG,KAAK;IAYhB,IAAI,CAACY,MAAM,CAACC,OAAO,GAAGT,KAAK,CAACW,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC;IAC5DC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACP,MAAM,CAACC,OAAO,CAAC;EACnD;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACT,MAAM,CAAC;IAClC,IAAI,CAACU,WAAW,EAAE;IAClB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAF,gBAAgBA,CAACT,MAAM;IACrB,IAAI,CAACN,QAAQ,CAACkB,IAAI,EAAE;IACpB,IAAI,CAACjB,YAAY,CACdkB,qBAAqB,CAAC,IAAI,CAACb,MAAM,CAACC,OAAO,EAAED,MAAM,CAAC,CAClDc,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAI,CAAChC,IAAI,GAAGgC,GAAG,CAAChC,IAAI;MACpB,IAAI,CAACc,aAAa,CAACmB,QAAQ,CAAC,IAAI,CAACjC,IAAI,EAAEG,IAAI,CAAC;MAC5C,IAAI,CAACpB,OAAO,GAAGiD,GAAG,CAAChC,IAAI,CAACjB,OAAO;MAC/B,IAAI,CAACvB,OAAO,GAAGwE,GAAG,CAACxE,OAAO;IAC5B,CAAC,CAAC;EACN;EAEA;EACAmE,WAAWA,CAAA;IACT,IAAI,CAAClB,KAAK,CAACQ,MAAM,CAACc,SAAS,CAAEd,MAAM,IAAI;MACrC,IAAI,CAACA,MAAM,CAACC,OAAO,GAAGD,MAAM,CAACC,OAAO;MACpC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,CAACT,MAAM,CAAC;IACpC,CAAC,CAAC;EACJ;EAEAW,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACtD,YAAY,CAAC4D,iBAAiB,EAAE,CAACH,SAAS,CAAEC,GAAG,IAAI;MACtD,IAAI,CAACb,aAAa,GAAGa,GAAG,CAACG,IAAI;MAE7BZ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACL,aAAa,CAAC;MAErD,IAAI,CAACd,UAAU,GAAG,IAAI,CAACc,aAAa,CAACiB,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACC,EAAE,KAAK,IAAI,CAACtC,IAAI,CAACsC,EAAE,CAAC,GACrE,IAAI,GACJ,KAAK;MAETf,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnB,UAAU,EAAE,YAAY,CAAC;IAC5C,CAAC,CAAC;EACJ;EAEAb,iBAAiBA,CAAA;IACf,IAAI,CAAClB,YAAY,CAACiE,mBAAmB,CAAC,IAAI,CAACvC,IAAI,CAACsC,EAAE,CAAC,CAACP,SAAS,CAAEC,GAAG,IAAI;MACpE7E,IAAI,CAACqF,IAAI,CAAC;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC4B,OAAO,CAAC,SAAS,CAAC;QAChDC,IAAI,EAAEZ,GAAG,CAACa;OACX,CAAC;MACF,IAAI,CAACjB,oBAAoB,EAAE;IAC7B,CAAC,CAAC;EACJ;EAAC,QAAAkB,CAAA;qBA/EUvC,qBAAqB,EAAAnD,EAAA,CAAA2F,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7F,EAAA,CAAA2F,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA/F,EAAA,CAAA2F,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAjG,EAAA,CAAA2F,iBAAA,CAAAO,EAAA,CAAAC,iBAAA,GAAAnG,EAAA,CAAA2F,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAArG,EAAA,CAAA2F,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAAvG,EAAA,CAAA2F,iBAAA,CAAAa,EAAA,CAAAC,KAAA,GAAAzG,EAAA,CAAA2F,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAW,EAAA;UAArBvD,qBAAqB;IAAAwD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChBlCjH,EAAA,CAAAK,cAAA,aAA+C;QAE3CL,EAAA,CAAAuB,UAAA,IAAA4F,wCAAA,uBAiHU;QACZnH,EAAA,CAAAO,YAAA,EAAM;;;QAlHiCP,EAAA,CAAAQ,SAAA,GAAU;QAAVR,EAAA,CAAAE,UAAA,SAAAgH,GAAA,CAAAtE,IAAA,CAAU", "names": ["AppConfig", "coreConfig", "<PERSON><PERSON>", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "matches", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "player_r6", "photo", "ctx_r5", "app", "appLogoImage", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "_userService", "fullName", "user", "ɵɵpipeBind2", "dob", "ɵɵtemplate", "TeamFixturesComponent_section_2_ng_template_33_div_1_Template", "TeamFixturesComponent_section_2_ng_template_33_div_2_Template", "ctx_r3", "players", "length", "ɵɵlistener", "TeamFixturesComponent_section_2_Template_button_click_15_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "onClickFollowTeam", "TeamFixturesComponent_section_2_Template_ul_activeIdChange_22_listener", "$event", "ctx_r9", "activeId", "TeamFixturesComponent_section_2_ng_template_28_Template", "TeamFixturesComponent_section_2_ng_template_33_Template", "ctx_r0", "team", "club", "logo", "name", "group", "isFollowed", "_r1", "TeamFixturesComponent", "constructor", "route", "_trans", "_loading", "_tourService", "_commonService", "_titleService", "_translateService", "contentHeader", "params", "team_id", "favoriteTeams", "snapshot", "paramMap", "get", "console", "log", "ngOnInit", "getFixtureResult", "onUrlChange", "getListFavoriteTeams", "show", "fixturesResultsByTeam", "subscribe", "res", "setTitle", "getFavouriteTeams", "data", "find", "_v", "id", "toggleFavouriteTeam", "fire", "icon", "title", "instant", "text", "message", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "TranslateService", "i3", "LoadingService", "i4", "TournamentService", "i5", "CommonsService", "i6", "UserService", "i7", "Title", "_2", "selectors", "decls", "vars", "consts", "template", "TeamFixturesComponent_Template", "rf", "ctx", "TeamFixturesComponent_section_2_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\team-fixtures\\team-fixtures.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\team-fixtures\\team-fixtures.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { AppConfig, coreConfig } from 'app/app-config';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-team-fixtures',\r\n  templateUrl: './team-fixtures.component.html',\r\n  styleUrls: ['./team-fixtures.component.scss'],\r\n})\r\nexport class TeamFixturesComponent implements OnInit {\r\n  AppConfig = AppConfig;\r\n  coreConfig = coreConfig;\r\n  contentHeader = null;\r\n  activeId = 1;\r\n  matches = [];\r\n  params = {\r\n    team_id: null,\r\n  };\r\n  team = null;\r\n  players = [];\r\n\r\n  favoriteTeams = [];\r\n  isFollowed = false;\r\n\r\n  constructor(\r\n    public route: ActivatedRoute,\r\n    public _trans: TranslateService,\r\n    public _loading: LoadingService,\r\n    public _tourService: TournamentService,\r\n    public _commonService: CommonsService,\r\n    public _userService: UserService,\r\n    public _titleService: Title,\r\n    public _translateService: TranslateService\r\n  ) {\r\n    this.params.team_id = route.snapshot.paramMap.get('team_id');\r\n    console.log('this.team_id ', this.params.team_id);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getFixtureResult(this.params);\r\n    this.onUrlChange();\r\n    this.getListFavoriteTeams();\r\n  }\r\n\r\n  getFixtureResult(params) {\r\n    this._loading.show();\r\n    this._tourService\r\n      .fixturesResultsByTeam(this.params.team_id, params)\r\n      .subscribe((res) => {\r\n        this.team = res.team;\r\n        this._titleService.setTitle(this.team?.name);\r\n        this.players = res.team.players;\r\n        this.matches = res.matches;\r\n      });\r\n  }\r\n\r\n  // on url change\r\n  onUrlChange() {\r\n    this.route.params.subscribe((params) => {\r\n      this.params.team_id = params.team_id;\r\n      this.getFixtureResult(this.params);\r\n    });\r\n  }\r\n\r\n  getListFavoriteTeams() {\r\n    // get list of favorite teams\r\n    this._userService.getFavouriteTeams().subscribe((res) => {\r\n      this.favoriteTeams = res.data;\r\n\r\n      console.log('this.favoriteTeams', this.favoriteTeams);\r\n\r\n      this.isFollowed = this.favoriteTeams.find((_v) => _v.id === this.team.id)\r\n        ? true\r\n        : false;\r\n\r\n      console.log(this.isFollowed, 'isFollowed');\r\n    });\r\n  }\r\n\r\n  onClickFollowTeam() {\r\n    this._userService.toggleFavouriteTeam(this.team.id).subscribe((res) => {\r\n      Swal.fire({\r\n        icon: 'success',\r\n        title: this._translateService.instant('Success'),\r\n        text: res.message,\r\n      });\r\n      this.getListFavoriteTeams();\r\n    });\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <section id=\"team_fixtures-results\" *ngIf=\"team\">\r\n      <div class=\"row\">\r\n        <div class=\"container-fluid\">\r\n          <div class=\"card profile-header\">\r\n            <div class=\"card-body team-info_wrapper\">\r\n              <div class=\"club-logo\">\r\n                <img\r\n                  [src]=\"\r\n                    team?.club?.logo\r\n                      ? team.club.logo\r\n                      : coreConfig.app.appLogoImage\r\n                  \"\r\n                  class=\"rounded img-fluid\"\r\n                  alt=\"Card image\"\r\n                />\r\n              </div>\r\n              <!-- profile title -->\r\n              <div class=\"club-info_wrapper\">\r\n                <h2 class=\"club-info_name\">\r\n                  {{ team?.name }}\r\n                </h2>\r\n                <p class=\"club-info_group\">\r\n                  {{ team?.group.name }}\r\n                </p>\r\n                <p class=\"club-info_club\">\r\n                  {{ team.club?.name }}\r\n                </p>\r\n              </div>\r\n              <div class=\"club-button\">\r\n                <button\r\n                  type=\"button\"\r\n                  class=\"btn\"\r\n                  [ngClass]=\"isFollowed ? 'btn-warning' : 'btn-outline-warning'\"\r\n                  rippleEffect\r\n                  (click)=\"onClickFollowTeam()\"\r\n                >\r\n                  <i\r\n                    class=\"mr-25\"\r\n                    [ngClass]=\"\r\n                      isFollowed ? 'feather icon-bell-off' : 'feather icon-bell'\r\n                    \"\r\n                  ></i>\r\n                  {{\r\n                    isFollowed\r\n                      ? ('Unfollow' | translate)\r\n                      : ('Follow' | translate)\r\n                  }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-12\">\r\n          <div class=\"card\">\r\n            <ul\r\n              ngbNav\r\n              [(activeId)]=\"activeId\"\r\n              #nav=\"ngbNav\"\r\n              class=\"nav-tabs m-0\"\r\n            >\r\n              <li ngbNavItem [ngbNavItem]=\"1\">\r\n                <a ngbNavLink>\r\n                  {{ 'Fixtures & Results' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <tab-fixtures type=\"fixtures\" [matches]=\"matches\">\r\n                  </tab-fixtures>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem [ngbNavItem]=\"2\">\r\n                <a ngbNavLink>\r\n                  {{ 'Players' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <div class=\"row\">\r\n                    <div class=\"col\" *ngIf=\"players?.length == 0\">\r\n                      <h3 class=\"text-center\">\r\n                        {{ 'No results found' | translate }}\r\n                      </h3>\r\n                    </div>\r\n                    <div class=\"col-12 col-md-4\" *ngFor=\"let player of players\">\r\n                      <div class=\"card\">\r\n                        <div class=\"card-body\">\r\n                          <div class=\"row\">\r\n                            <div class=\"col-auto\">\r\n                              <img\r\n                                [src]=\"\r\n                                  player?.photo\r\n                                    ? player?.photo\r\n                                    : coreConfig.app.appLogoImage\r\n                                \"\r\n                                style=\"width: 80px\"\r\n                                class=\"rounded img-fluid\"\r\n                                alt=\"Card image\"\r\n                              />\r\n                            </div>\r\n                            <!-- profile title -->\r\n                            <div class=\"col text-dark\">\r\n                              <h4>{{ _userService.fullName(player.user) }}</h4>\r\n                              <h6>YOB: {{ player.dob | date : 'yyyy' }}</h6>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}