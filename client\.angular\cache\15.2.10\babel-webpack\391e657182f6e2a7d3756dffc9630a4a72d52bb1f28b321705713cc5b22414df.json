{"ast": null, "code": "import { FieldType } from '@ngx-formly/core';\nimport * as i0 from \"@angular/core\";\nexport class NumberTypeComponent extends FieldType {\n  constructor() {\n    super();\n  }\n  ngOnInit() {\n    // console.log(this.to);\n  }\n  onChange(event) {\n    // console.log(event);\n    this.formControl.setValue(event);\n  }\n  static #_ = this.ɵfac = function NumberTypeComponent_Factory(t) {\n    return new (t || NumberTypeComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NumberTypeComponent,\n    selectors: [[\"app-number-type\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 10,\n    consts: [[1, \"float-right\"], [3, \"numberValue\", \"minValue\", \"maxValue\", \"stepValue\", \"color\", \"size\", \"disable\", \"id\", \"onChange\"], [\"type\", \"input\", 2, \"display\", \"none\", 3, \"formControl\", \"formlyAttributes\"]],\n    template: function NumberTypeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"core-touchspin\", 1);\n        i0.ɵɵlistener(\"onChange\", function NumberTypeComponent_Template_core_touchspin_onChange_1_listener($event) {\n          return ctx.onChange($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(2, \"input\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵpropertyInterpolate(\"id\", ctx.field.key);\n        i0.ɵɵproperty(\"numberValue\", ctx.formControl.value)(\"minValue\", ctx.to.min)(\"maxValue\", ctx.to.max)(\"stepValue\", ctx.to.step)(\"color\", ctx.to.color ? ctx.to.color : \"primary\")(\"size\", ctx.to.size ? ctx.to.size : \"\")(\"disable\", ctx.to.disabled);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"formControl\", ctx.formControl)(\"formlyAttributes\", ctx.field);\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAAyBA,SAAS,QAAQ,kBAAkB;;AAO5D,OAAM,MAAOC,mBACX,SAAQD,SAA0B;EAGlCE,YAAA;IACE,KAAK,EAAE;EACT;EACAC,QAAQA,CAAA;IACN;EAAA;EAEFC,QAAQA,CAACC,KAAK;IACZ;IACA,IAAI,CAACC,WAAW,CAACC,QAAQ,CAACF,KAAK,CAAC;EAClC;EAAC,QAAAG,CAAA;qBAbUP,mBAAmB;EAAA;EAAA,QAAAQ,EAAA;UAAnBR,mBAAmB;IAAAS,SAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRhCP,EAAA,CAAAS,cAAA,aAAyB;QAErBT,EAAA,CAAAU,UAAA,sBAAAC,gEAAAC,MAAA;UAAA,OAAYJ,GAAA,CAAAhB,QAAA,CAAAoB,MAAA,CAAgB;QAAA,EAAC;QAS9BZ,EAAA,CAAAa,YAAA,EAAiB;QAClBb,EAAA,CAAAc,SAAA,eAKE;QACJd,EAAA,CAAAa,YAAA,EAAM;;;QARFb,EAAA,CAAAe,SAAA,GAAkB;QAAlBf,EAAA,CAAAgB,qBAAA,OAAAR,GAAA,CAAAS,KAAA,CAAAC,GAAA,CAAkB;QAPlBlB,EAAA,CAAAmB,UAAA,gBAAAX,GAAA,CAAAd,WAAA,CAAA0B,KAAA,CAAiC,aAAAZ,GAAA,CAAAa,EAAA,CAAAC,GAAA,cAAAd,GAAA,CAAAa,EAAA,CAAAE,GAAA,eAAAf,GAAA,CAAAa,EAAA,CAAAG,IAAA,WAAAhB,GAAA,CAAAa,EAAA,CAAAI,KAAA,GAAAjB,GAAA,CAAAa,EAAA,CAAAI,KAAA,sBAAAjB,GAAA,CAAAa,EAAA,CAAAK,IAAA,GAAAlB,GAAA,CAAAa,EAAA,CAAAK,IAAA,kBAAAlB,GAAA,CAAAa,EAAA,CAAAM,QAAA;QAYjC3B,EAAA,CAAAe,SAAA,GAA2B;QAA3Bf,EAAA,CAAAmB,UAAA,gBAAAX,GAAA,CAAAd,WAAA,CAA2B,qBAAAc,GAAA,CAAAS,KAAA", "names": ["FieldType", "NumberTypeComponent", "constructor", "ngOnInit", "onChange", "event", "formControl", "setValue", "_", "_2", "selectors", "features", "i0", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "NumberTypeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "NumberTypeComponent_Template_core_touchspin_onChange_1_listener", "$event", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵpropertyInterpolate", "field", "key", "ɵɵproperty", "value", "to", "min", "max", "step", "color", "size", "disabled"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\number-type\\number-type.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\number-type\\number-type.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FieldTypeConfig,FieldType } from '@ngx-formly/core';\r\n\r\n@Component({\r\n  selector: 'app-number-type',\r\n  templateUrl: './number-type.component.html',\r\n  styleUrls: ['./number-type.component.scss'],\r\n})\r\nexport class NumberTypeComponent\r\n  extends FieldType<FieldTypeConfig>\r\n  implements OnInit\r\n{\r\n  constructor() {\r\n    super();\r\n  }\r\n  ngOnInit(): void {\r\n    // console.log(this.to);\r\n  }\r\n  onChange(event) {\r\n    // console.log(event);\r\n    this.formControl.setValue(event);\r\n  }\r\n}\r\n", "<div class=\"float-right\">\r\n  <core-touchspin\r\n    (onChange)=\"onChange($event)\"\r\n    [numberValue]=\"formControl.value\"\r\n    [minValue]=\"to.min\"\r\n    [maxValue]=\"to.max\"\r\n    [stepValue]=\"to.step\"\r\n    [color]=\"to.color ? to.color : 'primary'\"\r\n    [size]=\"to.size ? to.size : ''\"\r\n    [disable]=\"to.disabled\"\r\n    id=\"{{field.key}}\"\r\n  ></core-touchspin>\r\n  <input\r\n    type=\"input\"\r\n    style=\"display: none\"\r\n    [formControl]=\"formControl\"\r\n    [formlyAttributes]=\"field\"\r\n  />\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}