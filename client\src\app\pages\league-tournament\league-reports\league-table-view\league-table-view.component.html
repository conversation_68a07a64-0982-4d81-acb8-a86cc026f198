<div class="p-1">
  <div class="row mb-1">
    <div class="col-12">
      <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="table border row-border hover">
      </table>
    </div>
  </div>
</div>

<core-sidebar class="modal modal-slide-in sidebar-todo-modal fade" [name]="table_name" overlayClass="modal-backdrop">
  <app-editor-sidebar [table]="dtElement" [fields]="fields" [params]="params">
  </app-editor-sidebar>
</core-sidebar>

<ng-template #rowActionBtn let-data="adtData" let-emitter="captureEvents">
  <app-btn-dropdown-action [actions]="rowActions" [data]="data" (emitter)="emitter($event)"
    btnStyle="font-size:15px;color:black!important"></app-btn-dropdown-action>
</ng-template>
