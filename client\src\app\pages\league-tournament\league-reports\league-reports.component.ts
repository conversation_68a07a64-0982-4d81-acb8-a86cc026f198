import { HttpClient } from '@angular/common/http';
import {
  Component,
  OnInit,
  Renderer2,
  ViewChild,
  HostListener,
  OnD<PERSON>roy
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { LoadingService } from 'app/services/loading.service';
import { environment } from 'environments/environment';
import { RegistrationService } from 'app/services/registration.service';
import { ClubService } from 'app/services/club.service';
import moment from 'moment';
import { Title } from '@angular/platform-browser';
import { NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';
import { LeagueTableViewComponent } from './league-table-view/league-table-view.component';

@Component({
  selector: 'app-league-reports',
  templateUrl: './league-reports.component.html',
  styleUrls: ['./league-reports.component.scss'],
})
export class LeagueReportsComponent implements OnInit, OnD<PERSON>roy
{
  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;
  @ViewChild(LeagueTableViewComponent, { static: false }) leagueTableViewComponent: LeagueTableViewComponent;

  public seasonId: any;
  public clubId: any = null;
  public tournamentId: any = null;
  public dateRange: any = { start_date: null, end_date: null };
  public dateRangeValue: string = '';
  hoveredDate: NgbDate | null = null;
  fromDate: NgbDate | null = null;
  toDate: NgbDate | null = null;
  isSelecting: boolean = false; // Track if we're in the middle of selecting range
  private clickOutsideTimeout: any;
  public matchStatus: string = 'all';
  public contentHeader: object;
  public seasons: any[];
  public clubs: any[];
  public tournaments: any[];

  matchStatusOptions = [
    { label: 'All Status', value: 'all' },
    { label: 'Upcoming', value: 'upcoming' },
    { label: 'Active', value: 'active' },
    { label: 'Completed', value: 'completed' },
    { label: 'Cancelled', value: 'cancelled' },
  ];

  constructor(
    private route: ActivatedRoute,
    public _router: Router,
    public _http: HttpClient,
    public _trans: TranslateService,
    public renderer: Renderer2,
    public _loadingService: LoadingService,
    public _registrationService: RegistrationService,
    public _clubService: ClubService,
    public _translateService: TranslateService,
    public _titleService: Title,
    private calendar: NgbCalendar,
    public formatter: NgbDateParserFormatter
  ) {
    this._titleService.setTitle('Matches Report');
  }

  _getCurrentSeason() {
    this._loadingService.show();
    this._registrationService.getAllSeasonActive().subscribe(
      (data) => {
        this.seasons = data;
        this.seasonId = this.seasons[0].id;
        this._getTournaments(); // Fetch tournaments after getting seasons
      },
      (error) => {
        Swal.fire({
          title: 'Error',
          text: error.message,
          icon: 'error',
          confirmButtonText: this._translateService.instant('OK')
        });
      },
      () => {
        this._loadingService.dismiss();
      }
    );
  }

  _getClubs() {
    this._clubService.getAllClubs().subscribe(
      (res) => {
        this.clubs = res.data;
      },
      (error) => {
        Swal.fire({
          title: 'Error',
          text: error.message,
          icon: 'error',
          confirmButtonText: this._translateService.instant('OK')
        });
      }
    );
  }

  onSelectSeason($event: any) {
    return new Promise((resolve, reject) => {
      this.seasonId = $event;
      this._getTournaments(); // Fetch tournaments when season changes
      // Reset tournament selection when season changes
      this.tournamentId = null;
      this.refreshChildDataTable();
      resolve(true);
    });
  }

  onSelectClub($event: any) {
    console.log(`onSelectClub: ${$event}`);
    this.clubId = $event;
    this.refreshChildDataTable();
  }

  onSelectTournament($event: any) {
    console.log(`onSelectTournament: ${$event}`);
    this.tournamentId = $event;
    this.refreshChildDataTable();
  }

  onSelectMatchStatus($event: any) {
    console.log(`onSelectMatchStatus: ${$event}`);
    this.matchStatus = $event;
    this.refreshChildDataTable();
  }

  onDateSelection(date: NgbDate) {

    if (!this.fromDate && !this.toDate) {
      console.log('Setting From Date');
      this.fromDate = date;
      this.isSelecting = true;
    } else if (
      this.fromDate &&
      !this.toDate &&
      date &&
      date.after(this.fromDate)
    ) {
      this.toDate = date;
      this.isSelecting = false;
      this.updateDateRange();
      setTimeout(() => {
        if (this.dateRangePicker && this.dateRangePicker.close) {
          this.dateRangePicker.close();
        }
      }, 0);
    } else {
      console.log('reset date form');
      this.toDate = null;
      this.fromDate = date;
      this.isSelecting = true;
    }
  }

  openDatePicker() {
    this.isSelecting = false; // Reset selection state when opening
    if (this.dateRangePicker) {
      this.dateRangePicker.open();
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    if (this.clickOutsideTimeout) {
      clearTimeout(this.clickOutsideTimeout);
    }

    this.clickOutsideTimeout = setTimeout(() => {
      this.handleClickOutside(event);
    }, 50);
  }

  private handleClickOutside(event: Event) {
    // Check if datepicker is open
    if (
      !this.dateRangePicker ||
      !this.dateRangePicker.isOpen ||
      !this.dateRangePicker.isOpen()
    ) {
      return;
    }

    if (this.isSelecting) {
      return;
    }

    const target = event.target as HTMLElement;

    if (!target) return;

    if (
      target.closest('.btn') ||
      target.classList.contains('feather') ||
      target.classList.contains('icon-calendar')
    ) {
      return;
    }

    if (
      target.tagName === 'INPUT' &&
      target.getAttribute('name') === 'daterange'
    ) {
      return;
    }

    const datepickerElement = document.querySelector('ngb-datepicker');
    if (datepickerElement && datepickerElement.contains(target)) {
      return;
    }

    this.dateRangePicker.close();
  }

  clearDateRange() {
    this.fromDate = null;
    this.toDate = null;
    this.isSelecting = false;
    this.updateDateRange();
  }

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    const parsed = this.formatter.parse(input);
    return parsed && this.calendar.isValid(NgbDate.from(parsed))
      ? NgbDate.from(parsed)
      : currentValue;
  }

  isHovered(date: NgbDate) {
    return (
      this.fromDate &&
      !this.toDate &&
      this.hoveredDate &&
      date.after(this.fromDate) &&
      date.before(this.hoveredDate)
    );
  }

  isInside(date: NgbDate) {
    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);
  }

  isRange(date: NgbDate) {
    return (
      date.equals(this.fromDate) ||
      (this.toDate && date.equals(this.toDate)) ||
      this.isInside(date) ||
      this.isHovered(date)
    );
  }

  onDateRangeChange() {
    console.log('onDateRangeChange:', this.dateRange);
    this.refreshChildDataTable();
  }

  private refreshChildDataTable() {
    // Refresh the league table view component's DataTable
    if (this.leagueTableViewComponent) {
      this.leagueTableViewComponent.refreshDataTable();
    }
  }

  updateDateRange() {
    if (this.fromDate && this.toDate) {
      const fromMoment = moment(
        `${this.fromDate.year}-${this.fromDate.month
          .toString()
          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`
      );
      const toMoment = moment(
        `${this.toDate.year}-${this.toDate.month
          .toString()
          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`
      );

      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');
      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');
      this.dateRangeValue = `${fromMoment.format(
        'MMM DD, YYYY'
      )} - ${toMoment.format('MMM DD, YYYY')}`;

      this.onDateRangeChange();
    } else if (this.fromDate) {
      const fromMoment = moment(
        `${this.fromDate.year}-${this.fromDate.month
          .toString()
          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`
      );
      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');
      this.dateRange.end_date = null;
      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');
    } else {
      this.dateRange.start_date = null;
      this.dateRange.end_date = null;
      this.dateRangeValue = '';
      this.onDateRangeChange();
    }
  }

  _getTournaments() {
    if (this.seasonId) {
      this._http
        .get<any>(
          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`
        )
        .subscribe(
          (data) => {
            this.tournaments = data.data;
          },
          (error) => {
            Swal.fire({
              title: 'Error',
              text: error.message,
              icon: 'error',
              confirmButtonText: this._translateService.instant('OK'),
            });
          }
        );
    }
  }

  ngOnInit(): void {
    this.contentHeader = {
      headerTitle: this._trans.instant('Matches Report'),
      actionButton: false,
      breadcrumb: {
        type: '',
        links: [
          {
            name: this._trans.instant('Home'),
            isLink: false
          },
          {
            name: this._trans.instant('Tournaments'),
            isLink: false
          },
          {
            name: this._trans.instant('Matches Report'),
            isLink: false
          }
        ]
      }
    };

    this._getCurrentSeason();
    this._getClubs();
  }

  ngOnDestroy(): void {
    if (this.clickOutsideTimeout) {
      clearTimeout(this.clickOutsideTimeout);
    }
  }
}
