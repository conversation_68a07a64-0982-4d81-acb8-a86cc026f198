{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild, HostListener } from '@angular/core';\nimport { environment } from 'environments/environment';\nimport moment from 'moment';\nimport { NgbDate } from '@ng-bootstrap/ng-bootstrap';\nimport Swal from 'sweetalert2';\nlet LeagueReportsComponent = class LeagueReportsComponent {\n  constructor(route, _router, _commonsService, _http, _trans, renderer, _teamService, _modalService, _loadingService, _toastService, _registrationService, _clubService, _translateService, _coreSidebarService, _titleService, _exportService, _tournamentService, calendar, formatter) {\n    this.route = route;\n    this._router = _router;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._teamService = _teamService;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._toastService = _toastService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this._titleService = _titleService;\n    this._exportService = _exportService;\n    this._tournamentService = _tournamentService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false; // Track if we're in the middle of selecting range\n    this.matchStatus = 'all';\n    this.viewTypes = [{\n      label: 'Table View',\n      value: 'league_table',\n      icon_name: 'fa-light fa-table-list'\n    }, {\n      label: 'Schedule View',\n      value: 'schedule_matches',\n      icon_name: 'fa-light fa-calendar'\n    }\n    // { label: 'Bracket View', value: 'bracket', icon_name: 'grid' },\n    ];\n\n    this.viewType = 'league_table';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Create New Tournament'),\n        edit: 'Edit team',\n        remove: 'Delete team'\n      },\n      url: `${environment.apiUrl}/teams/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'home_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Home team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }, {\n      key: 'away_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Away team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }];\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n      this.dtTrigger.next(this.dtOptions);\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n      // this.clubId = this.clubs[0];\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n    this.refreshDataTable();\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    var _this = this;\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      processing: true,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        // processing: `<div class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\n        //               <div class=\"spinner-border text-primary\" role=\"status\">\n        //                 <span class=\"sr-only\">Loading...</span>\n        //               </div>\n        //             </div>`,\n        processing: ``\n      },\n      ajax: (dataTablesParameters, callback) => {\n        // Build query parameters for all filters\n        let params = new URLSearchParams();\n        if (this.clubId != undefined && this.clubId !== null) {\n          params.append('club_id', this.clubId);\n        }\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\n          params.append('tournament_id', this.tournamentId);\n        }\n        if (this.dateRange.start_date) {\n          params.append('start_date', this.dateRange.start_date);\n        }\n        if (this.dateRange.end_date) {\n          params.append('end_date', this.dateRange.end_date);\n        }\n        if (this.matchStatus && this.matchStatus !== 'all') {\n          params.append('match_status', this.matchStatus);\n        }\n        const queryString = params.toString();\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${queryString ? '?' + queryString : ''}`;\n        this.isTableLoading = true;\n        this._http.post(url, dataTablesParameters).subscribe(resp => {\n          this.isTableLoading = false;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        }, error => {\n          this.isTableLoading = false;\n          console.error('Error loading table data:', error);\n          callback({\n            recordsTotal: 0,\n            recordsFiltered: 0,\n            data: []\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }],\n      columns: [{\n        title: this._translateService.instant('No.'),\n        data: null,\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: function (data, type, row, metadata) {\n          return metadata.row + 1;\n        }\n      }, {\n        title: this._translateService.instant('Tournament'),\n        data: 'name',\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: {\n          display: (data, type, row) => {\n            return data ?? 'TBD';\n          },\n          filter: (data, type, row) => {\n            return data;\n          }\n        }\n      }, {\n        title: this._translateService.instant('Round'),\n        data: 'round_name',\n        render: function (data) {\n          const displayData = data || 'TBD';\n          return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          const displayDate = !data || data === 'TBD' ? 'TBD' : moment(data).format('YYYY-MM-DD');\n          return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Home Team'),\n        data: 'home_team_name',\n        className: 'text-center'\n      }, {\n        title: 'VS',\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return 'vs';\n        }\n      }, {\n        title: this._translateService.instant('Away Team'),\n        data: 'away_team_name',\n        className: 'text-center'\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center'\n      }, {\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return '-';\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant('Export CSV')}`,\n          extend: 'csv',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              // Generate filename with current filter information\n              let filename = 'Matches_Report';\n              if (_this.seasonId) {\n                const season = _this.seasons?.find(s => s.id == _this.seasonId);\n                if (season) {\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.tournamentId) {\n                const tournament = _this.tournaments?.find(t => t.id == _this.tournamentId);\n                if (tournament) {\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.clubId) {\n                const club = _this.clubs?.find(c => c.id == _this.clubId);\n                if (club) {\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.matchStatus && _this.matchStatus !== 'all') {\n                filename += `_${_this.matchStatus}`;\n              }\n              if (_this.dateRange.start_date && _this.dateRange.end_date) {\n                filename += `_${_this.dateRange.start_date}_to_${_this.dateRange.end_date}`;\n              } else if (_this.dateRange.start_date) {\n                filename += `_from_${_this.dateRange.start_date}`;\n              }\n              filename += '.csv';\n              yield _this._exportService.exportCsv(data, filename);\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  setClubs(data) {\n    this.clubs = data;\n    // get field has key club_id\n    const clubField = this.fields.find(field => field.key === 'club_id');\n    // set options for club field\n    let current_clubs = [];\n    data.forEach(club => {\n      let club_name = this._translateService.instant(club.name);\n      current_clubs.push({\n        label: club_name,\n        value: club.id\n      });\n    });\n    clubField.props.options = current_clubs;\n  }\n  onCaptureEvent(event) {\n    // console.log(event);\n  }\n  editor(action, row) {\n    this.fields[0].defaultValue = this.clubId;\n    switch (action) {\n      case 'create':\n        this.fields[2].props.disabled = false;\n        break;\n      case 'edit':\n        this.fields[2].props.disabled = true;\n        break;\n      case 'remove':\n        break;\n      default:\n        break;\n    }\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('tournament_id')) {\n        let tournament_id = event.target.getAttribute('tournament_id');\n        let stage_id = event.target.getAttribute('stage_id');\n        //  navigate to path ':tournament_id/stages/:stage_id'\n        this._router.navigate([tournament_id, 'stages', stage_id], {\n          relativeTo: this.route\n        });\n      }\n    });\n  }\n  onSelectViewType(event) {\n    this.viewType = event;\n    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  refreshDataTable() {\n    if (this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    this.unlistener();\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n};\n__decorate([ViewChild('dateRangePicker', {\n  static: false\n})], LeagueReportsComponent.prototype, \"dateRangePicker\", void 0);\n__decorate([HostListener('document:click', ['$event'])], LeagueReportsComponent.prototype, \"onDocumentClick\", null);\nLeagueReportsComponent = __decorate([Component({\n  selector: 'app-league-reports',\n  templateUrl: './league-reports.component.html',\n  styleUrls: ['./league-reports.component.scss']\n})], LeagueReportsComponent);\nexport { LeagueReportsComponent };", "map": {"version": 3, "mappings": ";;AACA,SACEA,SAAS,EAGTC,SAAS,EACTC,YAAY,QAEP,eAAe;AAItB,SAASC,WAAW,QAAQ,0BAA0B;AAGtD,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAAsBC,OAAO,QAAgC,4BAA4B;AACzF,OAAOC,IAAI,MAAM,aAAa;AAOvB,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAwEjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,eAA+B,EAC/BC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,YAAyB,EACzBC,aAAuB,EACvBC,eAA+B,EAC/BC,aAA4B,EAC5BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,mBAAuC,EACvCC,aAAoB,EACnBC,cAA6B,EAC9BC,kBAAqC,EACpCC,QAAqB,EACtBC,SAAiC;IAlBhC,KAAAlB,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IAtFX,KAAAC,MAAM,GAAQ,IAAI;IAClB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,SAAS,GAAQ;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAC,WAAW,GAAmB,IAAI;IAClC,KAAAC,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAC,WAAW,GAAY,KAAK,CAAC,CAAC;IAEvB,KAAAC,WAAW,GAAW,KAAK;IAMlC,KAAAC,SAAS,GAAG,CACV;MAAEC,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE,cAAc;MAAEC,SAAS,EAAE;IAAwB,CAAE,EACnF;MAAEF,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,SAAS,EAAE;IAAsB;IACtF;IAAA,CACD;;IAED,KAAAC,QAAQ,GAAW,cAAc;IAEjC,KAAAC,kBAAkB,GAAG,CACnB;MAAEJ,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAK,CAAE,EACrC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,CAC3C;IAEM,KAAAI,MAAM,GAAwB;MACnCC,SAAS,EAAE,IAAI,CAACC,UAAU;MAC1BC,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAAC5B,iBAAiB,CAAC6B,OAAO,CAAC,uBAAuB,CAAC;QAC/DC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAGlD,WAAW,CAACmD,MAAM,eAAe;MACzCC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAEM,KAAAC,MAAM,GAAU,CACrB;MACEC,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLpB,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAAC6B,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAACxC,iBAAiB,CAAC6B,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,EACD;MACEJ,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLpB,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAAC6B,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAACxC,iBAAiB,CAAC6B,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,CACF;IA0BC,IAAI,CAACvC,aAAa,CAACwC,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC/C,eAAe,CAACgD,IAAI,EAAE;IAC3B,IAAI,CAAC9C,oBAAoB,CAAC+C,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACC,OAAO,GAAGD,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;MAClC,IAAI,CAACC,eAAe,EAAE,CAAC,CAAC;MACxB,IAAI,IAAI,CAACC,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;MAEJ,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC;IACrC,CAAC,EACAC,KAAK,IAAI;MACR3E,IAAI,CAAC4E,IAAI,CAAC;QACRlC,KAAK,EAAE,OAAO;QACdmC,IAAI,EAAEF,KAAK,CAACG,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACjE,iBAAiB,CAAC6B,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAACjC,eAAe,CAACsE,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAACpE,YAAY,CAACqE,WAAW,EAAE,CAACtB,SAAS,CACtCuB,GAAG,IAAI;MACN,IAAI,CAACC,KAAK,GAAGD,GAAG,CAACtB,IAAI;MACrB;MACA,IAAI,IAAI,CAACK,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;IAEN,CAAC,EACAI,KAAK,IAAI;MACR3E,IAAI,CAAC4E,IAAI,CAAC;QACRlC,KAAK,EAAE,OAAO;QACdmC,IAAI,EAAEF,KAAK,CAACG,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACjE,iBAAiB,CAAC6B,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEA0C,cAAcA,CAACC,MAAM;IACnB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAAC1B,QAAQ,GAAGuB,MAAM;MACtB,IAAI,CAACrB,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAAC3C,YAAY,GAAG,IAAI;MACxB,IAAI,CAACoE,gBAAgB,EAAE;MACvBF,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACL,MAAM;IACjBM,OAAO,CAACC,GAAG,CAAC,iBAAiBP,MAAM,EAAE,CAAC;IACtC,IAAI,CAACjE,MAAM,GAAGiE,MAAM;IACpB,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAI,kBAAkBA,CAACR,MAAM;IACvBM,OAAO,CAACC,GAAG,CAAC,uBAAuBP,MAAM,EAAE,CAAC;IAC5C,IAAI,CAAChE,YAAY,GAAGgE,MAAM;IAC1B,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAK,mBAAmBA,CAACT,MAAM;IACxBM,OAAO,CAACC,GAAG,CAAC,wBAAwBP,MAAM,EAAE,CAAC;IAC7C,IAAI,CAACvD,WAAW,GAAGuD,MAAM;IACzB,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAEAM,eAAeA,CAACC,IAAa;IAE3B,IAAI,CAAC,IAAI,CAACrE,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClC+D,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAACjE,QAAQ,GAAGqE,IAAI;MACpB,IAAI,CAACnE,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZoE,IAAI,IACJA,IAAI,CAACC,KAAK,CAAC,IAAI,CAACtE,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGoE,IAAI;MAClB,IAAI,CAACnE,WAAW,GAAG,KAAK;MACxB,IAAI,CAACqE,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAACC,KAAK,EAAE;UACtD,IAAI,CAACD,eAAe,CAACC,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACLV,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAAChE,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAGqE,IAAI;MACpB,IAAI,CAACnE,WAAW,GAAG,IAAI;;EAE3B;EAEAyE,cAAcA,CAAA;IACZ,IAAI,CAACzE,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACuE,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACG,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGP,UAAU,CAAC,MAAK;MACzC,IAAI,CAACS,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACL,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACS,MAAM,IAC5B,CAAC,IAAI,CAACT,eAAe,CAACS,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAAChF,WAAW,EAAE;MACpB;;IAGF,MAAMiF,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACV,eAAe,CAACC,KAAK,EAAE;EAC9B;EAEAkB,cAAcA,CAAA;IACZ,IAAI,CAAC5F,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACqE,eAAe,EAAE;EACxB;EAEAsB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAACxG,SAAS,CAACyG,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAACzG,QAAQ,CAAC2G,OAAO,CAAChI,OAAO,CAACiI,IAAI,CAACH,MAAM,CAAC,CAAC,GACxD9H,OAAO,CAACiI,IAAI,CAACH,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEAM,SAASA,CAAC/B,IAAa;IACrB,OACE,IAAI,CAACrE,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAACF,WAAW,IAChBsE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACtE,QAAQ,CAAC,IACzBqE,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACtG,WAAW,CAAC;EAEjC;EAEAuG,QAAQA,CAACjC,IAAa;IACpB,OAAO,IAAI,CAACpE,MAAM,IAAIoE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACtE,QAAQ,CAAC,IAAIqE,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACpG,MAAM,CAAC;EAC7E;EAEAsG,OAAOA,CAAClC,IAAa;IACnB,OACEA,IAAI,CAACmC,MAAM,CAAC,IAAI,CAACxG,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAIoE,IAAI,CAACmC,MAAM,CAAC,IAAI,CAACvG,MAAM,CAAE,IACzC,IAAI,CAACqG,QAAQ,CAACjC,IAAI,CAAC,IACnB,IAAI,CAAC+B,SAAS,CAAC/B,IAAI,CAAC;EAExB;EAEAoC,iBAAiBA,CAAA;IACfzC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACtE,SAAS,CAAC;IACjD,IAAI,CAACmE,gBAAgB,EAAE;EACzB;EAEAS,eAAeA,CAAA;IACb,IAAI,IAAI,CAACvE,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAMyG,UAAU,GAAGzI,MAAM,CACvB,GAAG,IAAI,CAAC+B,QAAQ,CAAC2G,IAAI,IAAI,IAAI,CAAC3G,QAAQ,CAAC4G,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC9G,QAAQ,CAAC+G,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAME,QAAQ,GAAG/I,MAAM,CACrB,GAAG,IAAI,CAACgC,MAAM,CAAC0G,IAAI,IAAI,IAAI,CAAC1G,MAAM,CAAC2G,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC7G,MAAM,CAAC8G,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAACnH,SAAS,CAACC,UAAU,GAAG8G,UAAU,CAACO,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACtH,SAAS,CAACE,QAAQ,GAAGmH,QAAQ,CAACC,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAACnH,cAAc,GAAG,GAAG4G,UAAU,CAACO,MAAM,CACxC,cAAc,CACf,MAAMD,QAAQ,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACR,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAACzG,QAAQ,EAAE;MACxB,MAAM0G,UAAU,GAAGzI,MAAM,CACvB,GAAG,IAAI,CAAC+B,QAAQ,CAAC2G,IAAI,IAAI,IAAI,CAAC3G,QAAQ,CAAC4G,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC9G,QAAQ,CAAC+G,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAACnH,SAAS,CAACC,UAAU,GAAG8G,UAAU,CAACO,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACtH,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG4G,UAAU,CAACO,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAACtH,SAAS,CAACC,UAAU,GAAG,IAAI;MAChC,IAAI,CAACD,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAAC2G,iBAAiB,EAAE;;EAE5B;EAEApE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAAC1D,KAAK,CACPyI,GAAG,CACF,GAAGlJ,WAAW,CAACmD,MAAM,YAAY,IAAI,CAACgB,QAAQ,+BAA+B,CAC9E,CACAH,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAACkF,WAAW,GAAGlF,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAa,KAAK,IAAI;QACR3E,IAAI,CAAC4E,IAAI,CAAC;UACRlC,KAAK,EAAE,OAAO;UACdmC,IAAI,EAAEF,KAAK,CAACG,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACjE,iBAAiB,CAAC6B,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAqG,QAAQA,CAAA;IAAA,IAAAC,KAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAAC7I,MAAM,CAACqC,OAAO,CAAC,gBAAgB,CAAC;MAClDyG,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVjG,IAAI,EAAE,EAAE;QACRkG,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAACjJ,MAAM,CAACqC,OAAO,CAAC,MAAM,CAAC;UACjC6G,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACjJ,MAAM,CAACqC,OAAO,CAAC,aAAa,CAAC;UACxC6G,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACjJ,MAAM,CAACqC,OAAO,CAAC,gBAAgB,CAAC;UAC3C6G,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAAC/F,iBAAiB,EAAE;IACxB,IAAI,CAACwB,SAAS,EAAE;IAChB,IAAI,CAACR,SAAS,GAAG;MACfgF,GAAG,EAAE,IAAI,CAACrJ,eAAe,CAACsJ,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;QACR,GAAG,IAAI,CAAC1J,eAAe,CAACsJ,iBAAiB,CAACK,IAAI;QAC9C;QACA;QACA;QACA;QAEA;QACAF,UAAU,EAAE;OACb;MACDxF,IAAI,EAAEA,CAAC2F,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAI3H,MAAM,GAAG,IAAI4H,eAAe,EAAE;QAElC,IAAI,IAAI,CAAC7I,MAAM,IAAI8I,SAAS,IAAI,IAAI,CAAC9I,MAAM,KAAK,IAAI,EAAE;UACpDiB,MAAM,CAAC8H,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC/I,MAAM,CAAC;;QAGvC,IAAI,IAAI,CAACC,YAAY,IAAI6I,SAAS,IAAI,IAAI,CAAC7I,YAAY,KAAK,IAAI,EAAE;UAChEgB,MAAM,CAAC8H,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC9I,YAAY,CAAC;;QAGnD,IAAI,IAAI,CAACC,SAAS,CAACC,UAAU,EAAE;UAC7Bc,MAAM,CAAC8H,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC7I,SAAS,CAACC,UAAU,CAAC;;QAGxD,IAAI,IAAI,CAACD,SAAS,CAACE,QAAQ,EAAE;UAC3Ba,MAAM,CAAC8H,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC7I,SAAS,CAACE,QAAQ,CAAC;;QAGpD,IAAI,IAAI,CAACM,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,KAAK,EAAE;UAClDO,MAAM,CAAC8H,MAAM,CAAC,cAAc,EAAE,IAAI,CAACrI,WAAW,CAAC;;QAGjD,MAAMsI,WAAW,GAAG/H,MAAM,CAACmG,QAAQ,EAAE;QACrC,MAAM3F,GAAG,GAAG,GAAGlD,WAAW,CAACmD,MAAM,YAAY,IAAI,CAACgB,QAAQ,WACxDsG,WAAW,GAAG,GAAG,GAAGA,WAAW,GAAG,EACpC,EAAE;QAEF,IAAI,CAACC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACjK,KAAK,CAACkK,IAAI,CAAMzH,GAAG,EAAEkH,oBAAoB,CAAC,CAACpG,SAAS,CACtD4G,IAAS,IAAI;UACZ,IAAI,CAACF,cAAc,GAAG,KAAK;UAC3BL,QAAQ,CAAC;YACPQ,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrC7G,IAAI,EAAE2G,IAAI,CAAC3G;WACZ,CAAC;QACJ,CAAC,EACAa,KAAK,IAAI;UACR,IAAI,CAAC4F,cAAc,GAAG,KAAK;UAC3B1E,OAAO,CAAClB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDuF,QAAQ,CAAC;YACPQ,YAAY,EAAE,CAAC;YACfC,eAAe,EAAE,CAAC;YAClB7G,IAAI,EAAE;WACP,CAAC;QACJ,CAAC,CACF;MACH,CAAC;MACD8G,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CAAC;MACpDC,OAAO,EAAE,CACP;QACEvI,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,KAAK,CAAC;QAC5CkB,IAAI,EAAE,IAAI;QACVoH,SAAS,EAAE,oBAAoB;QAC/B7H,IAAI,EAAE,YAAY;QAClB8H,MAAM,EAAE,SAAAA,CAAUrH,IAAI,EAAET,IAAI,EAAE+H,GAAG,EAAEC,QAAQ;UAEzC,OAAOA,QAAQ,CAACD,GAAG,GAAG,CAAC;QACzB;OACD,EACD;QACE1I,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,YAAY,CAAC;QACnDkB,IAAI,EAAE,MAAM;QACZoH,SAAS,EAAE,oBAAoB;QAC/B7H,IAAI,EAAE,YAAY;QAClB8H,MAAM,EAAE;UACNG,OAAO,EAAEA,CAACxH,IAAI,EAAET,IAAI,EAAE+H,GAAG,KAAI;YAC3B,OAAOtH,IAAI,IAAI,KAAK;UACtB,CAAC;UACDyH,MAAM,EAAEA,CAACzH,IAAI,EAAET,IAAI,EAAE+H,GAAG,KAAI;YAC1B,OAAOtH,IAAI;UACb;;OAEH,EACD;QACEpB,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,OAAO,CAAC;QAC9CkB,IAAI,EAAE,YAAY;QAClBqH,MAAM,EAAE,SAAAA,CAAUrH,IAAI;UACpB,MAAM0H,WAAW,GAAG1H,IAAI,IAAI,KAAK;UACjC,OAAO,uDAAuD0H,WAAW,QAAQ;QACnF;OACD,EACD;QACE9I,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,MAAM,CAAC;QAC7CkB,IAAI,EAAE,YAAY;QAClBoH,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAUrH,IAAI,EAAET,IAAI,EAAE+H,GAAG;UAC/B,MAAMK,WAAW,GACf,CAAC3H,IAAI,IAAIA,IAAI,KAAK,KAAK,GACnB,KAAK,GACLhE,MAAM,CAACgE,IAAI,CAAC,CAACgF,MAAM,CAAC,YAAY,CAAC;UACvC,OAAO,4DAA4D2C,WAAW,QAAQ;QACxF;OACD,EACD;QACE/I,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,YAAY,CAAC;QACnDkB,IAAI,EAAE,YAAY;QAClBoH,SAAS,EAAE,aAAa;QAExBC,MAAM,EAAE,SAAAA,CAASrH,IAAI,EAAET,IAAI,EAAE+H,GAAG;UAC9B,IAAI,CAACtH,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOhE,MAAM,CAACgE,IAAI,CAAC,CAACgF,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACEpG,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,UAAU,CAAC;QACjDkB,IAAI,EAAE,UAAU;QAChBoH,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASrH,IAAI,EAAET,IAAI,EAAE+H,GAAG;UAC9B,IAAI,CAACtH,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOhE,MAAM,CAACgE,IAAI,CAAC,CAACgF,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACEpG,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,UAAU,CAAC;QACjDkB,IAAI,EAAE,UAAU;QAChBqH,MAAM,EAAE,SAAAA,CAASrH,IAAI,EAAET,IAAI,EAAE+H,GAAG;UAC9B,IAAI,CAACtH,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOA,IAAI;QACb;OACD,EACD;QACEpB,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,WAAW,CAAC;QAClDkB,IAAI,EAAE,gBAAgB;QACtBoH,SAAS,EAAE;OACZ,EACD;QACExI,KAAK,EAAE,IAAI;QACXoB,IAAI,EAAE,IAAI;QACVoH,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASrH,IAAI,EAAET,IAAI,EAAE+H,GAAG;UAC9B,OAAO,IAAI;QACb;OACD,EACD;QACE1I,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,WAAW,CAAC;QAClDkB,IAAI,EAAE,gBAAgB;QACtBoH,SAAS,EAAE;OACZ,EACD;QACExI,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,YAAY,CAAC;QACnDkB,IAAI,EAAE,YAAY;QAClBoH,SAAS,EAAE;OACZ,EACD;QACEpH,IAAI,EAAE,IAAI;QACVoH,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASrH,IAAI,EAAET,IAAI,EAAE+H,GAAG;UAC9B,OAAO,GAAG;QACZ;OACD,EACD;QACE1I,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAAC6B,OAAO,CAAC,YAAY,CAAC;QACnDkB,IAAI,EAAE,YAAY;QAClBoH,SAAS,EAAE;OACZ,CACF;MACDQ,OAAO,EAAE;QACPhC,GAAG,EAAE,IAAI,CAACrJ,eAAe,CAACsJ,iBAAiB,CAAC+B,OAAO,CAAChC,GAAG;QACvDgC,OAAO,EAAE,CACP;UACE7G,IAAI,EAAE,6CAA6C,IAAI,CAAC9D,iBAAiB,CAAC6B,OAAO,CAC/E,YAAY,CACb,EAAE;UACH+I,MAAM,EAAE,KAAK;UACbzI,MAAM;YAAA,IAAA0I,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAMnI,IAAI,GAAGiI,EAAE,CAACL,OAAO,CAACQ,UAAU,EAAE;cAEpC;cACA,IAAIC,QAAQ,GAAG,gBAAgB;cAE/B,IAAIjD,KAAI,CAAClF,QAAQ,EAAE;gBACjB,MAAMoI,MAAM,GAAGlD,KAAI,CAACnF,OAAO,EAAEsI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrI,EAAE,IAAIiF,KAAI,CAAClF,QAAQ,CAAC;gBAC7D,IAAIoI,MAAM,EAAE;kBACVD,QAAQ,IAAI,IAAIC,MAAM,CAAC5C,IAAI,CAAC+C,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAItD,IAAIrD,KAAI,CAAC3H,YAAY,EAAE;gBACrB,MAAMiL,UAAU,GAAGtD,KAAI,CAACF,WAAW,EAAEqD,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACxI,EAAE,IAAIiF,KAAI,CAAC3H,YAAY,CAAC;gBACzE,IAAIiL,UAAU,EAAE;kBACdL,QAAQ,IAAI,IAAIK,UAAU,CAAChD,IAAI,CAAC+C,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAI1D,IAAIrD,KAAI,CAAC5H,MAAM,EAAE;gBACf,MAAMoL,IAAI,GAAGxD,KAAI,CAAC7D,KAAK,EAAEgH,IAAI,CAACM,CAAC,IAAIA,CAAC,CAAC1I,EAAE,IAAIiF,KAAI,CAAC5H,MAAM,CAAC;gBACvD,IAAIoL,IAAI,EAAE;kBACRP,QAAQ,IAAI,IAAIO,IAAI,CAACE,IAAI,CAACL,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAIpD,IAAIrD,KAAI,CAAClH,WAAW,IAAIkH,KAAI,CAAClH,WAAW,KAAK,KAAK,EAAE;gBAClDmK,QAAQ,IAAI,IAAIjD,KAAI,CAAClH,WAAW,EAAE;;cAGpC,IAAIkH,KAAI,CAAC1H,SAAS,CAACC,UAAU,IAAIyH,KAAI,CAAC1H,SAAS,CAACE,QAAQ,EAAE;gBACxDyK,QAAQ,IAAI,IAAIjD,KAAI,CAAC1H,SAAS,CAACC,UAAU,OAAOyH,KAAI,CAAC1H,SAAS,CAACE,QAAQ,EAAE;eAC1E,MAAM,IAAIwH,KAAI,CAAC1H,SAAS,CAACC,UAAU,EAAE;gBACpC0K,QAAQ,IAAI,SAASjD,KAAI,CAAC1H,SAAS,CAACC,UAAU,EAAE;;cAGlD0K,QAAQ,IAAI,MAAM;cAElB,MAAMjD,KAAI,CAAChI,cAAc,CAAC2L,SAAS,CAAC/I,IAAI,EAAEqI,QAAQ,CAAC;YACrD,CAAC;YAAA,gBAAAjJ,OAAA4J,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAC,QAAQA,CAACtJ,IAAI;IACX,IAAI,CAACuB,KAAK,GAAGvB,IAAI;IACjB;IACA,MAAMuJ,SAAS,GAAG,IAAI,CAAClK,MAAM,CAACkJ,IAAI,CAAEiB,KAAK,IAAKA,KAAK,CAAClK,GAAG,KAAK,SAAS,CAAC;IACtE;IACA,IAAImK,aAAa,GAAG,EAAE;IACtBzJ,IAAI,CAAC0J,OAAO,CAAEd,IAAI,IAAI;MACpB,IAAIe,SAAS,GAAG,IAAI,CAAC1M,iBAAiB,CAAC6B,OAAO,CAAC8J,IAAI,CAAClD,IAAI,CAAC;MACzD+D,aAAa,CAACG,IAAI,CAAC;QACjBxL,KAAK,EAAEuL,SAAS;QAChBtL,KAAK,EAAEuK,IAAI,CAACzI;OACb,CAAC;IACJ,CAAC,CAAC;IACFoJ,SAAS,CAAC/J,KAAK,CAACqK,OAAO,GAAGJ,aAAa;EACzC;EAEAK,cAAcA,CAACjH,KAAU;IACvB;EAAA;EAGFkH,MAAMA,CAAC3K,MAAM,EAAEkI,GAAI;IACjB,IAAI,CAACjI,MAAM,CAAC,CAAC,CAAC,CAAC2K,YAAY,GAAG,IAAI,CAACxM,MAAM;IACzC,QAAQ4B,MAAM;MACZ,KAAK,QAAQ;QACX,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACG,KAAK,CAACyK,QAAQ,GAAG,KAAK;QACrC;MACF,KAAK,MAAM;QACT,IAAI,CAAC5K,MAAM,CAAC,CAAC,CAAC,CAACG,KAAK,CAACyK,QAAQ,GAAG,IAAI;QACpC;MACF,KAAK,QAAQ;QACX;MACF;QACE;;IAEJ,IAAI,CAACxL,MAAM,CAACW,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACX,MAAM,CAAC6I,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAACpK,mBAAmB,CAACgN,kBAAkB,CAAC,IAAI,CAACvL,UAAU,CAAC,CAACwL,UAAU,EAAE;EAC3E;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC3N,QAAQ,CAAC4N,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGzH,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACK,MAAM,CAACqH,YAAY,CAAC,eAAe,CAAC,EAAE;QAC9C,IAAIC,aAAa,GAAG3H,KAAK,CAACK,MAAM,CAACK,YAAY,CAAC,eAAe,CAAC;QAC9D,IAAIkH,QAAQ,GAAG5H,KAAK,CAACK,MAAM,CAACK,YAAY,CAAC,UAAU,CAAC;QACpD;QACA,IAAI,CAACjH,OAAO,CAACoO,QAAQ,CAAC,CAACF,aAAa,EAAE,QAAQ,EAAEC,QAAQ,CAAC,EAAE;UACzDE,UAAU,EAAE,IAAI,CAACtO;SAClB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAuO,gBAAgBA,CAAC/H,KAAU;IACzB,IAAI,CAACtE,QAAQ,GAAGsE,KAAK;IACrB,IAAI,IAAI,CAACtE,QAAQ,KAAK,cAAc,IAAI,IAAI,CAAC8B,SAAS,CAACC,UAAU,EAAE;MACjE,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEQoB,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACxB,SAAS,CAACC,UAAU,EAAE;MAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEAoK,WAAWA,CAAA;IACT,IAAI,CAACnK,SAAS,CAACoK,WAAW,EAAE;IAC5B,IAAI,CAACT,UAAU,EAAE;IACjB,IAAI,IAAI,CAACvH,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;CACD;AA9rBkDiI,UAAA,EAAhDlP,SAAS,CAAC,iBAAiB,EAAE;EAAEmP,MAAM,EAAE;AAAK,CAAE,CAAC,+DAAsB;AAiNtED,UAAA,EADCjP,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,6DAS1C;AA3NUK,sBAAsB,GAAA4O,UAAA,EALlCnP,SAAS,CAAC;EACTqP,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,GACWhP,sBAAsB,CAgsBlC;SAhsBYA,sBAAsB", "names": ["Component", "ViewChild", "HostListener", "environment", "moment", "NgbDate", "<PERSON><PERSON>", "LeagueReportsComponent", "constructor", "route", "_router", "_commonsService", "_http", "_trans", "renderer", "_teamService", "_modalService", "_loadingService", "_toastService", "_registrationService", "_clubService", "_translateService", "_coreSidebarService", "_titleService", "_exportService", "_tournamentService", "calendar", "formatter", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "start_date", "end_date", "dateRangeValue", "hoveredDate", "fromDate", "toDate", "isSelecting", "matchStatus", "viewTypes", "label", "value", "icon_name", "viewType", "matchStatusOptions", "params", "editor_id", "table_name", "title", "create", "instant", "edit", "remove", "url", "apiUrl", "method", "action", "fields", "key", "type", "props", "placeholder", "required", "setTitle", "_getCurrentSeason", "show", "getAllSeasonActive", "subscribe", "data", "seasons", "seasonId", "id", "_getTournaments", "dtElement", "dtInstance", "then", "ajax", "reload", "dtTrigger", "next", "dtOptions", "error", "fire", "text", "message", "icon", "confirmButtonText", "dismiss", "_getClubs", "getAllClubs", "res", "clubs", "onSelectSeason", "$event", "Promise", "resolve", "reject", "refreshDataTable", "onSelectClub", "console", "log", "onSelectTournament", "onSelectMatchStatus", "onDateSelection", "date", "after", "updateDateRange", "setTimeout", "dateRangePicker", "close", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "clearDateRange", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "from", "isHovered", "before", "isInside", "isRange", "equals", "onDateRangeChange", "fromMoment", "year", "month", "toString", "padStart", "day", "toMoment", "format", "get", "tournaments", "ngOnInit", "_this", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "dom", "dataTableDefaults", "select", "rowId", "processing", "language", "lang", "dataTablesParameters", "callback", "URLSearchParams", "undefined", "append", "queryString", "isTableLoading", "post", "resp", "recordsTotal", "recordsFiltered", "responsive", "scrollX", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "row", "metadata", "display", "filter", "displayData", "displayDate", "buttons", "extend", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "filename", "season", "find", "s", "replace", "tournament", "t", "club", "c", "code", "exportCsv", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "setClubs", "clubField", "field", "current_clubs", "for<PERSON>ach", "club_name", "push", "options", "onCaptureEvent", "editor", "defaultValue", "disabled", "getSidebarRegistry", "toggle<PERSON><PERSON>", "ngAfterViewInit", "unlistener", "listen", "hasAttribute", "tournament_id", "stage_id", "navigate", "relativeTo", "onSelectViewType", "ngOnDestroy", "unsubscribe", "__decorate", "static", "selector", "templateUrl", "styleUrls"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  HostListener,\r\n  OnDestroy\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit, OnDestroy\r\n{\r\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\r\n\r\n  public seasonId: any;\r\n  public clubId: any = null;\r\n  public tournamentId: any = null;\r\n  public dateRange: any = { start_date: null, end_date: null };\r\n  public dateRangeValue: string = '';\r\n  hoveredDate: NgbDate | null = null;\r\n  fromDate: NgbDate | null = null;\r\n  toDate: NgbDate | null = null;\r\n  isSelecting: boolean = false; // Track if we're in the middle of selecting range\r\n  private clickOutsideTimeout: any;\r\n  public matchStatus: string = 'all';\r\n  public contentHeader: object;\r\n  public seasons;\r\n  public clubs;\r\n  public tournaments;\r\n\r\n  viewTypes = [\r\n    { label: 'Table View', value: 'league_table', icon_name: 'fa-light fa-table-list' },\r\n    { label: 'Schedule View', value: 'schedule_matches', icon_name: 'fa-light fa-calendar' },\r\n    // { label: 'Bracket View', value: 'bracket', icon_name: 'grid' },\r\n  ];\r\n\r\n  viewType: string = 'league_table';\r\n\r\n  matchStatusOptions = [\r\n    { label: 'All Status', value: 'all' },\r\n    { label: 'Upcoming', value: 'upcoming' },\r\n    { label: 'Active', value: 'active' },\r\n    { label: 'Completed', value: 'completed' },\r\n    { label: 'Cancelled', value: 'cancelled' },\r\n  ];\r\n\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: this._translateService.instant('Create New Tournament'),\r\n      edit: 'Edit team',\r\n      remove: 'Delete team'\r\n    },\r\n    url: `${environment.apiUrl}/teams/editor`,\r\n    method: 'POST',\r\n    action: 'create'\r\n  };\r\n\r\n  public fields: any[] = [\r\n    {\r\n      key: 'home_score',\r\n      type: 'number',\r\n      props: {\r\n        label: this._translateService.instant('Home team score'),\r\n        placeholder: this._translateService.instant('Enter score of team'),\r\n        required: true,\r\n      }\r\n    },\r\n    {\r\n      key: 'away_score',\r\n      type: 'number',\r\n      props: {\r\n        label: this._translateService.instant('Away team score'),\r\n        placeholder: this._translateService.instant('Enter score of team'),\r\n        required: true,\r\n      }\r\n    }\r\n  ];\r\n\r\n  // private variables\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _teamService: TeamService,\r\n    public _modalService: NgbModal,\r\n    public _loadingService: LoadingService,\r\n    public _toastService: ToastrService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _titleService: Title,\r\n    private _exportService: ExportService,\r\n    public _tournamentService: TournamentService,\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n        this.dtTrigger.next(this.dtOptions);\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n        // this.clubId = this.clubs[0];\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectTournament($event) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      rowId: 'id',\r\n      processing: true,\r\n      language: {\r\n        ...this._commonsService.dataTableDefaults.lang,\r\n        // processing: `<div class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\r\n        //               <div class=\"spinner-border text-primary\" role=\"status\">\r\n        //                 <span class=\"sr-only\">Loading...</span>\r\n        //               </div>\r\n                      \r\n        //             </div>`,\r\n        processing: ``,\r\n      },\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        // Build query parameters for all filters\r\n        let params = new URLSearchParams();\r\n\r\n        if (this.clubId != undefined && this.clubId !== null) {\r\n          params.append('club_id', this.clubId);\r\n        }\r\n\r\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\r\n          params.append('tournament_id', this.tournamentId);\r\n        }\r\n\r\n        if (this.dateRange.start_date) {\r\n          params.append('start_date', this.dateRange.start_date);\r\n        }\r\n\r\n        if (this.dateRange.end_date) {\r\n          params.append('end_date', this.dateRange.end_date);\r\n        }\r\n\r\n        if (this.matchStatus && this.matchStatus !== 'all') {\r\n          params.append('match_status', this.matchStatus);\r\n        }\r\n\r\n        const queryString = params.toString();\r\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${\r\n          queryString ? '?' + queryString : ''\r\n        }`;\r\n\r\n        this.isTableLoading = true;\r\n        this._http.post<any>(url, dataTablesParameters).subscribe(\r\n          (resp: any) => {\r\n            this.isTableLoading = false;\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          },\r\n          (error) => {\r\n            this.isTableLoading = false;\r\n            console.error('Error loading table data:', error);\r\n            callback({\r\n              recordsTotal: 0,\r\n              recordsFiltered: 0,\r\n              data: [],\r\n            });\r\n          }\r\n        );\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      columnDefs: [{ responsivePriority: 1, targets: -1 }],\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('No.'),\r\n          data: null,\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: function (data, type, row, metadata) {\r\n\r\n            return metadata.row + 1\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Tournament'),\r\n          data: 'name',\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: {\r\n            display: (data, type, row) => {\r\n              return data ?? 'TBD';\r\n            },\r\n            filter: (data, type, row) => {\r\n              return data;\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          render: function (data) {\r\n            const displayData = data || 'TBD';\r\n            return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Date'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n          render: function (data, type, row) {\r\n            const displayDate =\r\n              !data || data === 'TBD'\r\n                ? 'TBD'\r\n                : moment(data).format('YYYY-MM-DD');\r\n            return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Start time'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('End time'),\r\n          data: 'end_time',\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Location'),\r\n          data: 'location',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home Team'),\r\n          data: 'home_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: 'VS',\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return 'vs';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away Team'),\r\n          data: 'away_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home score'),\r\n          data: 'home_score',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return '-';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away score'),\r\n          data: 'away_score',\r\n          className: 'text-center'\r\n        }\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant(\r\n              'Export CSV'\r\n            )}`,\r\n            extend: 'csv',\r\n            action: async (e: any, dt: any, button: any, config: any) => {\r\n              const data = dt.buttons.exportData();\r\n              \r\n              // Generate filename with current filter information\r\n              let filename = 'Matches_Report';\r\n              \r\n              if (this.seasonId) {\r\n                const season = this.seasons?.find(s => s.id == this.seasonId);\r\n                if (season) {\r\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.tournamentId) {\r\n                const tournament = this.tournaments?.find(t => t.id == this.tournamentId);\r\n                if (tournament) {\r\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.clubId) {\r\n                const club = this.clubs?.find(c => c.id == this.clubId);\r\n                if (club) {\r\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.matchStatus && this.matchStatus !== 'all') {\r\n                filename += `_${this.matchStatus}`;\r\n              }\r\n              \r\n              if (this.dateRange.start_date && this.dateRange.end_date) {\r\n                filename += `_${this.dateRange.start_date}_to_${this.dateRange.end_date}`;\r\n              } else if (this.dateRange.start_date) {\r\n                filename += `_from_${this.dateRange.start_date}`;\r\n              }\r\n              \r\n              filename += '.csv';\r\n              \r\n              await this._exportService.exportCsv(data, filename);\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  setClubs(data) {\r\n    this.clubs = data;\r\n    // get field has key club_id\r\n    const clubField = this.fields.find((field) => field.key === 'club_id');\r\n    // set options for club field\r\n    let current_clubs = [];\r\n    data.forEach((club) => {\r\n      let club_name = this._translateService.instant(club.name);\r\n      current_clubs.push({\r\n        label: club_name,\r\n        value: club.id\r\n      });\r\n    });\r\n    clubField.props.options = current_clubs;\r\n  }\r\n\r\n  onCaptureEvent(event: any) {\r\n    // console.log(event);\r\n  }\r\n\r\n  editor(action, row?) {\r\n    this.fields[0].defaultValue = this.clubId;\r\n    switch (action) {\r\n      case 'create':\r\n        this.fields[2].props.disabled = false;\r\n        break;\r\n      case 'edit':\r\n        this.fields[2].props.disabled = true;\r\n        break;\r\n      case 'remove':\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('tournament_id')) {\r\n        let tournament_id = event.target.getAttribute('tournament_id');\r\n        let stage_id = event.target.getAttribute('stage_id');\r\n        //  navigate to path ':tournament_id/stages/:stage_id'\r\n        this._router.navigate([tournament_id, 'stages', stage_id], {\r\n          relativeTo: this.route\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  onSelectViewType(event: any) {\r\n    this.viewType = event;\r\n    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  private refreshDataTable() {\r\n    if (this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n    this.unlistener();\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}