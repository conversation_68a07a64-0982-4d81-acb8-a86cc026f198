{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport Swal from 'sweetalert2';\nimport moment from 'moment';\nimport { NgbDate } from '@ng-bootstrap/ng-bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/services/commons.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"app/services/team.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"app/services/loading.service\";\nimport * as i8 from \"ngx-toastr\";\nimport * as i9 from \"app/services/registration.service\";\nimport * as i10 from \"app/services/club.service\";\nimport * as i11 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i12 from \"@angular/platform-browser\";\nimport * as i13 from \"app/services/export.service\";\nimport * as i14 from \"app/services/tournament.service\";\nimport * as i15 from \"@angular/common\";\nimport * as i16 from \"@angular/flex-layout/extended\";\nimport * as i17 from \"@angular/forms\";\nimport * as i18 from \"app/layout/components/content-header/content-header.component\";\nimport * as i19 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i20 from \"angular-datatables\";\nimport * as i21 from \"@ng-select/ng-select\";\nimport * as i22 from \"../../../components/btn-dropdown-action/btn-dropdown-action.component\";\nimport * as i23 from \"../../../components/editor-sidebar/editor-sidebar.component\";\nconst _c0 = [\"rowActionBtn\"];\nconst _c1 = [\"dateRangePicker\"];\nfunction LeagueReportsComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_button_8_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const type_r6 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSelectViewType(type_r6.value));\n    });\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r0.viewType === type_r6.value);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"nav-\", type_r6.value, \"-tab\")(\"aria-controls\", \"nav-\", type_r6.value, \"\");\n    i0.ɵɵattribute(\"aria-selected\", ctx_r0.viewType === type_r6.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", type_r6.icon_name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 7, type_r6.label), \" \");\n  }\n}\nfunction LeagueReportsComponent_div_10_ng_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r19.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, season_r19.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_div_10_ng_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tournament_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", tournament_r20.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, tournament_r20.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_div_10_ng_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const club_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", club_r21.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, club_r21.code), \" \");\n  }\n}\nfunction LeagueReportsComponent_div_10_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵlistener(\"mouseenter\", function LeagueReportsComponent_div_10_ng_template_35_Template_span_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r25);\n      const date_r22 = restoredCtx.date;\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.hoveredDate = date_r22);\n    })(\"mouseleave\", function LeagueReportsComponent_div_10_ng_template_35_Template_span_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.hoveredDate = null);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r22 = ctx.date;\n    const focused_r23 = ctx.focused;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"focused\", focused_r23)(\"range\", ctx_r15.isRange(date_r22))(\"faded\", ctx_r15.isHovered(date_r22) || ctx_r15.isInside(date_r22));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", date_r22.day, \" \");\n  }\n}\nfunction LeagueReportsComponent_div_10_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelement(0, \"hr\", 37);\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_div_10_ng_template_37_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r28);\n      i0.ɵɵnextContext();\n      const _r13 = i0.ɵɵreference(30);\n      const ctx_r27 = i0.ɵɵnextContext();\n      ctx_r27.clearDateRange();\n      return i0.ɵɵresetView(_r13.close());\n    });\n    i0.ɵɵtext(3, \" Clear \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_div_10_ng_template_37_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r28);\n      i0.ɵɵnextContext();\n      const _r13 = i0.ɵɵreference(30);\n      return i0.ɵɵresetView(_r13.close());\n    });\n    i0.ɵɵtext(5, \" Close \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LeagueReportsComponent_div_10_ng_option_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r30 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r30.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, status_r30.label), \" \");\n  }\n}\nfunction LeagueReportsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 4)(3, \"label\", 18);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ng-select\", 19);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_div_10_Template_ng_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.seasonId = $event);\n    })(\"change\", function LeagueReportsComponent_div_10_Template_ng_select_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.onSelectSeason($event));\n    });\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵtemplate(8, LeagueReportsComponent_div_10_ng_option_8_Template, 3, 4, \"ng-option\", 20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 17)(10, \"div\", 21)(11, \"label\", 22);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ng-select\", 19);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_div_10_Template_ng_select_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.tournamentId = $event);\n    })(\"change\", function LeagueReportsComponent_div_10_Template_ng_select_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.onSelectTournament($event));\n    });\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵtemplate(16, LeagueReportsComponent_div_10_ng_option_16_Template, 3, 4, \"ng-option\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 21)(18, \"label\", 23);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"ng-select\", 19);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_div_10_Template_ng_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.clubId = $event);\n    })(\"change\", function LeagueReportsComponent_div_10_Template_ng_select_change_21_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.onSelectClub($event));\n    });\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵtemplate(23, LeagueReportsComponent_div_10_ng_option_23_Template, 3, 4, \"ng-option\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 21)(25, \"label\", 24);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 25)(29, \"input\", 26, 27);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_div_10_Template_input_click_29_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.openDatePicker());\n    })(\"dateSelect\", function LeagueReportsComponent_div_10_Template_input_dateSelect_29_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.onDateSelection($event));\n    });\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 28)(33, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function LeagueReportsComponent_div_10_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.openDatePicker());\n    });\n    i0.ɵɵelement(34, \"i\", 30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(35, LeagueReportsComponent_div_10_ng_template_35_Template, 2, 7, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(37, LeagueReportsComponent_div_10_ng_template_37_Template, 6, 0, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 21)(40, \"label\", 33);\n    i0.ɵɵtext(41);\n    i0.ɵɵpipe(42, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"ng-select\", 19);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_div_10_Template_ng_select_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.matchStatus = $event);\n    })(\"change\", function LeagueReportsComponent_div_10_Template_ng_select_change_43_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.onSelectMatchStatus($event));\n    });\n    i0.ɵɵpipe(44, \"translate\");\n    i0.ɵɵtemplate(45, LeagueReportsComponent_div_10_ng_option_45_Template, 3, 4, \"ng-option\", 20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 17)(47, \"div\", 4);\n    i0.ɵɵelement(48, \"table\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r14 = i0.ɵɵreference(36);\n    const _r16 = i0.ɵɵreference(38);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 34, \"Season\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(7, 36, \"Select Season\"));\n    i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx_r1.seasonId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.seasons);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 38, \"Tournament\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(15, 40, \"Select Tournament\"));\n    i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx_r1.tournamentId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.tournaments);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 42, \"Club\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(22, 44, \"Select Club\"));\n    i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx_r1.clubId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.clubs);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 46, \"Date Range\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(31, 48, \"Select Date Range\"));\n    i0.ɵɵproperty(\"value\", ctx_r1.formatter.format(ctx_r1.fromDate) + (ctx_r1.toDate ? \" - \" + ctx_r1.formatter.format(ctx_r1.toDate) : \"\"))(\"dayTemplate\", _r14)(\"footerTemplate\", _r16)(\"firstDayOfWeek\", 1)(\"displayMonths\", 2)(\"autoClose\", false);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(42, 50, \"Match Status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(44, 52, \"Select Status\"));\n    i0.ɵɵproperty(\"searchable\", false)(\"clearable\", false)(\"ngModel\", ctx_r1.matchStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.matchStatusOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dtOptions\", ctx_r1.dtOptions)(\"dtTrigger\", ctx_r1.dtTrigger);\n  }\n}\nfunction LeagueReportsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"Schedule Matches Content\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, \"Coming soon...\"));\n  }\n}\nfunction LeagueReportsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"Bracket Content\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, \"Coming soon...\"));\n  }\n}\nfunction LeagueReportsComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-btn-dropdown-action\", 43);\n    i0.ɵɵlistener(\"emitter\", function LeagueReportsComponent_ng_template_15_Template_app_btn_dropdown_action_emitter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r46);\n      const emitter_r44 = restoredCtx.captureEvents;\n      return i0.ɵɵresetView(emitter_r44($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r43 = ctx.adtData;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"actions\", ctx_r5.rowActions)(\"data\", data_r43);\n  }\n}\nexport class LeagueReportsComponent {\n  constructor(route, _router, _commonsService, _http, _trans, renderer, _teamService, _modalService, _loadingService, _toastService, _registrationService, _clubService, _translateService, _coreSidebarService, _titleService, _exportService, _tournamentService, calendar, formatter) {\n    this.route = route;\n    this._router = _router;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._teamService = _teamService;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._toastService = _toastService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this._titleService = _titleService;\n    this._exportService = _exportService;\n    this._tournamentService = _tournamentService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.dtElement = DataTableDirective;\n    this.dtTrigger = new Subject();\n    this.dtOptions = {};\n    this.currentTeam = {};\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false; // Track if we're in the middle of selecting range\n    this.matchStatus = 'all';\n    this.isTableLoading = false;\n    this.table_name = 'team-table';\n    this.viewTypes = [{\n      label: 'Table View',\n      value: 'league_table',\n      icon_name: 'fa-light fa-table-list'\n    }, {\n      label: 'Schedule View',\n      value: 'schedule_matches',\n      icon_name: 'fa-light fa-calendar'\n    }\n    // { label: 'Bracket View', value: 'bracket', icon_name: 'grid' },\n    ];\n\n    this.viewType = 'league_table';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Create New Tournament'),\n        edit: 'Edit team',\n        remove: 'Delete team'\n      },\n      url: `${environment.apiUrl}/teams/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'home_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Home team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }, {\n      key: 'away_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Away team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }];\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n      this.dtTrigger.next(this.dtOptions);\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n      // this.clubId = this.clubs[0];\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n    this.refreshDataTable();\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    var _this = this;\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      processing: true,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        // processing: `<div class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\n        //               <div class=\"spinner-border text-primary\" role=\"status\">\n        //                 <span class=\"sr-only\">Loading...</span>\n        //               </div>\n        //             </div>`,\n        processing: ``\n      },\n      ajax: (dataTablesParameters, callback) => {\n        // Build query parameters for all filters\n        let params = new URLSearchParams();\n        if (this.clubId != undefined && this.clubId !== null) {\n          params.append('club_id', this.clubId);\n        }\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\n          params.append('tournament_id', this.tournamentId);\n        }\n        if (this.dateRange.start_date) {\n          params.append('start_date', this.dateRange.start_date);\n        }\n        if (this.dateRange.end_date) {\n          params.append('end_date', this.dateRange.end_date);\n        }\n        if (this.matchStatus && this.matchStatus !== 'all') {\n          params.append('match_status', this.matchStatus);\n        }\n        const queryString = params.toString();\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${queryString ? '?' + queryString : ''}`;\n        this.isTableLoading = true;\n        this._http.post(url, dataTablesParameters).subscribe(resp => {\n          this.isTableLoading = false;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        }, error => {\n          this.isTableLoading = false;\n          console.error('Error loading table data:', error);\n          callback({\n            recordsTotal: 0,\n            recordsFiltered: 0,\n            data: []\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }],\n      columns: [{\n        title: this._translateService.instant('No.'),\n        data: null,\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: function (data, type, row, metadata) {\n          return metadata.row + 1;\n        }\n      }, {\n        title: this._translateService.instant('Tournament'),\n        data: 'name',\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: {\n          display: (data, type, row) => {\n            return data ?? 'TBD';\n          },\n          filter: (data, type, row) => {\n            return data;\n          }\n        }\n      }, {\n        title: this._translateService.instant('Round'),\n        data: 'round_name',\n        render: function (data) {\n          const displayData = data || 'TBD';\n          return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          const displayDate = !data || data === 'TBD' ? 'TBD' : moment(data).format('YYYY-MM-DD');\n          return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Home Team'),\n        data: 'home_team_name',\n        className: 'text-center'\n      }, {\n        title: 'VS',\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return 'vs';\n        }\n      }, {\n        title: this._translateService.instant('Away Team'),\n        data: 'away_team_name',\n        className: 'text-center'\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center'\n      }, {\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return '-';\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant('Export CSV')}`,\n          extend: 'csv',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              // Generate filename with current filter information\n              let filename = 'Matches_Report';\n              if (_this.seasonId) {\n                const season = _this.seasons?.find(s => s.id == _this.seasonId);\n                if (season) {\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.tournamentId) {\n                const tournament = _this.tournaments?.find(t => t.id == _this.tournamentId);\n                if (tournament) {\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.clubId) {\n                const club = _this.clubs?.find(c => c.id == _this.clubId);\n                if (club) {\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.matchStatus && _this.matchStatus !== 'all') {\n                filename += `_${_this.matchStatus}`;\n              }\n              if (_this.dateRange.start_date && _this.dateRange.end_date) {\n                filename += `_${_this.dateRange.start_date}_to_${_this.dateRange.end_date}`;\n              } else if (_this.dateRange.start_date) {\n                filename += `_from_${_this.dateRange.start_date}`;\n              }\n              filename += '.csv';\n              yield _this._exportService.exportCsv(data, filename);\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  setClubs(data) {\n    this.clubs = data;\n    // get field has key club_id\n    const clubField = this.fields.find(field => field.key === 'club_id');\n    // set options for club field\n    let current_clubs = [];\n    data.forEach(club => {\n      let club_name = this._translateService.instant(club.name);\n      current_clubs.push({\n        label: club_name,\n        value: club.id\n      });\n    });\n    clubField.props.options = current_clubs;\n  }\n  onCaptureEvent(event) {\n    // console.log(event);\n  }\n  editor(action, row) {\n    this.fields[0].defaultValue = this.clubId;\n    switch (action) {\n      case 'create':\n        this.fields[2].props.disabled = false;\n        break;\n      case 'edit':\n        this.fields[2].props.disabled = true;\n        break;\n      case 'remove':\n        break;\n      default:\n        break;\n    }\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('tournament_id')) {\n        let tournament_id = event.target.getAttribute('tournament_id');\n        let stage_id = event.target.getAttribute('stage_id');\n        //  navigate to path ':tournament_id/stages/:stage_id'\n        this._router.navigate([tournament_id, 'stages', stage_id], {\n          relativeTo: this.route\n        });\n      }\n    });\n  }\n  onSelectViewType(event) {\n    this.viewType = event;\n    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  refreshDataTable() {\n    if (this.dtElement.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    this.unlistener();\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function LeagueReportsComponent_Factory(t) {\n    return new (t || LeagueReportsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.TeamService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i7.LoadingService), i0.ɵɵdirectiveInject(i8.ToastrService), i0.ɵɵdirectiveInject(i9.RegistrationService), i0.ɵɵdirectiveInject(i10.ClubService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i11.CoreSidebarService), i0.ɵɵdirectiveInject(i12.Title), i0.ɵɵdirectiveInject(i13.ExportService), i0.ɵɵdirectiveInject(i14.TournamentService), i0.ɵɵdirectiveInject(i6.NgbCalendar), i0.ɵɵdirectiveInject(i6.NgbDateParserFormatter));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueReportsComponent,\n    selectors: [[\"app-league-reports\"]],\n    viewQuery: function LeagueReportsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowActionBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dateRangePicker = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    hostBindings: function LeagueReportsComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function LeagueReportsComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 17,\n    vars: 9,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\"], [1, \"col-12\"], [1, \"bg-white\"], [\"id\", \"nav-tab\", \"role\", \"tablist\", 1, \"nav\", \"nav-tabs\"], [\"class\", \"nav-link\", \"data-bs-toggle\", \"tab\", \"type\", \"button\", \"role\", \"tab\", 3, \"active\", \"id\", \"aria-controls\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"card\"], [\"class\", \"p-1\", 4, \"ngIf\"], [\"class\", \"p-2\", 4, \"ngIf\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\"], [\"rowActionBtn\", \"\"], [\"data-bs-toggle\", \"tab\", \"type\", \"button\", \"role\", \"tab\", 1, \"nav-link\", 3, \"id\", \"aria-controls\", \"click\"], [3, \"ngClass\"], [1, \"p-1\"], [1, \"row\", \"mb-1\"], [\"for\", \"season\", 1, \"form-label\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\"], [\"for\", \"tournament\", 1, \"form-label\"], [\"for\", \"club\", 1, \"form-label\"], [\"for\", \"dateRange\", 1, \"form-label\"], [1, \"input-group\"], [\"name\", \"daterange\", \"ngbDatepicker\", \"\", \"readonly\", \"\", \"outsideDays\", \"hidden\", 1, \"form-control\", 3, \"placeholder\", \"value\", \"dayTemplate\", \"footerTemplate\", \"firstDayOfWeek\", \"displayMonths\", \"autoClose\", \"click\", \"dateSelect\"], [\"dateRangePicker\", \"ngbDatepicker\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"feather\", \"icon-calendar\"], [\"dayTemplate\", \"\"], [\"footerTemplate\", \"\"], [\"for\", \"matchStatus\", 1, \"form-label\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [3, \"value\"], [1, \"custom-day\", 3, \"mouseenter\", \"mouseleave\"], [1, \"my-0\"], [1, \"d-flex\", \"justify-content-between\", \"p-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"p-2\"], [1, \"text-center\"], [\"btnStyle\", \"font-size:15px;color:black!important\", 3, \"actions\", \"data\", \"emitter\"]],\n    template: function LeagueReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"nav\")(7, \"div\", 6);\n        i0.ɵɵtemplate(8, LeagueReportsComponent_button_8_Template, 4, 9, \"button\", 7);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 8);\n        i0.ɵɵtemplate(10, LeagueReportsComponent_div_10_Template, 49, 54, \"div\", 9);\n        i0.ɵɵtemplate(11, LeagueReportsComponent_div_11_Template, 8, 6, \"div\", 10);\n        i0.ɵɵtemplate(12, LeagueReportsComponent_div_12_Template, 8, 6, \"div\", 10);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(13, \"core-sidebar\", 11);\n        i0.ɵɵelement(14, \"app-editor-sidebar\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(15, LeagueReportsComponent_ng_template_15_Template, 1, 2, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngForOf\", ctx.viewTypes);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.viewType === \"league_table\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.viewType === \"schedule_matches\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.viewType === \"bracket\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params);\n      }\n    },\n    dependencies: [i15.NgClass, i15.NgForOf, i15.NgIf, i16.DefaultClassDirective, i17.NgControlStatus, i17.NgModel, i6.NgbInputDatepicker, i18.ContentHeaderComponent, i19.CoreSidebarComponent, i20.DataTableDirective, i21.NgSelectComponent, i21.ɵr, i22.BtnDropdownActionComponent, i23.EditorSidebarComponent, i4.TranslatePipe],\n    styles: [\".min-w-max[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n\\n.custom-day[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 0.185rem 0.25rem;\\n  border-radius: 0.25rem;\\n  display: inline-block;\\n  width: 2rem;\\n  cursor: pointer;\\n}\\n\\n.custom-day[_ngcontent-%COMP%]:hover, .custom-day.focused[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n\\n.custom-day.range[_ngcontent-%COMP%], .custom-day[_ngcontent-%COMP%]:hover {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.custom-day.faded[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 123, 255, 0.5);\\n  color: white;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlLXJlcG9ydHMvbGVhZ3VlLXJlcG9ydHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUdBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFGOztBQUdBOztFQUVFLHlCQUFBO0FBQUY7O0FBR0E7O0VBRUUseUJBQUE7RUFDQSxZQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtFQUNBLFlBQUE7QUFBRjs7QUFHQTtFQUNFLHVCQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIubWluLXctbWF4IHtcclxuICBtaW4td2lkdGg6IG1heC1jb250ZW50O1xyXG59XHJcblxyXG4vLyBEYXRlIHJhbmdlIHBpY2tlciBzdHlsZXNcclxuLmN1c3RvbS1kYXkge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBwYWRkaW5nOiAwLjE4NXJlbSAwLjI1cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHdpZHRoOiAycmVtO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmN1c3RvbS1kYXk6aG92ZXIsXHJcbi5jdXN0b20tZGF5LmZvY3VzZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LnJhbmdlLFxyXG4uY3VzdG9tLWRheTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LmZhZGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDEyMywgMjU1LCAwLjUpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLm5hdi1saW5rIHtcclxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAoBA,SAASA,WAAW,QAAQ,0BAA0B;AAItD,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,IAAI,MAAM,aAAa;AAI9B,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAAsBC,OAAO,QAA8D,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICnB/GC,EAAA,CAAAC,cAAA,iBAWgD;IAF/CD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,gBAAA,CAAAL,OAAA,CAAAM,KAAA,CAA4B;IAAA,EAAC;IAGtCb,EAAA,CAAAc,SAAA,YAAkC;IAClCd,EAAA,CAAAe,MAAA,GACD;;IAAAf,EAAA,CAAAgB,YAAA,EAAS;;;;;IAVRhB,EAAA,CAAAiB,WAAA,WAAAC,MAAA,CAAAC,QAAA,KAAAZ,OAAA,CAAAM,KAAA,CAAwC;IACxCb,EAAA,CAAAoB,sBAAA,eAAAb,OAAA,CAAAM,KAAA,SAA2B,0BAAAN,OAAA,CAAAM,KAAA;IAM3Bb,EAAA,CAAAqB,WAAA,kBAAAH,MAAA,CAAAC,QAAA,KAAAZ,OAAA,CAAAM,KAAA,CAA8C;IAC3Cb,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAuB,UAAA,YAAAhB,OAAA,CAAAiB,SAAA,CAA0B;IAC7BxB,EAAA,CAAAsB,SAAA,GACD;IADCtB,EAAA,CAAAyB,kBAAA,MAAAzB,EAAA,CAAA0B,WAAA,OAAAnB,OAAA,CAAAoB,KAAA,OACD;;;;;IAiBC3B,EAAA,CAAAC,cAAA,oBAA8D;IAC7DD,EAAA,CAAAe,MAAA,GACD;;IAAAf,EAAA,CAAAgB,YAAA,EAAY;;;;IAF8BhB,EAAA,CAAAuB,UAAA,UAAAK,UAAA,CAAAC,EAAA,CAAmB;IAC5D7B,EAAA,CAAAsB,SAAA,GACD;IADCtB,EAAA,CAAAyB,kBAAA,MAAAzB,EAAA,CAAA0B,WAAA,OAAAE,UAAA,CAAAE,IAAA,OACD;;;;;IAaA9B,EAAA,CAAAC,cAAA,oBAA0E;IACzED,EAAA,CAAAe,MAAA,GACD;;IAAAf,EAAA,CAAAgB,YAAA,EAAY;;;;IAFsChB,EAAA,CAAAuB,UAAA,UAAAQ,cAAA,CAAAF,EAAA,CAAuB;IACxE7B,EAAA,CAAAsB,SAAA,GACD;IADCtB,EAAA,CAAAyB,kBAAA,MAAAzB,EAAA,CAAA0B,WAAA,OAAAK,cAAA,CAAAD,IAAA,OACD;;;;;IAWA9B,EAAA,CAAAC,cAAA,oBAAwD;IACvDD,EAAA,CAAAe,MAAA,GACD;;IAAAf,EAAA,CAAAgB,YAAA,EAAY;;;;IAF0BhB,EAAA,CAAAuB,UAAA,UAAAS,QAAA,CAAAH,EAAA,CAAiB;IACtD7B,EAAA,CAAAsB,SAAA,GACD;IADCtB,EAAA,CAAAyB,kBAAA,MAAAzB,EAAA,CAAA0B,WAAA,OAAAM,QAAA,CAAAC,IAAA,OACD;;;;;;IAkCAjC,EAAA,CAAAC,cAAA,eAOC;IAFAD,EAAA,CAAAE,UAAA,wBAAAgC,iFAAA;MAAA,MAAA9B,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAA8B,IAAA;MAAA,MAAAC,QAAA,GAAAhC,WAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA2B,OAAA,CAAAC,WAAA,GAAAH,QAAA;IAAA,EAAiC,wBAAAI,iFAAA;MAAAxC,EAAA,CAAAK,aAAA,CAAA8B,IAAA;MAAA,MAAAM,OAAA,GAAAzC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA8B,OAAA,CAAAF,WAAA,GACL,IAAI;IAAA,EADC;IAGjCvC,EAAA,CAAAe,MAAA,GACD;IAAAf,EAAA,CAAAgB,YAAA,EAAO;;;;;;IAPNhB,EAAA,CAAAiB,WAAA,YAAAyB,WAAA,CAAyB,UAAAC,OAAA,CAAAC,OAAA,CAAAR,QAAA,YAAAO,OAAA,CAAAE,SAAA,CAAAT,QAAA,KAAAO,OAAA,CAAAG,QAAA,CAAAV,QAAA;IAMzBpC,EAAA,CAAAsB,SAAA,GACD;IADCtB,EAAA,CAAAyB,kBAAA,MAAAW,QAAA,CAAAW,GAAA,MACD;;;;;;IAIA/C,EAAA,CAAAc,SAAA,aAAiB;IACjBd,EAAA,CAAAC,cAAA,cAAgD;IAI9CD,EAAA,CAAAE,UAAA,mBAAA8C,8EAAA;MAAAhD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAAjD,EAAA,CAAAU,aAAA;MAAA,MAAAwC,IAAA,GAAAlD,EAAA,CAAAmD,WAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAAU,aAAA;MAAS0C,OAAA,CAAAC,cAAA,EAAgB;MAAA,OAAErD,EAAA,CAAAW,WAAA,CAAAuC,IAAA,CAAAI,KAAA,EAAuB;IAAA,EAAC;IAEnDtD,EAAA,CAAAe,MAAA,cACD;IAAAf,EAAA,CAAAgB,YAAA,EAAS;IACThB,EAAA,CAAAC,cAAA,iBAIC;IADAD,EAAA,CAAAE,UAAA,mBAAAqD,8EAAA;MAAAvD,EAAA,CAAAK,aAAA,CAAA4C,IAAA;MAAAjD,EAAA,CAAAU,aAAA;MAAA,MAAAwC,IAAA,GAAAlD,EAAA,CAAAmD,WAAA;MAAA,OAASnD,EAAA,CAAAW,WAAA,CAAAuC,IAAA,CAAAI,KAAA,EAAuB;IAAA,EAAC;IAEjCtD,EAAA,CAAAe,MAAA,cACD;IAAAf,EAAA,CAAAgB,YAAA,EAAS;;;;;IAYVhB,EAAA,CAAAC,cAAA,oBAA4E;IAC3ED,EAAA,CAAAe,MAAA,GACD;;IAAAf,EAAA,CAAAgB,YAAA,EAAY;;;;IAFyChB,EAAA,CAAAuB,UAAA,UAAAiC,UAAA,CAAA3C,KAAA,CAAsB;IAC1Eb,EAAA,CAAAsB,SAAA,GACD;IADCtB,EAAA,CAAAyB,kBAAA,MAAAzB,EAAA,CAAA0B,WAAA,OAAA8B,UAAA,CAAA7B,KAAA,OACD;;;;;;IApHJ3B,EAAA,CAAAC,cAAA,cAAqD;IAGXD,EAAA,CAAAe,MAAA,GAAsB;;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IACrEhB,EAAA,CAAAC,cAAA,oBAKmC;IADlCD,EAAA,CAAAE,UAAA,2BAAAuD,0EAAAC,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAiD,OAAA,CAAAC,QAAA,GAAAH,MAAA;IAAA,EAAsB,oBAAAI,mEAAAJ,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAI,OAAA,GAAA/D,EAAA,CAAAU,aAAA;MAAA,OACZV,EAAA,CAAAW,WAAA,CAAAoD,OAAA,CAAAC,cAAA,CAAAN,MAAA,CAAsB;IAAA,EADV;;IAEtB1D,EAAA,CAAAiE,UAAA,IAAAC,kDAAA,wBAEY;IACblE,EAAA,CAAAgB,YAAA,EAAY;IAGdhB,EAAA,CAAAC,cAAA,cAAsB;IAEuBD,EAAA,CAAAe,MAAA,IAA0B;;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IAC7EhB,EAAA,CAAAC,cAAA,qBAKuC;IADtCD,EAAA,CAAAE,UAAA,2BAAAiE,2EAAAT,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAS,OAAA,GAAApE,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAyD,OAAA,CAAAC,YAAA,GAAAX,MAAA;IAAA,EAA0B,oBAAAY,oEAAAZ,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAY,OAAA,GAAAvE,EAAA,CAAAU,aAAA;MAAA,OAChBV,EAAA,CAAAW,WAAA,CAAA4D,OAAA,CAAAC,kBAAA,CAAAd,MAAA,CAA0B;IAAA,EADV;;IAE1B1D,EAAA,CAAAiE,UAAA,KAAAQ,mDAAA,wBAEY;IACbzE,EAAA,CAAAgB,YAAA,EAAY;IAEbhB,EAAA,CAAAC,cAAA,eAAsB;IACgBD,EAAA,CAAAe,MAAA,IAAoB;;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IACjEhB,EAAA,CAAAC,cAAA,qBAKiC;IADhCD,EAAA,CAAAE,UAAA,2BAAAwE,2EAAAhB,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAgB,OAAA,GAAA3E,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAgE,OAAA,CAAAC,MAAA,GAAAlB,MAAA;IAAA,EAAoB,oBAAAmB,oEAAAnB,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAmB,OAAA,GAAA9E,EAAA,CAAAU,aAAA;MAAA,OACVV,EAAA,CAAAW,WAAA,CAAAmE,OAAA,CAAAC,YAAA,CAAArB,MAAA,CAAoB;IAAA,EADV;;IAEpB1D,EAAA,CAAAiE,UAAA,KAAAe,mDAAA,wBAEY;IACbhF,EAAA,CAAAgB,YAAA,EAAY;IAEbhB,EAAA,CAAAC,cAAA,eAAsB;IACqBD,EAAA,CAAAe,MAAA,IAA0B;;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IAC5EhB,EAAA,CAAAC,cAAA,eAAyB;IASvBD,EAAA,CAAAE,UAAA,mBAAA+E,+DAAA;MAAAjF,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAuB,OAAA,GAAAlF,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAuE,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC,wBAAAC,oEAAA1B,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAA0B,OAAA,GAAArF,EAAA,CAAAU,aAAA;MAAA,OAGZV,EAAA,CAAAW,WAAA,CAAA0E,OAAA,CAAAC,eAAA,CAAA5B,MAAA,CAAuB;IAAA,EAHX;;IAR3B1D,EAAA,CAAAgB,YAAA,EAgBE;IACFhB,EAAA,CAAAC,cAAA,eAAgC;IAG9BD,EAAA,CAAAE,UAAA,mBAAAqF,gEAAA;MAAAvF,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAA6B,OAAA,GAAAxF,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA6E,OAAA,CAAAL,cAAA,EAAgB;IAAA,EAAC;IAE1BnF,EAAA,CAAAc,SAAA,aAAqC;IACtCd,EAAA,CAAAgB,YAAA,EAAS;IAIXhB,EAAA,CAAAiE,UAAA,KAAAwB,qDAAA,iCAAAzF,EAAA,CAAA0F,sBAAA,CAWc;IAEd1F,EAAA,CAAAiE,UAAA,KAAA0B,qDAAA,iCAAA3F,EAAA,CAAA0F,sBAAA,CAkBc;IACf1F,EAAA,CAAAgB,YAAA,EAAM;IACNhB,EAAA,CAAAC,cAAA,eAAsB;IACuBD,EAAA,CAAAe,MAAA,IAA4B;;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IAChFhB,EAAA,CAAAC,cAAA,qBAKwC;IADvCD,EAAA,CAAAE,UAAA,2BAAA0F,2EAAAlC,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAkC,OAAA,GAAA7F,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAkF,OAAA,CAAAC,WAAA,GAAApC,MAAA;IAAA,EAAyB,oBAAAqC,oEAAArC,MAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAqC,OAAA,GAAAhG,EAAA,CAAAU,aAAA;MAAA,OACfV,EAAA,CAAAW,WAAA,CAAAqF,OAAA,CAAAC,mBAAA,CAAAvC,MAAA,CAA2B;IAAA,EADZ;;IAEzB1D,EAAA,CAAAiE,UAAA,KAAAiC,mDAAA,wBAEY;IACblG,EAAA,CAAAgB,YAAA,EAAY;IAGdhB,EAAA,CAAAC,cAAA,eAAsB;IAEpBD,EAAA,CAAAc,SAAA,iBACQ;IACTd,EAAA,CAAAgB,YAAA,EAAM;;;;;;IAzHkChB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,kBAAsB;IAI5D1B,EAAA,CAAAsB,SAAA,GAA2C;IAA3CtB,EAAA,CAAAoG,qBAAA,gBAAApG,EAAA,CAAA0B,WAAA,yBAA2C;IAF3C1B,EAAA,CAAAuB,UAAA,oBAAmB,gCAAA8E,MAAA,CAAAxC,QAAA;IAKW7D,EAAA,CAAAsB,SAAA,GAAU;IAAVtB,EAAA,CAAAuB,UAAA,YAAA8E,MAAA,CAAAC,OAAA,CAAU;IAQEtG,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,uBAA0B;IAIpE1B,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAAoG,qBAAA,gBAAApG,EAAA,CAAA0B,WAAA,8BAA+C;IAF/C1B,EAAA,CAAAuB,UAAA,oBAAmB,+BAAA8E,MAAA,CAAAhC,YAAA;IAKerE,EAAA,CAAAsB,SAAA,GAAc;IAAdtB,EAAA,CAAAuB,UAAA,YAAA8E,MAAA,CAAAE,WAAA,CAAc;IAMZvG,EAAA,CAAAsB,SAAA,GAAoB;IAApBtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,iBAAoB;IAIxD1B,EAAA,CAAAsB,SAAA,GAAyC;IAAzCtB,EAAA,CAAAoG,qBAAA,gBAAApG,EAAA,CAAA0B,WAAA,wBAAyC;IAFzC1B,EAAA,CAAAuB,UAAA,oBAAmB,+BAAA8E,MAAA,CAAAzB,MAAA;IAKS5E,EAAA,CAAAsB,SAAA,GAAQ;IAARtB,EAAA,CAAAuB,UAAA,YAAA8E,MAAA,CAAAG,KAAA,CAAQ;IAMKxG,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,uBAA0B;IAKlE1B,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAAoG,qBAAA,gBAAApG,EAAA,CAAA0B,WAAA,8BAA+C;IAI/C1B,EAAA,CAAAuB,UAAA,UAAA8E,MAAA,CAAAI,SAAA,CAAAC,MAAA,CAAAL,MAAA,CAAAM,QAAA,KAAAN,MAAA,CAAAO,MAAA,WAAAP,MAAA,CAAAI,SAAA,CAAAC,MAAA,CAAAL,MAAA,CAAAO,MAAA,QAAuF,gBAAAC,IAAA,oBAAAC,IAAA;IAsD7C9G,EAAA,CAAAsB,SAAA,IAA4B;IAA5BtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,yBAA4B;IAIvE1B,EAAA,CAAAsB,SAAA,GAA2C;IAA3CtB,EAAA,CAAAoG,qBAAA,gBAAApG,EAAA,CAAA0B,WAAA,0BAA2C;IAF3C1B,EAAA,CAAAuB,UAAA,qBAAoB,gCAAA8E,MAAA,CAAAP,WAAA;IAKU9F,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAAuB,UAAA,YAAA8E,MAAA,CAAAU,kBAAA,CAAqB;IAQnC/G,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAuB,UAAA,cAAA8E,MAAA,CAAAW,SAAA,CAAuB,cAAAX,MAAA,CAAAY,SAAA;;;;;IAM3CjH,EAAA,CAAAC,cAAA,cAAyD;IAEnDD,EAAA,CAAAe,MAAA,GAAwC;;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACjDhB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAe,MAAA,GAA8B;;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;IADjChB,EAAA,CAAAsB,SAAA,GAAwC;IAAxCtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,mCAAwC;IACzC1B,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,yBAA8B;;;;;IAInC1B,EAAA,CAAAC,cAAA,cAAgD;IAE1CD,EAAA,CAAAe,MAAA,GAA+B;;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACxChB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAe,MAAA,GAA8B;;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;IADjChB,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,0BAA+B;IAChC1B,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA0B,WAAA,yBAA8B;;;;;;IAevC1B,EAAA,CAAAC,cAAA,kCACiD;IADaD,EAAA,CAAAE,UAAA,qBAAAgH,0FAAAxD,MAAA;MAAA,MAAAtD,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAA8G,IAAA;MAAA,MAAAC,WAAA,GAAAhH,WAAA,CAAAiH,aAAA;MAAA,OAAWrH,EAAA,CAAAW,WAAA,CAAAyG,WAAA,CAAA1D,MAAA,CAAe;IAAA,EAAC;IACxC1D,EAAA,CAAAgB,YAAA,EAA0B;;;;;IADlDhB,EAAA,CAAAuB,UAAA,YAAA+F,MAAA,CAAAC,UAAA,CAAsB,SAAAC,QAAA;;;ADpJhD,OAAM,MAAOC,sBAAsB;EAkFjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,eAA+B,EAC/BC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,YAAyB,EACzBC,aAAuB,EACvBC,eAA+B,EAC/BC,aAA4B,EAC5BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,mBAAuC,EACvCC,aAAoB,EACnBC,cAA6B,EAC9BC,kBAAqC,EACpCC,QAAqB,EACtBnC,SAAiC;IAlBhC,KAAAkB,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAnC,SAAS,GAATA,SAAS;IA/FlB,KAAAoC,SAAS,GAAQC,kBAAkB;IACnC,KAAA7B,SAAS,GAAyB,IAAIrH,OAAO,EAAe;IAC5D,KAAAoH,SAAS,GAAQ,EAAE;IAEZ,KAAA+B,WAAW,GAAQ,EAAE;IAErB,KAAAnE,MAAM,GAAQ,IAAI;IAClB,KAAAP,YAAY,GAAQ,IAAI;IACxB,KAAA2E,SAAS,GAAQ;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAA5G,WAAW,GAAmB,IAAI;IAClC,KAAAoE,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAwC,WAAW,GAAY,KAAK,CAAC,CAAC;IAEvB,KAAAtD,WAAW,GAAW,KAAK;IAC3B,KAAAuD,cAAc,GAAY,KAAK;IAM/B,KAAAC,UAAU,GAAG,YAAY;IAEhC,KAAAC,SAAS,GAAG,CACV;MAAE5H,KAAK,EAAE,YAAY;MAAEd,KAAK,EAAE,cAAc;MAAEW,SAAS,EAAE;IAAwB,CAAE,EACnF;MAAEG,KAAK,EAAE,eAAe;MAAEd,KAAK,EAAE,kBAAkB;MAAEW,SAAS,EAAE;IAAsB;IACtF;IAAA,CACD;;IAED,KAAAL,QAAQ,GAAW,cAAc;IAEjC,KAAA4F,kBAAkB,GAAG,CACnB;MAAEpF,KAAK,EAAE,YAAY;MAAEd,KAAK,EAAE;IAAK,CAAE,EACrC;MAAEc,KAAK,EAAE,UAAU;MAAEd,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEc,KAAK,EAAE,QAAQ;MAAEd,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEc,KAAK,EAAE,WAAW;MAAEd,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEc,KAAK,EAAE,WAAW;MAAEd,KAAK,EAAE;IAAW,CAAE,CAC3C;IAEM,KAAA2I,MAAM,GAAwB;MACnCC,SAAS,EAAE,IAAI,CAACH,UAAU;MAC1BI,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAACpB,iBAAiB,CAACqB,OAAO,CAAC,uBAAuB,CAAC;QAC/DC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAGpK,WAAW,CAACqK,MAAM,eAAe;MACzCC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAEM,KAAAC,MAAM,GAAU,CACrB;MACEC,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACL3I,KAAK,EAAE,IAAI,CAAC4G,iBAAiB,CAACqB,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAAChC,iBAAiB,CAACqB,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,EACD;MACEJ,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACL3I,KAAK,EAAE,IAAI,CAAC4G,iBAAiB,CAACqB,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAAChC,iBAAiB,CAACqB,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,CACF;IA0BC,IAAI,CAAC/B,aAAa,CAACgC,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACvC,eAAe,CAACwC,IAAI,EAAE;IAC3B,IAAI,CAACtC,oBAAoB,CAACuC,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACxE,OAAO,GAAGwE,IAAI;MACnB,IAAI,CAACjH,QAAQ,GAAG,IAAI,CAACyC,OAAO,CAAC,CAAC,CAAC,CAACzE,EAAE;MAClC,IAAI,CAACkJ,eAAe,EAAE,CAAC,CAAC;MACxB,IAAI,IAAI,CAAClC,SAAS,CAACmC,UAAU,EAAE;QAC7B,IAAI,CAACnC,SAAS,CAACmC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;MAEJ,IAAI,CAAClE,SAAS,CAACmE,IAAI,CAAC,IAAI,CAACpE,SAAS,CAAC;IACrC,CAAC,EACAqE,KAAK,IAAI;MACRxL,IAAI,CAACyL,IAAI,CAAC;QACR5B,KAAK,EAAE,OAAO;QACd6B,IAAI,EAAEF,KAAK,CAACG,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACnD,iBAAiB,CAACqB,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAACzB,eAAe,CAACwD,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAACtD,YAAY,CAACuD,WAAW,EAAE,CAAChB,SAAS,CACtCiB,GAAG,IAAI;MACN,IAAI,CAACtF,KAAK,GAAGsF,GAAG,CAAChB,IAAI;MACrB;MACA,IAAI,IAAI,CAACjC,SAAS,CAACmC,UAAU,EAAE;QAC7B,IAAI,CAACnC,SAAS,CAACmC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;IAEN,CAAC,EACAE,KAAK,IAAI;MACRxL,IAAI,CAACyL,IAAI,CAAC;QACR5B,KAAK,EAAE,OAAO;QACd6B,IAAI,EAAEF,KAAK,CAACG,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACnD,iBAAiB,CAACqB,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEA5F,cAAcA,CAACN,MAAM;IACnB,OAAO,IAAIqI,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACpI,QAAQ,GAAGH,MAAM;MACtB,IAAI,CAACqH,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAAC1G,YAAY,GAAG,IAAI;MACxB,IAAI,CAAC6H,gBAAgB,EAAE;MACvBF,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAjH,YAAYA,CAACrB,MAAM;IACjByI,OAAO,CAACC,GAAG,CAAC,iBAAiB1I,MAAM,EAAE,CAAC;IACtC,IAAI,CAACkB,MAAM,GAAGlB,MAAM;IACpB,IAAI,CAACwI,gBAAgB,EAAE;EACzB;EAEA1H,kBAAkBA,CAACd,MAAM;IACvByI,OAAO,CAACC,GAAG,CAAC,uBAAuB1I,MAAM,EAAE,CAAC;IAC5C,IAAI,CAACW,YAAY,GAAGX,MAAM;IAC1B,IAAI,CAACwI,gBAAgB,EAAE;EACzB;EAEAjG,mBAAmBA,CAACvC,MAAM;IACxByI,OAAO,CAACC,GAAG,CAAC,wBAAwB1I,MAAM,EAAE,CAAC;IAC7C,IAAI,CAACoC,WAAW,GAAGpC,MAAM;IACzB,IAAI,CAACwI,gBAAgB,EAAE;EACzB;EAEA5G,eAAeA,CAACjD,IAAa;IAE3B,IAAI,CAAC,IAAI,CAACsE,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClCuF,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAACzF,QAAQ,GAAGtE,IAAI;MACpB,IAAI,CAAC+G,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACzC,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZvE,IAAI,IACJA,IAAI,CAACgK,KAAK,CAAC,IAAI,CAAC1F,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGvE,IAAI;MAClB,IAAI,CAAC+G,WAAW,GAAG,KAAK;MACxB,IAAI,CAACkD,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAAClJ,KAAK,EAAE;UACtD,IAAI,CAACkJ,eAAe,CAAClJ,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACL6I,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAACxF,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAGtE,IAAI;MACpB,IAAI,CAAC+G,WAAW,GAAG,IAAI;;EAE3B;EAEAjE,cAAcA,CAAA;IACZ,IAAI,CAACiE,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACoD,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGL,UAAU,CAAC,MAAK;MACzC,IAAI,CAACO,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACH,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACO,MAAM,IAC5B,CAAC,IAAI,CAACP,eAAe,CAACO,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAAC3D,WAAW,EAAE;MACpB;;IAGF,MAAM4D,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACR,eAAe,CAAClJ,KAAK,EAAE;EAC9B;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAACsD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACwC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACkD,eAAe,EAAE;EACxB;EAEAmB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAACnH,SAAS,CAACoH,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAAChF,QAAQ,CAACkF,OAAO,CAAC/N,OAAO,CAACgO,IAAI,CAACH,MAAM,CAAC,CAAC,GACxD7N,OAAO,CAACgO,IAAI,CAACH,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEA7K,SAASA,CAACR,IAAa;IACrB,OACE,IAAI,CAACsE,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAACrE,WAAW,IAChBF,IAAI,CAACgK,KAAK,CAAC,IAAI,CAAC1F,QAAQ,CAAC,IACzBtE,IAAI,CAAC2L,MAAM,CAAC,IAAI,CAACzL,WAAW,CAAC;EAEjC;EAEAO,QAAQA,CAACT,IAAa;IACpB,OAAO,IAAI,CAACuE,MAAM,IAAIvE,IAAI,CAACgK,KAAK,CAAC,IAAI,CAAC1F,QAAQ,CAAC,IAAItE,IAAI,CAAC2L,MAAM,CAAC,IAAI,CAACpH,MAAM,CAAC;EAC7E;EAEAhE,OAAOA,CAACP,IAAa;IACnB,OACEA,IAAI,CAAC4L,MAAM,CAAC,IAAI,CAACtH,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAIvE,IAAI,CAAC4L,MAAM,CAAC,IAAI,CAACrH,MAAM,CAAE,IACzC,IAAI,CAAC9D,QAAQ,CAACT,IAAI,CAAC,IACnB,IAAI,CAACQ,SAAS,CAACR,IAAI,CAAC;EAExB;EAEA6L,iBAAiBA,CAAA;IACf/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACpD,SAAS,CAAC;IACjD,IAAI,CAACkD,gBAAgB,EAAE;EACzB;EAEAI,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3F,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAMuH,UAAU,GAAGrO,MAAM,CACvB,GAAG,IAAI,CAAC6G,QAAQ,CAACyH,IAAI,IAAI,IAAI,CAACzH,QAAQ,CAAC0H,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC5H,QAAQ,CAAC5D,GAAG,CAACuL,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAMC,QAAQ,GAAG1O,MAAM,CACrB,GAAG,IAAI,CAAC8G,MAAM,CAACwH,IAAI,IAAI,IAAI,CAACxH,MAAM,CAACyH,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC3H,MAAM,CAAC7D,GAAG,CAACuL,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAACvF,SAAS,CAACC,UAAU,GAAGkF,UAAU,CAACzH,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACsC,SAAS,CAACE,QAAQ,GAAGsF,QAAQ,CAAC9H,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAACyC,cAAc,GAAG,GAAGgF,UAAU,CAACzH,MAAM,CACxC,cAAc,CACf,MAAM8H,QAAQ,CAAC9H,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACwH,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAACvH,QAAQ,EAAE;MACxB,MAAMwH,UAAU,GAAGrO,MAAM,CACvB,GAAG,IAAI,CAAC6G,QAAQ,CAACyH,IAAI,IAAI,IAAI,CAACzH,QAAQ,CAAC0H,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC5H,QAAQ,CAAC5D,GAAG,CAACuL,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAACvF,SAAS,CAACC,UAAU,GAAGkF,UAAU,CAACzH,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACsC,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAGgF,UAAU,CAACzH,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAACsC,SAAS,CAACC,UAAU,GAAG,IAAI;MAChC,IAAI,CAACD,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAAC+E,iBAAiB,EAAE;;EAE5B;EAEAnD,eAAeA,CAAA;IACb,IAAI,IAAI,CAAClH,QAAQ,EAAE;MACjB,IAAI,CAACiE,KAAK,CACP2G,GAAG,CACF,GAAG9O,WAAW,CAACqK,MAAM,YAAY,IAAI,CAACnG,QAAQ,+BAA+B,CAC9E,CACAgH,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAACvE,WAAW,GAAGuE,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAO,KAAK,IAAI;QACRxL,IAAI,CAACyL,IAAI,CAAC;UACR5B,KAAK,EAAE,OAAO;UACd6B,IAAI,EAAEF,KAAK,CAACG,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACnD,iBAAiB,CAACqB,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEA8E,QAAQA,CAAA;IAAA,IAAAC,KAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAAC9G,MAAM,CAAC6B,OAAO,CAAC,gBAAgB,CAAC;MAClDkF,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACV1E,IAAI,EAAE,EAAE;QACR2E,KAAK,EAAE,CACL;UACElN,IAAI,EAAE,IAAI,CAACiG,MAAM,CAAC6B,OAAO,CAAC,MAAM,CAAC;UACjCqF,MAAM,EAAE;SACT,EACD;UACEnN,IAAI,EAAE,IAAI,CAACiG,MAAM,CAAC6B,OAAO,CAAC,aAAa,CAAC;UACxCqF,MAAM,EAAE;SACT,EACD;UACEnN,IAAI,EAAE,IAAI,CAACiG,MAAM,CAAC6B,OAAO,CAAC,gBAAgB,CAAC;UAC3CqF,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACvE,iBAAiB,EAAE;IACxB,IAAI,CAACkB,SAAS,EAAE;IAChB,IAAI,CAAC5E,SAAS,GAAG;MACfkI,GAAG,EAAE,IAAI,CAACrH,eAAe,CAACsH,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;QACR,GAAG,IAAI,CAAC1H,eAAe,CAACsH,iBAAiB,CAACK,IAAI;QAC9C;QACA;QACA;QACA;QAEA;QACAF,UAAU,EAAE;OACb;MACDpE,IAAI,EAAEA,CAACuE,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAIlG,MAAM,GAAG,IAAImG,eAAe,EAAE;QAElC,IAAI,IAAI,CAAC/K,MAAM,IAAIgL,SAAS,IAAI,IAAI,CAAChL,MAAM,KAAK,IAAI,EAAE;UACpD4E,MAAM,CAACqG,MAAM,CAAC,SAAS,EAAE,IAAI,CAACjL,MAAM,CAAC;;QAGvC,IAAI,IAAI,CAACP,YAAY,IAAIuL,SAAS,IAAI,IAAI,CAACvL,YAAY,KAAK,IAAI,EAAE;UAChEmF,MAAM,CAACqG,MAAM,CAAC,eAAe,EAAE,IAAI,CAACxL,YAAY,CAAC;;QAGnD,IAAI,IAAI,CAAC2E,SAAS,CAACC,UAAU,EAAE;UAC7BO,MAAM,CAACqG,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC7G,SAAS,CAACC,UAAU,CAAC;;QAGxD,IAAI,IAAI,CAACD,SAAS,CAACE,QAAQ,EAAE;UAC3BM,MAAM,CAACqG,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC7G,SAAS,CAACE,QAAQ,CAAC;;QAGpD,IAAI,IAAI,CAACpD,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,KAAK,EAAE;UAClD0D,MAAM,CAACqG,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC/J,WAAW,CAAC;;QAGjD,MAAMgK,WAAW,GAAGtG,MAAM,CAAC8E,QAAQ,EAAE;QACrC,MAAMvE,GAAG,GAAG,GAAGpK,WAAW,CAACqK,MAAM,YAAY,IAAI,CAACnG,QAAQ,WACxDiM,WAAW,GAAG,GAAG,GAAGA,WAAW,GAAG,EACpC,EAAE;QAEF,IAAI,CAACzG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACvB,KAAK,CAACiI,IAAI,CAAMhG,GAAG,EAAE0F,oBAAoB,CAAC,CAAC5E,SAAS,CACtDmF,IAAS,IAAI;UACZ,IAAI,CAAC3G,cAAc,GAAG,KAAK;UAC3BqG,QAAQ,CAAC;YACPO,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCpF,IAAI,EAAEkF,IAAI,CAAClF;WACZ,CAAC;QACJ,CAAC,EACAO,KAAK,IAAI;UACR,IAAI,CAAChC,cAAc,GAAG,KAAK;UAC3B8C,OAAO,CAACd,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDqE,QAAQ,CAAC;YACPO,YAAY,EAAE,CAAC;YACfC,eAAe,EAAE,CAAC;YAClBpF,IAAI,EAAE;WACP,CAAC;QACJ,CAAC,CACF;MACH,CAAC;MACDqF,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CAAC;MACpDC,OAAO,EAAE,CACP;QACE9G,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,KAAK,CAAC;QAC5CkB,IAAI,EAAE,IAAI;QACV2F,SAAS,EAAE,oBAAoB;QAC/BpG,IAAI,EAAE,YAAY;QAClBqG,MAAM,EAAE,SAAAA,CAAU5F,IAAI,EAAET,IAAI,EAAEsG,GAAG,EAAEC,QAAQ;UAEzC,OAAOA,QAAQ,CAACD,GAAG,GAAG,CAAC;QACzB;OACD,EACD;QACEjH,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,YAAY,CAAC;QACnDkB,IAAI,EAAE,MAAM;QACZ2F,SAAS,EAAE,oBAAoB;QAC/BpG,IAAI,EAAE,YAAY;QAClBqG,MAAM,EAAE;UACNG,OAAO,EAAEA,CAAC/F,IAAI,EAAET,IAAI,EAAEsG,GAAG,KAAI;YAC3B,OAAO7F,IAAI,IAAI,KAAK;UACtB,CAAC;UACDgG,MAAM,EAAEA,CAAChG,IAAI,EAAET,IAAI,EAAEsG,GAAG,KAAI;YAC1B,OAAO7F,IAAI;UACb;;OAEH,EACD;QACEpB,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,OAAO,CAAC;QAC9CkB,IAAI,EAAE,YAAY;QAClB4F,MAAM,EAAE,SAAAA,CAAU5F,IAAI;UACpB,MAAMiG,WAAW,GAAGjG,IAAI,IAAI,KAAK;UACjC,OAAO,uDAAuDiG,WAAW,QAAQ;QACnF;OACD,EACD;QACErH,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,MAAM,CAAC;QAC7CkB,IAAI,EAAE,YAAY;QAClB2F,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAU5F,IAAI,EAAET,IAAI,EAAEsG,GAAG;UAC/B,MAAMK,WAAW,GACf,CAAClG,IAAI,IAAIA,IAAI,KAAK,KAAK,GACnB,KAAK,GACLhL,MAAM,CAACgL,IAAI,CAAC,CAACpE,MAAM,CAAC,YAAY,CAAC;UACvC,OAAO,4DAA4DsK,WAAW,QAAQ;QACxF;OACD,EACD;QACEtH,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,YAAY,CAAC;QACnDkB,IAAI,EAAE,YAAY;QAClB2F,SAAS,EAAE,aAAa;QAExBC,MAAM,EAAE,SAAAA,CAAS5F,IAAI,EAAET,IAAI,EAAEsG,GAAG;UAC9B,IAAI,CAAC7F,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOhL,MAAM,CAACgL,IAAI,CAAC,CAACpE,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACEgD,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,UAAU,CAAC;QACjDkB,IAAI,EAAE,UAAU;QAChB2F,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS5F,IAAI,EAAET,IAAI,EAAEsG,GAAG;UAC9B,IAAI,CAAC7F,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOhL,MAAM,CAACgL,IAAI,CAAC,CAACpE,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACEgD,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,UAAU,CAAC;QACjDkB,IAAI,EAAE,UAAU;QAChB4F,MAAM,EAAE,SAAAA,CAAS5F,IAAI,EAAET,IAAI,EAAEsG,GAAG;UAC9B,IAAI,CAAC7F,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOA,IAAI;QACb;OACD,EACD;QACEpB,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,WAAW,CAAC;QAClDkB,IAAI,EAAE,gBAAgB;QACtB2F,SAAS,EAAE;OACZ,EACD;QACE/G,KAAK,EAAE,IAAI;QACXoB,IAAI,EAAE,IAAI;QACV2F,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS5F,IAAI,EAAET,IAAI,EAAEsG,GAAG;UAC9B,OAAO,IAAI;QACb;OACD,EACD;QACEjH,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,WAAW,CAAC;QAClDkB,IAAI,EAAE,gBAAgB;QACtB2F,SAAS,EAAE;OACZ,EACD;QACE/G,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,YAAY,CAAC;QACnDkB,IAAI,EAAE,YAAY;QAClB2F,SAAS,EAAE;OACZ,EACD;QACE3F,IAAI,EAAE,IAAI;QACV2F,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAS5F,IAAI,EAAET,IAAI,EAAEsG,GAAG;UAC9B,OAAO,GAAG;QACZ;OACD,EACD;QACEjH,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACqB,OAAO,CAAC,YAAY,CAAC;QACnDkB,IAAI,EAAE,YAAY;QAClB2F,SAAS,EAAE;OACZ,CACF;MACDQ,OAAO,EAAE;QACP/B,GAAG,EAAE,IAAI,CAACrH,eAAe,CAACsH,iBAAiB,CAAC8B,OAAO,CAAC/B,GAAG;QACvD+B,OAAO,EAAE,CACP;UACE1F,IAAI,EAAE,6CAA6C,IAAI,CAAChD,iBAAiB,CAACqB,OAAO,CAC/E,YAAY,CACb,EAAE;UACHsH,MAAM,EAAE,KAAK;UACbhH,MAAM;YAAA,IAAAiH,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAM1G,IAAI,GAAGwG,EAAE,CAACL,OAAO,CAACQ,UAAU,EAAE;cAEpC;cACA,IAAIC,QAAQ,GAAG,gBAAgB;cAE/B,IAAI/C,KAAI,CAAC9K,QAAQ,EAAE;gBACjB,MAAM8N,MAAM,GAAGhD,KAAI,CAACrI,OAAO,EAAEsL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChQ,EAAE,IAAI8M,KAAI,CAAC9K,QAAQ,CAAC;gBAC7D,IAAI8N,MAAM,EAAE;kBACVD,QAAQ,IAAI,IAAIC,MAAM,CAAC7P,IAAI,CAACgQ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAItD,IAAInD,KAAI,CAACtK,YAAY,EAAE;gBACrB,MAAM0N,UAAU,GAAGpD,KAAI,CAACpI,WAAW,EAAEqL,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACnQ,EAAE,IAAI8M,KAAI,CAACtK,YAAY,CAAC;gBACzE,IAAI0N,UAAU,EAAE;kBACdL,QAAQ,IAAI,IAAIK,UAAU,CAACjQ,IAAI,CAACgQ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAI1D,IAAInD,KAAI,CAAC/J,MAAM,EAAE;gBACf,MAAMqN,IAAI,GAAGtD,KAAI,CAACnI,KAAK,EAAEoL,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACrQ,EAAE,IAAI8M,KAAI,CAAC/J,MAAM,CAAC;gBACvD,IAAIqN,IAAI,EAAE;kBACRP,QAAQ,IAAI,IAAIO,IAAI,CAAChQ,IAAI,CAAC6P,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAIpD,IAAInD,KAAI,CAAC7I,WAAW,IAAI6I,KAAI,CAAC7I,WAAW,KAAK,KAAK,EAAE;gBAClD4L,QAAQ,IAAI,IAAI/C,KAAI,CAAC7I,WAAW,EAAE;;cAGpC,IAAI6I,KAAI,CAAC3F,SAAS,CAACC,UAAU,IAAI0F,KAAI,CAAC3F,SAAS,CAACE,QAAQ,EAAE;gBACxDwI,QAAQ,IAAI,IAAI/C,KAAI,CAAC3F,SAAS,CAACC,UAAU,OAAO0F,KAAI,CAAC3F,SAAS,CAACE,QAAQ,EAAE;eAC1E,MAAM,IAAIyF,KAAI,CAAC3F,SAAS,CAACC,UAAU,EAAE;gBACpCyI,QAAQ,IAAI,SAAS/C,KAAI,CAAC3F,SAAS,CAACC,UAAU,EAAE;;cAGlDyI,QAAQ,IAAI,MAAM;cAElB,MAAM/C,KAAI,CAACjG,cAAc,CAACyJ,SAAS,CAACrH,IAAI,EAAE4G,QAAQ,CAAC;YACrD,CAAC;YAAA,gBAAAxH,OAAAkI,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAApB,IAAA,CAAAqB,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAC,QAAQA,CAAC5H,IAAI;IACX,IAAI,CAACtE,KAAK,GAAGsE,IAAI;IACjB;IACA,MAAM6H,SAAS,GAAG,IAAI,CAACxI,MAAM,CAACyH,IAAI,CAAEgB,KAAK,IAAKA,KAAK,CAACxI,GAAG,KAAK,SAAS,CAAC;IACtE;IACA,IAAIyI,aAAa,GAAG,EAAE;IACtB/H,IAAI,CAACgI,OAAO,CAAEb,IAAI,IAAI;MACpB,IAAIc,SAAS,GAAG,IAAI,CAACxK,iBAAiB,CAACqB,OAAO,CAACqI,IAAI,CAACnQ,IAAI,CAAC;MACzD+Q,aAAa,CAACG,IAAI,CAAC;QACjBrR,KAAK,EAAEoR,SAAS;QAChBlS,KAAK,EAAEoR,IAAI,CAACpQ;OACb,CAAC;IACJ,CAAC,CAAC;IACF8Q,SAAS,CAACrI,KAAK,CAAC2I,OAAO,GAAGJ,aAAa;EACzC;EAEAK,cAAcA,CAACvG,KAAU;IACvB;EAAA;EAGFwG,MAAMA,CAACjJ,MAAM,EAAEyG,GAAI;IACjB,IAAI,CAACxG,MAAM,CAAC,CAAC,CAAC,CAACiJ,YAAY,GAAG,IAAI,CAACxO,MAAM;IACzC,QAAQsF,MAAM;MACZ,KAAK,QAAQ;QACX,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC+I,QAAQ,GAAG,KAAK;QACrC;MACF,KAAK,MAAM;QACT,IAAI,CAAClJ,MAAM,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC+I,QAAQ,GAAG,IAAI;QACpC;MACF,KAAK,QAAQ;QACX;MACF;QACE;;IAEJ,IAAI,CAAC7J,MAAM,CAACU,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACV,MAAM,CAACmH,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAACnI,mBAAmB,CAAC8K,kBAAkB,CAAC,IAAI,CAAChK,UAAU,CAAC,CAACiK,UAAU,EAAE;EAC3E;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAACzL,QAAQ,CAAC0L,MAAM,CAAC,UAAU,EAAE,OAAO,EAAG/G,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACK,MAAM,CAAC2G,YAAY,CAAC,eAAe,CAAC,EAAE;QAC9C,IAAIC,aAAa,GAAGjH,KAAK,CAACK,MAAM,CAACK,YAAY,CAAC,eAAe,CAAC;QAC9D,IAAIwG,QAAQ,GAAGlH,KAAK,CAACK,MAAM,CAACK,YAAY,CAAC,UAAU,CAAC;QACpD;QACA,IAAI,CAACzF,OAAO,CAACkM,QAAQ,CAAC,CAACF,aAAa,EAAE,QAAQ,EAAEC,QAAQ,CAAC,EAAE;UACzDE,UAAU,EAAE,IAAI,CAACpM;SAClB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA/G,gBAAgBA,CAAC+L,KAAU;IACzB,IAAI,CAACxL,QAAQ,GAAGwL,KAAK;IACrB,IAAI,IAAI,CAACxL,QAAQ,KAAK,cAAc,IAAI,IAAI,CAAC0H,SAAS,CAACmC,UAAU,EAAE;MACjE,IAAI,CAACnC,SAAS,CAACmC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEQe,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACrD,SAAS,CAACmC,UAAU,EAAE;MAC7B,IAAI,CAACnC,SAAS,CAACmC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEA6I,WAAWA,CAAA;IACT,IAAI,CAAC/M,SAAS,CAACgN,WAAW,EAAE;IAC5B,IAAI,CAACR,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC7G,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;EAAC,QAAAsH,CAAA;qBAzsBUzM,sBAAsB,EAAAzH,EAAA,CAAAmU,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArU,EAAA,CAAAmU,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAtU,EAAA,CAAAmU,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAxU,EAAA,CAAAmU,iBAAA,CAAAM,EAAA,CAAAC,UAAA,GAAA1U,EAAA,CAAAmU,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAA5U,EAAA,CAAAmU,iBAAA,CAAAnU,EAAA,CAAA6U,SAAA,GAAA7U,EAAA,CAAAmU,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAA/U,EAAA,CAAAmU,iBAAA,CAAAa,EAAA,CAAAC,QAAA,GAAAjV,EAAA,CAAAmU,iBAAA,CAAAe,EAAA,CAAAC,cAAA,GAAAnV,EAAA,CAAAmU,iBAAA,CAAAiB,EAAA,CAAAC,aAAA,GAAArV,EAAA,CAAAmU,iBAAA,CAAAmB,EAAA,CAAAC,mBAAA,GAAAvV,EAAA,CAAAmU,iBAAA,CAAAqB,GAAA,CAAAC,WAAA,GAAAzV,EAAA,CAAAmU,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAA5U,EAAA,CAAAmU,iBAAA,CAAAuB,GAAA,CAAAC,kBAAA,GAAA3V,EAAA,CAAAmU,iBAAA,CAAAyB,GAAA,CAAAC,KAAA,GAAA7V,EAAA,CAAAmU,iBAAA,CAAA2B,GAAA,CAAAC,aAAA,GAAA/V,EAAA,CAAAmU,iBAAA,CAAA6B,GAAA,CAAAC,iBAAA,GAAAjW,EAAA,CAAAmU,iBAAA,CAAAa,EAAA,CAAAkB,WAAA,GAAAlW,EAAA,CAAAmU,iBAAA,CAAAa,EAAA,CAAAmB,sBAAA;EAAA;EAAA,QAAAC,EAAA;UAAtB3O,sBAAsB;IAAA4O,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;uBAKtB1N,kBAAkB;;;;;;;;;;;;iBALlB2N,GAAA,CAAA/J,eAAA,CAAAhJ,MAAA,CAAuB;QAAA,UAAA1D,EAAA,CAAA0W,iBAAA;;;;;;;;QCtCpC1W,EAAA,CAAAC,cAAA,aAA+C;QAG7CD,EAAA,CAAAc,SAAA,4BAAyE;QAIzEd,EAAA,CAAAC,cAAA,aAAiB;QAKXD,EAAA,CAAAiE,UAAA,IAAA0S,wCAAA,oBAcS;QACV3W,EAAA,CAAAgB,YAAA,EAAM;QAIThB,EAAA,CAAAC,cAAA,aAAkB;QAEjBD,EAAA,CAAAiE,UAAA,KAAA2S,sCAAA,mBA8HM;QAEN5W,EAAA,CAAAiE,UAAA,KAAA4S,sCAAA,kBAKM;QAEN7W,EAAA,CAAAiE,UAAA,KAAA6S,sCAAA,kBAKM;QACP9W,EAAA,CAAAgB,YAAA,EAAM;QAMVhB,EAAA,CAAAC,cAAA,wBAAqH;QACpHD,EAAA,CAAAc,SAAA,8BACqB;QACtBd,EAAA,CAAAgB,YAAA,EAAe;QAEfhB,EAAA,CAAAiE,UAAA,KAAA8S,8CAAA,iCAAA/W,EAAA,CAAA0F,sBAAA,CAGc;;;QAzLQ1F,EAAA,CAAAsB,SAAA,GAA+B;QAA/BtB,EAAA,CAAAuB,UAAA,kBAAAkV,GAAA,CAAA7H,aAAA,CAA+B;QAW3B5O,EAAA,CAAAsB,SAAA,GAAc;QAAdtB,EAAA,CAAAuB,UAAA,YAAAkV,GAAA,CAAAlN,SAAA,CAAc;QAmB7BvJ,EAAA,CAAAsB,SAAA,GAAiC;QAAjCtB,EAAA,CAAAuB,UAAA,SAAAkV,GAAA,CAAAtV,QAAA,oBAAiC;QAgIjCnB,EAAA,CAAAsB,SAAA,GAAqC;QAArCtB,EAAA,CAAAuB,UAAA,SAAAkV,GAAA,CAAAtV,QAAA,wBAAqC;QAOrCnB,EAAA,CAAAsB,SAAA,GAA4B;QAA5BtB,EAAA,CAAAuB,UAAA,SAAAkV,GAAA,CAAAtV,QAAA,eAA4B;QAY4BnB,EAAA,CAAAsB,SAAA,GAAmB;QAAnBtB,EAAA,CAAAuB,UAAA,SAAAkV,GAAA,CAAAnN,UAAA,CAAmB;QACjEtJ,EAAA,CAAAsB,SAAA,GAAmB;QAAnBtB,EAAA,CAAAuB,UAAA,UAAAkV,GAAA,CAAA5N,SAAA,CAAmB,WAAA4N,GAAA,CAAAtM,MAAA,YAAAsM,GAAA,CAAAjN,MAAA", "names": ["environment", "Subject", "<PERSON><PERSON>", "moment", "NgbDate", "i0", "ɵɵelementStart", "ɵɵlistener", "LeagueReportsComponent_button_8_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "type_r6", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onSelectViewType", "value", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "ctx_r0", "viewType", "ɵɵpropertyInterpolate1", "ɵɵattribute", "ɵɵadvance", "ɵɵproperty", "icon_name", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "label", "season_r19", "id", "name", "tournament_r20", "club_r21", "code", "LeagueReportsComponent_div_10_ng_template_35_Template_span_mouseenter_0_listener", "_r25", "date_r22", "date", "ctx_r24", "hoveredDate", "LeagueReportsComponent_div_10_ng_template_35_Template_span_mouseleave_0_listener", "ctx_r26", "focused_r23", "ctx_r15", "isRange", "isHovered", "isInside", "day", "LeagueReportsComponent_div_10_ng_template_37_Template_button_click_2_listener", "_r28", "_r13", "ɵɵreference", "ctx_r27", "clearDateRange", "close", "LeagueReportsComponent_div_10_ng_template_37_Template_button_click_4_listener", "status_r30", "LeagueReportsComponent_div_10_Template_ng_select_ngModelChange_6_listener", "$event", "_r32", "ctx_r31", "seasonId", "LeagueReportsComponent_div_10_Template_ng_select_change_6_listener", "ctx_r33", "onSelectSeason", "ɵɵtemplate", "LeagueReportsComponent_div_10_ng_option_8_Template", "LeagueReportsComponent_div_10_Template_ng_select_ngModelChange_14_listener", "ctx_r34", "tournamentId", "LeagueReportsComponent_div_10_Template_ng_select_change_14_listener", "ctx_r35", "onSelectTournament", "LeagueReportsComponent_div_10_ng_option_16_Template", "LeagueReportsComponent_div_10_Template_ng_select_ngModelChange_21_listener", "ctx_r36", "clubId", "LeagueReportsComponent_div_10_Template_ng_select_change_21_listener", "ctx_r37", "onSelectClub", "LeagueReportsComponent_div_10_ng_option_23_Template", "LeagueReportsComponent_div_10_Template_input_click_29_listener", "ctx_r38", "openDatePicker", "LeagueReportsComponent_div_10_Template_input_dateSelect_29_listener", "ctx_r39", "onDateSelection", "LeagueReportsComponent_div_10_Template_button_click_33_listener", "ctx_r40", "LeagueReportsComponent_div_10_ng_template_35_Template", "ɵɵtemplateRefExtractor", "LeagueReportsComponent_div_10_ng_template_37_Template", "LeagueReportsComponent_div_10_Template_ng_select_ngModelChange_43_listener", "ctx_r41", "matchStatus", "LeagueReportsComponent_div_10_Template_ng_select_change_43_listener", "ctx_r42", "onSelectMatchStatus", "LeagueReportsComponent_div_10_ng_option_45_Template", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate", "ctx_r1", "seasons", "tournaments", "clubs", "formatter", "format", "fromDate", "toDate", "_r14", "_r16", "matchStatusOptions", "dtOptions", "dtTrigger", "LeagueReportsComponent_ng_template_15_Template_app_btn_dropdown_action_emitter_0_listener", "_r46", "emitter_r44", "captureEvents", "ctx_r5", "rowActions", "data_r43", "LeagueReportsComponent", "constructor", "route", "_router", "_commonsService", "_http", "_trans", "renderer", "_teamService", "_modalService", "_loadingService", "_toastService", "_registrationService", "_clubService", "_translateService", "_coreSidebarService", "_titleService", "_exportService", "_tournamentService", "calendar", "dtElement", "DataTableDirective", "currentTeam", "date<PERSON><PERSON><PERSON>", "start_date", "end_date", "dateRangeValue", "isSelecting", "isTableLoading", "table_name", "viewTypes", "params", "editor_id", "title", "create", "instant", "edit", "remove", "url", "apiUrl", "method", "action", "fields", "key", "type", "props", "placeholder", "required", "setTitle", "_getCurrentSeason", "show", "getAllSeasonActive", "subscribe", "data", "_getTournaments", "dtInstance", "then", "ajax", "reload", "next", "error", "fire", "text", "message", "icon", "confirmButtonText", "dismiss", "_getClubs", "getAllClubs", "res", "Promise", "resolve", "reject", "refreshDataTable", "console", "log", "after", "updateDateRange", "setTimeout", "dateRangePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "from", "before", "equals", "onDateRangeChange", "fromMoment", "year", "month", "toString", "padStart", "toMoment", "get", "ngOnInit", "_this", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "isLink", "dom", "dataTableDefaults", "select", "rowId", "processing", "language", "lang", "dataTablesParameters", "callback", "URLSearchParams", "undefined", "append", "queryString", "post", "resp", "recordsTotal", "recordsFiltered", "responsive", "scrollX", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "row", "metadata", "display", "filter", "displayData", "displayDate", "buttons", "extend", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "filename", "season", "find", "s", "replace", "tournament", "t", "club", "c", "exportCsv", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "setClubs", "clubField", "field", "current_clubs", "for<PERSON>ach", "club_name", "push", "options", "onCaptureEvent", "editor", "defaultValue", "disabled", "getSidebarRegistry", "toggle<PERSON><PERSON>", "ngAfterViewInit", "unlistener", "listen", "hasAttribute", "tournament_id", "stage_id", "navigate", "relativeTo", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "CommonsService", "i3", "HttpClient", "i4", "TranslateService", "Renderer2", "i5", "TeamService", "i6", "NgbModal", "i7", "LoadingService", "i8", "ToastrService", "i9", "RegistrationService", "i10", "ClubService", "i11", "CoreSidebarService", "i12", "Title", "i13", "ExportService", "i14", "TournamentService", "NgbCalendar", "NgbDateParserFormatter", "_2", "selectors", "viewQuery", "LeagueReportsComponent_Query", "rf", "ctx", "ɵɵresolveDocument", "LeagueReportsComponent_button_8_Template", "LeagueReportsComponent_div_10_Template", "LeagueReportsComponent_div_11_Template", "LeagueReportsComponent_div_12_Template", "LeagueReportsComponent_ng_template_15_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  AfterViewInit,\r\n  ViewEncapsulation,\r\n  TemplateRef,\r\n  HostListener,\r\n  OnDestroy\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { ExportService } from 'app/services/export.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport { Subject } from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { NgbCalendar, NgbDate, NgbDateStruct, NgbDateParserFormatter, NgbDatepicker } from '@ng-bootstrap/ng-bootstrap';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent\r\n  implements AfterViewInit, OnInit, OnDestroy\r\n{\r\n  @ViewChild('rowActionBtn') rowActionBtn: TemplateRef<any>;\r\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  dtOptions: any = {};\r\n  private unlistener: () => void;\r\n  public currentTeam: any = {};\r\n  public seasonId: any;\r\n  public clubId: any = null;\r\n  public tournamentId: any = null;\r\n  public dateRange: any = { start_date: null, end_date: null };\r\n  public dateRangeValue: string = '';\r\n  hoveredDate: NgbDate | null = null;\r\n  fromDate: NgbDate | null = null;\r\n  toDate: NgbDate | null = null;\r\n  isSelecting: boolean = false; // Track if we're in the middle of selecting range\r\n  private clickOutsideTimeout: any;\r\n  public matchStatus: string = 'all';\r\n  public isTableLoading: boolean = false;\r\n  public modalRef: any;\r\n  public contentHeader: object;\r\n  public seasons;\r\n  public clubs;\r\n  public tournaments;\r\n  public table_name = 'team-table';\r\n\r\n  viewTypes = [\r\n    { label: 'Table View', value: 'league_table', icon_name: 'fa-light fa-table-list' },\r\n    { label: 'Schedule View', value: 'schedule_matches', icon_name: 'fa-light fa-calendar' },\r\n    // { label: 'Bracket View', value: 'bracket', icon_name: 'grid' },\r\n  ];\r\n\r\n  viewType: string = 'league_table';\r\n\r\n  matchStatusOptions = [\r\n    { label: 'All Status', value: 'all' },\r\n    { label: 'Upcoming', value: 'upcoming' },\r\n    { label: 'Active', value: 'active' },\r\n    { label: 'Completed', value: 'completed' },\r\n    { label: 'Cancelled', value: 'cancelled' },\r\n  ];\r\n\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: this._translateService.instant('Create New Tournament'),\r\n      edit: 'Edit team',\r\n      remove: 'Delete team'\r\n    },\r\n    url: `${environment.apiUrl}/teams/editor`,\r\n    method: 'POST',\r\n    action: 'create'\r\n  };\r\n\r\n  public fields: any[] = [\r\n    {\r\n      key: 'home_score',\r\n      type: 'number',\r\n      props: {\r\n        label: this._translateService.instant('Home team score'),\r\n        placeholder: this._translateService.instant('Enter score of team'),\r\n        required: true,\r\n      }\r\n    },\r\n    {\r\n      key: 'away_score',\r\n      type: 'number',\r\n      props: {\r\n        label: this._translateService.instant('Away team score'),\r\n        placeholder: this._translateService.instant('Enter score of team'),\r\n        required: true,\r\n      }\r\n    }\r\n  ];\r\n\r\n  // private variables\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _teamService: TeamService,\r\n    public _modalService: NgbModal,\r\n    public _loadingService: LoadingService,\r\n    public _toastService: ToastrService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _titleService: Title,\r\n    private _exportService: ExportService,\r\n    public _tournamentService: TournamentService,\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n        this.dtTrigger.next(this.dtOptions);\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n        // this.clubId = this.clubs[0];\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectTournament($event) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n    this.refreshDataTable();\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      rowId: 'id',\r\n      processing: true,\r\n      language: {\r\n        ...this._commonsService.dataTableDefaults.lang,\r\n        // processing: `<div class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\r\n        //               <div class=\"spinner-border text-primary\" role=\"status\">\r\n        //                 <span class=\"sr-only\">Loading...</span>\r\n        //               </div>\r\n                      \r\n        //             </div>`,\r\n        processing: ``,\r\n      },\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        // Build query parameters for all filters\r\n        let params = new URLSearchParams();\r\n\r\n        if (this.clubId != undefined && this.clubId !== null) {\r\n          params.append('club_id', this.clubId);\r\n        }\r\n\r\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\r\n          params.append('tournament_id', this.tournamentId);\r\n        }\r\n\r\n        if (this.dateRange.start_date) {\r\n          params.append('start_date', this.dateRange.start_date);\r\n        }\r\n\r\n        if (this.dateRange.end_date) {\r\n          params.append('end_date', this.dateRange.end_date);\r\n        }\r\n\r\n        if (this.matchStatus && this.matchStatus !== 'all') {\r\n          params.append('match_status', this.matchStatus);\r\n        }\r\n\r\n        const queryString = params.toString();\r\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${\r\n          queryString ? '?' + queryString : ''\r\n        }`;\r\n\r\n        this.isTableLoading = true;\r\n        this._http.post<any>(url, dataTablesParameters).subscribe(\r\n          (resp: any) => {\r\n            this.isTableLoading = false;\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          },\r\n          (error) => {\r\n            this.isTableLoading = false;\r\n            console.error('Error loading table data:', error);\r\n            callback({\r\n              recordsTotal: 0,\r\n              recordsFiltered: 0,\r\n              data: [],\r\n            });\r\n          }\r\n        );\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      columnDefs: [{ responsivePriority: 1, targets: -1 }],\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('No.'),\r\n          data: null,\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: function (data, type, row, metadata) {\r\n\r\n            return metadata.row + 1\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Tournament'),\r\n          data: 'name',\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: {\r\n            display: (data, type, row) => {\r\n              return data ?? 'TBD';\r\n            },\r\n            filter: (data, type, row) => {\r\n              return data;\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          render: function (data) {\r\n            const displayData = data || 'TBD';\r\n            return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Date'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n          render: function (data, type, row) {\r\n            const displayDate =\r\n              !data || data === 'TBD'\r\n                ? 'TBD'\r\n                : moment(data).format('YYYY-MM-DD');\r\n            return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Start time'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('End time'),\r\n          data: 'end_time',\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Location'),\r\n          data: 'location',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home Team'),\r\n          data: 'home_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: 'VS',\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return 'vs';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away Team'),\r\n          data: 'away_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home score'),\r\n          data: 'home_score',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return '-';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away score'),\r\n          data: 'away_score',\r\n          className: 'text-center'\r\n        }\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant(\r\n              'Export CSV'\r\n            )}`,\r\n            extend: 'csv',\r\n            action: async (e: any, dt: any, button: any, config: any) => {\r\n              const data = dt.buttons.exportData();\r\n              \r\n              // Generate filename with current filter information\r\n              let filename = 'Matches_Report';\r\n              \r\n              if (this.seasonId) {\r\n                const season = this.seasons?.find(s => s.id == this.seasonId);\r\n                if (season) {\r\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.tournamentId) {\r\n                const tournament = this.tournaments?.find(t => t.id == this.tournamentId);\r\n                if (tournament) {\r\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.clubId) {\r\n                const club = this.clubs?.find(c => c.id == this.clubId);\r\n                if (club) {\r\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\r\n                }\r\n              }\r\n              \r\n              if (this.matchStatus && this.matchStatus !== 'all') {\r\n                filename += `_${this.matchStatus}`;\r\n              }\r\n              \r\n              if (this.dateRange.start_date && this.dateRange.end_date) {\r\n                filename += `_${this.dateRange.start_date}_to_${this.dateRange.end_date}`;\r\n              } else if (this.dateRange.start_date) {\r\n                filename += `_from_${this.dateRange.start_date}`;\r\n              }\r\n              \r\n              filename += '.csv';\r\n              \r\n              await this._exportService.exportCsv(data, filename);\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  setClubs(data) {\r\n    this.clubs = data;\r\n    // get field has key club_id\r\n    const clubField = this.fields.find((field) => field.key === 'club_id');\r\n    // set options for club field\r\n    let current_clubs = [];\r\n    data.forEach((club) => {\r\n      let club_name = this._translateService.instant(club.name);\r\n      current_clubs.push({\r\n        label: club_name,\r\n        value: club.id\r\n      });\r\n    });\r\n    clubField.props.options = current_clubs;\r\n  }\r\n\r\n  onCaptureEvent(event: any) {\r\n    // console.log(event);\r\n  }\r\n\r\n  editor(action, row?) {\r\n    this.fields[0].defaultValue = this.clubId;\r\n    switch (action) {\r\n      case 'create':\r\n        this.fields[2].props.disabled = false;\r\n        break;\r\n      case 'edit':\r\n        this.fields[2].props.disabled = true;\r\n        break;\r\n      case 'remove':\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('tournament_id')) {\r\n        let tournament_id = event.target.getAttribute('tournament_id');\r\n        let stage_id = event.target.getAttribute('stage_id');\r\n        //  navigate to path ':tournament_id/stages/:stage_id'\r\n        this._router.navigate([tournament_id, 'stages', stage_id], {\r\n          relativeTo: this.route\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  onSelectViewType(event: any) {\r\n    this.viewType = event;\r\n    if (this.viewType === 'league_table' && this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  private refreshDataTable() {\r\n    if (this.dtElement.dtInstance) {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n      });\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n    this.unlistener();\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n\t<div class=\"content-body\">\r\n\t\t<!-- content-header component -->\r\n\t\t<app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n\t\t\r\n\r\n\t\t<div class=\"row\">\r\n\t\t\t<div class=\"col-12\">\r\n\t\t\t\t\t<div class=\"bg-white\">\r\n\t\t\t\t\t\t<nav>\t\r\n\t\t\t\t\t\t\t<div class=\"nav nav-tabs\" id=\"nav-tab\" role=\"tablist\">\r\n\t\t\t\t\t\t\t\t<button \r\n\r\n\t\t\t\t\t\t\t\t\t*ngFor=\"let type of viewTypes; let i = index\" \r\n\t\t\t\t\t\t\t\t\tclass=\"nav-link\" \r\n\t\t\t\t\t\t\t\t\t[class.active]=\"viewType === type.value\"\r\n\t\t\t\t\t\t\t\t\tid=\"nav-{{type.value}}-tab\" \r\n\t\t\t\t\t\t\t\t\tdata-bs-toggle=\"tab\" \r\n\t\t\t\t\t\t\t\t\ttype=\"button\" \r\n\t\t\t\t\t\t\t\t\trole=\"tab\"\r\n\t\t\t\t\t\t\t\t\t(click)=\"onSelectViewType(type.value)\"\r\n\t\t\t\t\t\t\t\t\taria-controls=\"nav-{{type.value}}\" \r\n\t\t\t\t\t\t\t\t\t[attr.aria-selected]=\"viewType === type.value\">\r\n\t\t\t\t\t\t\t\t\t<i [ngClass]=\"type.icon_name\"></i>\r\n\t\t\t\t\t\t\t\t\t{{ type.label | translate }}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</nav>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div class=\"card\">\r\n\t\t\t\t\t<!-- Content for League Table tab -->\r\n\t\t\t\t\t<div *ngIf=\"viewType === 'league_table'\" class=\"p-1\">\r\n\t\t\t\t\t\t<div class=\"row mb-1\">\r\n\t\t\t\t\t\t\t<div class=\"col-12\">\r\n\t\t\t\t\t\t\t\t<label for=\"season\" class=\"form-label\">{{'Season'|translate}}</label>\r\n\t\t\t\t\t\t\t\t<ng-select \r\n\t\t\t\t\t\t\t\t\t[searchable]=\"true\" \r\n\t\t\t\t\t\t\t\t\t[clearable]=\"false\" \r\n\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Season'|translate}}\"\r\n\t\t\t\t\t\t\t\t\t[(ngModel)]=\"seasonId\" \r\n\t\t\t\t\t\t\t\t\t(change)=\"onSelectSeason($event)\">\r\n\t\t\t\t\t\t\t\t\t<ng-option *ngFor=\"let season of seasons\" [value]=\"season.id\">\r\n\t\t\t\t\t\t\t\t\t\t{{ season.name | translate }}\r\n\t\t\t\t\t\t\t\t\t</ng-option>\r\n\t\t\t\t\t\t\t\t</ng-select>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"row mb-1\">\r\n\t\t\t\t\t\t\t<div class=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t<label for=\"tournament\" class=\"form-label\">{{'Tournament'|translate}}</label>\r\n\t\t\t\t\t\t\t\t<ng-select \r\n\t\t\t\t\t\t\t\t\t[searchable]=\"true\" \r\n\t\t\t\t\t\t\t\t\t[clearable]=\"true\" \r\n\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Tournament'|translate}}\"\r\n\t\t\t\t\t\t\t\t\t[(ngModel)]=\"tournamentId\" \r\n\t\t\t\t\t\t\t\t\t(change)=\"onSelectTournament($event)\">\r\n\t\t\t\t\t\t\t\t\t<ng-option *ngFor=\"let tournament of tournaments\" [value]=\"tournament.id\">\r\n\t\t\t\t\t\t\t\t\t\t{{ tournament.name | translate }}\r\n\t\t\t\t\t\t\t\t\t</ng-option>\r\n\t\t\t\t\t\t\t\t</ng-select>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t<label for=\"club\" class=\"form-label\">{{'Club'|translate}}</label>\r\n\t\t\t\t\t\t\t\t<ng-select \r\n\t\t\t\t\t\t\t\t\t[searchable]=\"true\" \r\n\t\t\t\t\t\t\t\t\t[clearable]=\"true\" \r\n\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Club'|translate}}\" \r\n\t\t\t\t\t\t\t\t\t[(ngModel)]=\"clubId\"\r\n\t\t\t\t\t\t\t\t\t(change)=\"onSelectClub($event)\">\r\n\t\t\t\t\t\t\t\t\t<ng-option *ngFor=\"let club of clubs\" [value]=\"club.id\">\r\n\t\t\t\t\t\t\t\t\t\t{{ club.code | translate }}\r\n\t\t\t\t\t\t\t\t\t</ng-option>\r\n\t\t\t\t\t\t\t\t</ng-select>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t<label for=\"dateRange\" class=\"form-label\">{{'Date Range'|translate}}</label>\r\n\t\t\t\t\t\t\t\t<div class=\"input-group\">\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\tname=\"daterange\"\r\n\t\t\t\t\t\t\t\t\t\tclass=\"form-control\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Date Range'|translate}}\"\r\n\t\t\t\t\t\t\t\t\t\tngbDatepicker\r\n\t\t\t\t\t\t\t\t\t\treadonly\r\n\t\t\t\t\t\t\t\t\t\t#dateRangePicker=\"ngbDatepicker\"\r\n\t\t\t\t\t\t\t\t\t\t[value]=\"formatter.format(fromDate) + (toDate ? ' - ' + formatter.format(toDate) : '')\"\r\n\t\t\t\t\t\t\t\t\t\t(click)=\"openDatePicker()\"\r\n\t\t\t\t\t\t\t\t\t\t[dayTemplate]=\"dayTemplate\"\r\n\t\t\t\t\t\t\t\t\t\t[footerTemplate]=\"footerTemplate\"\r\n\t\t\t\t\t\t\t\t\t\t(dateSelect)=\"onDateSelection($event)\"\r\n\t\t\t\t\t\t\t\t\t\t[firstDayOfWeek]=\"1\"\r\n\t\t\t\t\t\t\t\t\t\t[displayMonths]=\"2\"\r\n\t\t\t\t\t\t\t\t\t\toutsideDays=\"hidden\"\r\n\t\t\t\t\t\t\t\t\t\t[autoClose]=\"false\"\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<div class=\"input-group-append\">\r\n\t\t\t\t\t\t\t\t\t\t<button \r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"btn btn-outline-secondary\" \r\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"openDatePicker()\" \r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"button\">\r\n\t\t\t\t\t\t\t\t\t\t\t<i class=\"feather icon-calendar\"></i>\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<ng-template #dayTemplate let-date=\"date\" let-focused=\"focused\">\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tclass=\"custom-day\"\r\n\t\t\t\t\t\t\t\t\t\t[class.focused]=\"focused\"\r\n\t\t\t\t\t\t\t\t\t\t[class.range]=\"isRange(date)\"\r\n\t\t\t\t\t\t\t\t\t\t[class.faded]=\"isHovered(date) || isInside(date)\"\r\n\t\t\t\t\t\t\t\t\t\t(mouseenter)=\"hoveredDate = date\"\r\n\t\t\t\t\t\t\t\t\t\t(mouseleave)=\"hoveredDate = null\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{{ date.day }}\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t</ng-template>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<ng-template #footerTemplate>\r\n\t\t\t\t\t\t\t\t\t<hr class=\"my-0\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"d-flex justify-content-between p-2\">\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"btn btn-outline-secondary btn-sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"clearDateRange(); dateRangePicker.close()\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tClear\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"btn btn-primary btn-sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t(click)=\"dateRangePicker.close()\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tClose\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</ng-template>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"col-md-3\">\r\n\t\t\t\t\t\t\t\t<label for=\"matchStatus\" class=\"form-label\">{{'Match Status'|translate}}</label>\r\n\t\t\t\t\t\t\t\t<ng-select \r\n\t\t\t\t\t\t\t\t\t[searchable]=\"false\" \r\n\t\t\t\t\t\t\t\t\t[clearable]=\"false\" \r\n\t\t\t\t\t\t\t\t\tplaceholder=\"{{'Select Status'|translate}}\"\r\n\t\t\t\t\t\t\t\t\t[(ngModel)]=\"matchStatus\" \r\n\t\t\t\t\t\t\t\t\t(change)=\"onSelectMatchStatus($event)\">\r\n\t\t\t\t\t\t\t\t\t<ng-option *ngFor=\"let status of matchStatusOptions\" [value]=\"status.value\">\r\n\t\t\t\t\t\t\t\t\t\t{{ status.label | translate }}\r\n\t\t\t\t\t\t\t\t\t</ng-option>\r\n\t\t\t\t\t\t\t\t</ng-select>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"row mb-1\">\r\n\t\t\t\t\t\t\t<div class=\"col-12\">\r\n\t\t\t\t\t\t\t\t<table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"table border row-border hover\">\r\n\t\t\t\t\t\t\t\t</table>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div *ngIf=\"viewType === 'schedule_matches'\" class=\"p-2\">\r\n\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t<h4>{{'Schedule Matches Content'|translate}}</h4>\r\n\t\t\t\t\t\t\t<p>{{'Coming soon...'|translate}}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div *ngIf=\"viewType === 'bracket'\" class=\"p-2\">\r\n\t\t\t\t\t\t<div class=\"text-center\">\r\n\t\t\t\t\t\t\t<h4>{{'Bracket Content'|translate}}</h4>\r\n\t\t\t\t\t\t\t<p>{{'Coming soon...'|translate}}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</div>\r\n\r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n\t<app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\r\n\t</app-editor-sidebar>\r\n</core-sidebar>\r\n\r\n<ng-template #rowActionBtn let-data=\"adtData\" let-emitter=\"captureEvents\">\r\n\t<app-btn-dropdown-action [actions]=\"rowActions\" [data]=\"data\" (emitter)=\"emitter($event)\"\r\n\t\tbtnStyle=\"font-size:15px;color:black!important\"></app-btn-dropdown-action>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}